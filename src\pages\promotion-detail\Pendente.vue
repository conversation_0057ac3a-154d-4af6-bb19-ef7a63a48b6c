<script setup lang='ts' name='Pendente'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const emailRef = ref()
//隐藏底下菜单
// appStore.setFooterDialogVisble(false)



</script>

<template>
    <div class = "rect">
        <span class="one">Bônus</span>
        <span class="two"> 0,00</span>
    </div>
    <div class = "content">
        <app-empty text="Sem Registros"/>
    </div>
    
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.rect{
    height: 300px;
    .one{
        top:30px;
        right: 72px;
        position: absolute;
        color: var(--theme-text-color-lighten);
        font-size: 24px;
    }
    .two{
        top:30px;
        right: 16px;
        position: absolute;
        color: var(--theme-secondary-color-finance);
        font-size: 24px;
    }
}

.content{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45vh; /* 使用视口高度来使容器占满屏幕垂直空间 */
}

</style>
<route lang="yaml">
  meta:
    auth: true
</route>