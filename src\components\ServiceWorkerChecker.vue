<template>
  <div v-if="showChecker" class="sw-checker">
    <div class="checker-content">
      <div class="checker-header">
        <h3>🔧 PWA安装环境检查</h3>
        <button @click="closeChecker" class="close-btn">×</button>
      </div>

      <div class="check-results">
        <!-- Service Worker检查 -->
        <div class="check-item" :class="checks.serviceWorker.status">
          <div class="check-icon">{{ checks.serviceWorker.icon }}</div>
          <div class="check-content">
            <div class="check-title">Service Worker</div>
            <div class="check-description">{{ checks.serviceWorker.message }}</div>
            <div v-if="checks.serviceWorker.details" class="check-details">
              {{ checks.serviceWorker.details }}
            </div>
          </div>
        </div>

        <!-- Manifest检查 -->
        <div class="check-item" :class="checks.manifest.status">
          <div class="check-icon">{{ checks.manifest.icon }}</div>
          <div class="check-content">
            <div class="check-title">Web App Manifest</div>
            <div class="check-description">{{ checks.manifest.message }}</div>
          </div>
        </div>

        <!-- HTTPS检查 -->
        <div class="check-item" :class="checks.https.status">
          <div class="check-icon">{{ checks.https.icon }}</div>
          <div class="check-content">
            <div class="check-title">HTTPS协议</div>
            <div class="check-description">{{ checks.https.message }}</div>
          </div>
        </div>

        <!-- 小米浏览器特殊检查 -->
        <div v-if="isMiuiBrowser" class="check-item" :class="checks.miuiSpecial.status">
          <div class="check-icon">{{ checks.miuiSpecial.icon }}</div>
          <div class="check-content">
            <div class="check-title">小米浏览器兼容性</div>
            <div class="check-description">{{ checks.miuiSpecial.message }}</div>
            <div class="check-details">
              <ul>
                <li>Service Worker必须在根目录 (/service-worker.js)</li>
                <li>无法手动触发安装，只能等待自动弹出</li>
                <li>如果之前拒绝过，需要清除缓存</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 修复建议 -->
      <div v-if="hasIssues" class="fix-suggestions">
        <h4>🛠️ 修复建议</h4>
        <div class="suggestions-list">
          <div v-for="suggestion in suggestions" :key="suggestion.type" class="suggestion-item">
            <div class="suggestion-icon">{{ suggestion.icon }}</div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-description">{{ suggestion.description }}</div>
              <button v-if="suggestion.action" @click="suggestion.action" class="suggestion-btn">
                {{ suggestion.actionText }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="checker-actions">
        <button @click="recheckAll" class="action-btn primary">重新检查</button>
        <button @click="closeChecker" class="action-btn secondary">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

const showChecker = ref(false)
const isMiuiBrowser = ref(false)

// 检查结果
const checks = ref({
  serviceWorker: {
    status: 'checking',
    icon: '⏳',
    message: '检查中...',
    details: ''
  },
  manifest: {
    status: 'checking',
    icon: '⏳',
    message: '检查中...'
  },
  https: {
    status: 'checking',
    icon: '⏳',
    message: '检查中...'
  },
  miuiSpecial: {
    status: 'checking',
    icon: '⏳',
    message: '检查中...'
  }
})

// 修复建议
const suggestions = ref<Array<{
  type: string
  icon: string
  title: string
  description: string
  action?: () => void
  actionText?: string
}>>([])

// 是否有问题
const hasIssues = computed(() => {
  return Object.values(checks.value).some(check => check.status === 'error' || check.status === 'warning')
})

// 检测是否为小米浏览器
const checkMiuiBrowser = () => {
  const userAgent = navigator.userAgent
  isMiuiBrowser.value = /MiuiBrowser|XiaoMi|MI\s|MIUI/i.test(userAgent)
}

// 检查Service Worker
const checkServiceWorker = async () => {
  try {
    if (!('serviceWorker' in navigator)) {
      checks.value.serviceWorker = {
        status: 'error',
        icon: '❌',
        message: '浏览器不支持Service Worker',
        details: ''
      }
      return
    }

    const registration = await navigator.serviceWorker.getRegistration()
    if (registration) {
      const swUrl = registration.scope
      const isRootScope = swUrl.endsWith('/')
      
      checks.value.serviceWorker = {
        status: isRootScope ? 'success' : 'warning',
        icon: isRootScope ? '✅' : '⚠️',
        message: isRootScope ? 'Service Worker已注册在根目录' : 'Service Worker未在根目录',
        details: `作用域: ${swUrl}`
      }

      if (!isRootScope && isMiuiBrowser.value) {
        suggestions.value.push({
          type: 'sw-scope',
          icon: '📁',
          title: '移动Service Worker到根目录',
          description: '小米浏览器要求Service Worker必须在根目录才能触发安装提示',
          action: () => {
            window.open('https://developer.mozilla.org/zh-CN/docs/Web/API/Service_Worker_API', '_blank')
          },
          actionText: '查看文档'
        })
      }
    } else {
      checks.value.serviceWorker = {
        status: 'error',
        icon: '❌',
        message: 'Service Worker未注册',
        details: ''
      }
    }
  } catch (error) {
    checks.value.serviceWorker = {
      status: 'error',
      icon: '❌',
      message: 'Service Worker检查失败',
      details: String(error)
    }
  }
}

// 检查Manifest
const checkManifest = async () => {
  try {
    const manifestLink = document.querySelector('link[rel="manifest"]') as HTMLLinkElement
    if (!manifestLink) {
      checks.value.manifest = {
        status: 'error',
        icon: '❌',
        message: '未找到Manifest文件链接'
      }
      return
    }

    const response = await fetch(manifestLink.href)
    if (response.ok) {
      const manifest = await response.json()
      const hasRequiredFields = manifest.name && manifest.icons && manifest.start_url
      
      checks.value.manifest = {
        status: hasRequiredFields ? 'success' : 'warning',
        icon: hasRequiredFields ? '✅' : '⚠️',
        message: hasRequiredFields ? 'Manifest配置完整' : 'Manifest缺少必要字段'
      }
    } else {
      checks.value.manifest = {
        status: 'error',
        icon: '❌',
        message: 'Manifest文件加载失败'
      }
    }
  } catch (error) {
    checks.value.manifest = {
      status: 'error',
      icon: '❌',
      message: 'Manifest检查失败'
    }
  }
}

// 检查HTTPS
const checkHTTPS = () => {
  const isSecure = location.protocol === 'https:' || location.hostname === 'localhost'
  
  checks.value.https = {
    status: isSecure ? 'success' : 'error',
    icon: isSecure ? '✅' : '❌',
    message: isSecure ? 'HTTPS协议正常' : '需要HTTPS协议'
  }

  if (!isSecure) {
    suggestions.value.push({
      type: 'https',
      icon: '🔒',
      title: '启用HTTPS',
      description: 'PWA需要HTTPS协议才能正常工作（localhost除外）'
    })
  }
}

// 检查小米浏览器特殊要求
const checkMiuiSpecial = () => {
  if (!isMiuiBrowser.value) {
    checks.value.miuiSpecial = {
      status: 'success',
      icon: '✅',
      message: '非小米浏览器，无特殊限制'
    }
    return
  }

  const swScope = checks.value.serviceWorker.details
  const isRootScope = swScope?.includes('scope: ') && swScope.endsWith('/')
  
  checks.value.miuiSpecial = {
    status: isRootScope ? 'success' : 'warning',
    icon: isRootScope ? '✅' : '⚠️',
    message: isRootScope ? '满足小米浏览器要求' : '不满足小米浏览器要求'
  }

  if (!isRootScope) {
    suggestions.value.push({
      type: 'miui-cache',
      icon: '🗑️',
      title: '清除浏览器缓存',
      description: '如果之前拒绝过安装，需要清除缓存才能重新触发安装提示',
      action: () => {
        alert('请在MIUI浏览器设置中清除浏览数据和缓存')
      },
      actionText: '查看步骤'
    })
  }
}

// 重新检查所有项目
const recheckAll = async () => {
  // 重置状态
  Object.keys(checks.value).forEach(key => {
    checks.value[key as keyof typeof checks.value] = {
      status: 'checking',
      icon: '⏳',
      message: '检查中...',
      details: ''
    }
  })
  suggestions.value = []

  // 执行检查
  await Promise.all([
    checkServiceWorker(),
    checkManifest(),
    checkHTTPS()
  ])
  
  checkMiuiSpecial()
}

// 关闭检查器
const closeChecker = () => {
  showChecker.value = false
}

// 显示检查器
const showServiceWorkerChecker = () => {
  showChecker.value = true
  recheckAll()
}

onMounted(() => {
  checkMiuiBrowser()
  
  // 如果是小米浏览器，自动显示检查器
  if (isMiuiBrowser.value) {
    setTimeout(() => {
      showServiceWorkerChecker()
    }, 5000) // 5秒后显示
  }
})

// 暴露方法给父组件
defineExpose({
  showServiceWorkerChecker
})
</script>

<style scoped>
.sw-checker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  padding: 20px;
}

.checker-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.checker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.checker-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-results {
  padding: 20px;
}

.check-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid #eee;
}

.check-item.success {
  background: #d4edda;
  border-color: #c3e6cb;
}

.check-item.warning {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.check-item.error {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.check-item.checking {
  background: #d1ecf1;
  border-color: #bee5eb;
}

.check-icon {
  font-size: 24px;
  margin-right: 16px;
  margin-top: 2px;
}

.check-content {
  flex: 1;
}

.check-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.check-description {
  color: #666;
  margin-bottom: 8px;
}

.check-details {
  font-size: 12px;
  color: #888;
  font-family: monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
}

.check-details ul {
  margin: 0;
  padding-left: 16px;
}

.check-details li {
  margin-bottom: 4px;
}

.fix-suggestions {
  padding: 0 20px 20px;
  border-top: 1px solid #eee;
  margin-top: 20px;
}

.fix-suggestions h4 {
  margin: 20px 0 16px 0;
  color: #333;
  font-size: 16px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.suggestion-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.suggestion-description {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.suggestion-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestion-btn:hover {
  background: #0056CC;
}

.checker-actions {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.primary:hover {
  background: #0056CC;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
}

.action-btn.secondary:hover {
  background: #e9ecef;
}

@media (max-width: 480px) {
  .sw-checker {
    padding: 10px;
  }
  
  .checker-content {
    max-height: 95vh;
  }
  
  .checker-actions {
    flex-direction: column;
  }
}
</style>
