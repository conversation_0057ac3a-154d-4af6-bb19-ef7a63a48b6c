<script setup lang='ts'>
import { colorFilter, stateFilter,format_date } from '~/utils/utils';

const formQuery = reactive({
  flag: '271',
  page: 1,
  page_size: 50
})

const total = ref(0)
const list = ref<any[]>([])
const finished = ref(false)
const havaBetMoney = ref(0)//未解锁金额

const {run: runGetFlowRecordList,data:promoInspectionDatas,loading } = useRequest(() => ApiGetFlowRecordList(), {
  manual: true,
  onSuccess(res:any) {
    console.log(res,promoInspectionDatas.value)
    // promoInspectionDatas.value=[{ID:"123",ChangeMoney:112221,Status:1},{ID:"123456789123456789",ChangeMoney:112221,Status:1}] //测试
  }
})


//未解锁的金币
const {run: runGetWithdrawConf, } = useRequest(ApiGetWithdrawConf,{
    onSuccess(data) {
      console.log(data)
      havaBetMoney.value = data.lock_amount || 0;
    },
    onError(resData){
      
    }
})


// const havaBetMoney = computed(()=>{
//   if(!promoInspectionDatas || !promoInspectionDatas.value)
//   {
//     return 0;
//   }
//   let allNeedWageRequire = 0;
//   for(let i = 0; i < promoInspectionDatas.value.length; i++ ){
//     let item = promoInspectionDatas.value[i]
//     if(item.Status ==0){ //未完成的充值金额
//       allNeedWageRequire += item.ChangeMoney;
//       // allCurWageRequire += item.CurWageRequire;
//     }
//   }
//   return allNeedWageRequire 
// })

runGetFlowRecordList()
//没有多少数据 不需要分页
const refresh = () => {
  // formQuery.page = 1
  // finished.value = false
  // list.value.length = 0
  // run()
  runGetFlowRecordList()
}
const loadMoreData = () => {
  // formQuery.page++
  // run()
}

</script>

<template>
  <div class="topDiv">
    <AppIndexHeader />
    <AppPageTitle left-arrow  background="#324b6e" title-weight="700" />
    <div class="content">
      <div class="top-btn">
        <label class="top-btn-item">Você pode sacar <span >{{ UsAmountFormat(havaBetMoney) }}</span> após completar a tarefa</label>
      </div>
      <div class="listbox">
        <!-- 充值 -->
        <div class="listbox_ItemTitle"  >
          <span class="element1" >ID</span>
          <span class="element2" >Quantia</span>
          <!-- <span class="element3">Já apostei</span> -->
          <span class="element4">Estado</span>
        </div>
       
        <AppList :loading="loading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData" v-show="promoInspectionDatas?.length"  style="padding-top: 0px;">
          <div class="list-item"  v-for="(item, index) in  promoInspectionDatas" :key="index">
            <span class="element7">{{ item.ID}}</span>  
            <span class="element8">{{ UsAmountFormat(item.ChangeMoney) }}</span> 
            <!-- <span class="element9">{{ UsAmountFormat(item.CurWageRequire) }}</span> -->
            <AppImage v-if="item.Status==1" src="/img/finance/g" class="element10" /> <!--0进行中  1 已经完成-->
            <AppImage v-if="item.Status==0" src="/img/finance/x" class="element10" /> <!--0进行中  1 已经完成-->
          </div>
        </AppList>

      <app-empty v-show="!promoInspectionDatas?.length" />
      
    </div>

      
  </div>

  </div>
  
</template>

<style lang='scss' scoped>


.topDiv {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 132px;
  position: relative;

}

.content{
  width: 700px;
  margin:0 auto;
  border-radius: 10px;
  height:78vh; 

  .top-btn{
    display: flex;
    justify-content: center;
    align-items:center;
    width: 700px;
    height:82px;
    background:#f8e5f6;
    border-radius:15px;
   
  }
  .top-btn-item{
    font-family:Arial;
    color:#172554;
    font-size:28px;
    // padding-bottom:7px;
    text-align:center;
    span{
      color: #ff0000;
    }
  }
}


.top-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  height: 210px;
}

.flex-sb {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.listbox {
    margin: auto;
    margin-top: 15px;
    font-size: 24px;
    width: 700px;
    .listbox_ItemTitle{
      // @extend .flex-sb;
      display: flex;
      color: #ffffff;
      justify-content:center;
      align-items:center;
      background:#3b82f6;
      border-radius:10px;
      height: 69px;
      font-family:Arial;
    }

    .element1 {
      left:100px; /* 向右移动40px */
     
    }

    .element2{
      left:290px; /* 向右移动40px */
      width: 140px;
    }

    .element3{
      left:440px; /* 向右移动40px */
     
    }
    .element4{
      left:600px; /* 向右移动40px */
    
    }


    .list-item {
      height: 45px;
      margin-top:10px;
      overflow: hidden;
      height:80px;
      color: #ffffff;
      @extend .flex-sb;
       &:nth-child(2n){
        background:#0f306a;
        border-radius:10px;   
        height:80px;
      }
    }

    .list-item span , .listbox_ItemTitle span{
      word-wrap: break-word;
      text-align: center;
      position: absolute;

    }

    // 
    .element7{
      left:12px; 
      width: 150px;
    }

    .element8{
      left:245px; 
      width: 180px;
    }

    .element9{
      left: 380px; 
      width: 180px;
    }

    .element10{
      margin-left: 593px;
      width: 80px;
      width: 41px;
    }

   
  }

</style>

<route lang="yaml">
  meta:
    auth: true
</route>

















