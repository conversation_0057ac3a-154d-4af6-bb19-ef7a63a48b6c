<script setup lang="ts" name="IndexMenu">
const router = useRouter();
import copyIcon from "/icons/svg/copy.svg?raw";
const appStore = useAppStore();
const { isLogin, appFooterVisible, secletType, userInfo } =
  storeToRefs(appStore);
const { isIos } = getBrowser();
const dragStore = useDragStore();
const { showChatDrag } = storeToRefs(dragStore);

enum Menus {
  VIP = "vip",
  INICIO = "inicio",
  PROMOGAO = "promogao",
  DEPOSIT = "deposit",
  CONVIDAR = "convidar",
  CONTA = "conta",
  SUPORTE = "suporte",
  SAQUE = "saque",
  MENU = "menu",
  BAIXAR = "baixar",
}

const saqueAref = ref();
const menuAref = ref();
const depositAref = ref();
const inicioAref = ref();
const contaAref = ref();
const convidarAref = ref();
const oldSelref = ref();
const baixarAref = ref();

const enum JumpViewType {
  Suporte = 0,
  Noticia,
  Notificacao,
  PainelRolante,
  BonusDeSugestao,
}

const menus = ref([
  { path: "/", name: Menus.INICIO, eleref: inicioAref, auth: false },
  { path: "/advancement", name: Menus.PROMOGAO, eleref: inicioAref, auth: false },
  { path: "/finance", name: Menus.DEPOSIT, eleref: depositAref, auth: true },
  {
    path: "/agent/?tab=0",
    name: Menus.CONVIDAR,
    eleref: convidarAref,
    auth: true,
  },
  { path: "/personalCenter", name: Menus.CONTA, eleref: contaAref, auth: true },
  { path: "/takeout", name: Menus.SAQUE, eleref: saqueAref, auth: false },
  { path: "/menu", name: Menus.MENU, eleref: menuAref, auth: false },
  { path: "/serviceMessages", name: Menus.SUPORTE, eleref: inicioAref, auth: false },
  { path: "/serviceMessages", name: Menus.BAIXAR, eleref: baixarAref, auth: false },
  { path: "/advancement?key=2", name: Menus.VIP, eleref: inicioAref, auth: true },
]);

const activeMenu = ref();

const toggleAni = function (curref: any) {
  // 动画效果可以在这里实现
};

const goPay = () => {
  appStore.setShowRecharge(false);
  appStore.setPayVisble(true);
};

//获取玩家VIP信息
const { run: runGetUserVipInfo, data: userVipInfo } = useRequest(
  ApiGetVipMemberInfoData,
  {
    manual: true,
    onSuccess(data) {
      appStore.setUserVipInfo(userVipInfo);
      for (let i = 0; i < vipdata.value?.length; i++) {
        if (vipdata.value[i].level == userVipInfo.value.level) {
          validWaged.value = userVipInfo.value.validWaged;
          if (userVipInfo.value.level != 50) {
            curLevelNeedRunning.value = vipdata.value[i].needRunning;
            nextLevelNeedRunning.value = vipdata.value[i + 1].needRunning;
            break;
          }
        }
      }
    },
  }
);

runGetUserVipInfo();

const menuAClick = function (ty: Menus) {
  if (ty == Menus.DEPOSIT) {
    if (isLogin.value) {
      appStore.setPayVisble(true);
    } else {
      appStore.setLoginDialogVisible(true);
    }
    return;
  } else if (ty == Menus.BAIXAR) {
    if (isIos) {
      // iOS 处理
    } else {
      // Android 或其他平台处理
      if (typeof window !== "undefined" && "installButtonclick" in window) {
        (window as any).installButtonclick();
      }
    }
    return;
  }
  let routerPush = (menu: Menus) => {
    const item = menus.value.filter((i) => i.name === menu)[0];
    if (item) {
      if (!item.auth || isLogin.value) {
        activeMenu.value = menu;
        if (item.name !== Menus.DEPOSIT) {
          toggleAni(item.eleref);
        }
      }
      if (item.name == Menus.PROMOGAO) {
        secletType.value = 0;
      }
      if (item.name == Menus.DEPOSIT) {
        goPay();
      } else {
        router.push(item.path);
      }
    }
  };

  // 关闭menu，返回主页
  if (activeMenu.value === ty && ty === "menu") {
    if (!oldSelref.value) {
      router.push("/");
    } else {
      activeMenu.value = oldSelref.value;
      routerPush(activeMenu.value);
    }
    return;
  } else if (ty !== "menu") {
    const item = menus.value.filter((i) => i.name === ty)[0];
    if (item) {
      if (!item.auth || isLogin.value) {
        oldSelref.value = ty;
      }
    }
  }
  routerPush(ty);
};

// 刷新金额
const loading = ref(false);
const refreshBalance = () => {
  if (loading.value) {
    return;
  }

  loading.value = true;
  appStore.runGetMemberInfo();
  setTimeout(() => {
    loading.value = false;
  }, 1500);
};

const copyName = () => {
  copy(userInfo.value.username || "");
  showToast("Copied!");
};

const isOpenMenu = ref(false);
const isIOSDevice = ref(false);

const { run: runGetPlatformLinkData, data: platformLinkData } = useRequest(
  () => ApiGetPlatformLinkData(),
  {
    manual: true,
    onSuccess(res: any) {
      // 成功处理
    },
  }
);
runGetPlatformLinkData();

const jumpUrl = (url: string) => {
  window.open(url, "_blank");
};

const userAgent = navigator.userAgent || navigator.vendor;

function gotoDownLoad() {
  appStore.setIsShowAppBaixar(false);

  if (
    userAgent.indexOf("iPad") > -1 ||
    userAgent.indexOf("iPhone") > -1 ||
    userAgent.indexOf("iPod") > -1
  ) {
    isIOSDevice.value = true;
  } else if (userAgent.indexOf("Android") > -1) {
    if (typeof window !== "undefined" && "installButtonclick" in window) {
      (window as any).installButtonclick();
    }
  } else {
    if (typeof window !== "undefined" && "installButtonclick" in window) {
      (window as any).installButtonclick();
    }
  }
}
</script>
<template>
  <div class="IndexMenu">
    <div class="loginDiv" v-if="!isLogin">
      <div class="content">
        <div class="button-container">
          <div
            class="login-btn"
            @click="() => appStore.setLoginDialogVisible(true)"
          >
            Login
          </div>
          <div
            class="register-btn"
            @click="() => appStore.setRegisterDialogVisible(true)"
          >
            Registro
          </div>
        </div>
        <div class="menu-icons">
          <div class="menu-icon" @click="() => menuAClick(Menus.DEPOSIT)">
            <AppImage src="/img/index/icon_eposit.png" :width="55" />
            <span>Depósito</span>
          </div>
          <div class="menu-icon" @click="() => menuAClick(Menus.SAQUE)">
            <AppImage src="/img/index/icon_saque.png" :width="55" />
            <span>Saques</span>
          </div>
          <div class="menu-icon" @click="() => menuAClick(Menus.SUPORTE)">
            <AppImage src="/img/index/icon_support.png" :width="55" />
            <span>Suporte</span>
          </div>
        </div>
      </div>
    </div>

    <div class="loginDiv" v-if="isLogin">
      <div class="content">
        <div class="name">
          <span>
            <span class="vip-box-num">
              vip{{ userVipInfo ? userVipInfo.level : 0 }}
            </span>
            {{ userInfo.username }}
          </span>
          <!-- <AppImage
            class="copy"
            src="/icons/personal_copy.webp"
            :width="24"
            @click="() => copyName()"
          /> -->
          <div v-html="copyIcon" @click="() => copyName()"></div>
        </div>
        <div class="right-balance">
          <AppImage class="right-logo" src="icons/content.webp" alt="" />
          <label class="right-text" @click="appStore.setPayVisble(true)">
            {{ userInfo.formatAmount }}</label
          >
          <div
            class="right-refresh"
            :class="{ rotate: loading }"
            @click="refreshBalance"
          ></div>
        </div>

        <div class="menu-icons logged-in-menu">
          <div class="menu-icon" @click="() => menuAClick(Menus.DEPOSIT)">
            <AppImage src="/img/index/icon_eposit.png" :width="55" />
            <span>Depósito</span>
          </div>
          <div class="menu-icon" @click="() => menuAClick(Menus.SAQUE)">
            <AppImage src="/img/index/icon_saque.png" :width="55" />
            <span>Saques</span>
          </div>
          <div class="menu-icon" @click="() => menuAClick(Menus.SUPORTE)">
            <AppImage src="/img/index/icon_support.png" :width="55" />
            <span>Suporte</span>
          </div>
        </div>
      </div>
    </div>

    <div class="menuDiv" style="display: none">
      <div class="menu-btn">
        <AppImage
          src="/img/homeTab/agent_promote.webp"
          :width="57"
          @click="() => menuAClick(Menus.CONVIDAR)"
        />
        <span>Agente</span>
      </div>
      <div class="menu-btn" @click="() => menuAClick(Menus.VIP)">
        <AppImage src="/img/homeTab/vip.webp" :width="57" />
        <span>VIP</span>
      </div>
      <div class="menu-btn" @click="() => gotoDownLoad()">
        <AppImage src="/img/homeTab/app_download.webp" :width="57" />
        <span>Baixar App</span>
      </div>
      <div class="menu-btn" @click="isOpenMenu = !isOpenMenu">
        <AppImage src="/img/homeTab/more_menu.webp" :width="57" />
        <span>Mais</span>
      </div>
    </div>

    <div
      class="overlay"
      v-if="isOpenMenu && showChatDrag"
      @click="isOpenMenu = !isOpenMenu"
    ></div>

    <div
      class="menuPoup"
      v-if="isOpenMenu && showChatDrag"
      @click="isOpenMenu = !isOpenMenu"
    >
      <div class="menuLayout">
        <div class="menuItem" @click.stop="() => menuAClick(Menus.SUPORTE)">
          <AppImage src="/img/homeTab/customer_support.webp" :width="57" />
          <span>Suporte</span>
        </div>
        <div class="menuItem" @click.stop="() => router.push('/takeout')">
          <AppImage src="/img/homeTab/withdraw_money.webp" :width="57" />
          <span>Saque</span>
        </div>
        <div class="menuItem" @click.stop="() => menuAClick(Menus.PROMOGAO)">
          <AppImage src="/img/homeTab/promotions.webp" :width="57" />
          <span>Eventos</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () =>
              router.push({
                path: '/serviceMessages',
                query: { key: JumpViewType.BonusDeSugestao },
              })
          "
        >
          <AppImage src="/img/homeTab/referral_bonus.webp" :width="57" />
          <span>Bônus de Sugestão</span>
        </div>
        <div
          class="menuItem"
          @click.stop="() => router.push('/personalCenter/security')"
        >
          <AppImage src="/img/homeTab/security_settings.webp" :width="57" />
          <span>Segurança</span>
        </div>
        <div
          class="menuItem"
          @click.stop="() => router.push('/personalCenter/userInfo')"
        >
          <AppImage src="/img/homeTab/dados.webp" :width="57" />
          <span>Dados</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/report', query: { key: 0 } })
          "
        >
          <AppImage src="/img/homeTab/account_info.webp" :width="57" />
          <span>Conta</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/report', query: { key: 1 } })
          "
        >
          <AppImage src="/img/homeTab/apostas.webp" :width="57" />
          <span>Apostas</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/report', query: { key: 2 } })
          "
        >
          <AppImage src="/img/homeTab/transaction_report.webp" :width="57" />
          <span>Relatório</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/advancement', query: { key: 4 } })
          "
        >
          <AppImage src="/img/homeTab/pending_tasks.webp" :width="57" />
          <span>Pendente</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/takeout', query: { key: 2 } })
          "
        >
          <AppImage src="/img/homeTab/withdraw_management.webp" :width="57" />
          <span>Gestão Retiradas</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/takeout', query: { key: 1 } })
          "
        >
          <AppImage src="/img/homeTab/betting_tasks.webp" :width="57" />
          <span>Tarefas de Apostas</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/advancement', query: { key: 5 } })
          "
        >
          <AppImage src="/img/homeTab/Histórico.webp" :width="57" />
          <span>Histórico</span>
        </div>
        <div
          class="menuItem"
          v-show="platformLinkData?.instagram.length > 0"
          @click.stop="jumpUrl(platformLinkData.instagram)"
        >
          <AppImage src="/img/leftMenu/Instagram.webp" :width="57" />
          <span>Instagram</span>
        </div>
        <div
          class="menuItem"
          v-show="platformLinkData?.telegram.length > 0"
          @click.stop="jumpUrl(platformLinkData.telegram)"
        >
          <AppImage src="/img/leftMenu/Telegram.webp" :width="57" />
          <span>Telegram</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.vip-box-num {
  // position: absolute;
  
  display:inline-block;
  width: 9.333333vw;
  height: 4vw;
  top: 3.733333vw;
  background-color: #24b299;
  border-radius: 1.6vw;
  // border-bottom-right-radius: 1.6vw;
  text-align: center;
  padding: 0px 0;
  font-style: italic;
  // font-size: 16em;
  color: #e2b54a !important;
}

.IndexMenu {
  height: 115px;
  width: 720px;
  position: relative;
  align-items: center;
  display: flex !important;
  justify-content: space-between;
  position: relative;
  padding: 10px;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin: 0 10px 10px 15px;

  .loginDiv {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    padding: 10px 5px;
    width: 100%;
    box-sizing: border-box;
    font-size: 22px;

    .content {
      display: flex;
      justify-content: space-between;
      width: 100%;

      .button-container {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 10px;
        padding-top: 10px;

        .login-btn {
          background-image: url("/img/index/login_bg.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 155px;
          height: 70px;
          border-radius: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          font-weight: bold;
          cursor: pointer;
          font-size: 22px;
        }

        .register-btn {
          background-image: url("/img/index/reg_bg.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin-left: 10px;
          width: 155px;
          height: 70px;
          border-radius: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          font-weight: bold;
          cursor: pointer;
          font-size: 22px;
        }
      }

      .menu-icons {
        display: flex;
        justify-content: center;
        flex: 0 0 48%;
        gap: 40px;
        margin-top: 5px;

        .menu-icon {
          display: flex;
          flex-direction: column;
          align-items: center;

          span {
            color: var(--theme-primary-font-color);
            font-size: 16px;
            margin-top: 5px;
          }
        }
      }

      span {
        color: var(--theme-primary-color);
        cursor: pointer;
      }
    }

    .name {
      align-items: center;
      display: flex;
      position: absolute;
      left: 20px;
      top: 10px;
      .copy {
        filter: saturate(90%) brightness(82%);
      }

      span {
        color: var(--theme-text-color);
        margin-right: 5px;
        font-size: 24px;
      }
    }

    .right-balance {
      display: inline;
      position: absolute;
      left: 10px;
      bottom: 20px;
      min-width: 100px;
      max-width: 300px;
      height: 44px;
      font-size: 30px;
      background: var(--theme-top-nav-bg);
      border: 1px solid;
      border-color: var(--theme-color-line);
      border-radius: 85px;

      .right-logo {
        display: inline-block;
        vertical-align: middle;
        width: 33px;
        height: 33px;
        margin-left: 5px;
      }

      .right-text {
        display: inline-block;
        vertical-align: middle;
        font-size: 30px;
        padding-left: 2.333333vw;
        padding-right: 2.333333vw;
        color: var(--theme-secondary-color-finance);
        text-decoration: underline;
      }

      .right-refresh {
        display: inline;
        margin-top: 5px;
        margin-right: 5px;
        padding-left: 5px;
        float: right;
        width: 29px;
        height: 31px;
        background-image: url(/icons/refresh-green.webp);
        background-repeat: no-repeat;
        background-size: 29px 31px;

        &.rotate {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
          -webkit-transition: -webkit-transform 0.3s linear;
          transition: transform 0.3s linear;
        }
      }
    }

    .logged-in-menu {
      position: absolute;
      right: 20px;
      bottom: 20px;
    }
  }

  .menuDiv {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    font-size: 14px;

    .menu-btn {
      text-align: center;
      align-items: center;
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-right: 5px;
      max-width: 100px;
      min-width: 90px;
      position: relative;
      box-sizing: border-box;

      span {
        word-wrap: break-word;
        display: block;
        font-size: 20px;
        text-overflow: clip;
        -webkit-box-orient: vertical;
        color: var(--theme-text-color);
        line-height: 1.1;
        margin-top: 6.2px;
        overflow: hidden;
        text-align: center;
        width: 100px;
      }
    }
  }

  .menuPoup {
    top: 100px;
    background-color: var(--theme-main-bg-color);
    border: 1px solid var(--theme-color-line);
    border-radius: 14px;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: auto;
    position: absolute;
    right: 10px;
    z-index: 99;
    display: block;
    -webkit-font-smoothing: antialiased;
    color: var(--theme-text-color);
    font-size: 14px;
    user-select: none;
    width: 544px;
    height: 420px;
    padding: 20px 10px 10px 10px;

    .menuLayout {
      display: flex;
      flex-wrap: wrap;

      .menuItem {
        align-items: center;
        display: flex;
        flex: 1;
        flex-direction: column;
        margin-right: 5px;
        max-width: 100px;
        min-width: 90px;
        position: relative;
        margin-bottom: 30px;

        span {
          line-height: 1.2;
          word-wrap: break-word;
          display: block;
          font-size: 20px;
          text-overflow: clip;
          color: var(--theme-text-color);
          margin-top: 6.2px;
          overflow: hidden;
          text-align: center;
          width: 100px;
        }
      }
    }
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: transparent;
  }
}
</style>
