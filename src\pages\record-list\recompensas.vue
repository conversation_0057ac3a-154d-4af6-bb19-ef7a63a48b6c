<script setup lang='ts'>
const flag = ref('1')
const flagList = [
  { label: 'Hoje', value: '1' },
  { label: 'Últimos 7 dias', value: '7' },
  { label: 'Últimos 60 dias', value: '60' },
]
const ty = ref('0')
const tyList = [
  { label: 'Todos', value: '0' }, // 全部
  { label: 'depositar', value: '6' }, // 存款优惠
  { label: 'entrar', value: '305' }, // 邀请奖励
  { label: 'sokoban', value: '307' }, // 宝箱奖励
]
const onSelect = () => {
  window.scrollTo(0, 0)
  list.value.length = 0
  finished.value = false
  page.value = 1
  runGetBetRecord()
}

const list = ref<any[]>([])
const total = ref(0)
const finished = ref(false)
const page = ref(1)
const page_size = ref(50)
const { loading, run: runGetBetRecord } = useRequest(() => ApiGetBonusRecord({ ty: ty.value, flag: flag.value, page: page.value, page_size: page_size.value }), {
  onSuccess(res) {
    if (page.value === 1) total.value = res.t

    if (res.d) {
      const arr = res.d.map((item) => {
        const time = timestamp2Date(item.created_at)
        return { ...item, time }
      })
      list.value = [...list.value, ...arr]
    }

    // const arr = [
    //     { amount: 22, remark: 22, time: '2024-01-08' },
        
    //     { amount: 22, remark: 22, time: '2024-01-08' },
        
    //     { amount: 22, remark: 22, time: '2024-01-08' }
    // ]
    list.value = [...list.value, ...arr]
    if (list.value.length >= total.value) {
      finished.value = true;
    } else {
      finished.value = false;
    }
  }
})

const loadMoreData = () => {
  page.value++
  runGetBetRecord()
}
</script>

<template>
  <div class='p-record-recompensas'>
    <app-header title="Histórico de recompensas" :fixed="true" :placeholder="true" leftArrow></app-header>
    <div class="selectbox">
      <AppSelect :width="253" v-model="flag" :list-data="flagList" @onSelect="onSelect" />
      <AppSelect :width="253" v-model="ty" :list-data="tyList" @onSelect="onSelect" />
    </div>
    <div class="listbox">
      <!-- <AppList :loading="loading" :finished="finished" @loadMoreData="loadMoreData" v-if="list.length > 0">
        <div class="list-item" v-for="(item, index) in list" :key="index">
          <div>{{ toDecimal(item.amount) }}</div>
          <div>{{ item.remark }}</div>
          <div>{{ item.time }}</div>
        </div>
      </AppList> -->
        <!-- table -->
      <div v-if="list.length > 0" class="table-bg">
        <div class="table-wrapper">
          <table>
            <colgroup>
              <col style="width:var(--app-px-158);">
              <col style="width:var(--app-px-154);">
              <col style="width:var(--app-px-195);">
              <col style="width:var(--app-px-151);">
            </colgroup>
            <thead>
              <tr>
                <th>Name<br>do jogo</th>
                <th>Tempo</th>
                <th>Valor<br>da aposta</th>
                <th>Lucro</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item, i in list" :key="i">
                <td>{{ item.name }}</td>
                <td>{{ item.time }}</td>
                <td>R${{ toDecimal(item.bet_amount) }}</td>
                <td>R${{ toDecimal(item.net_amount) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <app-empty v-else />
    </div>
  </div>
</template>

<style lang='scss' scoped>
.p-record-recompensas {
  background: #131a2c;
  min-height: 100vh;

  .selectbox {
    width: 750px;
    // height: 76px;
    display: flex;
    background: #131a2c;
    align-items: center;
    justify-content: space-evenly;
    // background-color: #00102D;
    // box-shadow: 0px -1px 0px 0px rgba(255, 255, 255, 0.1) inset;
    // border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 90px;
    padding-top: 40px;
    padding-bottom: 30px;
    z-index: 1;
  }

  .listbox {
    background-color: #131a2c;
    // min-height: 100vh;
    padding-top: 76px;

    .list-item {
      margin: 30px 20px;
      padding: 20px 22px;
      border-radius: 20px;
      font-size: 28px;
      overflow: hidden;
      color: #fff;
      border: 1px solid rgba(14, 209, 244, 0.25);
      background: linear-gradient(180deg, rgba(4, 75, 154, 0.7) 0%, rgba(1, 26, 81, 0.7) 100%),
        linear-gradient(0deg, rgba(14, 209, 244, 0.25), rgba(14, 209, 244, 0.25));
      text-transform: capitalize;

      div+div {
        margin-top: 32px;
      }

      >div:nth-of-type(2) {
        font-size: 32px;
        color: rgba(14, 209, 244, 1);
      }

      >div:last-of-type {
        color: rgba(255, 255, 255, 0.7);
      }
    }
    .table-bg {
      margin: 0 auto;
      margin-top: 50px;
      width: 710px;
      border-radius: 50px;
      background: #28374d;
      // padding: 25px;

      .table-wrapper {
        border-radius: 8px;
        table {
          width: 710px;
          border-collapse: collapse;
          padding: 0;
          thead{
            th{
              background: #324b6e;
              color: #fff;
              font-size: 24px;
              font-weight: normal;
              height: 80px;
              padding: 14px 0;
              // border-bottom: 1px solid #152237;
              border-right: 1px solid #152237;
              &:nth-child(1){
                border-radius: 50px 0px 0px 0px;
              }
              &:nth-child(4){
                border-radius: 0px 50px 0px 0px;
                border-right: none;
              }
            }
          }
          tbody {
            tr {
              color: #fefefe;
              font-size: 24px;
              font-weight: normal;
              height: 80px;
              text-align: center;
              border-bottom: 1px solid #152237;
              td{
                border-right: 1px solid #152237;
                &:nth-child(4){
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
