<script setup lang='ts' name='notificacao'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const emailRef = ref()
//隐藏底下菜单
appStore.setFooterDialogVisble(false)



</script>

<template>
    <div class = "rect"></div>
    <div class = "content">
        <app-empty text="Sem Mensagem"/>
    </div>
    
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.rect{
    height: 300px;
}

.content{
    height: 400px;
}

</style>
<route lang="yaml">
  meta:
    auth: true
</route>