<script setup lang='ts'>
const cols = [
  {
    title: 'Name <br /> do jogo',
    key: 'game_name'
  },
  {
    title: 'Te<PERSON>',
    key: 'time'
  },
  {
    title: 'Valor <br />da aposta',
    key: 'bet_amount'
  },
  {
    title: 'Lucro',
    key: 'net_amount'
  }
]

const flag = ref('1')
const flagList = [
  { label: 'Hoje', value: '1' },
  { label: 'Últimos 7 dias', value: '7' },
  { label: 'Últimos 60 dias', value: '60' },
]
const ty = ref('')
const tyList = [
  { label: 'todos os jogos', value: '' }, // 全部
  { label: 'Ao Vivo', value: '1' }, // 真人
  { label: 'Pesca', value: '2' }, // 捕鱼
  { label: 'Slot', value: '3' }, // 电子
  { label: 'Esporte', value: '4' }, // 体育
  { label: 'Pôquer', value: '5' }, // 棋牌
  { label: 'Esports', value: '6' }, // 电竞
]
const onSelect = () => {
  window.scrollTo(0, 0)
  list.value.length = 0
  finished.value = false
  page.value = 1
  runGetBetRecord()
}

const gt = ref('0')
const gtList = [
  { label: 'Todas', value: '0' },
  { label: 'ganho', value: '1' },
]

const list = ref<any[]>([])
const total = ref(0)
const finished = ref(false)
const page = ref(1)
const page_size = ref(50)
const { loading, run: runGetBetRecord } = useRequest(() => ApiGetBetRecord({ gt: gt.value, ty: ty.value, flag: flag.value, page: page.value, page_size: page_size.value }), {
  onSuccess(res) {
    if (page.value === 1) total.value = res.t

    if (res.d) {
      const arr = res.d.map((item) => {
        const time = timestamp2Date(item.bet_time)
        item.bet_amount = toDecimal(item.bet_amount)
        item.net_amount = toDecimal(item.net_amount)
        return { ...item, time }
      })
      list.value = [...list.value, ...arr]
    }

    // const arr = [
    //     { game_name:'1',net_amount: 22, bet_amount: 22, time: '2024-01-08' },
    //     { game_name:'1',net_amount: 22, bet_amount: 22, time: '2024-01-08' },
    //     { game_name:'1',net_amount: 22, bet_amount: 22, time: '2024-01-08' }
    // ]
    // list.value = [...list.value, ...arr]
    if (list.value.length >= total.value) {
      finished.value = true;
    } else {
      finished.value = false;
    }
  }
})

const loadMoreData = () => {
  page.value++
  runGetBetRecord()
}
</script>
<template>
  <div class='p-record-transaction'>
    <app-header title="Histórico de Apostas" leftArrow></app-header>
    <div class="fixedbox">
      <div class="tabbox">
        <AppTab :list-data="gtList" v-model="gt" @change="onSelect" :height="100" />
      </div>
      <div class="selectbox">
        Pesquisar
        <AppSelect @onSelect="onSelect" v-model="flag" :list-data="flagList" />
        <AppSelect @onSelect="onSelect" v-model="ty" :list-data="tyList" />
      </div>
      <!-- <div class="marginbox30"></div> -->
    </div>
    <div class="tablebox">
      <app-table :local="316" :data="list" :cols="cols" :loading="loading" :finished="finished"
        @loadMoreData="loadMoreData"></app-table>
    </div>
  </div>
</template>

<style lang='scss' scoped>
.flex-sb {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.p-record-transaction {
  background: #131a2c;
  min-height: 100vh;
  padding-bottom: 79px;

  .tabbox {
    background: linear-gradient(180deg, rgba(1, 26, 81, 0) 35.94%, #011A51 100%);
  }

  .fixedbox {
    background: #131a2c;
    position: fixed;
    z-index: 11;
    width: 100%;
    top: 90px;
    padding-top: 20px;

    .marginbox30 {
      height: 30px;
      background: rgba(217, 217, 217, 0.1);
    }
  }

  .selectbox {
    padding: 28px 24px;
    display: flex;
    align-items: center;
    gap: 0 14px;
    box-sizing: border-box;
    background-color: #131a2c;
    // box-shadow: 0px -1px 0px 0px rgba(255, 255, 255, 0.1) inset;
    // border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 24px;
  }

  .tablebox {
    padding: 285px 16px 79px 16px;
    border-radius: 20px;
    // background: #131a2c;
    // min-height: calc(100vh - 100px);
    .table-bg {
      margin: 0 auto;
      margin-top: 50px;
      width: 710px;
      border-radius: 50px;
      background: #28374d;
      // padding: 25px;

      .table-wrapper {
        border-radius: 8px;
        table {
          width: 710px;
          border-collapse: collapse;
          padding: 0;
          thead{
            th{
              background: #324b6e;
              color: #fff;
              font-size: 24px;
              font-weight: normal;
              height: 80px;
              padding: 14px 0;
              // border-bottom: 1px solid #152237;
              border-right: 1px solid #152237;
              &:nth-child(1){
                border-radius: 50px 0px 0px 0px;
              }
              &:nth-child(4){
                border-radius: 0px 50px 0px 0px;
                border-right: none;
              }
            }
          }
          tbody {
            tr {
              color: #fefefe;
              font-size: 24px;
              font-weight: normal;
              height: 80px;
              text-align: center;
              border-bottom: 1px solid #152237;
              td{
                border-right: 1px solid #152237;
                &:nth-child(4){
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>

