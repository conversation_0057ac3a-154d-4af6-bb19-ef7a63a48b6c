<script lang="ts" setup name="AppSplash">

const appStore = useAppStore();
const { isShowLoading} = storeToRefs(appStore);

const isShowAppSplash = ref(true);
setTimeout(() => {
  if(isShowAppSplash.value){
    isShowAppSplash.value = false;
  }

}, 3000);
const iconLoad = ()=>{
  setTimeout(() => {
    isShowAppSplash.value = false;
  }, 1000);
}
</script>

<template>


  <van-popup class="appSplash" v-model:show="isShowAppSplash" teleport="body" :close-on-click-overlay="false" :round="false" :overlay="false" :closeable="false" :z-index="2100">
    <div class="appSplash-content">
      <!-- <van-loading vertical>
        <template #icon>
          <van-icon name="star-o" size="30" />
        </template>
      </van-loading> -->
      <!-- <van-loading color="#1989fa"  /> -->
      <!-- <img class="app-image" src="/logo1.png.webp" @load="() => iconLoad()"/>-->
      <!-- <img class="app-image" src="/favicon1.ico" @load="() => iconLoad()"/>
      <div class="app-image-spacing"></div> -->
      <img class="app-image" src="/logo_left_1.webp" @load="() => iconLoad()"/>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>

.appSplash {

  .appSplash-content {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--theme-top-nav-bg);
   
    flex-direction: column;
  // margin: 0 auto;
  }
}
.appSplash.van-popup--center {
  position: absolute;
  max-width: 100vw;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  transform: translateY(0);
}

.app-image-spacing {
  height: 50px;
}
</style>
