<script setup lang="ts" name="app-login-register">
const appStore = useAppStore();

const {iosInfoShow} = storeToRefs(appStore);

// const show = ref(true)
const showInfo =ref(true)
const isIos =ref(false)
const isAndroid =ref(false)
const showtest = ref("Adicione nosso aplicativo a sua tela inicial.")

// const route = useRoute()
const isApp = GetQueryString("isApp")
console.log("isApp = " + isApp)
if(isApp){
  iosInfoShow.value =false
  appStore.setIosInfoShow(false)

  //领取奖励
  const { run: runAppDownloadBonus } = useRequest(AppDownloadBonus, {
      onSuccess: (data) => {
        if(data){
          appStore.runGetUserBalance()
          // appStore.setIsShowMessage(true,"Bônus +R$ "+ data)
          appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
        }
      }
    })
}else{  
    //获取奖励数据
    const {run: runGetPlatformLinkData} = useRequest(() => ApiGetPlatformLinkData(), {
    onSuccess(res:any) {
      if(Number(res.AppDownloadBonus) > 0){
        showtest.value = "Adicione nosso aplicativo a sua tela inicial e ganhe R$ "+res.AppDownloadBonus+"."
      }
    }
  })


}

//关闭界面
function cancelar(){
  // show.value = false
  isIos.value = false;
  appStore.setIosInfoShow(false)
}

const iosImg = ref(false)
const userAgent = navigator.userAgent || navigator.vendor 
if (userAgent.indexOf("iPad") > -1 || userAgent.indexOf("iPhone") > -1 || userAgent.indexOf("iPod") > -1) {
  iosImg.value = true  //'iOS'; // 苹果浏览器
 }

function gotoDownLoad(){
  
  showInfo.value= false
 if (userAgent.indexOf("iPad") > -1 || userAgent.indexOf("iPhone") > -1 || userAgent.indexOf("iPod") > -1) {
   isIos.value=  true  //'iOS'; // 苹果浏览器
 } else if (userAgent.indexOf("Android") > -1) {
    appStore.setIosInfoShow(false)  //'Android'; // 安卓浏览器
   if(typeof window !== "undefined" && "installButtonclick" in window){
    (window as any).installButtonclick()
   }

 } else {
  //  show.value= false // 'unknown'; // 其他浏览器或平台
   appStore.setIosInfoShow(false)
   if(typeof window !== "undefined" && "installButtonclick" in window){
    (window as any).installButtonclick()
   }
 }
}

</script>

<template>
  <!-- <van-popup class="app-login-register" v-model:show="iosInfoShow" teleport="body" round>
  </van-popup> -->
  <van-popup class="app-login-register"  v-model:show="iosInfoShow" position="bottom" teleport="body" :overlay=false :lock-scroll=false>
    
    <div class="content" v-if="isIos" >
      <div class="buttom">
        <AppImage class="iosInfo" :src="`/img/ios_info.webp`" alt="" />
          <!-- <label class="iosTest"> ios 图片收藏图片</label> -->
      </div>
    </div>

    <div class="contentAndroid" v-if="showInfo" >
      <div class="buttomAndroid">
        <div class="buttomAndroid-left"  @click="gotoDownLoad">
          <i class="buttomAndroid-left-box" style="display: inline-flex; justify-content: center; align-items: center;">
            <AppImage v-if="!iosImg" class="buttomAndroid-left-img" :src="`/img/index/browser_add.webp`" alt="" />
            <AppImage v-else class="buttomAndroid-left-img" :src="`/img/index/ios_add.webp`" alt="" />
          </i>
          <span class="buttomAndroid-left-title">
            <span>Adicione</span>
            primeiro ao ecrã principal
          </span>
        </div>
        <i class="buttomAndroid-right" style="display: inline-flex; justify-content: center; align-items: center;" >
          <AppImage class="buttomAndroid-right-img" :src="`/img/index/bottom_tip_close.webp`" alt="" @click="cancelar"/>
          <div class="buttomAndroid-right-close" @click="cancelar">

          </div>
        </i>
        <!-- <AppImage class="androidInfo" src="/icons/logo.png.webp" alt="" />
        <label class="text">{{ showtest }}</label>
        <div class="btnDiv">
            <label class="cancelar" @click="cancelar">Cancelar</label>
            <AppButton @click="gotoDownLoad"  width="180" height="60"  blue :radius="15" white-text fontSize="25" class="continuar">
          Continuar
        </AppButton>
        </div> -->
      </div>
    </div>


  </van-popup>
  <van-popup class="app-login-register"  v-model:show="isIos" position="bottom" teleport="body" >
    
    <div class="ios-add-content"  >

        <div class="ios-add-content-close" @click="">
          <AppImage class="ios-add-content-close-img" :src="`/img/index/close.webp`" alt="" @click="cancelar"/>
        </div>

        <div class="ios-add-content-body">
          <h1>Adicionar à tela inicial</h1>
          <div class="ios-add-content-body-tip">
            <p>1. Toque no ícone "Mais" e, em seguida, toque em Adicionar ao ecrã principal</p>
            <div class="ios-add-content-body-tip-imgBox">
              <AppImage class="ios-add-content-body-tip-img" :src="`/img/index/img_ios_jc_1.webp`" alt="" />
              <span>Adicionar a memorando rápido</span>
              <span>Encontrar na página</span>
              <span>Adicionar à tela inicial</span>
              <span>Marcador</span>
            </div>
            <p class="ios-add-content-body-tip-span">2. Clique em Adicionar e selecione “Adicionar”</p>

            <div class="ios-add-content-body-tip-imgBox1">
              <AppImage class="ios-add-content-body-tip-img1" :src="`/img/index/img_ios_jc_2_zh.webp`" alt="" />
              <span>Cancelar</span>
              <span>Adicionar à tela inicial</span>
              <span>Adicionar</span>
              <!-- <span>CAGADOSLOT.COM</span> -->
              <span>CAGADOSLOT.COM</span>
              <span>https://cagadoslot.com/home/<USER>/span>
              <span>Será adicionado um ícone ao seu ecrã inicial para aceder rapidamente a este website</span>
              <AppImage class="ios-add-content-body-tip-h5icon" :src="`ic_launcher1.png.webp`" alt="" />
            </div>
          </div>
          <AppImage class="ios-add-content-body-img imgAnimation" :src="`/img/index/pointer.webp`" alt=""/>
        </div>
        
    </div>


  </van-popup>
</template>


<!-- <style lang="scss">
[theme='blue']:root {
  
}
</style> -->
<style lang="scss" scoped>
@import '../theme/mixin.scss';



.content {
  width: 100%;
  height: 200px;
  position: relative;
  font-family: Arial;
  // background-color: aqua;
  .buttom{
    position: absolute;
    // margin: 0 auto;
    bottom:0;
    width: 100%;
    height: 200px;
    // background-color:#ffffff;
    text-align: center;
  }

  .iosInfo{
    width: 100%;
  }
}


//--------------------------------------
.contentAndroid {
  display: flex;
  height: auto;
  position: fixed;
  right: 10px;
  width: 0;
  top: auto;
  left: 50%;
  bottom: 16px;
  font-family: Arial;
  // background-color: aqua;
  .buttomAndroid{
    display: flex;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border: 1px solid var(--theme-text-color-lighten);
    border-radius: 14px;
    box-shadow:  4px 10px 0 rgba(0, 0, 0, 0.1);
    color: var(--theme-text-color-darken);
    font-size: 35px;
    height: 66px;
    opacity: 0.9;
    padding-right: 20px;
    position: relative;
    transform: translateX(-50%);
    width: auto;
  }

  .buttomAndroid-left {
    display: flex;;
  }

  .buttomAndroid-left-box {
    cursor: pointer;
    color: var(--theme-primary-color);
    font-size: 39px;
    padding: 0 16px;
  }

  .buttomAndroid-left-img {
    // width: 39px;
    height: 39px;
  }

  .buttomAndroid-left-title {
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--theme-primary-color);
    cursor: pointer;
    display: block;
    white-space: nowrap;
    word-break: keep-all;
  }

  .buttomAndroid-right {
    border-left: 1px solid var(--theme-text-color-lighten);
    color:var(--theme-text-color);
    cursor: pointer;
    font-size: 30px;
    height: 34px;
    margin-left: 16px;
    padding-left: 16px;
  }
  .buttomAndroid-right-img {
    height: 30px;
    width: 30px;
  }
  .buttomAndroid-right-close {
    position: absolute;
    height: 60px;
    width: 60px;
  }
  .androidInfo{
    // width: 100%;
    padding-top: 40px;
    width: 90px;
  }
  .text{
    position: absolute;
    top: 55px;
    left:120px;
    width: 614px;
    font-size: 29px;
    line-height: 40px;
    height: 100px;
    // line-height: 100px;
    // text-align: right;
   
  }


  .btnDiv{
    position: absolute;
    // background-color: aqua;
    width: 100%;
    height: 100px;
    bottom: 25px;
  }

  .cancelar{
    display: inline-block;
    text-align: center;
    width: 150px;
    height: 50px;
    line-height: 50px;
    margin-top: 35px;
    margin-left: 300px;
    color:var(--app-red-color);
    font-size:25px;
  }

  .continuar{
    position: absolute;
    top: 35px;
    right: 30px;
  }

}

.ios-add-content {
  width: 100%;
  height: 866px;
  position: relative;
  font-family: Arial;
  border-radius: 20px 20px 0 0;
  background-color: var(--theme-main-bg-color);
  border: thin solid;
  border-color: var(--theme-color-line);
  overflow: auto;

  .ios-add-content-close {
    width: 60px;
    height: 60px;
    position: absolute;
    top: 0px;
    right: 0px;
    text-rendering: auto;
    text-align: center;
    text-transform: none;
  }
  .ios-add-content-close-img {
    width: 20px;
    height: 20px;
    vertical-align: -10px;
  }
  .ios-add-content-body{
    height: 100%;
    line-height: 30px;
    font-size: 28px;
    word-wrap: break-word;
    padding: 20px 30px 50px;

    h1 {
      font-size: 30px;
      font-weight: 700;
      line-height: 45px;
      text-align: center;
      color: var(--theme-hx-color);
      margin-top: 0;
      margin-bottom: 40px;
    }

    .ios-add-content-body-tip {
      width: 480px;
      margin: 0 auto;
      p {
          display: block;
          unicode-bidi: isolate;
          margin-top: 0;
          color: var(--theme-text-color-darken);
          font-size: 22px;
          margin-left: -30px;
          line-height: 33px;
      }

      .ios-add-content-body-tip-span {
            margin-top: 30px;
          }
    }

    .ios-add-content-body-tip-imgBox {
      height: 258px;
      width: 480px;
      margin-top: 20px;
      position: relative;  

      span {
        position: absolute;
        line-height: 30px;
        font-family: MicrosoftYaHeiLobby;
        font-stretch: normal;
        font-style: normal;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 350px;
        letter-spacing: 0.5px;
        font-stretch: 100%;
        left: 40px;
        font-size: 20px;
        color: #211f20;

        &:nth-child(2) {
          top: 18px;
        }
        &:nth-child(3) {
          top: 79px;
        }
        &:nth-child(4) {
          top: 140px;
        }
        &:nth-child(5) {
          top: 214px;
        }
      }
      
    }

    .ios-add-content-body-tip-img {
      width: 100%;
      display: inline;
    }

    .ios-add-content-body-tip-imgBox1 {
      height: 280px;
      width: 480px;
      margin-top: 20px;
      position: relative;
        
      .ios-add-content-body-tip-img1{
        width: 100%;
        display: inline;
      }

      span {
        position: absolute;
        font-family: MicrosoftYaHeiLobby;
        font-stretch: normal;
        font-style: normal;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 350px;
        font-size: 20px;

        &:nth-child(2) {
          color: #1678ff;
          height: 28px;
          left: 20px;
          text-align: left;
          top: 19px;
          width: 90px;
        }
        &:nth-child(3) {
          color: #333;
          font-weight: 700;
          height: 28px;
          left: 110px;
          text-align: center;
          top: 19px;
          width: 250px;
        }
        &:nth-child(4) {
          color: #1678ff;
          height: 28px;
          right: 20px;
          text-align: right;
          top: 19px;
          width: 90px;
        }
        &:nth-child(5) {
          color: #211f20;
          left: 110px;
          top: 125px;
          width: 350px;
        }
        &:nth-child(6) {
          color: #8f8f8f;
          left: 110px;
          top: 175px;
          width: 350px;
        }
        &:nth-child(7) {
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          color: #8f8f8f;
          display: -webkit-box;
          font-size: 15px;
          left: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          top: 227px;
          vertical-align: middle;
          white-space: inherit;
          width: 440px;
          line-height: 22.5px;
        }
        
      }
      .ios-add-content-body-tip-h5icon {
        height: 75px;
        left: 18px;
        position: absolute;
        top: 127px;
        width: 75px;
      }
    }
   

    
  }

  @-webkit-keyframes spin {
    0% {
      bottom: 0;
    }

    50% {
      bottom: 30px;
    }

    100% {
      bottom: 0;
    }
  }

  @keyframes spin {
    0% {
      bottom: 0;
    }

    50% {
      bottom: 30px;
    }

    100% {
      bottom: 0;
    }
  }
  .ios-add-content-body-img {
    height: 110px;
    position: absolute;
    width: 86.9px;
    transform: translateX(-65%) rotate(180deg);
    left: 50%;
    bottom: 0;
    
    
  }
  .imgAnimation {
      animation: spin .9s ease-in-out infinite;
      
      bottom: 0;
  }

}


</style>
