<script setup lang='ts' name='date-picker'>
interface Props {
  modelValue: boolean
  group?: boolean
  startDate?: string
  initialDate?: string
  endDate?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'cancel', 'confirm', 'change'])

const showPopup = computed(() => props.modelValue)
const initialDate = ref(props.initialDate?.split(/[/-]/) ?? [
  new Date().getFullYear().toString(),
  (new Date().getMonth() + 1).toString(),
  new Date().getDate().toString(),
])
const startDate = ref(props.startDate?.split(/[/-]/) ?? [
  new Date().getFullYear().toString(),
  (new Date().getMonth() + 1).toString(),
  new Date().getDate().toString(),
])
const endDate = ref(props.endDate?.split(/[/-]/) ?? [
  new Date().getFullYear().toString(),
  (new Date().getMonth() + 1).toString(),
  new Date().getDate().toString(),
])

watch(() => props.endDate, () => {
  startDate.value = props.endDate?.split(/[/-]/) ?? [
    new Date().getFullYear().toString(),
    (new Date().getMonth() + 1).toString(),
    new Date().getDate().toString(),
  ]
})

watch(() => props.endDate, () => {
  if (new Date(props.endDate!).getTime() > new Date().getTime()) {
    endDate.value = [
      new Date().getFullYear().toString(),
      (new Date().getMonth() + 1).toString(),
      new Date().getDate().toString(),
    ]
  } else {
    endDate.value = props.startDate?.split(/[/-]/) ?? [
      new Date().getFullYear().toString(),
      (new Date().getMonth() + 1).toString(),
      new Date().getDate().toString(),
    ]
  }

})

const minDate = new Date(1924, 0, 1)
const maxDate = new Date()

const onCancel = () => {
  emit('update:modelValue', false)
}
const onConfirm = (data: any) => {
  emit('update:modelValue', false)
  if (props.group) {
    if(new Date(data[0].selectedValues.join('/')).getTime() > new Date(data[1].selectedValues.join('/')).getTime()){
      data[1].selectedValues = data[0].selectedValues;
    }
    const start = data[0].selectedValues.join('/')
    const end = data[1].selectedValues.join('/')
    emit('confirm', { start, end })
  }
  else {
    emit('confirm', data.selectedValues.join('/'))
  }
}
const onChange = (data: any) => {
  
  const start = data[0].selectedValues.join('/')
  const end = data[1].selectedValues.join('/')
  if(new Date(start).getTime() > new Date(end).getTime()){
    data[1].selectedValues = data[0].selectedValues
  }
  emit('change', data.selectedValues.join('/'))
}
</script>
<template>
  <van-popup v-model:show="showPopup" @click-overlay="onCancel" position="bottom"
    :style="{ borderRadius: '7px 7px 0 0 ' }">
    <van-picker-group v-if="group" cancel-button-text="Cancelar" confirm-button-text="Confirme"
      :tabs="['data de início', 'data final']" @confirm="onConfirm" @cancel="onCancel">
      <template #title>
        <span class="title">Selecione a data</span>
      </template>
      <van-date-picker v-model="startDate" :min-date="minDate" :max-date="maxDate" />
      <van-date-picker v-model="endDate" :min-date="minDate" :max-date="maxDate" />
    </van-picker-group>
    <van-date-picker v-else cancel-button-text="Cancelar" confirm-button-text="Confirme" v-model="initialDate"
      :min-date="minDate" :max-date="maxDate" @cancel="onCancel" @confirm="onConfirm" @change="onChange">
      <template #title>
        <span class="title">Selecione a data</span>
      </template>
    </van-date-picker>
  </van-popup>
</template>
<style lang="scss" scoped>
.title {
  color: #fff;
  font-weight: 700;
}
</style>

<style lang='scss'>
:root {
  --van-picker-background: var(--theme-main-bg-color);
  --van-picker-mask-color: #324b6e;
  --van-picker-option-text-color: #fff;
  --van-picker-confirm-action-color: var(--theme-filter-active-color);
  --van-picker-cancel-action-color: var(--theme-text-color);
  --van-popup-background: #324b6e;
  --van-picker-group-background: #324b6e;
  --van-tabs-nav-background: #324b6e;
  --van-tabs-nav-border-color: #323c4b;
  --van-tab-text-color: rgba(255, 255, 255, .7);
  --van-tab-active-text-color: #fff;

  [class*=van-hairline]:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 1px solid #FFFFFF40;
    border-left: none;
    border-right: none;
    transform: scale(.5);
  }
}
</style>
