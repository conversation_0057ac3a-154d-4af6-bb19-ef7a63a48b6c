# 活动背景和进展 (Active Context and Progress)

## 当前工作重点 (Current Work Focus)

*   初始化项目设置。
*   解决项目依赖安装问题。
*   参考cerejamk.com网站设计，结合现有功能进行仿站开发。
*   优化登录页面UI，实现符合green主题的页面风格。
*   统一注册页面与登录页面的样式风格。
*   改进网站布局，实现左侧垂直菜单+右侧内容的结构。
*   完善左侧垂直导航菜单 (`AppGameTab.vue`) 的视觉效果和交互。
*   优化游戏图标显示，确保游戏列表呈现为正方形图标，每行显示3个。
*   统一顶部菜单按钮的样式，使用主题色变量控制图标颜色。
*   创建并优化横向导航菜单 (`AppIndexMenu.vue`)，实现登录/注册按钮和快捷功能导航。
*   优化广告图展示方式，增加一行一列的长方形广告图布局选项。
*   解决导航菜单（Popular/Slots）点击后滚动定位不精确的问题。

## 最近变更 (Recent Changes)
**2025-04-25**
*   **修复导航滚动定位问题 (`AppGameTab.vue` -> `pages/index/index.vue`)**: 
    *   **问题描述**: 点击左侧菜单（Popular, Slots）时，右侧内容区域滚动到的位置不精确，导致目标区域的标题被顶部固定 Header 遮挡。
    *   **解决过程**: 
        1.  最初尝试使用 `targetElement.offsetTop`，但不够精确。
        2.  改用 `targetElement.getBoundingClientRect().top + scrollContainer.scrollTop`，定位到容器顶部，但未考虑 Header 高度。
        3.  尝试减去动态获取的 Header 高度 (`headerElement.offsetHeight`)，仍然有偏差。
        4.  **最终方案**: 区分处理不同区域。
            *   对于 `Popular` (`AppIndexGameContainer`)，查找其内部的 `.popular-title` 元素。
            *   对于 `Slots` (`AppIndexSlotsContainer` / `AppSingleSlotsContainer`)，查找其内部的第一个子元素 (`firstElementChild`，即 `<AppIndexTitle>`)。
            *   基于找到的**特定标题元素**和**Header 元素**的 `getBoundingClientRect()` 进行计算：`最终滚动位置 = scrollTop + titleRect.top - headerRect.bottom`。这可以精确地将标题元素的顶部对齐到 Header 元素的底部。
            *   如果找不到特定标题或 Header，则回退到基于容器顶部和 Header 高度的计算。
    *   **涉及文件**: `pages/index/index.vue` (主要修改 `scrollToSection` 函数), `components/AppGameTab.vue` (触发滚动事件), `components/AppIndexGameContainer.vue` (包含 `.popular-title`), `components/AppIndexSlotsContainer.vue` & `components/AppSingleSlotsContainer.vue` (包含 `<AppIndexTitle>` 作为第一个子元素)。

20250423
*   创建并实现新的广告图布局组件：
    1. 创建 `AppSingleSlotItem.vue` 组件：
       - 基于原有 `AppSlotsItem.vue` 组件重构
       - 调整为全宽(100%)的长方形广告图样式
       - 设置高度为180px，保持圆角和边距效果
       - 复用原有的图片懒加载和点击路由导航功能
       - 优化样式结构，移除未使用的代码
    2. 创建 `AppSingleSlotsContainer.vue` 组件：
       - 从 `AppIndexSlotsContainer.vue` 组件派生
       - 更改布局方式，从网格布局(grid)改为垂直柱状布局(flex-direction: column)
       - 保留左右滚动功能和标题组件
       - 简化API请求逻辑，移除不必要的功能
       - 使用相同的数据结构，确保与原组件兼容
    3. 在 `index.vue` 中集成新组件：
       - 根据 `isSmallGameIcon` 状态动态切换显示模式
       - 当 `isSmallGameIcon` 为 false 时，显示原有的三列网格布局(AppIndexSlotsContainer)
       - 当 `isSmallGameIcon` 为 true 时，显示新的一行一列布局(AppSingleSlotsContainer)
       - 集成所需的组件导入

20250417
*   创建并优化 `AppIndexMenu.vue` 组件：
    1. 参考AppIndexMenu.vue组件样式，实现新的横向菜单组件
    2. 在home.vue的AppGameJackpot组件上方添加该菜单
    3. 优化登录和注册按钮的视觉效果：
       - 使用/img/index/login_bg.png和/img/index/reg_bg.png作为按钮背景
       - 设置按钮宽度为155px，高度为70px
       - 使用圆角和白色文字增强可读性
    4. 添加快捷功能导航图标：
       - 实现Depósito(存款)、Saques(提款)和Suporte(支持)三个快捷入口
       - 使用/img/index/icon_eposit.png等专属图标
       - 为图标添加点击事件，链接到对应页面
    5. 设置组件整体样式：
       - 使用白色背景和阴影效果提升层次感
       - 设置固定宽度(720px)和圆角(20px)
       - 添加适当的外边距和内边距
    6. 处理登录状态切换：
       - 未登录状态显示登录/注册按钮和功能导航
       - 已登录状态显示用户名和余额信息
    7. 实现点击功能：
       - 集成现有的menuAClick函数处理导航逻辑
       - 处理登录状态检查和路由跳转

20250416
*   重构页面布局，实现参考cerejamk.com的设计：
    1. 将AppGameTab组件从home.vue移动到index.vue的left-menu中
    2. 调整AppGameTab组件样式，设置固定宽度(130px)和高度(calc(100vh - 88px))
    3. 降低左侧菜单z-index值为1，避免遮挡右侧内容
    4. 为左侧菜单添加背景色(--theme-home-bg)，与右侧内容区分开
    5. 使用flex布局，实现左侧菜单和右侧内容区域的良好排版
    6. 右侧内容区域使用padding-left留出左侧菜单空间，而不是margin-left
*   优化游戏列表显示：
    1. 修改游戏列表为一行显示3个游戏项
    2. 使用calc((100% - 30px) / 3)计算每个游戏项的宽度
    3. 添加适当的间距(gap: 15px)，确保布局美观
    4. 为游戏项添加底部边距(margin-bottom: 10px)
*   修复类型错误和组件属性问题：
    1. 修复GameHallTopEnum和GameNavEnum类型不匹配警告
    2. 为AppIndexTitle组件添加缺失的isCallback属性

*   重构并优化 `AppGameTab.vue` (左侧导航菜单)：
    1.  移除 `van-tabs` 组件依赖，改用自定义 `<div>` 列表实现垂直布局。
    2.  为导航项 `.nav-item` 添加自定义样式：
        -   设置默认背景图片 `url('/img/indexMenu/app_menu_bg.png')`。
        -   设置选中状态 `.active` 的背景图片 `url('/img/indexMenu/app_menu_curbg.png')`。
        -   调整背景尺寸 (`background-size: 100% 100%;`)。
        -   添加边框 (`border: .02rem solid #999;`) 和圆角 (`border-radius: 20px;`)。
        -   添加底部外边距 (`margin-bottom: 20px;`)。
        -   调整文本颜色和大小。
    3.  更新模板中的图标路径为 `/icons/index_menu/nav_${item.id}.png`。
    4.  将脚本中使用的 `GameHallTopEnum` 替换为 `GameNewHallTopEnum`。
    5.  移除了与旧 `van-tabs` 相关的未使用变量和 watcher。
*   优化游戏图标布局和显示效果：
    1. 修改 `AppGameItemBig.vue` 和 `AppGameItem.vue` 组件样式，使游戏图标呈现为正方形
    2. 通过设置 `aspect-ratio: 1 / 1` 确保图标始终保持正方形比例
    3. 调整图标定位方式，从 `transform: translateX(-50%)` 改为 `left: 0` 和 `width: 100%`
    4. 优化图标边角和文字标签的视觉效果
    5. 使用 `z-index` 正确处理默认背景图与实际图片的层叠关系
*   优化顶部菜单按钮：
    1. 在 `AppIndexHeader.vue` 中应用 `mask-image` 技术替代直接使用 `AppImage` 组件
    2. 使用 CSS 变量 `--theme-primary-font-color` 控制 SVG 图标颜色
    3. 调整菜单图标大小为50px x 50px，增强可点击区域
    4. 保留原有旋转动画效果，同时应用主题颜色

20250415
*   更新 `package.json` 中的 `cg-utils` 依赖版本至 `^0.0.47` 以解决安装错误。
*   替换菜单图标为SVG格式，并使用CSS变量 `--theme-primary-font-color` 控制颜色。
*   优化登录页面样式：
    1. 将背景色修改为蓝色(使用--app-page-bg-color变量，值为#08A0E9)
    2. 输入框背景改为白色(使用--theme-main-bg-color变量，值为#F5F5F5)
    3. 登录按钮改为绿色(使用AppButton组件的green属性)
    4. 登录图标改为绿色SVG(使用mask-image技术和--theme-primary-font-color变量)
    5. 文本和底部线条修改为绿色(--theme-primary-font-color，值为#368D45)
*   统一注册页面样式：
    1. 使用相同的mask-image技术将注册图标改为绿色(使用专用的icon_reg.svg)
    2. 输入框背景改为白色(使用--theme-main-bg-color变量)
    3. 注册按钮改为绿色(使用AppButton组件的green属性)
    4. 文本和链接颜色修改为绿色(--theme-primary-font-color变量)
    5. 保持与登录页面相同的色彩方案和视觉风格



## 待构建内容和后续步骤 (What's Left to Build & Next Steps)

*   完成项目初始化和依赖安装。
*   基于cerejamk.com网站，继续改进UI组件和页面结构：
    1. 完善左侧垂直导航菜单的交互效果
    2. 继续优化游戏卡片样式和布局，增强视觉吸引力
    3. 实现邀请奖励系统，包含视觉吸引力强的卡通形象
    4. 优化用户账户区域显示方式
    5. 增加显眼的促销活动展示区域
    6. 完善AppIndexMenu组件的响应式布局
    7. 优化AppIndexMenu的用户登录状态下的余额显示和刷新功能
*   继续优化其他页面的UI风格，保持与登录页面的设计一致性
*   根据 `projectbriefAndProduct.md` 规划后续开发任务。

## 当前决策和考量 (Active Decisions and Considerations)

*   参考cerejamk.com网站布局，但保持自有品牌特色。
*   优先实现用户体验较好的功能模块。
*   使用现代化的UI组件和动效提升用户体验。
*   采用CSS变量控制主题颜色，便于后期整体风格调整：
    - 使用green.scss中定义的变量实现一致的颜色方案
    - 对SVG图标使用mask-image技术配合变量实现颜色控制
    - 确保各组件间视觉风格统一
*   统一用户入口（登录/注册）界面风格，提高品牌一致性
*   采用左侧固定导航+右侧流动内容的布局：
    - 保持导航菜单始终可见，提高用户操作便利性
    - 左侧菜单使用垂直布局，节省横向空间
    - 游戏内容区域采用响应式布局，适应不同屏幕尺寸
*   游戏图标采用多种布局选项：
    - 基础模式：正方形布局，每行显示三个，使用Grid布局
    - 大图模式：长方形布局，一行一列，使用Flex垂直布局
    - 通过isSmallGameIcon状态切换不同布局模式
*   横向导航菜单的设计考量：
    - 使用白色背景和阴影效果增强层次感和美观度
    - 选择醒目的按钮背景图片提高点击率
    - 直观的图标设计提升用户理解和操作效率
    - 根据登录状态显示不同内容，优化用户体验

## 重要模式和偏好 (Important Patterns and Preferences)

*   使用 `pnpm` 作为包管理器。
*   项目基于 `Vue 3` 和 `Vite`。
*   遵循组件化和模块化开发原则。
*   使用CSS变量实现主题控制。
*   对SVG图标优先使用mask-image技术替代filter，便于使用主题变量。
*   保持登录与注册页面的视觉和交互一致性。
*   布局采用主流的左侧导航+右侧内容模式。
*   游戏列表布局采用多种可切换方案：
    - 小图标模式：使用grid布局，每行三列
    - 大图标模式：使用flex垂直布局，每行一列
    - 通过变量控制切换，无需修改主体代码结构
*   在特定场景下（如 `AppGameTab.vue`），倾向于使用自定义布局而非 UI 库默认组件，以实现更精确的样式控制。
*   导航菜单相关背景图片存放于 `public/img/indexMenu/`。
*   导航菜单相关图标存放于 `public/icons/index_menu/`。
*   游戏大厅顶层导航分类使用 `GameNewHallTopEnum` 枚举。
*   功能图标存放于 `public/img/index/` 目录，按功能命名。
*   按钮背景图片保存在 `public/img/index/` 目录。
*   组件命名约定：
    - 原始组件如：AppSlotsItem、AppIndexSlotsContainer
    - 变体组件加前缀描述特征：AppSingleSlotItem（表示单列布局）

## 经验和项目见解 (Learnings and Project Insights)

*   `cg-utils` 包的 `0.0.44` 和 `0.0.43` 版本似乎已从 npm registry 移除或不再可用。
*   参考网站使用明亮的色彩方案和卡通元素增强用户友好性。
*   简洁的界面结构和清晰的功能分区对用户体验至关重要。
*   CSS的mask-image技术是控制SVG图标颜色的有效方式，不需要修改SVG源文件。
*   使用AppButton组件的预设样式(如green)可快速实现统一的按钮风格。
*   为不同功能（登录/注册）准备专用SVG图标但保持一致的颜色处理方式，可以在保持视觉一致性的同时区分功能。
*   在实现左侧导航菜单时，需注意z-index设置，避免遮挡其他内容。
*   对游戏列表布局使用grid及响应式单位，可以实现更灵活的布局控制。
*   图片组件的替代方案：当需要控制颜色时，使用div元素配合mask-image技术比直接使用img更灵活。
*   正方形图标布局的实现关键是使用aspect-ratio属性而非固定高度。
*   一个组件在不同位置使用时，应考虑其上下文环境和可能的属性需求。
*   当 UI 库组件无法满足特定视觉需求时，重构为自定义实现是可行的方案。
*   CSS 背景图片路径若以 `/` 开头，通常指向 `public` 目录。
*   使用背景图片实现按钮样式比纯CSS更灵活，尤其是需要复杂渐变或特殊效果时。
*   组件间保持一致的风格和交互模式可显著提升用户体验。
*   白色背景配合阴影效果是实现卡片式UI的有效方式，能增强组件层次感。
*   通过条件渲染实现多种布局切换比维护多个版本的页面更高效：
    - 共享核心逻辑和数据结构，减少代码重复
    - 根据状态变量动态切换样式和布局，提高灵活性
    - 能够快速响应产品需求变更，无需大规模代码重构
*   组件派生策略(如AppSingleSlotItem从AppSlotsItem派生)可有效复用逻辑的同时实现不同视觉效果。
*   使用Vue的条件渲染(v-if/v-else)配合状态变量实现布局切换是简洁有效的方法。

**2025-04-25**
*   **精确滚动定位**: 
    *   使用 `element.getBoundingClientRect()` 可以获取元素相对于视口的位置和大小，这对于复杂布局下的滚动计算通常比 `offsetTop` 更可靠。
    *   当需要将一个元素滚动到另一个固定/粘性元素（如 Header）下方时，基于两者 `getBoundingClientRect()` 计算相对位置 (`scrollTop + targetRect.top - fixedElementRect.bottom`) 是动态且精确的方法，可以避免硬编码偏移量。
    *   不同的内容区域可能需要不同的定位目标。不能假设所有区域都使用同一个内部元素（如标题）作为对齐基准。应根据具体组件的内部结构查找最合适的定位元素（如 `.popular-title` 或 `firstElementChild`）。
*   **滚动容器识别**: 页面滚动可能由 `<html>` (`document.documentElement`) 或某个内部 `div` (如 `.app-index`) 控制。需要通过测试或检查 CSS (`overflow: auto/scroll`) 来确定正确的滚动容器，并使用对应的 `scrollTop` 属性和 `scrollTo` 方法 (`window.scrollTo` 或 `element.scrollTo`)。

## 已知问题 (Known Issues)

*   AppRegister.vue组件中存在TypeScript类型错误，fromData对象的属性定义与使用不完全匹配。这些错误不影响功能，但应在后续开发中修复。
*   AppIndexGameContainer.vue中存在is_fav参数使用不当的TypeScript错误，需要修复或适配API接口。
*   AppIndexHeader.vue中存在与custService相关的TypeScript错误，需要在后续开发中解决。
*   AppIndexMenu.vue中使用window.installButtonclick时存在TypeScript类型错误，目前通过(window as any)类型断言解决，应考虑更优雅的实现方式。

## cerejamk.com网站分析结果

### 整体设计特点
- 使用明亮鲜艳的配色方案，主要为红色系搭配白色背景
- 界面简洁直观，功能区块划分清晰
- 适合移动设备浏览的响应式设计
- 大量使用卡通形象增强视觉吸引力
- 采用左侧垂直导航菜单+右侧内容的经典布局

### 主要功能模块
1. **顶部导航栏**：
   - 包含logo、菜单按钮和搜索/用户功能
   - 简洁明了，突出核心功能

2. **促销横幅**：
   - 顶部固定位置显示主要促销信息
   - 包含吸引用户注意的"HOT!"标签
   - 集成下载按钮，便于获取应用

3. **左侧导航菜单**：
   - 垂直排列的分类导航
   - 包含Popular、Slots、Recente、Favoritos等选项
   - 使用图标+文字的组合增强识别度
   - 选中状态有明显的视觉反馈

4. **游戏展示区域**：
   - 采用网格布局，一行显示3个游戏项
   - 游戏卡片包含图片和名称
   - 使用"PG"等标识区分游戏来源
   - 显示总游戏数量和当前页面信息

5. **邀请奖励系统**：
   - 使用明显的位置展示邀请好友奖励
   - 视觉效果突出，包含引人注目的卡通形象
   - 直接显示奖励金额，增强用户动力

6. **用户账户区域**：
   - 简洁地展示用户ID和VIP等级
   - 显眼地展示账户余额
   - 一目了然的主要功能入口：存款、提款、支持

### 可借鉴实现方案
1. 完善左侧导航菜单的交互和选中状态效果
2. 优化游戏卡片布局，增强视觉吸引力
3. 在首页顶部添加邀请奖励模块，使用卡通形象提升吸引力
4. 优化账户信息展示，简化布局同时突出核心信息
5. 增加促销活动专区，提高用户参与度
6. 应用整体色彩方案调整，增强品牌识别度和用户体验 

## 演进的项目决策 (Evolution of project decisions)

*   决定使用自定义实现替换 `AppGameTab.vue` 中的 `van-tabs` 组件，以更好地控制导航菜单的视觉样式和交互。
*   决定对游戏图标组件进行重构，改用正方形布局，每行显示3个，以提升整体视觉效果并保持与参考网站的一致性。
*   决定统一使用mask-image技术和CSS变量控制UI元素颜色，以便于后期整体颜色方案调整。
*   决定创建AppIndexMenu组件实现横向导航功能，提供直观的功能入口和登录/注册按钮，增强用户操作便捷性。
*   决定为AppIndexMenu组件使用白色背景和阴影效果，与网站整体设计形成视觉层次。
*   决定使用专属背景图片(/img/index/)优化按钮效果，增强视觉吸引力。
*   决定创建AppSingleSlotsContainer和AppSingleSlotItem组件，实现一行一列的长方形广告图布局，提供与现有三列网格布局的选择性切换功能，增强页面呈现的灵活性。 