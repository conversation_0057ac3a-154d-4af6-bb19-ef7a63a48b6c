/**
 *
 * 切换主题
 * @param {Themes} theme
 */
export function toggleTheme(theme: Themes) {
  const html = document.documentElement
  html.setAttribute('theme', theme)
}

/**
 * 判断值是否空
 */
export function isVEmpty(value: any) {
  return value === '' || value === undefined || value === null
}

// 保留2位小数
export function toDecimal(value: string | number, decimal = 2) {
  const originStr = value?.toString()

  if (!originStr || Number.isNaN(Number(originStr)) || originStr === '0') return decimal === 2 ? '0.00' : '0'

  const arr = originStr.split('.')
  if (decimal === 0) return arr[0]
  if (arr.length === 1) return arr[0] + '.00'

  const integerPart = arr[0]
  const decimalPart = arr[1]
  return integerPart + '.' + decimalPart.slice(0, decimal).padEnd(decimal, '0')
}
// 状态转颜色
export function colorFilter(state: number) {
  if ([0,1,2,4,5,6].includes(state)) {
    // 黄色
    return  '#172554';
  } else if ([100,28].includes(state)) {
    // 绿色
    return '#00FF0E';
  } else if ([3].includes(state)) {
    // 红色
    return '#E70B0B';
  }
}
// 状态
export function stateFilter(state: number) {
  // if ([0].includes(state)) {
    // return 'Pending approval';
  // }else if([1].includes(state)){
  //   return 'Review completed';
  // }else if([2].includes(state)){
  //   return 'Return to account';
  // }else if([3].includes(state)){
  //   return 'Freezing gold coins';
  // }else if([4].includes(state)){
  //   return 'Under process at bank';
  // }else if([5].includes(state)){
  //   return 'Return to account';
  // }else if([6].includes(state)){
  //   return 'Pending approval';
  // }else if([100].includes(state)){
  //   return 'Successfully processed';
  // }else if([28].includes(state)){
  //   return 'successo';
  // }


  if ([371].includes(state)) {
    return "Em análise"//'审核中';
  }else if([372].includes(state)){
    return  "Revisão Rejeitar"//'审核拒绝';
  }else if([373].includes(state)){
    return 'No processo de desembolso'; //出款中
  }else if([374].includes(state)){
    return 'Retirada bem sucedida'; //提款成功
  }else if([375].includes(state)){ 
    return 'O desembolso falhou'; //出款失败
  }else if([376].includes(state)){
    return 'Ordens anormais'; //异常订单
  }else if([377].includes(state)){
    return 'O pagamento falhou';//代付失败
  }
}


// 状态
export function stateFilter2(state: number) {
  if ([361].includes(state)) {
    return 'Confirmando'; //确认中
  }else if([362].includes(state)){
    return 'sucesso'; //存款成功
  }else if([363].includes(state)){
    return 'Depósito cancelado'; //存款已取消
  }else if([364].includes(state)){
    return 'Em revisão';  ///存款审核中
  }else if([365].includes(state)){
    return 'sucesso'; //补单成功   suplementar
  }
}



export function getBrowser() {
  const UserAgent: any = navigator.userAgent.toLowerCase();
  const browserInfo: any = {};
  const browserArray: any = {
    IE: 'ActiveXObject' in window, // IE
    Chrome: UserAgent.indexOf('chrome') > -1 && UserAgent.indexOf('safari') > -1, // Chrome浏览器
    Firefox: UserAgent.indexOf('firefox') > -1, // 火狐浏览器
    Opera: UserAgent.indexOf('opera') > -1, // Opera浏览器
    Safari: UserAgent.indexOf('safari') > -1 && UserAgent.indexOf('chrome') === -1, // safari浏览器
    Edge: UserAgent.lastIndexOf('edg') > -1, // Edge浏览器
    QQBrowser: /qqbrowser/.test(UserAgent), // qq浏览器
    WeixinBrowser: /MicroMessenger/i.test(UserAgent) // 微信浏览器
  };
  let versions = '';
  for (const i in browserArray) {
    if (browserArray[i]) {
      if (i === 'IE') {
        const match = UserAgent.match(/(msie\s|trident.*rv:)([\w.]+)/);
        versions = match ? match[2] : '9.2';
        browserInfo.type = 'IE';
      } else if (i === 'Chrome') {
        const match = UserAgent.match(/chrome\/([\d.]+)/);
        browserInfo.type = 'Chrome';
        versions = match ? match[1] : '88.2.2';
      } else if (i === 'Firefox') {
        const match = UserAgent.match(/firefox\/([\d.]+)/);
        browserInfo.type = 'Firefox';
        versions = match ? match[1] : '99.2.3';
      } else if (i === 'Opera') {
        const match = UserAgent.match(/opera\/([\d.]+)/);
        browserInfo.type = 'Opera';
        versions = match ? match[1] : '89.43.3';
      } else if (i === 'Safari') {
        const match = UserAgent.match(/version\/([\d.]+).*/);
        browserInfo.type = 'Safari';
        versions = match ? match[1] : '7.3.4';
      } else if (i === 'Edge') {
        const match = UserAgent.match(/edg\/([\d.]+)/);
        browserInfo.type = 'Edge';
        versions = match ? match[1] : '32.34';
      }
    }
  }

  browserInfo.browser_version = Number(versions.split('.')[0]);
  browserInfo.mobile = !!UserAgent.match(/applewebkit.*mobile.*/) ? 'mobile' : 'pc'; // 是否为移动终端
  browserInfo.isIos =
    !!UserAgent.match(/\(i[^;]+;( u;)? cpu.+mac os x/) || UserAgent.indexOf('intel mac os') > -1;

  return browserInfo;
}


/**
 * 获取目标时区的时间戳
 * @param {number} timeZone  时区,以格林尼治时间为基准，东区为正，西区为负 例如：北京时间东八区 填 8 ，夏威夷时间西10区填 -10
 * @param {number} time 标准时间戳（毫秒）
 * @returns {number} 变换过的时间戳
 */
function getTimeZoneTime(timeZone:any,time:any){
  //毫秒偏移量
  let offset = new Date().getTimezoneOffset() * 60 * 1000;
  //转化为格林尼治时间
  let utcTime = time + offset;
  return utcTime + timeZone *  60 * 60 * 1000;

}

//time 毫秒
export function getBrazilTime(time:any){
  return getTimeZoneTime(-3,time);
}

//time 毫秒
export function format_date(time:any) {
  time = time || new Date().getTime();
  let localTime = getBrazilTime(parseInt(time));
  let date = new Date(localTime);
  // return date.format("yyyy-MM-dd HH:mm:ss")

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedDate
}

export function format_date2(time:any) {
  time = time || new Date().getTime();
  let localTime = getBrazilTime(parseInt(time));
  let date = new Date(localTime);
  // return date.format("yyyy-MM-dd HH:mm:ss")

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  const formattedDate = `${day}/${month} ${hours}:${minutes}:${seconds}`;
  return formattedDate
}
//显示错误提示
export function showErrorTip(resData:object) {
  if (!isNaN(Number(resData.data))) {
    const key = resData.data
    if (key) {
      const toastMessage = key && errorJson[key] ? errorJson[key][language.value] : 'erro desconhecido'
      showToast(toastMessage)
    }
  }
}

//周
export  function getWeekDate(lastWeek:boolean=false) {
  if(lastWeek){
    return dayjs().startOf('Week').add(1,"day").format('YYYY-MM-DD HH:mm:ss') + "~"+ dayjs().endOf('Week').add(1,"day").format('YYYY-MM-DD HH:mm:ss')
  } else {
    return dayjs().startOf('Week').subtract(6,"day").format('YYYY-MM-DD HH:mm:ss') + "~" + dayjs().endOf('Week').subtract(6,"day").format('YYYY-MM-DD HH:mm:ss')
  }
}

//天
export  function getDayDate(lastWeek:boolean=false) {
  if(lastWeek){
    return dayjs().startOf('day').add(1,"day").format('YYYY-MM-DD HH:mm:ss') + "~"+ dayjs().endOf('day').add(1,"day").format('YYYY-MM-DD HH:mm:ss')
  } else {
    return dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss') + "~" + dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  }
}


export function QueryParam(url:any,key:any){
  let reg = new RegExp(`(${key}=[^&]+)`)
  let ret = url.match(reg)
  return ret ? ret[0].split("=")[1] : "";
}


export function GetQueryString(name:any)
{
   
  return QueryParam(window.location.search,name);
}



  //数字转换
export function transf(value:number, number = 2){
    if (typeof value !== "number") {
      return 0;
    }
       
    let num = parseFloat(value).toFixed(number);

    if (!/^([+\-])?(\d+)(\.\d+)?$/.test(num)) {
        // alert("wrong!");
        return num;
    }

    let a = RegExp.$1;
    let b = RegExp.$2;
    let c = RegExp.$3;
    let re = new RegExp().compile("(\\d)(\\d{3})(,|$)");
    while (re.test(b))
        b = b.replace(re, "$1,$2$3");

    
    b = b.replace(new RegExp(",", 'g'), ".");
    c = c.replace(".", ",");
    return a + "" + b + "" + c;
    
  }