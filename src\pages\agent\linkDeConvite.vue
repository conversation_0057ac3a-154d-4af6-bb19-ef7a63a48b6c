<script setup lang="ts" name="linkDeConvite">
const router = useRouter();
const isReceber = ref(false); //是否可领取
const isShowSelect = ref(false);
import agentInfoIcon from "/icons/svg/agentInfo.svg?raw";
import QRCode from "qrcode";
const qrCodeCanvas = ref<HTMLCanvasElement | null>(null);

const ChildCount = ref(0); //直属下级人数
const ChildRunningReturn = ref(0); //流水返利
const VipLevel = ref(0); //vip

const appStore = useAppStore();
const { userInfo } = storeToRefs(appStore);

const shareUrl = ref("");
const share = [
  {
    icon: "/icons/agent_share_Partilhar",
    name: "Partilhar",
    url: location.origin,
  },
  {
    icon: "/icons/agent_share_Facebook",
    name: "Facebook",
    url: "https://m.facebook.com/",
  },
  {
    icon: "/icons/agent_share_Instagram",
    name: "Instagram",
    url: "https://www.instagram.com/",
  },
  {
    icon: "/icons/agent_share_Telegram",
    name: "Telegram",
    url: "https://t.me/telegram",
  },
  {
    icon: "/icons/agent_share_WhatsApp",
    name: "WhatsApp",
    url: "https://api.whatsapp.com/",
  },
  {
    icon: "/icons/agent_share_TikTok",
    name: "TikTok",
    url: "https://vt.tiktok.com/",
  },
  {
    icon: "/icons/agent_share_Kwai",
    name: "Kwai",
    url: "https://www.kwai.com/",
  },
  { icon: "/icons/agent_share_Line", name: "Line", url: "https://line.me/" },
];

const li = computed(() => {
  if (userInfo.value.uid && userInfo.value.uid.length < 9) {
    return [
      location.origin +
        "/register" +
        `?id=${userInfo.value.uid}` +
        "&currency=BRL&type=2",
    ];
  } else {
    return [""];
  }
});

function closeSelect() {
  isShowSelect.value = false;
}

function openSelect() {
  isShowSelect.value = true;
}

function liClick(item: any) {
  shareUrl.value = li.value[0];
}
//复制分享地址
function copyUrl() {
  showToast("Copiado com sucesso");
  copy(li.value[0]);
}
//复制id
function copyID() {
  showToast("Copiado com sucesso");
  copy(userInfo.value.uid || "");
}

function openShareUrl(item: any) {
  window.open(item.url, "_blank");
}
//网络请求
//获取可领取数据
const { run: runQueryChildRunningReturnData, data: queryDatas } = useRequest(
  () => ApiQueryChildRunningReturnData(),
  {
    manual: true,
    onSuccess(res: any) {
      ChildCount.value = res.ChildCount;
      ChildRunningReturn.value = res.ChildRunningReturn;
      VipLevel.value = res.VipLevel;
      isReceber.value = ChildRunningReturn.value > 0;
    },
  }
);

//领取奖励
const { run: runGetChildRunningReturnData, data: getDatas } = useRequest(
  () => ApiGetChildRunningReturnData(),
  {
    manual: true,
    onSuccess(res: any) {
      if (res > 0) {
        // appStore.setIsShowMessage(true,res)
        appStore.setIsShowBonusMessage(
          true,
          "Reivindicado com êxito.Continue a jogar para obter mais bónus~"
        );
        appStore.runGetUserBalance();
        isReceber.value = false;
        ChildRunningReturn.value = 0;
      }
    },
  }
);

//查询口
runQueryChildRunningReturnData();

//获取金币
function getMoney() {
  runGetChildRunningReturnData();
}

onMounted(() => {
  watchEffect(() => {
    if (qrCodeCanvas.value && li.value[0]) {
      QRCode.toCanvas(qrCodeCanvas.value, li.value[0], {
        width: 140,
        margin: 1,
        color: {
          dark: "#000",
          light: "#FFF0",
        },
      });
    }
  });
});
</script>
<template>
  <div class="linkDeConvite">
    <div class="content">
      <div class="info">
        <!-- <AppImage src="/icons/agent_vipbg" class="agentvipbg" />
        <label class="vipLv">{{ VipLevel }}</label>
        <AppImage src="/icons/agent_quest" class="agentquest" />
        <div class="line"></div>
        <label class="cole"
          >Coletável
          <label class="cole_num">{{ transf(ChildRunningReturn) }}</label>
        </label>

        <label class="modo">Modo de Agente</label>
        <label class="difere"
          >Diferença de nível infinito (Liquidação diária)</label
        >
        <div class="receber" :class="{ active: isReceber }" @click="getMoney">
          Receber
        </div>
        <div class="hist" :class="{ active: isReceber }">Histórico</div> -->
        <!-- 二维码 -->
        <AppImage src="/icons/agent_qrcodebg" class="QRCodebg" />
        <canvas ref="qrCodeCanvas" class="QRCode" style="z-index: 10" />
        <div class="agent_bottom_right">
          <label class="men">Meu link:</label>
          <!-- <label class="alt" @click="openSelect">Alterar</label> -->
          <div class="httpAddress" :class="{ active: isShowSelect }">
            <label class="test">{{ li[0] }}</label>
            <AppImage src="/icons/agent_copy" class="copy" @click="copyUrl" />
          </div>
        </div>

        <div class="conviteNum">
          <label class="subord">Subordinados diretos</label>
          <!--直接下属-->
          <label class="subord_num">{{ ChildCount }}</label>
          <br />
          <label class="convite">Código de Convite</label>
          <label class="convite_id">{{ userInfo.uid || "" }}</label>
          <AppImage src="/icons/agent_copy" class="copy2" @click="copyID" />
        </div>

        <div class="share">
          <div v-for="item in share" class="item" @click="openShareUrl(item)">
            <AppImage :src="item.icon" class="itemImg" />
            <label class="itemName">{{ item.name }}</label>
          </div>
        </div>
      </div>
      <!-- <label class="rede">Rede do Agente</label> -->
      <!-- <AppImage src="/icons/agent_info" class="redeInfo" /> -->
      <div class="redeInfo" v-html="agentInfoIcon"></div>
      <div class="selectAddress" v-if="isShowSelect" @click="closeSelect">
        <div class="ul">
          <ul>
            <li
              v-for="item in li"
              @click="liClick(item)"
              :class="{ active: item == shareUrl }"
            >
              <span>{{ item }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.linkDeConvite {
  width: 100%;
  height: 100%;
}

.content {
  position: absolute;
  width: 100%;
  height: 100%;
  // background-color: #FFF;
}

.info {
  margin: auto;
  margin-top: 20px;
  width: 715px;
  height: 63vw;
  background: linear-gradient(143deg, #622bf7 10%, #087205 80%);
  border-radius: 30px;
  border-color: var(--theme-color-line);
  font-size: 23px;
  .agentvipbg {
    position: absolute;
    width: 130px;
    left: 40px;
  }

  .agentquest {
    position: absolute;
    width: 24px;
    left: 160px;
    top: 30px;
  }
  .vipLv {
    position: absolute;
    width: 90px;
    text-align: center;
    font-size: 40px;
    color: #cc5121;
    left: 60px;
    top: 60px;
  }

  .cole {
    position: absolute;
    color: var(--theme-text-color);

    left: 200px;
    top: 55px;
    .cole_num {
      position: absolute;
      color: var(--theme-secondary-color-finance);

      margin-left: 30px;
      // left:200px;
      // top: 60px;
    }
  }

  .modo {
    position: absolute;
    color: var(--theme-text-color);

    left: 200px;
    top: 108px;
    width: 90px;
  }

  .difere {
    position: absolute;
    color: var(--theme-text-color-lighten);
    left: 340px;
    top: 108px;
    width: 450px;
  }

  .receber {
    position: absolute;
    left: 435px;
    top: 43px;

    width: 120px;
    height: 48px;
    background: var(--theme-disabled-bg-color);
    border-radius: 8px;
    color: var(--theme-font-on-background-color);
    text-align: center;
    line-height: 50px;
    font-size: 20px;
    &.active {
      background: var(--theme-filter-active-color);
      color: var(--theme-main-bg-color);
    }
  }

  .hist {
    position: absolute;
    left: 583px;
    top: 43px;

    width: 120px;
    height: 48px;
    background: var(--theme-primary-color);
    color: var(--theme-font-on-background-color);
    border-radius: 8px;
    text-align: center;
    line-height: 50px;
    font-size: 20px;
  }

  .line {
    position: absolute;
    top: 180px;
    width: 710px;
    height: 1px;
    background: var(--theme-bg-color);
  }

  .QRCodebg {
    position: absolute;
    width: 154px;
    left: 40px;
    top: 5vw;
  }

  .QRCode {
    position: absolute;
    width: 18.666667vw !important;
    height: auto !important;
    left: 45px;
    top: 6vw;
  }

  .agent_bottom_right {
    position: absolute;
    width: 485px;
    height: 220px;
    left: 220px;
    top: 5vw;
    // background-color: #2a2727;
    .men {
      color: var(--theme-font-on-background-color);
      font-size: 30px;
      font-weight: bold;
    }

    .alt {
      color: var(--theme-primary-color);
      float: right;
    }

    .httpAddress {
      display: flex;
      align-items: center; /* 垂直居中 */
      margin-top: 15px;
      width: 494px;
      height: 92px;
      background: #e7e4ff33;
      border: 1px solid;
      border-color: var(--theme-color-line);
      border-radius: 8px;
      &.active {
        border-color: var(--theme-filter-active-color);
      }
      .test {
        // display: inline-block;
        color: var(--theme-font-on-background-color);
        margin-left: 30px;
        margin-top: 4px;
        width: 400px;
        white-space: normal; /* 允许自动换行 */
        word-wrap: break-word; /* 允许在单词内换行 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制为3行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .copy {
        position: absolute;
        width: 26px;
        top: 9.666667vw;
        right: 20px;
      }
    }
  }

  .conviteNum {
    position: absolute;
    width: 485px;
    height: 90px;
    left: 220px;
    top: 25vw;
    // background-color: #2a2727;
    color: var(--theme-font-on-background-color);
    font-size: 25px;

    .subord {
      display: inline-block;
      padding-top: 10px;
      // left:220px;
      // top: 350px;
    }

    .subord_num {
      padding-left: 30px;
      font-size: 30px;
    }

    .convite {
      display: inline-block;
      padding-top: 15px;
      // left:220px;
      // top: 390px;
    }

    .convite_id {
      padding-left: 10px;
      color: #ffffff;
      font-size: 30px;
    }
  }

  .copy2 {
    margin-left: 20px;
    width: 26px;
  }

  .share {
    position: absolute;
    left: 4.4vw;
    top: 45vw;
    width: 90%;

    display: flex;
    align-items: flex-start;
    // height: 120px;
    overflow: auto;
    overflow-x: scroll;
    // background-color: #2a2727;
    .item {
      // display: inline-block;
      // background-color: #5b112c;
      width: 100px;
      height: 120px;
      margin-right: 2.5vw;
    }

    .itemImg {
      display: block;
      margin: 0 auto;
      width: 8.5vw;
    }
    .itemName {
      width: 100px;
      margin-top: 10px;
      display: inline-block;
      text-align: center;
      color: var(--theme-font-on-background-color);
    }
  }

  // .share::-webkit-scrollbar{
  //   background-color: rgb(122, 178, 238);
  // }
}

.rede {
  display: inline-block;
  color: #00000096;
  font-size: 28px;
  padding-top: 20px;
  padding-left: 20px;
}

.redeInfo {
  width: 750px;
  //  margin-left: 17px
}

.selectAddress {
  position: absolute;
  width: 100vw;
  height: 100%;
  // background-color: #4c1934;
  left: 0;
  top: 0;
  .ul {
    position: absolute;
    width: 494px;
    // height: 220px;
    left: 220px;
    top: 340px;
    background: var(--theme-main-bg-color);
    border: 1px solid;
    border-color: var(--theme-color-line);
    border-radius: 8px;

    li {
      color: var(--theme-text-color-lighten);
      height: auto;
      font-size: 23px;
      padding-left: 30px;
      // background-color: var(--theme-text-color);
      padding-top: 10px;
      padding-bottom: 10px;
      &.active {
        color: var(--theme-filter-active-color);
      }
      span {
        width: 400px;
        white-space: normal; /* 允许自动换行 */
        word-wrap: break-word; /* 允许在单词内换行 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制为3行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
