[theme='green']:root {
    --bg-1: blue;
  
    --app-navbar-bg: #1c2533;
    --app-navbar-color: #b3c2d8;
    --app-navbar-height : 88px;
    --app-gameTitle-fontSize: 28px;
  
    // --app-container-bg: linear-gradient(180deg, #044B9A 0%, #011A51 100%);
  
    // --app-table-bg: linear-gradient(180deg, rgba(4, 75, 154, 0.7) 0%, rgba(1, 26, 81, 0.7) 100%),
    //   linear-gradient(0deg, rgba(14, 209, 244, 0.25), rgba(14, 209, 244, 0.25));
    --app-table-border: rgba(14, 209, 244, 0.25);
  
    --app-text-color: #fff;
    --app-text-color-1: rgba($color: #fff, $alpha: 0.6);
    --app-text-color-2: #0ED1F4;
  
    --app-text-color: #fff;
    --app-text-color-1: rgba($color: #fff, $alpha: 0.6);
    --app-text-color-2: #0ED1F4;
    --app-text-color-3:#C5E2E6;
    --van-popup-background: transparent;
  
  
    .van-tabs__nav{
      --van-border-radius-sm:15px;
      --van-border-width:0px;
    }
  
    .van-tab{
      border-radius: var(--van-border-radius-sm);
    }
  
    .warning-popover {
      --van-popover-light-background: #f7ba17;
      --van-popover-light-text-color: #fff;
      --van-popover-action-font-size: 24px;
    }
  
    .deep-dialog {
      --van-dialog-background: #011A51;
  
      .van-dialog__header,
      .van-dialog__message--has-title,
      .van-dialog__confirm,
      .van-dialog__confirm:active {
        color: #0ED1F4;
      }
  
      .van-button--default {
        background: transparent;
        border: 0;
      }
  
      .van-hairline--top:after {
        border: 0;
      }
    }
  
    .game-type-tabs {
      .van-tabs__wrap {
        height: auto;
      }
      .van-tab--grow {
        padding-right: 15px;
        padding-left: 0;
      }
      .van-tabs__nav--line.van-tabs__nav--complete {
        padding-left: 0;
      }
      .van-tabs__line {
        display: none !important;
      }
    }
    // 新皮肤-202402
    // 顶部背景颜色
    --app-top-bg-color: #B94B6B;
    // 顶部组件金额背景颜色
    --app-top-amount-bg-color: #164633;
    // 顶部组件金额颜色
    --app-top-amount-color: #ffffff;
    // 整页面背景颜色
  
    --app-page-bg-color: #08A0E9;
    --app-page-bg-color3: #e06f8b;
  
  
    // banner page
    // 红按钮颜色
    --app-red-color: #FFFFFF;
    // 蓝按钮颜色
    --app-blue-color: #FFFFFF;
  
    --theme-filter-active-color:#FFFFFF;
    
    --theme-bg-color: #F5F5F5;
    --theme-bg-color2: #F5F5F566;
    --theme-disabled-bg-color:#999;
    --theme-bg-shadow: rgba(0, 0, 0, 0.06);
    --theme-load-bg-color: #43BE6066;
    --theme-primay-color-hex:255,255,255;
 
    --theme-disabled-font-color:#fff;
    --theme-font-on-background-color:#ffffff;
    --theme-btm-def-color: #9B9B9B;
    --theme-aside-no-active-box-shadow:#0000001F;
  
    --theme-main-bg-color_op: #0D368A66;
    --theme-secondary-color-help-text-color:var(--theme-primary-font-color);
     //底部按钮背景颜色
    --theme-btm-info-color:#ffffff;
    //首页底部信息块颜色
    --theme-btm-bg-color:#F5F5F5;
    --theme-hx-color: #FFFFFF;
    --theme-text-color-darken: #FFFFFF;
    
    
    --theme-text-color-placeholder:#999999;
    --theme-text-color-activities-text:#fff;
  
    --theme-color-line: #DEDEDE;
    --theme-secondary-color-success: #20F511;
    --theme-secondary-color-error: #DF230E;
    
  
    --theme-top-nav-bg:#ffffff;
    --theme-side-menu-btn-color:#4fc266;
    --theme-home-bg:#368D45;
    --theme-left-nav-text-active-color: #368D45;
  
    --theme-alt-primary:#FFFFFF;
     //顶部背景颜色
    --theme-alt-neutral-1:#C2FFD0;
    --theme-alt-neutral-2:#000000;
    --theme-aside-active-box-shadow:0 2px 4px 0 rgba(0, 0, 0, 0.1);
    --theme-ant-primary-color-0:#FFFFFF;
  
    // 大厅列表 标题颜色
    --app-title-color: #333;
    //大厅三方游戏分类
    --app-platform-color: #472e4b;
    
  
    //大厅下面2024的描述字体颜色
    --app-desc-color: #a8a8a8;
  
    // 底部组件背景颜色
    --app-bottom-bg-color: #b94b6b;
    --app-bottom-item-color: #7cb39d;
    --app-bottom-itemSelect-color: #368D45;;
   
    //progress 进度条
    --app-progress-bg-color: #FFFFFF;
    --app-progress-top-color: #11b3fb;
  
    // toggle
    --app-toggle-norbg-color: #0f3882;
    --app-toggle-selbg-color: #ff3442;
  
    //邀请记录标题颜色
    --app-dados-item-title-bg-color: #679aec;
    
   
    //邀请界面蓝色按钮
    --app-btn-normal-blue-color: #3490ff;
    --app-btn-jumpweek-blue-color:  #11b3fb;
    //周返利比例标题颜色
    --app-ratio-title-color: #3c81f9;
  
    // 整模块背景颜色
    --app-box-bg-color: #2c2031;
  
    // 游戏标题背景颜色
    --app-game-title-bg-color: #3b82f6;
    //页脚高度
    --app-footer-height : 113px;
  
    //person 个人中心模块
    --app-top_username-color: #26244c;
    //修改密码顶部标题
    --app-psw-top-color:#503B58;
    --app-psw-input-color:#1c1c1c;
    --app-psw-inputtop-color:#999fb4;
    --app-psw-select-color:#b01ffa;// #3490ff;
    //withlayer
    --app-withdraw-title-color:#b01ffa;
    //shoplayer
    --app-shop-title-color:#b01ffa;
  
    --app-shop-pagar-color:#b01ffa;
  
  
   
  
    --app-bg-border-radius:15;
    --van-popup-close-icon-color:var(--van-black);
    //showMessasge
    --app-message-color:var(--theme-main-bg-color);
    --app-message-input-color:#262626;
    --overflow-value: auto;
  
  
  
  
  
    ///////////////// 通用界面
    --app-red-line-color:rgba(0, 0, 0, .1);
  
    //vip
    --app-red-vip-down:#5cbf3f;
    





    
    //颜色配置 250423  #A1A73E
    //主背景色
    --theme-main-bg-color: #F5F5F5;
    //副背景色
    --theme-sub-bg-color:#FFFFFF;
    //主色调
    --theme-primary-color: #368D45;//
    //辅助色
    --theme-sub-color: #6AD383;//
    //主色调字体
    --theme-primary-font-color:#368D45;
    //svg图标颜色
    --svg-icon-color:#368D45;
    //首页顶部下载
    --theme-download-color: #368D45;
    //返回箭头颜色
    --back-arrow-color: #4A4A4A;
    //基础文本颜色（建议浅色背景黑色 #26244c，深色背景浅色 #FFFFFF）
    --theme-text-color: #26244c;
    //副字体字体
    --theme-text-color-lighten:#999999;
    //按钮上的字体
    --theme-font-on-background-color:#ffffff;
    //货币颜色  
    --theme-secondary-color-finance: #FFAA09;
    //下劃線
    --theme-line-color:#ffffff;
    



  }
  
  html[theme='green'] {
    // background-color: #EDf2fA;
    // background-color: #021d4d;
    background-color: var(--theme-main-bg-color);
  }
  
  .warning-popover.van-popover[data-popper-placement=top-start] .van-popover__arrow {
    left: 20px;
  }
  
  .warning-popover.van-popover[data-popper-placement=top-end] .van-popover__arrow {
    right: 20px;
  }
  
  
  //时间选择器
  .van-picker-column__item {
    color: #FFC1CD;
    font-size:20px;
  }
  
  .van-picker-column__item--selected {
    color: #ffffff;
    font-size:20px;
  }
  
  