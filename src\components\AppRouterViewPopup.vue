<script lang="ts" setup>
const router = useRouter();
const appStore = useAppStore();
const { showRouterView } = storeToRefs(appStore);
</script>

<template>
  <van-popup
    class="app-pay-popup"
    v-model:show="showRouterView"
    position="right"
    teleport="body"
    :close-on-click-overlay="true"
    destroy-on-close
    z-index="1001"
  >
    <div class="view-container">
      <RouterView />
    </div>
  </van-popup>
</template>

<style lang="css">
.view-container {
  /* top:-1vw; */
  background-color: #E7E4FF;
  width: 100vw;
  height: 100vh;
}
</style>
