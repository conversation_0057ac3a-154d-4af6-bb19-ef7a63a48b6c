<script setup lang="ts" name="home">
import { GameNavEnum, GameSoltTypeEnum } from "~/types/common";
const router = useRouter();
const appStore = useAppStore();
const { isSmallGameIcon, showIndexModule } = storeToRefs(appStore);

const gameStore = useGameStore();
const { gameNavData, gameNavInit } = storeToRefs(gameStore);

const openService = (url?: string) => {
  if (!url) return;
  window.open(url, "_blank");
};

const filterType = ref("");

const setFilterGameParam = (v: string) => {
  filterType.value = v;
};
</script>

<template>
  <div class="app-index-layout-container">
    <!-- <div class="g-filter">
      <AppGameFilterGlobal :id="+GameNavEnum.Quente" :setFilterGameParam="setFilterGameParam" />
    </div> -->
    <!-- <AppIndexGameContainer
      :id="GameNavEnum.Quente"
      class="game-container-list"
      :filter-type="filterType"
    /> -->
    <!-- 热门游戏-->
    <!--<AppGamePlatform />   三方平台-->
    <!-- <AppIndexSlotsContainer
      :id="GameNavEnum.Slots"
      class="game-container-list"
      :filter-type="filterType"
      v-if="!isSmallGameIcon"
    /> -->
    <!--  老虎机分类-->

    <!-- <AppIndexGameContainer :id="GameNavEnum.Dentro_De_Casa" class="game-container-list" :filter-type="filterType" /> -->

    <!-- 写死 pg-->
    <!--捕鱼 视讯 binggo 接机-->
    <!-- <template v-for="item in GameSoltTypeEnum">  
      <AppIndexGameContainer v-if="typeof(item) == 'string' " :id="GameNavEnum.Slot" :platform_id="`${GameSoltTypeEnum[item]}`"  class="game-container-list" :filter-type="filterType" fav-type="rec" />
    </template> -->
    <!--捕鱼 视讯 binggo 接机-->
    <!-- <template v-for="nav in gameNavData">  
      <AppIndexGameContainer v-if="gameNavInit[nav.id] && gameNavInit[nav.id].length && nav.id !=GameNavEnum.Slot " :id="nav.id"
        class="game-container-list" :filter-type="filterType" fav-type="rec" />
    </template> -->
    <!-- <AppIndexLatestWin class="index-latest-win" /> -->
    <div class="app-index-bottom">
      <div>
        <img class="bg" src="/img/footerInfo/parceiros.png" />
        <div class="bg-item" style="top: 16vw; left: 41vw">
          <img src="/img/footerInfo/gblogo_pg.png" />
        </div>
        <div class="bg-item" style="top: 25vw; left: 66vw">
          <img src="/img/footerInfo/gblogo_jdb.png" />
        </div>
        <div class="bg-item" style="top: 44vw; left: 66vw">
          <img src="/img/footerInfo/gblogo_bg.png" />
        </div>
        <div class="bg-item" style="top: 53vw; left: 41vw">
          <img src="/img/footerInfo/gblogo_tada.png" />
        </div>
        <div class="bg-item" style="top: 44vw; left: 16vw">
          <img src="/img/footerInfo/gblogo_evo.png" />
        </div>
        <div class="bg-item" style="top: 25vw; left: 16vw">
          <img src="/img/footerInfo/gblogo_pp.png" />
        </div>

        <div class="bg-item" style="position: relative; top: -39.5vw">
          <img style="width: 13vw; height: auto" src="/logo_left_1.webp" />
        </div>
      </div>

      <div class="share">
        <div class="share-title">
          <img
            style="filter: invert(100%); width: 27px; margin-right: 7px"
            src="/img/footerInfo/share.png"
          />
          Compartilhar
        </div>
        <div
          class="share-content"
          style="box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.08)"
        >
          <img src="/img/footerInfo/home_share_18.png" />
          <img src="/img/footerInfo/home_share_facebook.png" />
          <img src="/img/footerInfo/home_share_telegram.png" />
          <img src="/img/footerInfo/home_share_insgram.png" />
        </div>

        <div class="privacy">
          H9BET.COM é operada pela Dubet N.V., número de registro da empresa
          18692, com endereço registrado em Zuikertuintjeweg Z/N (Zuikertuin
          Tower) Curaçao e é licenciada e autorizada pelo governo de Curaçao.
          WJCASINO opera sob a Master License of Gaming Services Provider, N.V.
          Número da Licença: GLH-OCCHKTW0682862024
        </div>

        <div class="company">
          ©2025 H9BET.COM.Todos os direitos reservados. 18+
        </div>
      </div>

      <!-- <div><img src="/img/footerInfo/.png" /></div> -->
    </div>
    <!-- <div class="copyright">
      Copyright © All Rights Reserved by top-panda
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
html {
  scroll-behavior: smooth;
}
.scroll_image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0.3rem 0 0.1rem;
  img {
    height: auto;
    max-width: 45vw;
    margin-right: 0;
    padding-bottom: 15.51px;
  }
}
.app-index-layout-container {
  position: relative;
  scroll-snap-type: y mandatory;
  overflow-x: visible !important;
  scroll-behavior: smooth;
  .g-filter {
    position: absolute;
    right: 0;
    top: 28px;
  }
}

.game-container-list {
  // padding-top: 30px;
}

.app-index-bottom {
  width: 100vw;
  padding: 20px;
  // border-radius: 20px;
  margin-top: 30px;
  padding-bottom: var(--app-footer-height);
  // background-color: var(--theme-btm-bg-color);
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  .bg {
    width: 100%;
  }
  .bg-item {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .share-title {
    display: flex;
    font-size: 30px;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #000000d9;
  }
  .share-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 25px;
    img {
      margin: 50px 0px;
      width: 80px;
      height: auto;
    }
  }
  .privacy {
    margin: 15vw 1vw;
    font-size: 2.3vw;
    line-height: 4vw;
    white-space: pre-wrap;
    color: #000000d9;
    text-align: left;
  }
  .company {
    width: 100%;
    font-size: 2.4vw;
    text-align: center;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 50px;
  }

  img {
    width: 140px;
    height: auto;
  }
}

.index-latest-win {
  margin-top: 80px;
}

.index-title {
  color: #fff;
  font-size: 32px;
  font-weight: 700;
  padding-top: 30px;
  padding-bottom: 27px;
}

.jobs-img {
  width: 100%;
}

.index-line {
  position: relative;
  width: 100%;
  margin-top: 39px;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed #828eb4;
    pointer-events: none;
    transform: scaleY(0.5);
    transform-origin: top left;
  }
}

.index-b-img {
  vertical-align: middle;
}

.index-b-img-lx {
  width: 76px;

  &:not(:last-child) {
    margin-right: 30px;
  }
}

.index-b-img-139 {
  width: 139px;
}

.index-b-img-92 {
  width: 92px;
}

.index-title-2 {
  color: rgba(255, 255, 255, 0.6);
  font-size: 26px;
  padding-top: 40px;
}

.bottom-link {
  color: #ccced2;
  text-align: center;
  font-size: 22px;
  display: flex;
  justify-content: space-around;
  padding: 35px 0 50px 0;

  .link-img {
    height: 51px;
  }
}

.copyright {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  font-size: 24px;
  height: 94px;
  background: #28374d;
  line-height: 94px;
}
</style>
<route lang="yaml">
meta:
  layout: home
</route>
