<template>
  <div v-if="shouldShow" class="miui-guide">
    <div class="guide-container">
      <div class="guide-header">
        <div class="browser-info">
          <div class="browser-icon">📱</div>
          <div class="browser-details">
            <h3>MIUI浏览器安装指导</h3>
            <p>将网站添加到小米手机桌面</p>
          </div>
        </div>
        <button @click="closeGuide" class="close-btn">×</button>
      </div>

      <div class="guide-content">
        <!-- 检测到的设备信息 -->
        <div class="device-info">
          <div class="info-item">
            <span class="label">设备:</span>
            <span class="value">{{ deviceInfo.device }}</span>
          </div>
          <div class="info-item">
            <span class="label">MIUI版本:</span>
            <span class="value">{{ deviceInfo.miuiVersion }}</span>
          </div>
          <div class="info-item">
            <span class="label">浏览器版本:</span>
            <span class="value">{{ deviceInfo.browserVersion }}</span>
          </div>
        </div>

        <!-- 安装步骤 -->
        <div class="install-steps">
          <h4>📋 安装步骤</h4>
          <div class="steps-container">
            <div 
              v-for="(step, index) in installSteps" 
              :key="index"
              class="step-item"
              :class="{ active: currentStep === index }"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-description">{{ step.description }}</div>
                <div v-if="step.tips" class="step-tips">
                  💡 <span>{{ step.tips }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 替代方法 -->
        <div class="alternative-methods">
          <h4>🔄 其他方法</h4>
          <div class="methods-list">
            <div class="method-item">
              <div class="method-icon">📌</div>
              <div class="method-content">
                <div class="method-title">长按地址栏</div>
                <div class="method-description">长按浏览器地址栏，可能会出现"添加到桌面"选项</div>
              </div>
            </div>
            <div class="method-item">
              <div class="method-icon">📤</div>
              <div class="method-content">
                <div class="method-title">分享功能</div>
                <div class="method-description">点击分享按钮，查找"添加到桌面"或"创建快捷方式"</div>
              </div>
            </div>
            <div class="method-item">
              <div class="method-icon">⭐</div>
              <div class="method-content">
                <div class="method-title">收藏夹</div>
                <div class="method-description">先收藏网页，然后在收藏夹中长按选择"添加到桌面"</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 重要提示 -->
        <div class="important-notice">
          <h4>⚠️ 重要提示</h4>
          <div class="notice-content">
            <div class="notice-item warning">
              <div class="notice-icon">🚫</div>
              <div class="notice-text">
                <strong>小米浏览器限制：</strong>无法手动触发安装提示，只能等待浏览器自动弹出安装选项。
              </div>
            </div>
            <div class="notice-item info">
              <div class="notice-icon">🔄</div>
              <div class="notice-text">
                <strong>缓存影响：</strong>如果之前拒绝过安装，需要清除浏览器缓存才能重新看到安装提示。
              </div>
            </div>
            <div class="notice-item tip">
              <div class="notice-icon">📁</div>
              <div class="notice-text">
                <strong>技术要求：</strong>网站的Service Worker必须在根目录下才能触发安装功能。
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="faq-section">
          <h4>❓ 常见问题</h4>
          <div class="faq-list">
            <details class="faq-item">
              <summary>为什么没有出现安装提示？</summary>
              <p>小米浏览器的安装提示有特殊限制：</p>
              <ul>
                <li><strong>无法手动触发</strong>：只能等待浏览器自动判断并弹出</li>
                <li><strong>需要满足条件</strong>：网站必须有Service Worker且在根目录</li>
                <li><strong>用户行为影响</strong>：需要一定的用户交互时间</li>
                <li><strong>缓存记忆</strong>：如果之前拒绝过，需要清除缓存</li>
              </ul>
            </details>
            <details class="faq-item">
              <summary>如何清除浏览器缓存？</summary>
              <p>清除缓存的步骤：</p>
              <ul>
                <li>打开MIUI浏览器设置</li>
                <li>找到"隐私设置"或"清除数据"</li>
                <li>选择"清除浏览数据"</li>
                <li>勾选"缓存"和"网站数据"</li>
                <li>确认清除后重新访问网站</li>
              </ul>
            </details>
            <details class="faq-item">
              <summary>添加后图标不正确？</summary>
              <p>这是正常现象，MIUI浏览器会使用网站的图标。如果图标显示不正确，可以：</p>
              <ul>
                <li>删除桌面图标重新添加</li>
                <li>清除浏览器缓存后重试</li>
                <li>等待网站图标加载完成后再添加</li>
              </ul>
            </details>
            <details class="faq-item">
              <summary>如何删除桌面图标？</summary>
              <p>长按桌面上的应用图标，选择"卸载"或"删除"即可。</p>
            </details>
          </div>
        </div>
      </div>

      <div class="guide-actions">
        <button @click="nextStep" class="action-btn primary" v-if="currentStep < installSteps.length - 1">
          下一步 ({{ currentStep + 2 }}/{{ installSteps.length }})
        </button>
        <button @click="completeGuide" class="action-btn success" v-else>
          完成指导
        </button>
        <button @click="closeGuide" class="action-btn secondary">
          稍后再说
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const shouldShow = ref(false)
const currentStep = ref(0)

// 设备信息
const deviceInfo = ref({
  device: '',
  miuiVersion: '',
  browserVersion: ''
})

// 安装步骤
const installSteps = ref([
  {
    title: '打开浏览器菜单',
    description: '点击MIUI浏览器底部的菜单按钮（通常是三条横线图标）',
    tips: '菜单按钮通常位于屏幕底部中央或右下角'
  },
  {
    title: '找到工具选项',
    description: '在菜单中查找"工具箱"、"更多工具"或"设置"选项',
    tips: '不同版本的MIUI浏览器菜单名称可能略有不同'
  },
  {
    title: '选择添加到桌面',
    description: '在工具选项中找到"添加到桌面"、"创建快捷方式"或"添加书签到桌面"',
    tips: '如果没有找到，可以尝试"书签"或"收藏"相关选项'
  },
  {
    title: '确认添加',
    description: '点击添加选项，系统会提示是否添加到桌面，点击确认',
    tips: '添加成功后，应用图标将出现在MIUI桌面上'
  }
])

// 检测是否为MIUI浏览器
const isMiuiBrowser = () => {
  const userAgent = navigator.userAgent
  return /MiuiBrowser/i.test(userAgent) || 
         /XiaoMi/i.test(userAgent) ||
         /MI\s/i.test(userAgent) ||
         /MIUI/i.test(userAgent)
}

// 检测是否为小米设备
const isXiaomiDevice = () => {
  const userAgent = navigator.userAgent
  return /XiaoMi|MI\s|MIUI|Redmi/i.test(userAgent)
}

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent
  
  // 检测设备型号
  let device = '小米设备'
  const deviceMatch = userAgent.match(/(MI|Redmi|POCOPHONE)\s+([A-Z0-9\s]+)/i)
  if (deviceMatch) {
    device = deviceMatch[0]
  }
  
  // 检测MIUI版本
  let miuiVersion = '未知'
  const miuiMatch = userAgent.match(/MIUI\/(\d+\.\d+)/i)
  if (miuiMatch) {
    miuiVersion = `MIUI ${miuiMatch[1]}`
  }
  
  // 检测浏览器版本
  let browserVersion = '未知'
  const browserMatch = userAgent.match(/MiuiBrowser\/(\d+\.\d+)/i)
  if (browserMatch) {
    browserVersion = `MIUI浏览器 ${browserMatch[1]}`
  } else if (/Chrome/i.test(userAgent)) {
    const chromeMatch = userAgent.match(/Chrome\/(\d+\.\d+)/i)
    if (chromeMatch) {
      browserVersion = `Chrome ${chromeMatch[1]}`
    }
  }
  
  deviceInfo.value = {
    device,
    miuiVersion,
    browserVersion
  }
}

// 下一步
const nextStep = () => {
  if (currentStep.value < installSteps.value.length - 1) {
    currentStep.value++
  }
}

// 完成指导
const completeGuide = () => {
  // 记录完成事件
  console.log('MIUI浏览器安装指导完成')
  closeGuide()
}

// 关闭指导
const closeGuide = () => {
  shouldShow.value = false
  // 24小时后再次显示
  localStorage.setItem('miui_guide_dismissed', Date.now().toString())
}

// 检查是否应该显示
const shouldShowGuide = () => {
  // 检查是否为MIUI浏览器或小米设备
  if (!isMiuiBrowser() && !isXiaomiDevice()) {
    return false
  }
  
  // 检查是否已安装
  if (window.matchMedia('(display-mode: standalone)').matches) {
    return false
  }
  
  // 检查是否最近关闭过
  const dismissedTime = localStorage.getItem('miui_guide_dismissed')
  if (dismissedTime) {
    const timeDiff = Date.now() - parseInt(dismissedTime)
    const oneDayInMs = 24 * 60 * 60 * 1000
    return timeDiff > oneDayInMs
  }
  
  return true
}

onMounted(() => {
  getDeviceInfo()
  
  if (shouldShowGuide()) {
    // 延迟3秒显示，给用户一些浏览时间
    setTimeout(() => {
      shouldShow.value = true
    }, 3000)
  }
})
</script>

<style scoped>
.miui-guide {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.guide-container {
  background: white;
  border-radius: 16px;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.browser-info {
  display: flex;
  align-items: center;
}

.browser-icon {
  font-size: 32px;
  margin-right: 12px;
}

.browser-details h3 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 18px;
}

.browser-details p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guide-content {
  padding: 0 20px;
}

.device-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.install-steps h4,
.alternative-methods h4,
.faq-section h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.steps-container {
  margin-bottom: 24px;
}

.step-item {
  display: flex;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 2px solid #eee;
  transition: all 0.3s ease;
}

.step-item.active {
  border-color: #007AFF;
  background: #f0f8ff;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 16px;
  flex-shrink: 0;
}

.step-item.active .step-number {
  background: #0056CC;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.step-description {
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
}

.step-tips {
  font-size: 12px;
  color: #007AFF;
  background: #f0f8ff;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #007AFF;
}

.methods-list {
  margin-bottom: 24px;
}

.method-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.method-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.method-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.method-description {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.important-notice {
  margin-bottom: 24px;
}

.notice-content {
  space-y: 12px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.notice-item.warning {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
}

.notice-item.info {
  background: #d1ecf1;
  border-left: 4px solid #17a2b8;
}

.notice-item.tip {
  background: #d4edda;
  border-left: 4px solid #28a745;
}

.notice-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.notice-text {
  flex: 1;
  color: #333;
  line-height: 1.4;
}

.notice-text strong {
  color: #333;
  font-weight: 600;
}

.faq-list {
  margin-bottom: 24px;
}

.faq-item {
  margin-bottom: 12px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.faq-item summary {
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  font-weight: 600;
  color: #333;
}

.faq-item p,
.faq-item ul {
  padding: 12px 16px;
  margin: 0;
  color: #666;
  line-height: 1.4;
}

.faq-item ul {
  padding-left: 32px;
}

.faq-item li {
  margin-bottom: 4px;
}

.guide-actions {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.primary:hover {
  background: #0056CC;
}

.action-btn.success {
  background: #28a745;
  color: white;
}

.action-btn.success:hover {
  background: #1e7e34;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
}

.action-btn.secondary:hover {
  background: #e9ecef;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@media (max-width: 480px) {
  .miui-guide {
    padding: 10px;
  }
  
  .guide-container {
    max-height: 95vh;
  }
  
  .guide-actions {
    flex-direction: column;
  }
  
  .step-item {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    margin: 0 auto 12px auto;
  }
}
</style>
