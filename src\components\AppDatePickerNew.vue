<script setup lang='ts' name='date-picker'>
interface Props {
  modelValue: boolean
  group?: boolean
  startDate?: string
  initialDate?: string
  endDate?: string
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'cancel', 'confirm', 'change'])

// const showPopup = computed(() => props.modelValue)
// const initialDate = ref(props.initialDate?.split(/[/-]/) ?? [
//   new Date().getFullYear().toString(),
//   (new Date().getMonth() + 1).toString(),
//   new Date().getDate().toString(),
// ])
const startDate = ref(props.startDate?.split(/[/-]/) ?? [
  new Date().getFullYear().toString(),
  (new Date().getMonth() + 1).toString(),
  new Date().getDate().toString(),
])
const endDate = ref(props.endDate?.split(/[/-]/) ?? [
  new Date().getFullYear().toString(),
  (new Date().getMonth() + 1).toString(),
  new Date().getDate().toString(),
])

// watch(() => props.endDate, () => {
//   startDate.value = props.endDate?.split(/[/-]/) ?? [
//     new Date().getFullYear().toString(),
//     (new Date().getMonth() + 1).toString(),
//     new Date().getDate().toString(),
//   ]
// })

// watch(() => props.endDate, () => {
//   if (new Date(props.endDate!).getTime() > new Date().getTime()) {
//     endDate.value = [
//       new Date().getFullYear().toString(),
//       (new Date().getMonth() + 1).toString(),
//       new Date().getDate().toString(),
//     ]
//   } else {
//     endDate.value = props.startDate?.split(/[/-]/) ?? [
//       new Date().getFullYear().toString(),
//       (new Date().getMonth() + 1).toString(),
//       new Date().getDate().toString(),
//     ]
//   }

// })

const minDate = new Date(2023, 0, 1)
const maxDate = new Date()

const onCancel = () => {
  emit('update:modelValue', false)
  emit('cancel')
}
const onConfirm = (data: any) => {
  emit('update:modelValue', false)
  if (props.group ) {

    if(new Date(startDate.value.join('-')).getTime() > new Date(endDate.value.join('-')).getTime()){
        console.log("startData="+startDate.value.join('-'))
        console.log("endData="+endDate.value.join('-'))
       
        endDate.value = startDate.value
    }

    const start = startDate.value.join('-')
    const end = endDate.value.join('-')
    emit('confirm', { start, end })

  }
  else {
    emit('confirm')
  }
}


const onChange = (data: any) => {
  
  const start = data[0].selectedValues.join('/')
  const end = data[1].selectedValues.join('/')
  if(new Date(start).getTime() > new Date(end).getTime()){
    data[1].selectedValues = data[0].selectedValues
  }
  emit('change', data.selectedValues.join('/'))
}
</script>
<template>

  <!-- <div class="content"> -->
          
    <div class="selectTime">
      <div class="startTime">
        <label class="startTime_test">Data de Início</label>
        <van-date-picker v-model="startDate" :min-date="minDate" :max-date="maxDate" option-height="30px"   visible-option-num="5" :show-toolbar="false" class="custom-picker"/>  
        <div class="div_cancel">
          <div class="btn_cancel" @click="onCancel">
            <span>Cancelar</span>
          </div> 
          </div>
      </div>
    
      <div class="startTime">
        <label class="startTime_test">Data de Fim</label>
        <van-date-picker v-model="endDate" :min-date="minDate" :max-date="maxDate" option-height="30px"  visible-option-num="5" :show-toolbar="false" />  
        <div class="div_confimar">
          <div class="btn_confimar" @click="onConfirm">
            <span>Confimar</span>
          </div> 
        </div>
      </div>
      <div class="line"></div>
    </div>

  <!-- </div> -->


  <!-- <van-popup v-model:show="showPopup" @click-overlay="onCancel" 
    :style="{ borderRadius: '20px 20px 0 0 ' }">
    <van-picker-group v-if="group" cancel-button-text="Cancelar" confirm-button-text="Confirme"
      :tabs="['data de início', 'data final']" @confirm="onConfirm" @cancel="onCancel">
      <template #title>
        <span class="title">Selecione a data</span>
      </template>
      <van-date-picker v-model="startDate" :min-date="minDate" :max-date="maxDate" />
      <van-date-picker v-model="endDate" :min-date="minDate" :max-date="maxDate" />
    </van-picker-group>
    <van-date-picker v-else cancel-button-text="Cancelar" confirm-button-text="Confirme" v-model="initialDate"
      :min-date="minDate" :max-date="maxDate" @cancel="onCancel" @confirm="onConfirm" @change="onChange">
      <template #title>
        <span class="title">Selecione a data</span>
      </template>
    </van-date-picker>
  </van-popup> -->

</template>
<style lang="scss" scoped>
.title {
  color: #fff;
  font-weight: 700;
}

// .content{
//   width: 710px;
//   height: 400px; 

// }

.selectTime{
  width: 710px;
  height: 440px;
  background: var(--theme-main-bg-color);
  // background:#461621;
  margin: auto;
  border-radius: 10px;
  border:1px solid;
  border-color:var(--theme-color-line);
  left: 20px;
  .line{
    position: absolute;
    width:1px;
    height:250px;
    top: 80px;
    left: 50%;
    background:var(--theme-color-line) ;
  }

  .startTime{
    display: inline-block;
    width:calc(50% - 1px);
    height: 100%;
    // background-color: #c5a6a6

    .startTime_test{
      display: inline-block;
      color: rgb(191, 221, 205);
      width: 100%;
      text-align: center;
      margin-top: 20px;
      font-weight: 900;
      margin-bottom:30px;
      
    }
  }

  .div_cancel{
      font-size: 20px;
      color: var(--theme-primary-color);
      margin: auto;
      margin-top: 20px;
      // width: 120px;
      // height: 50px;
      // background:#164633;
      // border-radius: 10px;
      // border:1px solid;
      // border-color:var(--theme-filter-active-color);
      text-align: center;
      .btn_cancel{
        width: 120px;
        height: 50px;
        border-radius: 10px;
        border:1px solid;
        border-color:var(--theme-filter-active-color);
        // line-height: 45px;
        position: relative;
        left: 120px;
        top: -24px;
        span{
          position: relative;
          top: 12px;
        }
      }
  }

  .div_confimar{
      font-size: 20px;
      color:var(--theme-bg-color);
      margin: auto;
      margin-top: 20px;
      // width: 120px;
      // height: 50px;
      // background:var(--theme-primary-color);
      // border-radius: 10px;
      text-align: center;
      .btn_confimar{
        width: 120px;
        height: 50px;
        background:var(--theme-primary-color);
        border-radius: 10px;
        // line-height: 45px;
        position: relative;
        left: 120px;
        top: -24px;
        span{
          position: relative;
          top: 12px;
        }
      }
  }

}

</style>

<style lang='scss'>
:root {
  --van-picker-background: var(--theme-main-bg-color);
  --van-picker-mask-color: var(--theme-main-bg-color);
  --van-picker-option-font-size:25px;
  --van-picker-option-text-color: var(--theme-text-color-lighten);
  
  // --van-picker-option-text-selected-color:#ffffff;

  // --van-picker-confirm-action-color: #679fea;
  // --van-picker-cancel-action-color: #FFFFFFB2;

  // --van-popup-background: #324b6e;
  // --van-picker-group-background: #164633;
 

  [class*=van-hairline]:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 3px solid var(--theme-color-line);
    border-left: none;
    border-right: none;
    // transform: scale(.5);
  }

  .custom-picker.van-picker__column--selected {
		color: #f00;
		font-size: 18px;
	}

  .custom-picker.van-picker-column__item--selected {
		color: #000;
		font-size: 18px;
	}

}
</style>
