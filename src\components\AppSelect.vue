<script setup lang="ts" name="AppSelect">
const props = withDefaults(defineProps<{
  modelValue?: string;
  bc?: string;
  downIcon?: string;
  listData: Array<{ label: string; value: string;[k: string]: any }>;
  width?: number;
  plain?: boolean;
  flag?: string;
  flagStyle?: string;
  flagSize?: number
}>(), {
  modelValue: '',
})
const emit = defineEmits(['update:modelValue', 'onSelect'])
const selectValue = useVModel(props, 'modelValue', emit)
const show = ref(false)

const btnClick = () => {
  show.value = !show.value
}

// const itemClick = (value: string) => {
//   selectValue.value = value
//   show.value = false
// }

const selectLabel = computed(() => {
  const item = props.listData.find(item => item.value === selectValue.value)
  return item ? item.label : ''
})

const onConfirm = (selectedValues: any) => {
  show.value = false
  selectValue.value = selectedValues.selectedValues[0]
  emit('onSelect')
}

defineExpose({ btnClick })

</script>

<template>
  <button v-if="!plain" class="app-select" @click="btnClick" :style="{
    width: props.width ? `var(--app-px-${props.width})` : '100%',
    backgroundColor: props.bc ? props.bc : `#28374d`
  }">
    <span class="select-content" :style="`${props.flagStyle}`">
      <AppImage v-if="props.flag" class="flag" :style="{width: `var(--app-px-${props.flagSize ? props.flagSize : 26})`}" :src="`/icons/${props.flag}`" alt="" />
      <slot>{{ selectLabel }}</slot>
    </span>
    <AppImage class="down" :src="`/icons/${downIcon ? downIcon : 'icon_pull_down.png'}`" alt="" />
  </button>
  <van-popup v-model:show="show" class="van-popup-transparent " position="bottom" teleport="body">
    <div class="popup-ul">
      <van-picker :columns="listData" :columns-field-names="{
        text: 'label',
        value: 'value'
      }" confirm-button-text="Confirme" cancel-button-text="Cancelar" title="Selecione a data" @cancel="show = false"
        @confirm="onConfirm" />
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.select-content{display: flex;flex-direction: row;align-items: center}
.flag{margin: -2px 10px 0 -4px}
.app-select {
  // --select-color: #0ED1F4;
  // --select-color-opacity: rgba(14, 209, 244, 0.5);
  height: 67px;
  background-color: #28374d;
  border-radius: 15px;
  padding: 0 20px;
  color: #fff;
  transition: all 0.15s ease-in-out;
  text-align: left;
  font-size: 24px;
  position: relative;
  .down {
    height: 14px;
    position: absolute;
    top: 25px;
    right: 25px;
  }
  >span{color: var(--theme-text-color-lighten)}

  &:focus {
    border-color: var(--select-color);
    color: var(--select-color);
  }

  &:active {
    border-color: var(--select-color-opacity);
    color: var(--select-color-opacity);
  }
}


.popup-ul {
  list-style: none;
  color: #fff;
  --van-text-color: #fff;
  --van-font-bold: 300;
  --van-picker-mask-color: #324b6e;
  --van-picker-option-text-color: #fff;
  --van-picker-option-font-size: 36px;
  --van-picker-title-font-size: 24px;
  --van-picker-action-font-size: 24px;
  --van-picker-toolbar-height: 100px;
  --van-border-color: #28374d;
  --van-border-width: 2px;
  --van-picker-cancel-action-color: #687d9e;
  --van-picker-confirm-action-color: #679fea;
  .van-picker__confirm{
    color: #679fea;
  }
  .van-picker__cancel{
    color: #687d9e;
  }

  :deep(.van-picker) {
    border-radius: 20px 20px 0px 0px;
    background-color: #324b6e;
    // background: linear-gradient(0deg, #011A51 0%, #011A51 100%), #D9D9D9;
  }

  .item {
    height: 90px;
    color: #fff;
    text-align: center;
    line-height: 90px;
  }
}
</style>
