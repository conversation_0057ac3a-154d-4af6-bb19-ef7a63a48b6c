// 检测PWA启动模式
function isPWAMode() {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true ||
         document.referrer.includes('android-app://');
}

// 按需加载优化的PWA初始化
async function initializePWA() {
  try {
    const isInPWAMode = isPWAMode();
    console.log('PWA Mode:', isInPWAMode, '- Using lazy loading strategy');

    // 检查浏览器支持
    if (!("serviceWorker" in navigator)) {
      console.log("Service Worker not supported");
      redirectToTarget();
      return;
    }

    // 如果是PWA模式启动，优先确保Service Worker激活
    if (isInPWAMode) {
      if (navigator.serviceWorker.controller) {
        console.log('Service Worker already active - fast PWA startup');
        redirectToTarget();
        return;
      }

      await navigator.serviceWorker.ready;
      console.log('Service Worker ready for PWA mode');
      redirectToTarget();
      return;
    }

    // 首次安装时的注册流程（按需加载策略）
    const registration = await navigator.serviceWorker.register("/service-worker.js", {
      scope: "/",
      updateViaCache: "none"
    });

    console.log("Service Worker registered with lazy loading:", registration.scope);

    // 立即重定向，不等待其他操作
    redirectToTarget();

    // 异步处理通知权限（不阻塞主流程）
    handleNotificationsAsync(registration);

  } catch (error) {
    console.error("PWA initialization failed:", error);
    redirectToTarget();
  }
}

// 异步处理通知相关功能（按需加载）
async function handleNotificationsAsync(registration) {
  try {
    if (!("Notification" in window)) {
      return;
    }

    const permission = await Notification.requestPermission();

    if (permission === "granted") {
      console.log("Notification permission granted - loading push manager");

      // 等待Service Worker准备就绪
      await navigator.serviceWorker.ready;

      // 按需订阅推送通知
      await subscribeToPushNotifications(registration);
    }
  } catch (error) {
    console.log("Notification handling failed:", error);
  }
}

// 按需推送通知订阅
async function subscribeToPushNotifications(registration) {
  try {
    const vapidPublicKey = "BC8XJduh5Rfe1WPGCGN9ehgscaKwpGVH_rl865Nfa6eK6YhTJcJ6BXivB6ACE_UeTEmAqD-KHB6V6EQrbZDtTRA";
    const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);

    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: convertedVapidKey,
    });

    console.log("Push subscription successful (lazy loaded)");

  } catch (error) {
    console.log("Push subscription failed:", error);
  }
}

// 智能预加载 - 只在需要时加载
function smartPreload() {
  const targetUrl = getRedirectUrl("https://www.777PGDAY.com/?isApp=1");

  // 只进行DNS预解析，不预加载整个页面
  const dnsLink = document.createElement('link');
  dnsLink.rel = 'dns-prefetch';
  dnsLink.href = 'https://www.777PGDAY.com';
  document.head.appendChild(dnsLink);

  // 预连接（建立连接但不下载内容）
  const preconnectLink = document.createElement('link');
  preconnectLink.rel = 'preconnect';
  preconnectLink.href = 'https://www.777PGDAY.com';
  document.head.appendChild(preconnectLink);

  console.log('Smart preload applied for:', targetUrl);
}

// 重定向到目标网站
function redirectToTarget() {
  const targetUrl = getRedirectUrl("https://www.777PGDAY.com/?isApp=1");

  // 如果是PWA模式，使用智能预加载
  if (isPWAMode()) {
    smartPreload();
    // 短暂延迟让预连接生效
    setTimeout(() => {
      window.location.href = targetUrl;
    }, 50); // 减少延迟时间
  } else {
    window.location.href = targetUrl;
  }
}

// 启动PWA初始化
initializePWA();

function urlBase64ToUint8Array(base64String) {
  const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, "+")
    .replace(/_/g, "/");
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// 获取URL参数
function getUrlParameter(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

// 构建重定向URL，添加channelId参数
function getRedirectUrl(baseUrl) {
  const channelId = getUrlParameter('channelId');
  if (channelId) {
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}channelId=${encodeURIComponent(channelId)}`;
  }
  return baseUrl;
}
