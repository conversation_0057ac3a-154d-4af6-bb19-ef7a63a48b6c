// 优化的PWA初始化 - 解决性能问题
async function initializePWA() {
  try {
    // 检查浏览器支持
    if (!("serviceWorker" in navigator)) {
      console.log("Service Worker not supported");
      redirectToTarget();
      return;
    }

    // 注册Service Worker（优化配置）
    const registration = await navigator.serviceWorker.register("/service-worker.js", {
      scope: "/",
      updateViaCache: "none" // 确保Service Worker及时更新
    });

    console.log("Service Worker registered successfully:", registration.scope);

    // 立即重定向，不等待其他操作
    redirectToTarget();

    // 异步处理通知权限和推送订阅（不阻塞主流程）
    handleNotificationsAsync(registration);

  } catch (error) {
    console.error("PWA initialization failed:", error);
    redirectToTarget();
  }
}

// 异步处理通知相关功能
async function handleNotificationsAsync(registration) {
  try {
    if (!("Notification" in window)) {
      return;
    }

    const permission = await Notification.requestPermission();

    if (permission === "granted") {
      console.log("Notification permission granted");

      // 等待Service Worker准备就绪
      await navigator.serviceWorker.ready;

      // 尝试订阅推送通知
      await subscribeToPushNotifications(registration);
    }
  } catch (error) {
    console.log("Notification handling failed:", error);
  }
}

// 推送通知订阅
async function subscribeToPushNotifications(registration) {
  try {
    const vapidPublicKey = "BC8XJduh5Rfe1WPGCGN9ehgscaKwpGVH_rl865Nfa6eK6YhTJcJ6BXivB6ACE_UeTEmAqD-KHB6V6EQrbZDtTRA";
    const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);

    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: convertedVapidKey,
    });

    console.log("Push subscription successful");

    // 可选：发送订阅信息到服务器
    // const subscriptionData = {
    //   id: "2",
    //   subscription: subscription,
    // };
    //
    // fetch('https://push.web2appify.com/subscribe', {
    //   method: 'POST',
    //   body: JSON.stringify(subscriptionData),
    //   headers: {
    //     'Content-Type': 'application/json'
    //   }
    // });

  } catch (error) {
    console.log("Push subscription failed:", error);
  }
}

// 重定向到目标网站
function redirectToTarget() {
  const targetUrl = getRedirectUrl("https://www.777PGDAY.com/?isApp=1");
  window.location.href = targetUrl;
}

// 启动PWA初始化
initializePWA();

function urlBase64ToUint8Array(base64String) {
  const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, "+")
    .replace(/_/g, "/");
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// 获取URL参数
function getUrlParameter(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

// 构建重定向URL，添加channelId参数
function getRedirectUrl(baseUrl) {
  const channelId = getUrlParameter('channelId');
  if (channelId) {
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}channelId=${encodeURIComponent(channelId)}`;
  }
  return baseUrl;
}
