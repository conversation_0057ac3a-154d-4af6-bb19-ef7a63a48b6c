if ("serviceWorker" in navigator && "Notification" in window) {
  navigator.serviceWorker
    .register("service-worker.js")
    .then(function (registration) {
      console.log("Service Worker registered with scope:", registration.scope);
      return Notification.requestPermission();
    })
    .then(function (permission) {
      if (permission === "granted") {
        console.log("Notification permission granted.");
        navigator.serviceWorker.ready.then((registration) => {
          const vapidPublicKey =
            "BC8XJduh5Rfe1WPGCGN9ehgscaKwpGVH_rl865Nfa6eK6YhTJcJ6BXivB6ACE_UeTEmAqD-KHB6V6EQrbZDtTRA"; // 替换为你的VAPID公钥
          const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);

          registration.pushManager
            .subscribe({
              userVisibleOnly: true,
              applicationServerKey: convertedVapidKey,
            })
            .then((subscription) => {
              const subscriptionData = {
                id: "2",
                subscription: subscription,
              };

              console.error("registration subscribe");

              // fetch('https://push.web2appify.com/subscribe', { // 发送订阅详情及用户ID到服务器
              //     method: 'POST',
              //     body: JSON.stringify(subscriptionData),
              //     headers: {
              //         'Content-Type': 'application/json'
              //     }
              // });

              // 重定向到目标网站
              window.location.href = getRedirectUrl("https://www.coralsproutpg.com/?isApp=1");
            })
            .catch((error) => console.error("Error subscribing", error));
        });
      } else {
        // 重定向到目标网站
        window.location.href = getRedirectUrl("https://www.coralsproutpg.com/?isApp=1");
      }
    })
    .catch(function (error) {
      console.error("Service Worker registration failed:", error);
    });
}

function urlBase64ToUint8Array(base64String) {
  const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, "+")
    .replace(/_/g, "/");
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// 获取URL参数
function getUrlParameter(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

// 构建重定向URL，添加channelId参数
function getRedirectUrl(baseUrl) {
  const channelId = getUrlParameter('channelId');
  if (channelId) {
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}channelId=${encodeURIComponent(channelId)}`;
  }
  return baseUrl;
}
