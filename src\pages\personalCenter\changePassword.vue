<script setup lang='ts' name='changePassword'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const passwordStrength = ref(0);
const passwordRef = ref();
const passwordRef2 = ref();
//隐藏底下菜单
appStore.setFooterDialogVisble(false)
const fromData = reactive({
    old_password: '',
    password: '',
    confirm_password: ''
})

const sidData = reactive({
    sid: '',
    ts: ''
})

const authPassword = () => {
    let strength = 0;
    let pd = fromData.password;

    // 检查大写字母
    if (/[A-Z]/.test(pd)) strength++;

    // 检查小写字母
    if (/[a-z]/.test(pd)) strength++;

    // 检查数字
    if (/\d/.test(pd)) strength++;

    // 检查特殊字符
    if (/[\W_]/.test(pd)) strength++;
    passwordStrength.value = strength;
};

function onClickBtn(){
    passwordRef.value.validation();
    passwordRef2.value.validation();
    if (fromData.password !== fromData.confirm_password) {
        return showToast("As senhas inseridas não coincidem");
    }
    runUpdatePwd()
}

const { run: runUpdatePwd, loading } = useRequest(() => ApiUpdateLoginPwd(fromData), {
  manual: true,
  onSuccess: () => {
    showToast("Estabelecer com sucesso");
    router.go(-1)
  }
})

</script>

<template>
    <AppPageTitle left-arrow title="Senha de Login" title-weight="700" />
    <div class="content">
        <label>Alterar Senha</label>
        <div class="password">
            <AppInput icon-with="30" width="100%" height="70" v-model="fromData.password" ref="passwordRef"
                :required="true" double-action="true" :pattern="passwordReg3" :err-height="42" placeholder="Senha"
                @change="authPassword" msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos"
                clearable icon-left="input-password-icon" type="password" :style-obj="{
                    background: 'var(--theme-main-bg-color)',
                    color: '#000',
                    borderRadius: '5px',
                }" />
        </div>
        <div :class="`ant-space-item passwordStrength-${passwordStrength}`">
            <span>Força</span>
            <span class="step"></span>
            <span class="step"></span>
            <span class="step"></span>
            <span class="step"></span>
        </div>
        <div class="password">
            <AppInput icon-with="30" width="100%" height="70" v-model="fromData.confirm_password" ref="passwordRef2"
                :required="true" double-action="true" :pattern="passwordReg3" :err-height="42"
                placeholder="Confirme a senha novamente, o mesmo que a senha!"
                msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos" clearable
                icon-left="input-password-icon" type="password" :style-obj="{
                    background: 'var(--theme-main-bg-color)',
                    color: '#000',
                    borderRadius: '5px',
                }" />
        </div>
        <div class="content-bottom">
            <AppButton @click="onClickBtn" :loading="loading" width="100%" height="70" :radius="15" fontSize="12px"
                background="var(--theme-primary-color)"
                color="var(--theme-primary-font-color)">
                Confirme
            </AppButton>
        </div>
    </div>

</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.content {
    height: calc(100vh - 100px);
    font-size: 25px;
    background-color: var(--theme-bg-color);
    .password{
        position: relative;
        top: 120px;
        padding: 0px 30px;
        
    }
    .ant-space-item {
        position: relative;
        top:160px;
        color: white;
        font-size: 0.9rem;
        margin: 5px 30px 55px 30px;
        display: flex;
        align-items: center;
        flex-direction: row;

        &.passwordStrength-0 .step {
            background-color: var(--theme-color-line);
        }
        &.passwordStrength-1 span:nth-child(2) {
            background-color: var(--theme-secondary-color-error);
        }
        &.passwordStrength-2 span:nth-child(2),
        &.passwordStrength-2 span:nth-child(3) {
            background-color: var(--theme-secondary-color-finance);
        }
        &.passwordStrength-3 .step {
            background-color: var(--theme-secondary-color-success);
        }
        .step {
            border-radius: 0.4rem;
            height: 0.5rem;
            width: 3rem;
            display: block;
            margin-left: 0.4rem;
            background-color: var(--theme-color-line);
        }
    }


    label {
        position: absolute;
        padding: 50px 0 0 35px;
        color: white;
    }

    &-bottom {
        position: absolute;
        bottom: 0;
        height: 120px;
        width: 100%;
        padding: 25px 25px;
        background-color: var(--theme-main-bg-color);
    }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>