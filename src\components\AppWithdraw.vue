<script lang="ts" setup name="WithdrawPage">
import { showFailToast } from 'vant';

const router = useRouter()
const appStore = useAppStore()
const { balanceDetailInfo, withdrawConf, userBanks } = storeToRefs(appStore)

const amount = ref('')
const pay_password = ref('')

const payPwdRef = ref()
const amountRef = ref()

const currentTypeId = ref(0)
const withdrawTypeList = computed(() => {
  if (userBanks.value.length > 0) {
    return userBanks.value.map((item, index) => {
      return { ...item, index }
    })
  }
  return []
})
const bank_id = computed(() => {
  if (withdrawTypeList.value.length > 0) {
    return withdrawTypeList.value.find(a => a.index === currentTypeId.value)?.id
  }
  return ''
})

const conf = computed(() => {
  if (withdrawConf.value && withdrawConf.value.config) {
    const temp = withdrawConf.value.config
    return { ...temp, fmin: +temp.fmin || 0, fmax: +temp.fmax || 0 }
  }
  return { fmin: 0, fmax: 0 }
})

const { run: postWithdraw } = useRequest(() => ApiPostWithdraw({
  amount: amount.value,
  bank_id: bank_id.value,
  fid: conf.value.fid,
  pay_password: pay_password.value
}), {
  manual: true,
  onError: () => {
  },
  onSuccess: () => {
    amount.value = ''
    pay_password.value = ''
    currentTypeId.value = 0
    appStore.runGetUserBalance()
    showToast({
      type: 'success',
      message: 'Retirada bem-sucedida'
    })
  }
})

const { run: runGetWithdrawFee, data: feeTip } = useRequest(ApiWithdrawFee, {
  manual: true,
  debounceInterval: 1000,
  onSuccess: (data) => {
    console.log(data)
  }
})

watchEffect(async () => {
  const val = amount.value
  if (val && +val > 0) {
    const withdrawTotal = balanceDetailInfo.value ? balanceDetailInfo.value.brl_amount : 0
    if (+val > withdrawTotal) {
      return
    }
    if (+conf.value.fmin >= 0) {
      if (+val < +conf.value.fmin) {
        return
      }
    }
    if (+conf.value.fmax >= 0) {
      if (+val > +conf.value.fmax) {
        return
      }
    }

    // onCleanup(cancelReqFee)
    runGetWithdrawFee({ amount: val })
  }
})

const doWthdraw = () => {
  const withdrawTotal = balanceDetailInfo.value ? balanceDetailInfo.value.brl_amount : 0
  if (+amount.value > withdrawTotal) {
    showFailToast('O valor da retirada não pode ser maior que o valor da retirada da conta')
    return
  }
  amountRef.value.validation()
  payPwdRef.value.validation()
  if (+conf.value.fmin >= 0) {
    if (+amount.value < +conf.value.fmin) {
      showFailToast('Insira um valor que atenda ao intervalo')
      return
    }
  }
  if (+conf.value.fmax >= 0) {
    if (+amount.value > +conf.value.fmax) {
      showFailToast('Insira um valor que atenda ao intervalo')
      return
    }
  }
  if (amountRef.value.isValid && payPwdRef.value.isValid) {
    postWithdraw()
  }
}
</script>

<template>
  <div class="content-withdraw">
    <div class="warn-desc"> Uma conta só pode ser vinculada a um número de CPF para saque, uma vez vinculada não
      pode ser alterada.</div>

    <div class="info-bar">
      <label class="amount-text">Valor da retirada</label>
      <AppInput icon-with="32"  ref="amountRef" v-model="amount" plain width="460" :placeholder="'Retirada mínima R$' + conf.fmin"
        :msg="conf.fmax > 0 ? 'Valor da retirada (' + conf.fmin + ' - ' + conf.fmax + ')' : 'Valor da retirada ' + conf.fmin"
        type="number" required :min="conf.fmin" :max="conf.fmax > 0 ? conf.fmax : undefined" />
    </div>
    <div v-if="feeTip && +feeTip > 0" class="warn-desc"> Taxa de manuseio {{ feeTip }} reais.</div>
    <div v-if="withdrawTypeList.length > 0" class="choose-pay">
      <p>Escolha o metodo de pagamento</p>
      <div class="pay-type" v-for="t in withdrawTypeList" :key="t.id" @click="currentTypeId = t.index">
        <AppImage class="i-radio" :src="`/icons/btn_${currentTypeId === t.index ? 'option' : 'no'}.png`" alt="" />
        <span>{{ t.pix_id }}</span>
      </div>
    </div>
    <div v-else class="no-card">
      <p>Para vincular cartão de banco</p>
      <AppButton width="132" height="48" radius="15" blue white-text font-size="28"
        @click="router.push('/safe-center/banks')">Retirar</AppButton>
    </div>

    <div class="info-bar mb-34">
      <AppInput icon-with="32"  ref="payPwdRef" v-model="pay_password" plain width="650" placeholder="Digite a senha do fundo"
        msg="Digite a senha do fundo" required :max-length="6" :pattern="payPasswordReg" clearable type="payPassword" />
    </div>

    <div class="btn-box">
      <AppButton :disabled="withdrawTypeList.length === 0" fontSize="36" radius="15" whiteText blue width="348" height="80" center @click="doWthdraw">
        Retirar
      </AppButton>
    </div>

    <div class="withdraw-desc">
      <h3>Regras de retirada</h3>
      <p>1. O valor e a frequência do saque diário estão diretamente relacionados ao seu nível VIP.</p>
      <p>2. O valor da retirada deve ser em múltiplos de 10. Por exemplo: 10, 20, 80, 120, 990, 19820…</p>
      <p>3. As recompensas da promoção podem ser retiradas diretamente.</p>
      <p>4. O saldo não retirável na conta de recarga (Atividade) (incluindo, entre outros, o valor da recarga,
        recompensas por participar de atividades e valor de ganhos e perdas do jogo, etc.).</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.btn-box {
  padding-bottom: 54px;
  padding-top: 66px;
}

.i-radio {
  display: block;
  object-fit: cover;
  width: 32px;
  height: 32px;
  margin-right: 20px;
}

.mb-34 {
  margin-bottom: 34px;
}

.content-withdraw {
  padding: 0 40px 88px;
}

.warn-desc {
  margin: 36px auto 30px;
  width: 670px;
  color: #F7BA17;
  text-align: center;
  font-size: 24px;
  line-height: 34px;
  // background: linear-gradient(90deg, rgba(247, 186, 23, 0.00) 0%, rgba(247, 186, 23, 0.16) 51.56%, rgba(247, 186, 23, 0.00) 100%);
}

.info-bar {
  width: 670px;
  margin: 0 auto;
  border-radius: 20px;
  // border: 1px solid rgba(255, 255, 255, 0.10);
  background: #28374d;
  line-height: 72px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 0 24px;
  position: relative;

  .amount-text {
    color: #FFF;
    font-size: 28px;
    position: absolute;
    right: 21px;
  }
}

.no-card {
  margin: 50px auto 30px;
  display: flex;
  justify-content: space-between;

  p {
    color: #679fea;
    font-size: 32px;
    margin-bottom: 17px;
  }
}



.choose-pay {
  margin: 30px auto;

  p {
    color: #679fea;
    font-size: 32px;
    margin-bottom: 17px;
  }

  .pay-type {
    width: 670px;
    height: 72px;
    border-radius: 20px;
    font-size: 30px;
    color: #fff;
    display: flex;
    align-items: center;
    padding-left: 24px;
    margin-bottom: 16px;
    background: #28374d;
    img{
      margin-top: -2px;
      width: 46px;
      height: 46px;
    }
  }
}

.withdraw-desc {
  width: 670px;
  padding: 30px;
  border-radius: 40px;
  background-color: #28374d;
  h3 {
    color: #F7BA17;
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 22px;
  }

  p {
    color: #FFF;
    font-size: 26px;
    font-weight: 400;
    line-height: 40px;
  }
}
</style>
