<script setup lang="ts" name="dados">
import Telegram from "../promotion-detail/telegram.vue";

const router = useRouter();
const appStore = useAppStore();
const { userInfo } = storeToRefs(appStore);
const whatsappRef = ref();
const facebookRef = ref();
const telRef = ref();
const showDatePicker = ref(false);
const dateStr = ref("Dia Mês Ano");

const currentDate = ref(false);

// appStore.setFooterDialogVisble(false);
const formdata = reactive({
  whatsapp: "",
  facebook: "",
  tel: "",
});

const copyName = () => {
  copy(userInfo.value.uid || "");
  showToast("Copied!");
};

const onClickBtn = () => {
  router.go(-1);
};

const onConfirm = (data: any) => {
  console.log(data);
  currentDate.value = true;
  dateStr.value = data;
  //dateStr = data
  //refresh();
};
</script>

<template>
  <AppPageTitle left-arrow title="Dados" title-weight="700" />
  <div class="content">
    <div class="name-configurar">
      Configurar
      <div class="uid">ID:{{ userInfo.uid }}</div>
      <AppButton
        class="img-copy"
        fontSize="24"
        radius="22"
        width="20"
        height="38"
        center
        trans
        @click="copyName"
      >
        <AppImage src="/icons/agent_copy.webp" :width="24" />
      </AppButton>
    </div>

    <AppImage class="top-head" src="/img/user/avatar-default.png" />

    <div class="vip-num">vip{{ userVipInfo ? userVipInfo.level : 0 }}</div>
    <div class="play-info">
      <div class="play-info-box">
        <!-- <AppImage
          class="play-info-box-img"
          src="/img/user/Nome-de-Usuario.webp"
        /> -->
        <div class="play-info-box-img">
          <svg
            t="1745430388137"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="42155"
            width="19"
            height="19"
          >
            <path
              d="M277.333333 320a234.666667 234.666667 0 1 1 469.333334 0 234.666667 234.666667 0 0 1-469.333334 0zM512 170.666667a149.333333 149.333333 0 1 0 0 298.666666 149.333333 149.333333 0 0 0 0-298.666666zM397.909333 618.666667H626.090667c52.352 0 88.106667 0 118.485333 9.173333a213.333333 213.333333 0 0 1 142.250667 142.250667c9.216 30.378667 9.173333 66.133333 9.173333 118.485333V896a42.666667 42.666667 0 0 1-85.333333 0c0-62.634667-0.426667-84.48-5.546667-101.12a128 128 0 0 0-85.333333-85.333333c-16.64-5.12-38.485333-5.546667-101.12-5.546667h-213.333334c-62.634667 0-84.48 0.426667-101.12 5.546667a128 128 0 0 0-85.333333 85.333333c-5.12 16.64-5.546667 38.485333-5.546667 101.12a42.666667 42.666667 0 1 1-85.333333 0v-7.424c0-52.352 0-88.106667 9.173333-118.485333a213.333333 213.333333 0 0 1 142.250667-142.250667c30.378667-9.216 66.133333-9.216 118.485333-9.173333z"
              fill="var(--svg-icon-color)"
              p-id="42156"
            ></path>
          </svg>
        </div>
        <label>{{ userInfo.username }}</label>
      </div>
      <div class="play-info-box">
        <!-- <AppImage
          class="play-info-box-img1"
          src="/img/user/No-de-Telefone.webp"
        /> -->
        <div class="play-info-box-img1">
          <svg
            t="1745430002393"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="28165"
            width="19"
            height="19"
          >
            <path
              d="M217.6 80.128a128 128 0 0 1 115.541333 0c18.432 9.301333 34.474667 25.386667 52.992 43.946667l4.096 4.096 8.32 8.32 2.688 2.688c12.245333 12.245333 22.784 22.741333 30.165334 34.133333a128 128 0 0 1 0 139.52c-7.381333 11.349333-17.92 21.888-30.165334 34.133333l-2.688 2.645334a269.056 269.056 0 0 0-4.608 4.650666 3.84 3.84 0 0 0-0.042666 0.469334 91.562667 91.562667 0 0 0 2.133333 4.565333 580.437333 580.437333 0 0 0 113.152 159.36 580.352 580.352 0 0 0 163.882667 115.285333h0.256l0.256-0.085333 4.650666-4.565333 2.688-2.688c12.202667-12.245333 22.698667-22.784 34.090667-30.165334a128 128 0 0 1 139.52 0c11.392 7.381333 21.888 17.92 34.133333 30.165334l2.688 2.688 8.277334 8.32 4.096 4.053333c18.602667 18.56 34.645333 34.56 43.989333 53.034667a128 128 0 0 1 0 115.541333c-9.344 18.474667-25.386667 34.474667-43.946667 53.034667l-4.138666 4.053333-6.698667 6.741333-2.901333 2.901334c-19.84 19.882667-35.072 35.114667-55.637334 46.677333-23.466667 13.141333-57.173333 21.930667-84.096 21.845333-23.552-0.085333-40.704-4.949333-62.848-11.221333l-2.432-0.682667a854.954667 854.954667 0 0 1-371.925333-218.837333 854.997333 854.997333 0 0 1-219.52-374.357333c-6.272-22.144-11.178667-39.296-11.221333-62.848a186.24 186.24 0 0 1 21.845333-84.096c11.562667-20.565333 26.794667-35.797333 46.677333-55.637334l2.901334-2.901333 6.698666-6.741333 4.096-4.053334c18.517333-18.602667 34.56-34.688 53.034667-43.989333z m77.013333 76.117333a42.666667 42.666667 0 0 0-38.528 0c-4.522667 2.304-10.24 7.253333-35.242666 32.298667l-6.741334 6.698667c-23.893333 23.893333-30.336 30.805333-35.498666 39.978666-5.973333 10.666667-10.965333 29.866667-10.922667 42.069334 0 10.837333 1.621333 17.365333 8.661333 42.24a769.664 769.664 0 0 0 197.077334 334.933333 769.664 769.664 0 0 0 334.933333 197.034667c24.832 7.04 31.36 8.618667 42.197333 8.661333 12.202667 0.042667 31.402667-4.949333 42.026667-10.922667 9.173333-5.12 16.085333-11.605333 40.021333-35.498666l6.698667-6.741334c25.045333-25.045333 29.994667-30.72 32.256-35.285333a42.666667 42.666667 0 0 0 0-38.485333c-2.261333-4.565333-7.210667-10.24-32.256-35.285334l-8.32-8.32c-16.426667-16.469333-20.138667-19.84-22.954667-21.632a42.666667 42.666667 0 0 0-46.506666 0c-2.816 1.792-6.485333 5.162667-22.954667 21.632l-1.024 1.024c-3.84 3.84-8.661333 8.704-14.506667 12.842667a87.253333 87.253333 0 0 1-73.514666 12.544 106.24 106.24 0 0 1-17.92-7.296 665.6 665.6 0 0 1-182.784-129.706667 665.770667 665.770667 0 0 1-129.706667-182.826666l-0.512-1.024c-2.133333-4.352-4.821333-9.984-6.826667-16.853334a87.253333 87.253333 0 0 1 12.544-73.557333c4.181333-5.802667 9.045333-10.666667 12.885334-14.506667l1.024-0.981333c16.426667-16.469333 19.797333-20.138667 21.632-22.954667a42.666667 42.666667 0 0 0 0-46.506666c-1.834667-2.816-5.205333-6.528-21.632-22.997334l-8.32-8.277333c-25.045333-25.045333-30.72-29.994667-35.285334-32.298667z"
              p-id="28166"
              fill="var(--svg-icon-color)"
            ></path>
          </svg>
        </div>

        <label>{{ "+55-" + userInfo.phone }}</label>
      </div>
      <div class="play-info-box">
        <!-- <AppImage
          class="play-info-box-img2"
          src="/img/user/Endereco-de-e-mail.webp"
        /> -->
        <div class="play-info-box-img2">
          <svg
            t="1745430212995"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="28445"
            width="19"
            height="19"
          >
            <path
              d="M288.384 128H735.573333c34.346667 0 62.72 0 85.76 1.877333 24.021333 1.962667 46.08 6.186667 66.773334 16.725334a170.666667 170.666667 0 0 1 74.581333 74.581333c10.197333 19.968 14.464 41.216 16.512 64.213333a42.666667 42.666667 0 0 1 1.28 21.333334c0.853333 19.2 0.853333 41.429333 0.853333 66.986666v276.565334c0 34.346667 0 62.72-1.92 85.76-1.962667 24.021333-6.186667 46.08-16.725333 66.773333a170.666667 170.666667 0 0 1-74.581333 74.581333c-20.693333 10.538667-42.752 14.762667-66.730667 16.725334-23.082667 1.877333-51.456 1.877333-85.76 1.877333H288.341333c-34.346667 0-62.72 0-85.76-1.877333-24.021333-1.962667-46.08-6.186667-66.773333-16.725334a170.666667 170.666667 0 0 1-74.581333-74.581333c-10.538667-20.693333-14.762667-42.752-16.725334-66.730667C42.666667 712.96 42.666667 684.629333 42.666667 650.325333V373.674667c0-25.557333 0-47.786667 0.768-66.986667a42.666667 42.666667 0 0 1 1.322666-21.333333c2.048-23.04 6.314667-44.245333 16.512-64.213334a170.666667 170.666667 0 0 1 74.581334-74.581333c20.693333-10.538667 42.752-14.762667 66.730666-16.725333C225.706667 128 254.037333 128 288.341333 128zM128 380.586667v267.946666c0 16.341333 0 30.378667 0.170667 42.581334l193.578666-174.890667-193.706666-135.594667zM413.866667 476.544l1.450666 1.024 42.88 30.037333c31.146667 21.76 37.717333 25.557333 43.52 27.008a42.666667 42.666667 0 0 0 20.650667 0c5.802667-1.450667 12.373333-5.248 43.52-27.008l42.88-30.037333a41.130667 41.130667 0 0 1 1.493333-1.024l282.24-197.546667a65.109333 65.109333 0 0 0-5.76-19.029333 85.333333 85.333333 0 0 0-37.290666-37.290667c-6.656-3.413333-16.213333-6.186667-34.944-7.68C795.264 213.333333 770.432 213.333333 733.866667 213.333333H290.133333c-36.522667 0-61.397333 0-80.597333 1.621334-18.688 1.493333-28.245333 4.266667-34.944 7.68a85.333333 85.333333 0 0 0-37.290667 37.290666c-2.261333 4.437333-4.266667 10.154667-5.76 18.986667l282.282667 197.589333z m-20.352 89.941333l-241.493334 218.154667c6.485333 6.784 14.122667 12.458667 22.613334 16.768 6.698667 3.413333 16.213333 6.186667 34.986666 7.68C228.736 810.666667 253.568 810.666667 290.133333 810.666667h443.733334c36.565333 0 61.397333 0 80.597333-1.621334 18.730667-1.493333 28.288-4.266667 34.986667-7.68 8.448-4.309333 16.085333-9.984 22.613333-16.768l-241.493333-218.154666-15.786667 11.050666a1803.093333 1803.093333 0 0 0-4.949333 3.413334c-23.210667 16.341333-43.648 30.677333-66.858667 36.48a128 128 0 0 1-61.952 0c-23.210667-5.802667-43.605333-20.138667-66.858667-36.437334a1805.909333 1805.909333 0 0 0-4.906666-3.456l-15.786667-11.050666z m308.778666-50.218666l193.621334 174.890666C896 678.826667 896 664.874667 896 648.533333V380.586667l-193.706667 135.68z"
              p-id="28446"
              fill="var(--svg-icon-color)"
            ></path>
          </svg>
        </div>
        <label class="img-text">{{
          userInfo.email == ""
            ? "Introduza o endereço de e-mail completo"
            : userInfo.email
        }}</label>
        <a @click="$router.push('/personalCenter/changeEmail')"
          >Ir para Vinculação</a
        >
      </div>
      <div class="play-info-box">
        <AppInput
          icon-with="30"
          ref="whatsappRef"
          v-model="formdata.whatsapp"
          placeholder="Entre no WhatsApp"
          icon-left="agent_share_WhatsApp"
          width="700"
          height="70"
          msg="Introduza 6-40 números"
          type="number"
          err-height="34"
          clearable
          :style-obj="{
            margin: '0 auto',
            background: 'var(--theme-main-bg-color)',
            color: '#fff',
          }"
        />
      </div>
      <div class="play-info-box">
        <AppInput
          icon-with="30"
          ref="facebookRef"
          v-model="formdata.facebook"
          placeholder="Por favor insira suaconta do facebook"
          icon-left="agent_share_Facebook"
          width="700"
          height="70"
          msg="Por favor, insira 3-50 bits de inglês/números/caracteres"
          type="text"
          err-height="34"
          clearable
          :style-obj="{
            margin: '0 auto',
            background: 'var(--theme-main-bg-color)',
            color: '#fff',
          }"
        />
      </div>
      <div class="play-info-box">
        <AppInput
          icon-with="30"
          ref="telRef"
          v-model="formdata.tel"
          placeholder="Por favor, informe seu nome de usúario no Telegram"
          icon-left="agent_share_Telegram"
          width="700"
          height="70"
          msg="Introduza 5-40 letras/números/sublinhados"
          type="text"
          err-height="34"
          clearable
          :style-obj="{
            margin: '0 auto',
            background: 'var(--theme-main-bg-color)',
            color: '#fff',
          }"
        />
      </div>

      <p class="selecione">
        Selecione a data de nascimento (depois de definida, não pode ser
        modificada)
      </p>
      <div class="play-info-date" @click="showDatePicker = true">
        <div
          :style="{
            color: currentDate
              ? 'var(--theme-text-color)'
              : 'var(--theme-text-color-placeholder)',
          }"
        >
          {{ dateStr }}
        </div>
        <AppImage class="down" src="/img/user/icon-arrow-green" />
      </div>

      <AppDatePicker v-model="showDatePicker" @confirm="onConfirm" />
    </div>

    <div class="content-bottom">
      <AppButton
        class="btn"
        @click="onClickBtn"
        :loading="loading"
        width="320px"
        height="70"
        trans
        :radius="15"
        fontSize="12px"
        border="1px var(--theme-primary-font-color) solid"
        color="var(--theme-primary-font-color)"
      >
        Retornar
      </AppButton>

      <AppButton
        class="btn"
        @click="onClickBtn"
        :loading="loading"
        width="320px"
        height="70"
        :radius="15"
        fontSize="12px"
        background="var(--theme-primary-color)"
        color="var(--theme-font-on-background-color)"
      >
        Salvar
      </AppButton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../../theme/mixin.scss";

.content {
  height: calc(100vh - 100px);
  font-size: 30px;
  background-color: var(--theme-main-bg-color);

  .content-bottom {
    display: flex;
    position: absolute;
    bottom: 0;
    height: 120px;
    width: 100%;
    padding: 25px 10px;
    background-color: var(--theme-main-bg-color);
    //margin-right: 25px;
    .btn {
      margin: 0 10px;
    }
  }

  .top-head {
    width: 120px;
    margin: 16px 0 0 20px;
  }

  .vip-num {
    position: relative;
    width: 80px;
    height: 35px;
    padding: 5px auto;
    top: -42px;
    right: 0;
    left: 60px;
    background-color: #24b299;
    border-radius: 5px;
    text-align: center;
    font-style: italic;
    color: #e2b54a;
  }

  .name-configurar {
    position: absolute;
    color: var(--theme-text-color);
    left: 175px;
    top: 130px;
    bottom: 10px;
    font-size: 30px;
    display: flex;

    .uid {
      position: absolute;
      color: var(--theme-text-color-placeholder);
      top: 40px;
      font-size: 22px;
    }

    .img-copy {
      position: relative;
      top: 35px;
      left: 35px;

      //top: -20px;
    }
  }

  .play-info {
    position: relative;
    width: 100%;
    height: 200px;
    //background-color: #e2b54a;
    .play-info-date {
      width: calc(100% - 50px);
      height: 70px;
      margin-left: 25px;
      border-radius: 15px;
      margin-bottom: 30px;
      background: var(--theme-main-bg-color);
      border: 2px var(--theme-color-line) solid;
      .down {
        position: relative;
        top: -40px;
        width: 23px;
        left: calc(100% - 40px);
        padding-right: 10px;
        transform: rotate(90deg);
      }
      div {
        //position: relative;
        height: 100%;
        //text-align: center;
        padding: 20px;
        color: var(--theme-text-color-lighten);
        font-size: 20px;
      }
    }

    .play-info-box {
      width: calc(100% - 50px);
      height: 70px;
      //left: 25px;
      margin-left: 25px;
      position: relative;
      border-radius: 15px;
      margin-bottom: 45px;
      border: 1px var(--theme-color-line) solid;
      //padding: 30px 70px;
      color: var(--theme-text-color);
      display: flex;
      .img-text {
        font-size: 20px;
        color: var(--theme-text-color-placeholder);
      }

      a {
        position: absolute;
        font-size: 22px;
        right: 20px;
        top: 20px;
        color: var(--theme-primary-color);
      }

      .play-info-box-img {
        position: absolute;
        margin-top: 20px;
        margin-left: 17px;
        width: 29px;
        //padding: 12px 60px;
      }

      .play-info-box-img1 {
        position: absolute;
        margin-top: 12px;
        margin-left: 17px;
        width: 29px;
        //padding: 12px 60px;
      }

      .play-info-box-img2 {
        position: absolute;
        margin-top: 12px;
        margin-left: 17px;
        width: 29px;
      }

      label {
        padding: 25px 75px;
        position: relative;
        font-size: 18px;
        text-align: left;
      }
    }

    .selecione {
      margin-left: 25px;
      color: var(--theme-text-color);
      font-size: 22px;
      margin-bottom: 15px;
    }
  }
}
</style>
<route lang="yaml">
meta:
  auth: true
</route>
