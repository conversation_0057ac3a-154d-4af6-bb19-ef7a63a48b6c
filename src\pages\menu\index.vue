<script lang="ts" setup>
const router = useRouter()

const appStore = useAppStore()
const { custService } = storeToRefs(appStore)

const records = ref([
  { icon: '', text: 'Convidar', path: '/promotion-detail/invite', iconWidth: 0 },
  { icon: '', text: 'BÔNUS de depósito', path: '/finance/details', iconWidth: 0 },
  { icon: '', text: 'BÔNUS Semanal', path: '/weekly-loss-cashback/goldCollect', iconWidth: 0 },
  { icon: '', text: 'Promoções', path: '/advancement', iconWidth: 0 },
])
const gameTypes = ref([
  { icon: 'nav_100', text: 'Popular', path: '/popular', iconWidth: 0 },
  // { icon: 'nav_600', text: 'Slots', path: '/Slots', iconWidth: 0 },
  { icon: 'nav_600', text: 'Slots', path: '/game-list/3', iconWidth: 0 },
  { icon: 'nav_2', text: 'Pescaria', path: '/game-list/2', iconWidth: 0 },
  { icon: 'nav_1', text: 'Casino Ao Vivo', path: '/game-list/1', iconWidth: 0 },
  { icon: 'nav_7', text: 'Bingo', path: '/game-list/7', iconWidth: 0 },
  { icon: 'nav_8', text: 'Jogos de vídeo', path: '/game-list/8', iconWidth: 0 },
])
const shares = ref([
  { icon: 'Baixar', text: 'Baixar APP', path: '', iconWidth: 0 },
  { icon: 'Suporte', text: 'Suporte', path: '', iconWidth: 0 },
])
// 图片大小处理
const iconLoad = (e: any, item: any, type: number) => {
  if(type === 0) {
    for (let i = 0; i < records.value.length; i++) {
      if (item.icon === records.value[i].icon) {
        records.value[i].iconWidth = Math.ceil(e.target.naturalWidth)
        break
      }
    }
  }
  if(type === 1) {
    for (let i = 0; i < gameTypes.value.length; i++) {
      if (item.icon === gameTypes.value[i].icon) {
        gameTypes.value[i].iconWidth = Math.ceil(e.target.naturalWidth)
        break
      }
    }
  }
  if(type === 2) {
    for (let i = 0; i < shares.value.length; i++) {
      if (item.icon === shares.value[i].icon) {
        shares.value[i].iconWidth = Math.ceil(e.target.naturalWidth)
        break
      }
    }
  }
}
// 菜单点击事件
const recordClick = (index: any) => {
  router.push(records.value[index].path)
}
const gameTypeClick = (item: any) => {
  if (item.fn) {
    item.fn()
    return
  }
  if (item.path) {
    router.push(item.path)
  }
}
const { isIos } = getBrowser();
const showAddToDesktop = ref(false)
const { data: downloadUrl } = useRequest(() => getAppDownloadUrl(isIos ? 36 : 35))
const setShowAddToDesktop = (value: boolean) => {
  showAddToDesktop.value = value
}
const openUrl = (url?: string) => {
  if (!url) return
  window.open(url, '_blank')
}
const shareClick = (item: any) => {
  if (item.icon === 'Baixar') {
    // showAddToDesktop.value = true;
    if (isIos) {
        showAddToDesktop.value = true;
    } else {
      // openUrl(downloadUrl.value?.url)
      if(typeof window !== "undefined" && "installButtonclick" in window){
        (window as any).installButtonclick()
      }
    }
  }
  if (item.icon === 'Suporte') {
    // window.open(custService?.onlinecs, '_blank')
    router.push('/supper/supperIndex')
  }
}

const {run: runGetPlatformLinkData,data:platformLinkData } = useRequest(() => ApiGetPlatformLinkData(), {
  manual: true,
  onSuccess(res:any) {
    // console.log(res)
  }
})
runGetPlatformLinkData();

const jumpUrl = (url: string) => {
  window.open(url, '_blank')
}
</script>

<template>
  <div class="menu">
    <AppIndexHeader />
    <section class="content">
        <div class="content_div">
          <div class="records-list" @click="() => recordClick(0)">
          <div>{{('Convidar').toUpperCase()}}</div>
        </div>
        <div class="records-list" @click="() => recordClick(1)">
          <div>{{('Bônus de depósito').toUpperCase()}}</div>
        </div>
        <div class="records-list" @click="() => recordClick(2)">
          <div>{{('Bônus Semanal').toUpperCase()}}</div>
        </div>
        <div class="records-list" @click="() => recordClick(3)">
          <!-- <div>{{('Promoções').toUpperCase()}}</div> -->
        </div>
      </div>
      <!-- <ul class="reports">
        <li v-for="(item, idx) in records" :key="item.icon" class="item" @click="() => recordClick(item)">
          <div class="icon">
            <AppImage :src="'/icons/' + item.icon + '.png'"
              :style="{ width: item.iconWidth ? 'var(--app-px-' + item.iconWidth + ')' : '0' }"
              @load="$event => iconLoad($event, item, 0)" />
          </div>
          <span>{{ item.text }}</span>
        </li>
      </ul> -->
     
    </section>
    <section class="content">
      <ul class="reports">
        <li v-for="(item, idx) in gameTypes" :key="item.icon" class="item" @click="() => gameTypeClick(item)">
          <div class="icon">
            <AppImage :src="'/icons/' + item.icon + '.png'"
              :style="{ width: item.iconWidth ? 'var(--app-px-' + item.iconWidth + ')' : '0' }"
              @load="$event => iconLoad($event, item, 1)" />
          </div>
          <span>{{ item.text }}</span>
          <AppImage class="arrow" src='/icons/right.png' />
        </li>
      </ul>
    </section>
    <section class="content">
      <ul class="reports">
        <li v-for="(item, idx) in shares" :key="item.icon" class="item" @click="() => shareClick(item)">
          <div class="icon">
            <AppImage :src="'/icons/' + item.icon + '.png'"
              :style="{ width: item.iconWidth ? 'var(--app-px-' + item.iconWidth + ')' : '0' }"
              @load="$event => iconLoad($event, item, 2)" />
          </div>
          <span>{{ item.text }}</span>
        </li>
      </ul>
    </section>
    <section class="content">
      <div class="shareUrl">
        <p>Junte-se a nossa comunidade</p>
        <div class="link">
          <AppImage v-show="platformLinkData?.telegram.length > 0" src="/icons/telegram.webp" @click="jumpUrl(platformLinkData.telegram)"/>
          <AppImage v-show="platformLinkData?.facebook.length > 0" src="/icons/facebook.webp" @click="jumpUrl(platformLinkData.facebook)"/>
          <AppImage v-show="platformLinkData?.twitter.length > 0" src="/icons/twitter.webp" @click="jumpUrl(platformLinkData.twitter)" />
          <AppImage v-show="platformLinkData?.instagram.length > 0" src="/icons/instagram.webp" @click="jumpUrl(platformLinkData.instagram)" />
        </div>
      </div>
    </section>
    
    <van-popup v-model:show="showAddToDesktop">
    <div class="add-desktop">
      <AppImage class="img" src="/img/add-to-desktop.png" />
      <div class="close" @click="setShowAddToDesktop(false)">
        <AppImage src="/icons/dialog-close.png" />
      </div>
    </div>
  </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';
.menu {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 132px;
  background: var(--theme-main-bg-color);
}
.records-list{
  width: 678px;
  margin: 0 auto;
  margin-top: 24px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  position: relative;
  font-family:Arial;
  font-size:30px;
  font-weight:700;
  &:nth-child(1){
    height: 120px;
    @include webp('/img/Convidar.png');
    div{
      width:160px;
      height:33px;
      position: absolute;
      margin: 52px 0 0 136px;
    }
  }
  &:nth-child(2){
    width: 330px;
    height: 140px;
    display: inline-block;
    // margin-left: 36px;
    margin-right: 18px;
    @include webp('/img/Bônus_de_depósito.png');
    div{
      width:167px;
      height:67px;
      position: absolute;
      margin: 40px 0 0 136px;
    }
  }
  &:nth-child(3){
    width: 330px;
    height: 140px;
    display: inline-block;
    @include webp('/img/Bônus_semanal.png');
    div{
      width:148px;
      height:67px;
      position: absolute;
      margin: 40px 0 0 129px;
    }
  }
  &:nth-child(4){
    height: 140px;
    @include webp('/img/Promoções.png');
  }
}
.reports {
  margin: 0 auto;
  padding: 20px 0;
  list-style: none;
  border-radius: 15px;
  width: 678px;
  color: #000;
  font-size: 28px;
  margin-top: 24px;
  background-color: var(--app-box-bg-color);
  .item {
    position: relative;
    // border-bottom: 1px solid #152237;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 92px;
    // background: #fff;
    &:nth-child(1) {
      border-top-left-radius: 15px;
      border-top-right-radius: 15px;
    }
    &:last-child {
      border-bottom-left-radius: 15px;
      border-bottom-right-radius: 15px;
    }
    .icon {
      width: 82px;
      margin-left: 15px;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:nth-child(2){
      .icon {
        margin-left: 19px;
      }
    }

    &:last-child {
      border: 0;
    }

    .arrow {
      width: 60px;
      position: absolute;
      top: 50%;
      right: 15px;
      transform: translateY(-50%);
    }
  }
  span{
    color: var(--app-title-color);
  }
}
.content{
  &:last-child {
    .reports{
      .item {
        height: 110px;
      }
    }
  }
}

.content_div{
  width: 678px;
  margin: 0 auto;
}


.add-desktop {
  .img {
    width: 100%;
  }
  .close {
    margin: 0 auto;
    padding: 30px 0;
    text-align: center;
    img {
      width: 40px;
    }
  }
  
}
.shareUrl {
    // display: block;
    width: 678px;
    margin: 0 auto;
    margin-top: 24px;
    height: 179px;
    position: relative;
    background-color: var(--app-box-bg-color);
    border-radius: 15px;
    p {
      padding-top: 25px;
      padding-left: 26px;
      width: 100%;
      height: 33px;
      // font-weight: normal;
      font-family: Arial;
      font-size: 30px;
      color: var(--app-title-color);
    }
    .link {
      width: 678px;
      height: 70px;
      margin-top: 51px;
      padding-left: 20px;
      img {
        width: 70px;
        // margin-top: 28px;
        margin-right: 42px;
      }
    }
  }
</style>
