<script setup lang="ts" name="app-find-pwd">
 const router = useRouter()
const appStore = useAppStore();
const {findPayPasswordDialogVisible,userInfo} = storeToRefs(appStore);


const phoneRef = ref()
const emailRef = ref()

const codeRef = ref()
const isPhone = ref(true)
// const { userNameReg } = useRegExpUserName();
const { phoneReg } = useRegExpPhone();
const fromData = reactive({
  email: '',
  phone: '',
  password: '',
  password2: '',
  code: '',
  sid: '',
  ts: ''
})

const fromData2 = reactive({
    update_type:2,   // 1 新增密码 2 修改旧密码 3 忘记密码，通过验证码重置
    ty: 1,           // 1 手机号 2 邮箱的验证码
    sid: '',
    ts: '',
    code: '',
    confirm_password: '',
    password: '',
    old: '',
    phone:'',
})

watchEffect(()=>{
    if(userInfo.value.phone){
      fromData.phone = userInfo.value.phone
    }
})

// const { run: runResetPwd, loading: resetLoading } = useRequest(() => ApiResetPwd(fromData), {
//   manual: true,
//   onSuccess: () => {
//     appStore.setFindPasswordDialogVisible(false)
//     // localStorage.clear()
//     location.replace(location.origin)
//   }
// })


//设置支付密码
const { run: runResetPwd, loading: resetLoading } = useRequest(() => ApiUpdatePayPwd({update_type:3,ty:1,sid:fromData.sid,ts:fromData.ts,code:fromData.code,phone:fromData.phone,
                                                                           confirm_password:"",password:fromData.password ,old:"" }), {
  manual: true,
  onSuccess(data: string) {
    appStore.setPay_password(fromData.password)
    appStore.setFindPayPasswordDialogVisible(false)
    showToast("Estabelecer com sucesso");
  },
  onError(resData){
    showErrorTip(resData)
  }
})

//修改提现密码
const submit = () => {
  // emailRef.value.validation();
//   if( fromData.password!=fromData.password2){
//     showToast('As senhas inseridas não coincidem');
//     return 
//   }
  if (fromData.password)
  runResetPwd()
}

//获取手机验证码
const { run: runSendCode, loading: codeLoading } = useRequest(() => ApiSendOfflineSms({ flag: 'text', tel: fromData.phone, ty: 2 }), {
  manual: true,
  onSuccess(data: string) {
    const [sid, ts] = data.split(':')
    fromData.sid = sid
    fromData.ts = ts
    startCountdown()
    showToast('Código de verificação enviado')
  },
  onError(data){
    showErrorTip(data)
  }
})

// 邮箱注册验证码获取
const { run: runEmailCode, loading: emailCodeLoading } = useRequest(() => ApiEmailCode({ ty: '2', mail: fromData.email }), {
  manual: true,
  onSuccess: (data) => {
    const [sid, ts] = data.split(':')
    fromData.sid = sid
    fromData.ts = ts
    startCountdown()
    showToast('Código de verificação enviado')
  }
})


//倒计时
const countdowm = ref(0)
const startCountdown = () => {
  countdowm.value = 120
  const timer = setInterval(() => {
    countdowm.value--
    sessionStorage.setItem('countdowm', countdowm.value.toString())
    if (countdowm.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const handleSendCode = () => {
  if( fromData.password!=fromData.password2){
    showToast('As senhas inseridas não coincidem');
    return 
  }

  if( fromData.password==""){
    showToast('A senha não pode estar vazia');
    return 
  }

  if (isPhone.value) {
    phoneRef.value.validation();
  } else {
    emailRef.value.validation();
  }
  const isPass = isPhone.value ? phoneRef.value.isValid : emailRef.value.isValid
  if (isPass) {
    if (isPhone.value) {
      runSendCode()
    } else {
      runEmailCode()
    }
  }
}

const codeDisabled = computed(() => {
  if (codeLoading.value || emailCodeLoading.value) {
    return true
  }
  if (isPhone.value && phoneReg.value.test(fromData.phone) && countdowm.value <= 0) {
    return false
  }
  if (!isPhone.value && emailReg.test(fromData.email) && countdowm.value <= 0) {
    return false
  }
  return true
})




//关闭界面
const closePage=()=>{
    fromData.password =""
  appStore.setFindPayPasswordDialogVisible(false)
}

const getEditPhoneIsRead = computed(() => {
  return !!userInfo.value.phone && userInfo.value.phone.length > 0;
})

const onClickProximo =() =>{
    if (fromData.password && fromData.password !="")
    {
        runGetPasswordVerification()
    }
}

const { run: runGetPasswordVerification } = useRequest(() => ApiGetVerificationPasswordParam({pay_password:fromData.password}), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
      console.log(data)
      closePage()
      appStore.setAdicionarPixDialogVisible(true)
    }
})



</script>

<template>
   <van-popup class="tip-poup" v-model:show="findPayPasswordDialogVisible" round zIndex="9999">
            <div class="content">
                <div class = "title">Inserir Senha</div>
                <AppPasswordInput2 class="passwordInput"
                    v-model="fromData.password" 
                    info="Senha de Saque" 
                    color="var(--theme-text-color-lighten)"
                    style="margin-top:10px;">
                </AppPasswordInput2>
                <div class="levantamento">
                    <div>Para a segurança da sua conta,introduza a<br>palavra-passe de levantamento</div>
                    <div class = "Senha" @click="() =>{
                        closePage()
                        router.push('/personalCenter/changePayPassword')
                    }">Esqueceu s<br>Senha?</div>
                </div>
                <AppButton class = "proximo" @click="onClickProximo" :loading="loading" width="92%" height="70" yellow :radius="15" fontSize="15px"
                    color="var(--theme-bg-color)">
                    Próximo
                </AppButton>
                <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp" alt="" @click="() => {
                    closePage()
                }" />
            </div>
            

        </van-popup>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';


.tip-poup {
    width: 750px;
    height: 100%;
    display: flex;
    align-items: center;
    //position: absolute;
    justify-content: center;
    //overflow: auto;
    //background-color: #0ED1F4;
    .content {
        width: 710px;
        height: 450px;
        border-radius: 15px;
        background-color: var(--theme-main-bg-color);
        border: 2px var(--theme-text-color-lighten) solid;
        text-align: center;
        .title{
            position: relative;
            top: 20px;
            color: var(--theme-text-color-lighten);
            font-size: 28px;
        }

        .levantamento{
            position: relative;
            width: 660px;
            margin: 0px 0 0 30px;
            text-align: left;
            color: var(--theme-text-color-lighten);
            font-size: 20px;
            line-height: 1.5;
            display: flex;

            .Senha{
                color: var(--theme-text-color-lighten);
                position: relative;
                width: 270px;
                text-align: right;
                font-size: 22px;
            }
        }

        .proximo{
            margin: 30px 25px 30px 25px;
            background-color: var(--theme-primary-color);
        }
    
        .passwordInput{
            position: relative;
            height: 210px;
            margin-top: 40px;
            //scale: 0.9;
        }
        
    }

    .tip {
        padding-top: 30px;
        font-size: 24px;
        color: var(--theme-text-color-lighten);
        //          display: block;
        text-align: center;

    }

    // .content {
    //     //width: 100%;
    //     height: 180px;
    //     position: relative;
    //     margin: 20px 40px;
    //     text-align: center;
    //     font-size: 21px;
    //     line-height: 30px;
    //     color: white;
    // }

    .close-btn {
        position: absolute;
        width: 56px;
        height: 56px;
        left: 45%;
        margin-top: 20px;
        // display: flex;
        // justify-content: center;
        // align-items: center;
        
        
        //top: 1100px;
    }
}

.content {
  // width:721px;
  height: 1000px;
  border-radius:20px;
  background-color: var(--app-box-bg-color);

  .titlebg {
    // width:721px;
    height:97px;
    line-height: 97px;
    font-weight:bolder;
    background:var(--app-psw-top-color);
    border-radius:10px 10px 0px 0px;
    color: #FFFFFF;
    text-align: center;
    font-size: 36px;
    // padding-top:40px;
    // padding-left: -50px;
  }

  // .phone, .email {
  //   padding-top: 0;
  // }
}

.close{
  width: 40px;
  position: absolute;
  top: 25px;
  right: 20px;
}


.email,
.phone {
  margin-left: 10px;
  padding-top: 1px;
  padding-bottom: 25px;
  margin:  0 auto;
}

.password {
  padding-top: 10px;
}

.code {
  padding-top: 35px;
}

.submit {
  padding-top:80px;
  margin-left: -30px;
 
  button{
    margin: 0 auto;
  }
}

.app-login {
  margin-top: 60px;
  margin-left: 36px;
  width: 650px;
}


.login-img {
  text-align: center;
  padding-top: 40px;
  padding-bottom: 35px;
  // padding: 0 0 40px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .line {
    width: 1px;
    height: 72px;
    background: rgba(255, 255, 255, 0.25);
    margin: 0 36px;
  }

  img {
    width: 72px;
    height: 72px;
    vertical-align: middle;
  }

  .reg-tab {
    width: 229px;
    height: 70px;
    border-radius: 10px;
    background: #192841;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all .15s;
    &:last-child{
      margin-left: 30px;
    }
    &.active {
      background: #679fea
    }

    .email {
      width: 45px;
      height: 45px;
      // background: url() no-repeat center center;
      @include webp('/icons/icon_email_1.png');
      background-position: center center;
      background-size: 100% 100%;

      &.active {
        // background: url() no-repeat center center;
        @include webp('/icons/icon_email_1-active.png');
        background-position: center center;
        background-size: 100% 100%;
      }
    }

    .phone {
      width: 48px;
      height: 48px;
      // background: url() no-repeat center center;
      @include webp('/icons/icon_phone_1.png');
      background-position: center center;
      background-size: 100% 100%;

      &.active {
        // background: url() no-repeat center center;
        @include webp('/icons/icon_phone_1-active.png');
        background-size: 100% 100%;
        background-position: center center;
      }
    }
  }
}


.retrieve {
  width: 260px;
  height: 46px;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid #0ED1F4;
  color: #0ED1F4;
  text-align: center;
  font-size: 26px;
  line-height: 46px;
}


</style>
