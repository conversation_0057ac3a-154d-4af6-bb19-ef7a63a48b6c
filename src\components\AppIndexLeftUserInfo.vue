<script setup lang="ts" name="app-index-left-user-info-header">
const appStore = useAppStore()
const { userInfo, loadingBalance, loadingUserinfo } = storeToRefs(appStore)
// const uid = computed(() => {
//   if (userInfo.value?.uid) {
//     return userInfo.value?.uid.toString().length > 6 ? userInfo.value?.uid.toString().slice(0, 6) + '...' : userInfo.value?.uid
//   }
// })
// const copyName = () => {
//   copy(userInfo.value?.uid)
//   showToast('Copied!')
// }

const openAvatar = () => {
  appStore.setEditAvatarDialogVisble(true)
}


const loading = ref(false)

const refreshBalance = () => {
  loading.value = true
  appStore.runGetMemberInfo()
  setTimeout(() => {
    loading.value = false
  }, 1500);
}
</script>

<template>
  <!-- <div class="app-index-left-user-info-header">
    <div class="avatar">
      <AppImage class="img" :src="'/img/avatar/avatar' + userInfo.avatar + '.png'" alt="" @click="openAvatar" />
    </div>
    <div class="user-name">
      <div class="right-c" @click="copyName">
        {{ uid }}
        <AppImage class="copy" src="/icons/i-copy.png" alt="" />
      </div>
    </div>
    <div class="vip-container">
      <AppImgVip :vip-level="0" width="132" />
    </div>
  </div> -->
  <div class="nav-user-info">
    <div class="avatar">
      <AppImage class="img" :src="'/img/avatar/avatar' + userInfo.avatar + '.png'" alt="" @click="openAvatar" />
    </div>
    <div class="right">
      <div class="money">
        <span class="num">{{ userInfo.formatAmount }}</span>
        <AppImage class="refresh-img" :class="{ 'rotate': loading || loadingBalance || loadingUserinfo }"
          src="/icons/icon_refresh.png" @click="refreshBalance" />
      </div>

      <div class="vip-container">
        <AppImage src="/icons/icon_vip-gray.png" />
        <div class="vip-container-num">{{ userInfo.vip }}</div>
        <!-- <AppImgVip :vip-level="userInfo.vip" width="60" /> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';
.nav-user-info {
  position: relative;
  width: 476px;
  height: 62px;
  border-radius: 10px;
  // border: 1px solid #fff;
  // background: linear-gradient(90deg, #0034A6 0%, #0034A6 100%);
  margin-left: 21px;
  // display: flex;
  // align-items: center;
  // gap: 20px;
  // justify-content: center;
  :deep(.app-img-vip) {
    height: auto;
    img {
      display: block;
    }
  }
  .avatar {
    width: 62px;
    height: 62px;
    border-radius: 100px;
    border: 4px solid #0ED1F4;
    position: absolute;
    // top: -13px;
    // left: 26px;

    img {
      width: 100%;
      height: 100%;
    }
  }
  .right {
    // display: flex;
    position: absolute;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    height: 100%;
    padding-right: 22px;
    margin-left: 81px;
    margin-top: 7px; 
  }
  .money {
    width: 290px;
    height: 55px;
    line-height: 55px;
    @include webp('/icons/fream_purses.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    color: #FFF;
    font-size: 28px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .unit{
      text-indent: 80px;
    }
    .num {
      padding-left: 80px;
      width: 200px;
      overflow: hidden;
      white-space: nowrap;
    }

    img {
      width: 34px;
      height: 32px;
      position: absolute;
      right: 36px;
      // margin-right: 10px;
    }
  }
  .vip-container {
    position: absolute;
    right: -35px;
    top: 2px;
    img{
      width: 51px;
      height: 41px;
    }
    &-num{
      font-size: 20px;
      color: #7d8aa2;
      margin-top: -20px;
      margin-left: 40px;
    }
  }
}
.app-index-left-user-info-header {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;

  .avatar {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    border: 4px solid #0ED1F4;
    overflow: hidden;

    .img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }
  }

  .user-name {
    display: flex;
    color: #fff;
    align-items: center;
    justify-content: center;
    padding: 24px 0;

    .left-c {
      color: #FFF;
      font-size: 28px;
      font-weight: 700;
      margin-right: 13px;
    }

    .right-c {
      height: 46px;
      border-radius: 50px;
      background: rgba(0, 0, 0, 0.25);
      color: #FFF;
      font-size: 26px;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 17px;

      .copy {
        width: 25px;
        vertical-align: middle;
        margin-left: 11px;
      }
    }


  }

}


.reload {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  // margin-right: 20px;
  &.rotate {
    animation: spin 1s linear infinite;
  }
}

@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
