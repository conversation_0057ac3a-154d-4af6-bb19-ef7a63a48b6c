@import './red.scss';
@import './green.scss';
@import './blue.scss';
@import './yellow.scss';
@import './font.scss';

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  outline: none;
}

html,
body {
  font-family: 'Arial';
}

.RobotRegular {
  font-family: 'RobotRegular';
}

.RobotBlack {
  font-family: 'RobotBlack';
}

.RobotBlackItalic {
  font-family: 'RobotBlackItalic';
}

.RobotBold {
  font-family: 'RobotBold';
}

.RobotBoldCondensed {
  font-family: 'RobotBoldCondensed';
}

.RobotBoldCondensedItalic {
  font-family: 'RobotBoldCondensedItalic';
}

.RobotBoldItalic {
  font-family: 'RobotBoldItalic';
}

.RobotCondensed {
  font-family: 'RobotCondensed';
}

.RobotCondensedItalic {
  font-family: 'RobotCondensedItalic';
}

.RobotItalic {
  font-family: 'RobotItalic';
}

.RobotLight {
  font-family: 'RobotLight';
}

.RobotLightItalic {
  font-family: 'RobotLightItalic';
}

.RobotMedium {
  font-family: 'RobotMedium';
}

.RobotMediumItalic {
  font-family: 'RobotMediumItalic';
}

.RobotThin {
  font-family: 'RobotThin';
}

.RobotThinItalic {
  font-family: 'RobotThinItalic';
}


.money-text {
  font-weight: 700;
  color: #0ED1F4;
}

// vant
.van-popup.van-popup-transparent {
  background-color: transparent;
}

.van-popup.van-popup-dark {
  background-color: #000A1D;
}

.van-tab__text--ellipsis {
  overflow: unset !important;
}

.van-picker__cancel,
.van-picker__confirm {
  padding: 0 16px !important;
}

// toast样式
.cg-toast {

  .van-toast__text {
    text-transform: capitalize;
  }
}



:root {
  --van-toast-text-padding: 16px 24px;
  --van-toast-text-color: #333;
  --van-toast-background: #fff;
  --van-toast-font-size: 28px;
  --van-toast-default-padding: 40px;
  --van-toast-text-padding: 24px 40px;
  --van-toast-icon-size:42px;
  --van-toast-line-height: 50px;

  .van-toast {
    word-break: break-word;
    
  }
  .van-toast__text {
    text-transform: none !important;
  }
  @for $i from 1 through 850 {
    --app-px-#{$i}: #{$i}px;
  }

  @for $i from 1 through 850 {
    --app-npx-#{$i}: -#{$i}px;
  }
}
.van-popover--light .van-popover__content{
  background: #c15473 !important;
  box-shadow: none !important;
}
.van-popover--light .van-popover__arrow{
  color: #c15473 !important;
}
.small-white-txt {
  width: 524px;
  padding: 12px 20px;
  line-height: 28px;
  font-size: 24px;
  background: #334b6f;
  color: #4e7aaf;
}

.finance-page {
  .van-popover__wrapper {
    flex: 1;

    li {
      width: 100% !important;
    }
  }
}
