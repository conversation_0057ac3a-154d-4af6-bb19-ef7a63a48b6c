<script lang="ts" setup name="AppPayPopup">
import checkBorderIcon from "/icons/svg/checkBorder.svg?raw";
import checkBorderIcon2 from "/icons/svg/checkBorder2.svg?raw";
const router = useRouter();
const appStore = useAppStore();
const { isLogin, isShowPay, showIndexModule } = storeToRefs(appStore);
import bindCPFInfoPopup from "./bindCPFInfoPopup.vue";
const list = ref([1, 2, 3, 4, 5, 6, 7, 8]);
const selectIndex = ref();
const moneyStr = ref();
const numberReg = ref(/^[0-9]*$/);
const isShowIframe = ref(false);
const close = () => {
  router.push("/");
  appStore.setShowIndexModule(false);
  appStore.setPayVisble(false);
  isShowIosTip.value = false;
};
const cpf = "";
const isShow = ref(false);
const clickImg = (item: any) => {
  if (item.jump_type == 1) {
    //跳转内部
    router.push(item.redirect_url);
  } else {
    window.open(item.redirect_url, "_blank"); // //跳转外部
  }
  appStore.setPayVisble(false);
};

const onClickOverlay = () => {};

const onClickRegistro = () => {
  console.log("onClickRegistro");
  appStore.setPayRecordsVisble(true);
};

const onClickItem = (index: number) => {
  selectIndex.value = index;
  moneyStr.value = items.value[index].amount;
};
//充值物品
const items = ref<any>([]);
//渠道信息
const depositChannel = ref({
  fid: "",
  fmax: 0,
  fmin: 0,
  showName: "",
});
const depositGift = ref({
  isOpen: false,
  max: 0,
  min: 0,
});
//获取充值选项
const { run: runApiGetDepositChannel, data: channelsConf } = useRequest(
  ApiGetDepositChannel,
  {
    ready: isLogin,
    onError: () => {},
    onSuccess: (data) => {
      if (data && data.d && data.d[0]) {
        items.value = [...data.d[0].amount_array];
        depositChannel.value.fid = data.d[0].fid;
        depositChannel.value.fmin = Number(data.d[0].fmin);
        depositChannel.value.fmax = Number(data.d[0].fmax);
        depositChannel.value.showName = data.d[0].show_name || "";
      }
      if (data.min) {
        depositGift.value.min = Number(
          (Math.floor(data.min * 1000) / 10).toFixed(1)
        );
        depositGift.value.isOpen = true;
      }
      if (data.max) {
        depositGift.value.max = Number(
          (Math.floor(data.max * 1000) / 10).toFixed(1)
        );
        depositGift.value.isOpen = true;
      }
    },
  }
);

const giftMinAndMaxTip = computed(() => {
  if (depositGift.value.max) {
    return (
      (depositGift.value.min ? depositGift.value.min + "-" : "") +
      (depositGift.value.max + "%")
    );
  } else if (depositGift.value.min) {
    return depositGift.value.min + "%";
  } else {
    return "";
  }
});

onBeforeUpdate(() => {
  selectIndex.value = null;
  moneyStr.value = "";
  depositChannel.value = {
    fid: "",
    fmax: 0,
    fmin: 0,
    showName: "",
  };
  depositGift.value = {
    isOpen: false,
    max: 0,
    min: 0,
  };
  runApiGetDepositChannel();
  console.log("runUserDepositList");
});
const inputTip = computed(() => {
  return (
    "Mínimo " +
    (depositChannel.value.fmin || 10) +
    ", Máximo " +
    (depositChannel.value.fmax || 50000)
  );
});

function openUrl() {
  //   window.open(chargeUrl.value)
  isShowIframe.value = true;
  isShowIosTip.value = false;
}

function closeIframe() {
  isShowIframe.value = false;
  appStore.runGetMemberInfo();
}

const rechargeAmount = ref(0);
const showMessage = ref("");
const chargeUrl = ref("");
const bonusText = ref(0); //送多少金币
const isShowIosTip = ref(false);

//充值发送  () => ApiGetMemberRecord(formQuery)
const { run: postDeposit, loading } = useRequest(
  () =>
    ApiDoDeposit({
      fid: depositChannel.value.fid,
      amount: moneyStr.value + "",
      flag: "1",
    }),
  {
    manual: true,
    onError: () => {
      appStore.setIsShowLoading(false);
    },
    onSuccess: (data: any) => {
      appStore.setIsShowLoading(false);
      appStore.setBindCPFInfoVisible(false);
      if (data && data.addr) {
        chargeUrl.value = data.addr;
        openUrl();
        return;
        //ios 打开浏览器提示
        const userAgent = navigator.userAgent || navigator.vendor;
        if (
          userAgent.indexOf("iPad") > -1 ||
          userAgent.indexOf("iPhone") > -1 ||
          userAgent.indexOf("iPod") > -1
        ) {
          showMessage.value = "R$ " + moneyStr.value;
          if (Number(bonusText.value) > 0) {
            showMessage.value =
              "R$ " + moneyStr.value + " Bônus " + bonusText.value;
          }
          isShowIosTip.value = true;
        } else {
          openUrl();
        }
      }
    },
    onAfter: () => {
      appStore.setIsShowLoading(false);
      appStore.runGetMemberInfo();
      // showMessage.value = "R$ "+rechargeAmount.value
      // if(Number(bonusText.value)>0){
      //   showMessage.value = "R$ "+rechargeAmount.value + " Bônus"+bonusText.value
      // }
      // isShowIosTip.value=true
    },
  }
);

watch(moneyStr, (val) => {
  // console.log(val)
  if (
    val &&
    val > 0 &&
    val >= depositChannel.value.fmin &&
    val <= depositChannel.value.fmax
  ) {
    for (let i = 0; i < items.value.length; i++) {
      if (
        Number(val) >= Number(items.value[i].amount) &&
        (items.value[i + 1]
          ? Number(val) < Number(items.value[i + 1].amount)
          : true)
      ) {
        bonusText.value = items.value[i].discount;
        break;
      }
    }
  } else {
    bonusText.value = 0;
    selectIndex.value = null;
  }
});

const isShowCPFInfo = ref(0);
const { run: runGetPlatformLinkData } = useRequest(
  () => ApiGetPlatformLinkData(),
  {
    manual: true,
    onSuccess(res: any) {
      if (res && res.cfg_deposit_cpf_switch !== undefined) {
        isShowCPFInfo.value = res.cfg_deposit_cpf_switch;
      }
    },
  }
);
runGetPlatformLinkData();

const { run: runUpdateVerifyInfo } = useRequest(
  () => ApiUpdateVerifyInfo({ real_name: "" }),
  {
    manual: true,
    onError: (data) => {
      console.log(data);
    },
    onSuccess: (data) => {
      console.log(data);
      if (data == "1000") {
        isSendMessage(true);
      }
    },
  }
);

//点击充值
function goToRecharge() {
  // console.log(`充值金额：${rechargeAmount.value}`)
  if (moneyStr.value > 0) {
    // && curOptionIndex.value>=0
    runUpdateVerifyInfo();
    // if(isShowCPFInfo.value) {
    //     appStore.setBindCPFInfoVisible(true);
    // } else {
    //     postDeposit();
    // }
  }
}

function closePage() {
  isShowIosTip.value = false;
  appStore.runGetMemberInfo();
}

const setIframeCallback = () => {
  const iframe: any = document.getElementById("iframeShop");
  console.log(iframe);
  if (!iframe) return;
  let iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
  let div = iframeDoc.getElementById("myDiv");
  console.log("------------" + iframeDoc);
  iframe.onload = function () {
    console.log("iframeShop");

    // div.style.color = 'red';
    // div.style.border = '1px solid blue';
  };

  iframe.onerror = function () {};
  window.addEventListener("message", (data: any) => {
    switch (data.data.action) {
      case "goBack":
        // router.push('/home');

        break;
      case "login":
        // appStore.removeToken();
        // router.push({ path: '/entry/login' });
        break;
      default:
        break;
    }
  });
};
onMounted(() => {});
watch(isShowIframe, () => {
  console.log("isShowIframe");
  setTimeout(() => {
    setIframeCallback();
  }, 1000);
});

const isSendMessage = (event) => {
  console.log("isSendMessage   " + event);
  if (event) {
    postDeposit();
  }
};

const showModuleVisible = computed({
  get: () => showIndexModule.value === 3,
  set: (val: boolean) => {
    appStore.setShowIndexModule(val ? 3 : false);
  },
});
</script>

<template>
  <van-popup
    class="app-pay-popup"
    v-model:show="showModuleVisible"
    position="bottom"
    teleport="body"
    z-index="998"
  >
    <div class="content">
      <div
        class="content-header"
        style="background-color: var(--theme-bg-color)"
      >
        <!-- <AppImage
          src="/img/finance/finance-back.png"
          class="content-header-back"
          @click="close"
        /> -->

        <div
          class="content-header-back"
          @click="close"
          style="display: inline-block"
        >
          <svg
            t="1745438032429"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="56450"
            width="30"
            height="30"
          >
            <path
              d="M670.165333 225.834667a42.666667 42.666667 0 0 1 0 60.330666L444.330667 512l225.834666 225.834667a42.666667 42.666667 0 0 1-60.330666 60.330666l-256-256a42.666667 42.666667 0 0 1 0-60.330666l256-256a42.666667 42.666667 0 0 1 60.330666 0z"
              fill="var(--svg-icon-color)"
              p-id="56451"
            ></path>
          </svg>
        </div>

        <div class="content-header-title">Depósito</div>
        <!-- <div class="content-header-registro">
          <span @click="onClickRegistro">Registro de Depósito</span>
        </div> -->
      </div>
      <div
        style="
          background-color: #f0f1fb;
          border-top-left-radius: 40px;
          border-top-right-radius: 40px;
        "
      >
        <div class="content-online">
          <ul class="content-online-ul">
            <li class="content-online-li">
              <!-- <AppImage
              src="/img/finance/finance-phone.png"
              class="content-online-li-img"
            /> -->
              <div>
                <!-- <svg
                t="1745471243932"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="8543"
                width="30"
                height="30"
              >
                <path
                  d="M752 624a64 64 0 0 1-64-64 16 16 0 0 0-16-16H224a16 16 0 0 0-16 16 64 64 0 0 1-64 64 16 16 0 0 0-16 16v160a16 16 0 0 0 16 16 64 64 0 0 1 64 64 16 16 0 0 0 16 16h448a16 16 0 0 0 16-16 64 64 0 0 1 64-64 16 16 0 0 0 16-16V640a16 16 0 0 0-16-16zM448 816c-34.72 0-64-44-64-96s29.28-96 64-96 64 44 64 96-29.28 96-64 96z"
                  p-id="8544"
                  fill="var(--svg-icon-color)"
                ></path>
                <path
                  d="M951.84 667.04l-32-56.16 5.28-9.12a48 48 0 0 0-17.6-65.6l-46.72-26.72L615.84 84.96a48 48 0 0 0-65.6-17.6l-228 131.52a46.24 46.24 0 0 0-33.6-3.52 47.04 47.04 0 0 0-29.28 22.4L107.84 480A48 48 0 0 0 64 528v384a48 48 0 0 0 48 48h672a48 48 0 0 0 48-48v-120.32l102.24-59.04a48 48 0 0 0 17.6-65.6z m-55.04-81.28l-9.6 16L832 697.92v-168l9.12 5.28 49.44 28.64a16 16 0 0 1 6.24 21.92zM566.24 96a16 16 0 0 1 21.76 5.76l217.28 375.68-54.88-32-133.92-231.36a16 16 0 0 0-21.92-6.08 64 64 0 0 1-87.36-23.36 16.96 16.96 0 0 0-9.76-7.52 16 16 0 0 0-12.16 1.6l-98.56 56.96-32-18.56zM288 233.76a16 16 0 0 1 9.76-7.52 16 16 0 0 1 12 1.76l5.6 3.2 64 36.64h0.8l352 202.72 14.88 8.48h-64L390.08 311.52a16 16 0 0 0-19.2 2.56 6.88 6.88 0 0 0-1.44 1.6v1.76a70.08 70.08 0 0 1-4.48 6.72 56.32 56.32 0 0 1-4.96 5.6 28.96 28.96 0 0 1-5.28 4.64 42.24 42.24 0 0 1-7.52 5.28 64 64 0 0 1-64.8 0 16 16 0 0 0-21.92 5.92L181.92 480H144.96zM800 912a16 16 0 0 1-16 16H112a16 16 0 0 1-16-16V528a16 16 0 0 1 16-16h672a16 16 0 0 1 16 16v384z m125.76-216.96a16 16 0 0 1-7.52 9.76l-80 46.08 61.44-106.24 0.96-1.76 23.36 40.32a16 16 0 0 1 1.76 11.84z"
                  p-id="8545"
                  fill="var(--svg-icon-color)"
                ></path>
              </svg> -->
              </div>
              <span class="content-online-li-title">Depósito on-line</span>
              <div class="gift-percentage" v-if="depositGift.isOpen">
                <p class="tool-tips-box">{{ depositGift.max + "%" }}</p>
                <p class="tool-tips-tail"></p>
              </div>

              <!-- <AppImage src="/img/finance/finance-qipao1.png" class="content-online-li-qipao"/> -->
              <!-- <p class="content-online-tips-box">5%</p> -->
            </li>
          </ul>
        </div>
        <div class="globalLine"></div>
        <div class="content-tabs">
          <div class="content-tabs-div">
            <ul class="tabs-div-ul">
              <!-- box-border -->
              <li class="tabs-div-li">
                <dl class="tabs-div-dl">
                  <AppImage
                    src="/img/finance/finance-icon.png"
                    class="tabs-div-li-img"
                  />
                  <dd style="text-align: left; font-size: 3.5vw">PIX</dd>
                  <!-- <AppImage
                  src="/img/finance/finance-kuang.png"
                  class="tabs-div-li-kuang" checked-border
                /> -->
                  <!-- <div class="tabs-div-li-kuang" v-html="checkBorderIcon"></div> -->

                  <div class="gift-percentage" v-if="depositGift.isOpen">
                    <p class="tool-tips-box">{{ giftMinAndMaxTip }}</p>
                    <p class="tool-tips-tail"></p>
                  </div>
                </dl>
              </li>
            </ul>
            <ul class="tabs-div-ul-2">
              <!-- box-border -->
              <li class="tabs-div-2-li">
                <dl class="tabs-div-2-dl">
                  <dd class="tabs-div-2-dl-title">
                    {{ depositChannel.showName }}
                  </dd>
                  <!-- <AppImage
                  src="/img/finance/finance-kuang.png"
                  class="tabs-div-li-kuang1"
                /> -->
                  <!-- <div class="tabs-div-li-kuang" v-html="checkBorderIcon"></div> -->
                </dl>
              </li>
              <!-- box-border -->
              <li class="tabs-div-2-li" v-if="false">
                <dl class="tabs-div-2-dl">
                  <dd class="tabs-div-2-dl-title" :class="{ norsel: true }">
                    {{ depositChannel.showName }}
                  </dd>
                  <!-- <AppImage src="/img/finance/finance-kuang.png" class="tabs-div-li-kuang" /> -->
                  <!-- <div class="tabs-div-li-kuang" v-html="checkBorderIcon"></div> -->
                </dl>
              </li>
            </ul>
            <section class="content-tabs-div-dep">
              <div class="dep-content">
                <!-- <div class="dep-content-title">
                <span>Depósito</span>
              </div> -->
                <div class="content-tabs-div-input">
                  <AppInput
                    icon-with="43"
                    v-model="moneyStr"
                    ref="phoneRef"
                    :placeholder="`${inputTip}`"
                    type="tel"
                    msg=""
                    width="690"
                    height="110"
                    :style-obj="{
                      background: '#ffffff',
                      color: '#000',
                      borderRadius: 'var(--app-px-10)',
                      borderColor: 'var(--theme-color-line)',
                      fontSize: '25px',
                    }"
                  >
                    <template #left>
                      <div
                        :style="{
                          color: 'var(--app-title-color)',
                          paddingRight: 'var(--app-px-16)',
                          paddingLeft: 'var(--app-px-16)',
                        }"
                      >
                        R$
                      </div>
                    </template>
                  </AppInput>
                </div>

                <ul class="dep-content-ul">
                  <!-- box-border -->
                  <li
                    v-for="(item, idx) in items"
                    :key="idx"
                    class="dep-content-li"
                    :class="{ sel: selectIndex == idx }"
                    @click="onClickItem(idx)"
                  >
                    <!-- <AppImage
                    v-show="selectIndex == idx"
                    src="/img/finance/finance-kuang2.webp"
                    class="dep-content-li-kuang"
                  /> -->
                    <!-- <div
                    v-show="selectIndex == idx"
                    class="dep-content-li-kuang"
                    v-html="checkBorderIcon2"
                  ></div> -->

                    <div
                      class="dep-content-li-text"
                      :class="{ sel: selectIndex == idx }"
                    >
                      R$ {{ item.amount }}
                    </div>
                  </li>
                </ul>
              </div>
            </section>
          </div>
        </div>
        <div class="content-btn">
          <AppButton
            @click="goToRecharge"
            :loading="loading"
            class=""
            width="var(--app-px-690)"
            height="90"
            blue
            :radius="14"
            color="var(--theme-font-on-background-color)"
            font-size="4vw"
            >Recarregue Agora</AppButton
          >
        </div>
      </div>
    </div>
  </van-popup>
  <!-- ios提示框 -->
  <van-popup
    class="app-login-register"
    v-model:show="isShowIosTip"
    teleport="body"
    round
    :overlay-style="{ background: 'rgba(255,255,255,0)' }"
    :close-on-click-overlay="false"
  >
    <div class="contentIosTip">
      <div class="textbgIosTip">
        <AppImage src="/icons/SaldoLivre.webp" class="iconIosTip" />
        <label class="textIosTip">{{ showMessage }}</label>
      </div>
      <div class="submitIosTip">
        <AppButton
          @click="closePage"
          class=""
          width="var(--app-px-220)"
          height="80"
          green
          :radius="15"
          white-text
          font-size="var(--app-px-30)"
          bold
          >Cancelar</AppButton
        >
      </div>
      <div class="submitIosTip2">
        <AppButton
          @click="openUrl"
          class=""
          width="var(--app-px-220)"
          height="80"
          blue
          :radius="15"
          color="#000"
          font-size="var(--app-px-30)"
          bold
          >Confirmar</AppButton
        >
      </div>
    </div>
  </van-popup>

  <van-popup
    v-model:show="isShowIframe"
    class="iframe_div"
    :style="{ width: 'var(--app-px-750)' }"
    teleport="body"
  >
    <div class="iframe_div_bg">
      <div class="iframe_div_content">
        <div class="iframe_div_content_header">
          <div class="iframe_div_content_header_title">
            <div class="iframe_div_content_header_close">
              <AppImage
                class="close"
                src="/img/finance/finance-back.png.webp"
                @click="closeIframe"
              />
            </div>
            <span>Depósito</span>
          </div>
        </div>
        <iframe id="iframeShop" class="iframe_style" :src="chargeUrl"></iframe>
      </div>
    </div>
  </van-popup>
  <bindCPFInfoPopup
    v-if="isShowPay"
    :cpf="cpf"
    @updateMessage="isSendMessage($event)"
    :isShow="isShow"
  />
</template>

<style lang="scss" scoped>
dd {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  -ms-flex-align: center;
  -ms-flex-pack: center;
  align-items: center;
  color: var(--theme-font-on-background-color);
  font-weight: bold;
  display: -webkit-box;
  -ms-flex: 1;
  flex: 1;
  font-size: 22px;
  justify-content: center;
  margin: 0;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  vertical-align: middle;
  word-break: break-all;
}

.box-border {
  border-style: solid;
  border-color: var(--theme-color-line);
  border-width: 2px;
  border-radius: 10px;
}

.app-pay-popup {
  max-width: 100%;
  height: 100%;
  border-radius: 0px;
  // top: 90px;
}

.content {
  background: #f0f1fb;
  background-image: url(/img/index/main_bg.webp);
  background-size: cover;
  background-repeat: repeat;
  top: 0px;
  left: 0px;
  position: fixed;
  width: 100vw;
  bottom: 0px;
  // border-top-left-radius: 20px;
  // border-top-right-radius: 20px;
}

.globalLine {
  width: 100%;
  height: 1px;
}

.content-header {
  display: block;
  width: 100%;
  height: 90px;
  position: relative;
  padding: 20px 30px 0px 20px;

  .content-header-back {
    // width: 24px;
    // filter: hue-rotate(69deg) saturate(1%) brightness(34%);
    height: 24px;
    margin-top: 3px;
  }

  .content-header-title {
    display: inline;
    font-size: 34px;
    color: var(--theme-text-color);
    line-height: 50px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .content-header-registro {
    display: inline;
    position: absolute;
    color: var(--theme-text-color-lighten);
    font-size: 24px;
    line-height: 36px;
    top: 15px;
    right: 30px;
  }
}

.gift-percentage {
  position: absolute;
  right: -10px;
  top: -16px;
  width: fit-content;

  .tool-tips-box {
    color: #fff;
    border-radius: 14px 14px 14px 0;
    background-color: var(--theme-secondary-color-error);
    padding: 0 8px;
    line-height: 26px;
    height: 26px;
    font-size: 18px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding-top: 2px;
  }

  .tool-tips-tail {
    border-bottom: 7px solid transparent;
    border-left: 7px solid var(--theme-secondary-color-error);
    border-right: 7px solid transparent;
    height: 0;
    width: 0;
  }
}

.content-online {
  height: 92px;
  margin: 0px 30px;
  border-bottom-color: var(--theme-color-line);
  border-bottom-style: solid;
  border-bottom-width: 2px;

  .content-online-ul {
    width: 221px;
    height: 92px;
    padding-top: 20px;
  }

  .content-online-li {
    width: 221px;
    height: 72px;
    align-items: center;
    display: flex;
    border-bottom-color: transparent;
    border-bottom-style: solid;
    border-bottom-width: 5px;
    position: relative;
  }
  .content-online-tips-box {
    color: white;
    font-size: 18px;
    position: absolute;
    top: 0px;
    right: 0px;
  }
  .content-online-li-img {
    width: 38px;
    height: 38px;
  }

  .content-online-li-title {
    color: var(--theme-primary-font-color);
    font-size: 3.5vw;
    font-weight: bold;
    line-height: 24px;
    margin-left: 10px;
    white-space: nowrap;
    box-sizing: border-box;
  }

  .content-online-li-qipao {
    width: 42px;
    height: 32px;
    position: absolute;
    right: -10px;
    top: -4px;
  }
}

.content-tabs {
  display: block;
  bottom: 0px;

  .content-tabs-div {
    padding: 20px 30px 0px 30px;
    // height: 620px;
  }

  .tabs-div-ul {
    width: 690px;
    height: 14vw;
    display: flex;
    border-bottom-color: var(--theme-color-line);
    border-bottom-style: solid;
    border-bottom-width: 2px;
  }
  .tabs-div-li {
    width: 214px;
    height: 11vw;
    display: list-item;
    position: relative;
    background-color: var(--theme-primary-color);
    border-radius: 20px;
  }
  .tabs-div-dl {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .tabs-div-li-img {
    margin-left: 45px;
    margin-right: 1.5vw;
    width: 44px;
    height: 44px;
  }

  .tabs-div-li-kuang {
    position: absolute;
    width: 28.663333vw;
    height: 10.333333vw;
    left: -2px;
  }

  .tabs-div-li-qipao {
    width: 72px;
    height: 32px;
    position: absolute;
    left: 145px;
    top: -22px;
  }
  .tabs-div-li-tips-box {
    color: white;
    font-size: 18px;
    position: absolute;
    top: -18px;
    right: -4px;
  }

  .tabs-div-ul-2 {
    width: 690px;
    height: 132px;
    display: flex;
    padding-top: 30px;
    padding-bottom: 10px;
    border-bottom-color: var(--theme-color-line);
    border-bottom-style: solid;
    border-bottom-width: 2px;
  }

  .tabs-div-2-li {
    width: 214px;
    height: 11vw;
    display: list-item;
    position: relative;
    margin-bottom: 20px;
    margin-right: 24px;
    background-color: var(--theme-primary-color);
    border-radius: 20px;
  }

  .tabs-div-2-dl {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .tabs-div-2-dl-title {
    font-size: 24px;
    &.norsel {
      color: var(--theme-text-color);
    }
  }

  .tabs-div-li-kuang1 {
    position: absolute;
    width: 214px;
    height: 70px;
    left: -1px;
  }

  .content-tabs-div-dep {
    width: 690px;
    // height: 376px;
  }

  .dep-content {
    width: 100%;
    height: 100%;
    padding: 40px 0;
  }

  .dep-content-title {
    width: 100%;
    height: 26px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    span {
      font-size: 24px;
      color: var(--theme-text-color);
      line-height: 26px;
    }
  }

  .dep-content-ul {
    width: 100%;
    // height: 180px;
    max-height: 40vw;
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    scrollbar-width: none; /* 设置滚动条宽度 */
    scrollbar-color: #ccc transparent; /* 设置滚动条颜色和背景色 */
  }

  .dep-content-li {
    width: 28.533333vw;
    height: 11vw;
    position: relative;
    margin-right: 24px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--theme-color-line);
    border-radius: 20px;

    &:nth-child(3n) {
      margin-right: 0px;
    }
    &.sel {
      background-color: var(--theme-primary-color);
    }
  }
  .dep-content-li-kuang {
    position: absolute;
    width: 20.663333vw;
    height: 11.333333vw;
    left: -2px;
  }
  .dep-content-li-text {
    display: block;
    font-size: 3.8vw;
    // font-weight: bold;
    color: var(--theme-text-color);
    line-height: 29px;
    &.sel {
      color: var(--theme-font-on-background-color);
      font-weight: bold;
    }
  }

  .content-tabs-div-input {
    width: 690px;
    height: 90px;
    margin-bottom: 60px;
  }
}
.content-btn {
  margin-top: 40px;
  width: 750px;
  height: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
}

//--------------------------------------------
.contentIosTip {
  width: 680px;
  height: 350px;
  border-radius: 20px;
  background-color: var(--theme-main-bg-color);
}

.textbgIosTip {
  width: 612px;
  height: 100px;
  background: var(--app-message-input-color);
  border-radius: 15px;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: (50%-17);
}

.iconIosTip {
  width: 57px;
  margin-left: 35px;
  margin-top: 30px;
}

.textIosTip {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: (50%+30);
  text-align: center;
  width: 500px;
  height: 100px;
  // line-height: 100px;

  font-weight: 700;
  color: #ffffff;
  font-size: 36px;

  // margin-left: 60px;
  // margin-top: 25px;
}

.submitIosTip {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 30%;
  top: (50%+26);
}

.submitIosTip2 {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 70%;
  top: (50%+26);
}

.iframe_div {
  //   background-color: rgba(0, 0, 0, .45);
  height: 100%;
  width: 750px;
  overflow: hidden;
  max-width: 750px;
  display: flex;
  align-items: center;
  justify-content: center;
  ::-webkit-scrollbar {
    display: none;
  }
  .iframe_div_bg {
    width: 718px;
    height: 100%;
  }
  .iframe_div_content {
    width: 718px;
    height: calc(100% - 50px);
    margin-top: 16px;
    background-color: var(--theme-bg-color);
    border-color: var(--theme-color-line);
    border: 2px solid var(--theme-color-line);
  }

  .iframe_div_content_header {
    width: 714px;
    height: 100px;
    background-color: var(--theme-main-bg-color);
  }

  .iframe_div_content_header_title {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-text-color-darken);
    font-size: 32px;
    height: 100px;
    position: relative;
    width: 100%;
  }

  .iframe_div_content_header_close {
    width: 110px;
    height: 100px;
    padding-left: 30px;
    display: flex;
    align-items: center;
    // justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
  }
  .iframe_style {
    margin: 20px !important;
    display: block;
    margin: 0 auto;
    width: 674px;
    height: calc(100% - 140px);
    overflow: hidden;
    scrollbar-width: none;
    color: #ffffff;
    border-radius: 14px;
    background-color: var(--theme-main-bg-color);
    // padding-top: 100px;
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .iframe_style::-webkit-scrollbar {
    display: none;
  }
  .close {
    height: 30px;
  }
}

.checked-border {
  border: var(--svg-icon-color) solid 0.566667vw;
  position: relative;
  overflow: hidden;
  &::after {
    content: "✔";
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 32px;
    height: 32px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cpath d='M0 32 L32 32 L32 0 Z' fill='rgb(54, 141, 69)'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    color: black;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    font-size: 16px;
    line-height: 1;
    z-index: 1;
    padding: 0 5px 4px 0;
  }
}
</style>
