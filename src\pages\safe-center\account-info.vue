<script lang="ts" setup name="AccountInfo">
const router = useRouter()

const phoneRef = ref()
const emailRef = ref()
const telgRef = ref()
const PhoneDialogVisible = ref(false)
const emailDialogVisible = ref(false)
const passwordDialogVisible = ref(false)

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)

const formdata = reactive({
  phone: userInfo.value.phone || '',
  email: userInfo.value.email || '',
  telegram: userInfo.value.telegram || '',
})

const { phoneReg } = useRegExpPhone();

const normalStyle = {
  margin: '0 auto',
  background:'#192841',
  color: '#fff'
}

const disableStyle = {
  margin: '0 auto',
  background:'#192841',
  color: '#324b6e'
}

const { run: runUpdateInfo, loading: updateLoading } = useRequest(ApiUpdateUserInfo, {
  manual: true,
  onSuccess: () => {
    router.go(-1)
  }
})

const confirmFn = () => {
  if (phoneRef.value.validation() && emailRef.value.validation() && telgRef.value.validation()) {
    runUpdateInfo({ phone: userInfo.value.phone ? '' : formdata.phone, email: userInfo.value.email ? '' : formdata.email, telegram: formdata.telegram })
  }
}

watch(userInfo, (val) => {
  if (val) {
    formdata.phone = val.phone || ''
    formdata.email = val.email || ''
    formdata.telegram = val.telegram || ''
  }
})
const iconLoad = (e: any, item: any) => {
  for (let i = 0; i < records.value.length; i++) {
    if (item.icon === records.value[i].icon) {
      records.value[i].iconWidth = Math.ceil(e.target.naturalWidth)
      break
    }
  }
}
const records = ref([
  { icon: 'mobile_number', text: 'Mobile Number', iconWidth: 20, rightColor: '#f43f5e', rightIcon: 'fujian' }, // icon 图标从 figma 导出 x3 倍图
  { icon: 'email', text: 'Email', iconWidth: 20, rightColor: '#f43f5e', rightIcon: 'fujian' },
  { icon: 'password', text: 'Password', iconWidth: 30, rightColor: '#3b82f6', rightIcon: 'password_edit' },
])
const open = (type: number) => {
  if (type === 0) {
    PhoneDialogVisible.value = true
  } else if (type === 1) {
    PhoneDialogVisible.value = true
  } else if (type === 2) {
    PhoneDialogVisible.value = true
  }

}
const submit = () => {
  PhoneDialogVisible.value = false
}
</script>

<template>
  <div class="account-info">
    <AppIndexHeader />
    <AppPageTitle left-arrow :title="`${('informações pessoais').toUpperCase()}`" background="#324b6e" title-weight="700" />
    <ul class="reports">
      <li v-for="(item, idx) in records" :key="item.icon" class="item">
        <div class="icon">
          <AppImage :src="'/icons/' + item.icon + '.png'"
            :style="{ width: item.iconWidth ? 'var(--app-px-' + item.iconWidth + ')' : '0' }"
            @load="$event => iconLoad($event, item)" />
        </div>
        <span>{{ item.text }}</span>
        <div class="right" :style="{background: item.rightColor}" @click="open(idx)">
          <AppImage class="arrow"  :src="'/icons/' + item.rightIcon + '.png'" />
        </div>
      </li>
    </ul>
    <van-popup class="eidt-info" close-icon="cross" v-model:show="PhoneDialogVisible" position="bottom" teleport="body" :style="{height:'100%',width:'100%'}">
      <div class="content">
        <AppImage class="close"  src="/icons/close.close_black" @click="PhoneDialogVisible = false" />
        <div class="pop-title">Alterar o número de telemóvel</div>
        <AppInput icon-with="43" ref="loginRef"  clearable  width="627" height="89" :style-obj="{
          background:'white',
          color: '#000',
          borderRadius: '10px',
          marginBottom: '10px',
        }" ></AppInput>
        <AppButton fontSize="36" radius="15" whiteText red width="627" height="96" center @click="submit">
          OK
      </AppButton>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
  .btn-box {
    padding-top: 86px;
  }
  .divider {
    height: 34px;
  }
  .app-nav-bar{
    margin-top: 100px;
  }
  .eidt-info{
    .van-popup__close-icon--top-right{
      right: 50px !important;
    }
  }
  .content{
    width: 674px;
    padding: 20px;
    border-radius: 20px;
    color: #000;
    margin: 0 auto;
    margin-top: 50%;
    background-color: #dbe4ef;
    position: relative;
    .close{
      width: 40px;
      position: absolute;
      right: 20px;
    }
    .pop-title{
      margin-top: 20px;
      margin-bottom: 30px;
    }
  }
  .account-info {
    min-height: 100vh;
    box-sizing: border-box;
    padding-top: 112px;
    padding-bottom: 132px;
    background: var(--theme-main-bg-color);
    .reports {
      margin: 0 auto;
      padding: 0;
      list-style: none;
      border-radius: 15px;
      width: 700px;
      color: #000;
      font-size: 28px;

      .item {
        position: relative;
        // border-bottom: 1px solid #152237;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 178px;
        background: #fff;
        margin-bottom: 20px;
        border-radius: 15px;
        .icon {
          width: 82px;
          margin-left: 20px;
          margin-right: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        &:nth-child(2){
          .icon {
            margin-left: 24px;
          }
        }

        &:last-child {
          border: 0;
        }
        .right{
          width: 175px;
          height: 87px;
          border-radius: 15px;
          position: absolute;
          right: 20px;
          text-align: center;
        }
        .arrow {
          margin: 0 auto;
          margin-top: 18px;
          width: 50px;
        }
      }
      .item:last-child {
        // color: rgba(255, 255, 255, 0.50);
      }
    }
  }
</style>