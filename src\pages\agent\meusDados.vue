<script setup lang='ts' name='meusDados'>
import { dir } from 'console';

const router = useRouter()
const timeSelect = ref(2)
const data=[    {text:"Ontem",type:1},{text:"Hoje",type:2},{text:"Está Semana",type:3},
                {text:"Última Semana",type:4},{text:"Este Mês",type:5},{text:"Mês Passado",type:6}           
              ]

 const emits = defineEmits(['clickDados'])
//  昨天  今天 本星期 上星期 这个月 上个月
timeSelect.value = data[1].type
//
const dirChild = ref({
    TeamCounts:0,
    FirstDepositCounts:0,
    DepositCounts :0,
    DepositNums:0,
    Waged:0,
    Commi:0,
    first_deposit_to_parent:0
})
//-------------------------------------------

const dirInfo = ref({
  ChildCounts:0,
  OtherCounts:0,

  ChildWaged :0,
  OtherdWaged:0,
  ChildWageReturn:0,
  OtherWageReturn:0,

  ChildCommi:0,
  OtherCommi:0,

  enable_first_deposit_to_parent:0
})

//获取数据
function getData(type:any){
  timeSelect.value =type
  runQueryProxyDirectChildInfo()
}
//跳转成员列表
function getMeuTime(){
  // router.push("/agent/?tab=5")
  emits('clickDados', 5)
}
//跳转流水列表
function getDesem(){
  // router.push("/agent/?tab=3")
  emits('clickDados', 3)
}
//跳转返利列表
function getCom(){
  // router.push("/agent/?tab=4")
  emits('clickDados', 4)
}
//网络数据
//查询直属下级数据 按时间
const {run: runQueryProxyDirectChildInfo } = useRequest(() => ApiQueryProxyDirectChildInfo({QueryType:timeSelect.value}), {
  manual: true,
  onSuccess(res) {
    dirChild.value=res
  }
})

runQueryProxyDirectChildInfo()
//查询总数据不需要按时间
//查询总信息
const {run: runQueryProxyChildInfo } = useRequest(() => ApiQueryProxyChildInfo(), {
  manual: true,
  onSuccess(res) {
    dirInfo.value = res;
  }
})
runQueryProxyChildInfo()


const agent_waged_times = ref(1)

const {run: runGetWithdrawConf } = useRequest(ApiGetWithdrawConf,{
    onSuccess(data) {
      // console.log(data)
      if(data){
        agent_waged_times.value = data.agent_waged_times || 1;
      }
      
    },
    onError(resData){
      
    }
})
runGetWithdrawConf();

</script>
<template>
  <div class="meusDados">
    <!--时间 -->
    <div class="timeSelect">
        <div v-for="item in data" class="timeSelectItem" @click="getData(item.type)" :class="{active : item.type ==timeSelect}" >
          <label class="timeSelect_text">{{ item.text }} </label>
        </div>
    </div>
    <!--个人代理信息 都是一级 -->
    <div class="info">
      <div class="info_item">
        <label class="item_text">Adicionar membros diretos</label> <!--一级人员数量-->
        <label class="item_num">{{ dirChild.TeamCounts }}</label>
      </div>

      <div class="info_item">
        <label class="item_text">Primeiros Depósitos</label>  <!--首充人数-->
        <label class="item_num">{{ dirChild.FirstDepositCounts }}</label>
      </div>
      
      <div class="info_item">
        <label class="item_text">Depósitos</label>  <!--总充值人数-->
        <label class="item_num">{{ dirChild.DepositCounts }}</label>
      </div>
      <!--下一行-->
      <div class="info_item">
        <label class="item_text">Depósito</label>  <!--总金额-->
        <label class="item_num">{{transf(dirChild.DepositNums)}}</label>
      </div>

      <div class="info_item">
        <label class="item_text">Desempenho</label>   <!--直属下级流水-->
        <label class="item_num">{{transf(dirChild.Waged * agent_waged_times)}}</label>
      </div>

      <div class="info_item">
        <label class="item_text">Comissão</label>   <!--奖励 佣金-->
        <label class="item_num item_num2">{{transf(dirChild.Commi + dirChild.first_deposit_to_parent)}}</label>
      </div>
    </div>

    <!--Visão Geral dos Dados  -->
    <div class="visao">
      <label class="visaoTitle">Visão Geral dos Dados</label>
      <div class="line"></div>
      <!-- meu time  成员 -->
       <div class="meuTime"> 
          <label class="meuTime_text" @click="getMeuTime">Meu Time</label>
          <AppImage src="/icons/agent_arrRight" class="arrRight" @click="getMeuTime"/>
          <div class="meuTimeInfo">
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Membros</label>  <!--总下级人数-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">{{dirInfo.ChildCounts +dirInfo.OtherCounts}}</label>
              </div>
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Membros Diretos</label>  <!--直属下级-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">{{dirInfo.ChildCounts }}</label>
              </div>
              <div class="meuTimeInfo_item">
                <label class="meuTimeInfo_item_text">Outros Membros</label> <!--二三下级-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">{{dirInfo.OtherCounts }}</label>
              </div>
          </div>
       </div>

       <div class="meuTime"> 
          <label class="meuTime_text" @click="getDesem">Desempenho</label>
          <AppImage src="/icons/agent_arrRight" class="arrRight" @click="getDesem" />
          <div class="meuTimeInfo">
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Rendimento Total</label>  <!--总流水-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">{{ transf((dirInfo.ChildWaged + dirInfo.OtherdWaged) * agent_waged_times) }}</label>
              </div>
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">D. Direto</label>  <!--直属流水-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">{{ transf(dirInfo.ChildWaged * agent_waged_times) }}</label>
              </div>
              <div class="meuTimeInfo_item">
                <label class="meuTimeInfo_item_text">D. dos Outros</label>  <!--二三级流水-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">{{ transf(dirInfo.OtherdWaged * agent_waged_times) }}</label>
              </div>
          </div>
       </div>

       <div class="meuTime"> 
          <label class="meuTime_text" @click="getCom">Comissão</label>
          <AppImage src="/icons/agent_arrRight" class="arrRight" @click="getCom" />
          <div class="meuTimeInfo">
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Comissões Totais</label> <!--总佣金-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num2">{{transf(dirInfo.ChildWageReturn+dirInfo.ChildCommi + dirInfo.OtherWageReturn + dirInfo.OtherCommi + dirInfo.enable_first_deposit_to_parent) }}</label>
              </div>
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Comissão Direta</label> <!--直属佣金-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num2">{{transf(dirInfo.ChildWageReturn+dirInfo.ChildCommi + dirInfo.enable_first_deposit_to_parent) }}</label>
              </div>
              <div class="meuTimeInfo_item">
                <label class="meuTimeInfo_item_text">Outra Comissão</label><!--二三级佣金-->
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num2">{{transf(dirInfo.OtherWageReturn+dirInfo.OtherCommi) }}</label>
              </div>
          </div>
       </div>


    </div>


  </div>
</template>

<style lang='scss' scoped>
.meusDados{
   width: 100%;
   font-size:23px;

  
}

.timeSelect{
  margin-top: 0px;
  width:100%;
  height:82px;
  background:var(--theme-main-bg-color);
  border-top: 1px solid var(--theme-color-line);
  border-bottom: 1px solid var(--theme-color-line);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;

  .timeSelectItem{
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:103px;
    padding: 8px 18px;
    margin: 10px;
    color:var(--theme-text-color);
    &.active{
      color: var(--theme-font-on-background-color);
      background:var(--theme-primary-color);
    }
  }

  .timeSelect_text{
    white-space: nowrap;
  }
}


.info{
  width: 100%;
  height: 360px;

  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;
  flex-wrap:wrap;

  .info_item{
    width:224px;
    height:148px;
    background:var(--theme-main-bg-color);
    border-radius:10px;
    margin-left: 20px;
  }

  .item_text{
    display: flex;
    color:var(--theme-text-color-lighten);
    align-items: center;
    justify-content: center;
    margin-left: 10%;
    margin-top: 15px;
    width: 80%;
    text-align: center;
    height: 60px;
  }

  .item_num{
    display: inline-block;
    font-size: 32px;
    color: var(--theme-text-color);
    width: 100%;
    text-align: center;
    margin-top: 15px;
  }

  .item_num2{
    color: var(--theme-secondary-color-finance);
  }

}

.visao{
  margin: 0 auto;
  width:710px;
  height:654px;
  background:var(--theme-main-bg-color);
  border-radius:10px;

  .visaoTitle{
    display: block;
    color:white;
    font-size:24px;
    padding-top: 25px;
    padding-left: 20px;
    margin-bottom: 25px;
  }

  .line{
    // position: absolute;
    // top: 180px;
    width:100%;
    height:1px;
    background:var(--theme-color-line) ;
    // margin-top: 25px;

  }

  .meuTime{
    // background:var(--theme-color-line) ;
   
    width:710px;
   
    .meuTime_text{
      // display: flex;
      // align-items: center;
      // justify-content: center;

      display: inline-block;
      margin-left:7.5%;
      width: 85%;
      text-align: center;
      color:var(--theme-text-color);
      // height: 100%;
      line-height: 100%;
      padding-top: 17.5px;
      height:65px;
    }
    .arrRight{
      position: absolute;
      float:right;
      width: 14px;
      margin-top: 17.5px;
    }

    .meuTimeInfo{
      width:93%;
      height:125px;
      margin: 0 auto;
      border-top: 1px solid;
      border-bottom: 1px solid;
      border-color:var(--theme-color-line);
      display: flex;
      align-items: center;
      justify-content: center;
      .meuTimeInfo_item{
        width: 33.3%;
        height: 60%;
      }
      .meuTimeInfo_item_right{
        border-right: 1px solid;
        border-color:var(--theme-color-line);
      }

      .meuTimeInfo_item_text{
        display: flex;
        color:var(--theme-text-color-lighten);
        align-items: center;
        justify-content: center;
        margin-left: 15px;

        width: 90%;
       
      }

      .meuTimeInfo_item_num{
        margin-top: 15px;
        color: var(--theme-text-color);
        
      }

      .meuTimeInfo_item_num2{
        margin-top: 15px;
        color: var(--theme-secondary-color-finance);
        
      }

      
    }
  }

  .color{
    color: var(--theme-secondary-color-finance);
  }

  .desemenho{
    margin-top: 120px;
  }

}

</style>
