# Project Brief and Product Definition

## 核心需求和目标 (Core Requirements and Goals)

*   构建一个基于 Vue 3 和 Vite 的现代化 Web 应用。
*   优先适配移动端设备 (使用 Vant UI 库)。
*   集成 Pinia 进行状态管理，Vue Router 进行页面导航。
*   实现国际化支持 (Vue I18n)。
*   包含交互式功能，例如抽奖 (Lucky Canvas) 和 Lottie 动画。
*   通过 Axios 与后端 API 进行数据交互。
*   使用 TypeScript 提高代码健壮性。
*   利用 UnoCSS 实现高效的样式开发。

## 解决的问题 (Problems it Solves)

*   (需要根据具体业务场景补充，例如：提供一个移动端的活动平台，通过抽奖等方式提高用户参与度；或构建一个特定业务的移动操作界面等)

## 工作原理 (How it Should Work)

*   用户通过浏览器访问应用。
*   应用是一个单页应用 (SPA)，由 Vue Router 管理页面路由。
*   用户界面主要由 Vant 组件构成，适配移动端屏幕。
*   用户的操作可能触发 Pinia store 中的状态变更。
*   应用通过 Axios 向后端服务器发送请求获取数据或提交数据。
*   特定页面可能包含 Lucky Canvas 实现的抽奖转盘或 Lottie 实现的动画效果。
*   应用可以根据用户设置或浏览器语言切换显示语言。 