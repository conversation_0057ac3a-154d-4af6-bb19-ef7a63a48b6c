<script setup lang='ts' name='Poupanca'>
import Transferir from './Transferir.vue'
import Deposito from './Deposito.vue'
import { provide, ref ,inject} from 'vue'
import { eventBus } from './eventBus'
const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
import { RecPiggyBankItem, PiggyBankInfo, PiggyBankChangeInfoParam } from "~/core/http/api"

const transferirRef = ref()
const depositoRef = ref()
//隐藏底下菜单
appStore.setFooterDialogVisble(true)

const isDisabled = ref(true);
const isCountDown = ref(false);
const isReceberDisabled = ref(true);
const time = ref(5000);
// const time = ref(30 * 60 * 60 * 1000);

const showPopover = ref(false);
const showTypePopover = ref(false);

const timeIndex = ref(2);//查询时间 0 所有 ,1 昨天, 2 今天,3 7天, 4 15天,5 30天
const selText = ref("Hoje");
const selText1 = ref("Tudo");
const typeIndex = ref(3);//查询类型 0:储蓄罐转到钱包 1(钱包转到储蓄罐) 2 利息  否则所有
const finished = ref(false);
const pageDatas = ref({
    pageIndex: 0,
    pageCount: 30,
});

const totalInterest = ref(0);
// const userPiggyBankInfo: PiggyBankInfo = {
//     CanAchieveInterest: 0,
//     Interest: "",
//     PiggyAmount: "",
//     Return: "",
//     TotalInterest: "",
// }
const RecPiggyBankItemArray = ref<RecPiggyBankItem[]>([]);

// let i = 0
// for (i; i < 15; i++) {
//     const temp: RecPiggyBankItem = {
//         amount: '**********',//时间
//         type: 2,//类型
//         created_at: "4544444",//金额
//     }

//     RecPiggyBankItemArray.value.push(temp)
// }

//查询时间 0 所有 ,1 昨天, 2 今天,3 7天, 4 15天,5 30天
//查询类型 0:储蓄罐转到钱包 1(钱包转到储蓄罐) 2 利息  否则所有
let actions = [{ text: "Hoje", calssName: "2", color: timeIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Ontem", calssName: "1", color: timeIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 7 Dias", calssName: "3", color: timeIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 15 Dias", calssName: "4", color: timeIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 30 Dias", calssName: "5", color: timeIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
]

let typeActions = [{ text: "Tudo", calssName: "3", color: typeIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Depósito", calssName: "1", color: typeIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Transferir", calssName: "0", color: typeIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Resgatar lucro", calssName: "2", color: typeIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
]

const popoverStatus = (isOpen: boolean) => {
    if (!isOpen) {
        actions = [{ text: "Hoje", calssName: "2", color: timeIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Ontem", calssName: "1", color: timeIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 7 Dias", calssName: "3", color: timeIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 15 Dias", calssName: "4", color: timeIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 30 Dias", calssName: "5", color: timeIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        ]
    }
}

const typePopoverStatus = (isOpen: boolean) => {
    if (!isOpen) {
        typeActions = [{ text: "Tudo", calssName: "3", color: typeIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Depósito", calssName: "1", color: typeIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Transferir", calssName: "0", color: typeIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Resgatar lucro", calssName: "2", color: typeIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        ]
    }
}

const onSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selText.value = action.text;
    timeIndex.value = Number(action.calssName)
    runApiGetPiggyBankChangeInfo({flag: timeIndex.value, type: typeIndex.value});
}

const onTypeSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selText1.value = action.text;
    typeIndex.value = Number(action.calssName)
    runApiGetPiggyBankChangeInfo({flag: timeIndex.value, type: typeIndex.value});
}

// 创建一个全局的依赖注入存储
const globalDepositoModal = ref({
  methods: {
    show: () => {},
    hide: () => {}
  }
})

// 提供一个方法来设置 Deposito 的模态框方法
const setDepositoModalMethods = (methods: { show: () => void, hide: () => void }) => {
  globalDepositoModal.value.methods = methods
}

// 提供给子组件
provide('depositoModal', {
  getMethods: () => globalDepositoModal.value.methods,
  setMethods: setDepositoModalMethods
})

// 如果需要直接调用存款弹窗
const openDepositModal = () => {
    try {
    // 尝试从当前实例获取
    const currentInstance = getCurrentInstance()
    
    // 首选从当前实例的 provide 获取
    let depositoModal = currentInstance?.provides['depositoModal']
    
    // 如果当前实例没有，则使用全局存储
    if (!depositoModal) {
      depositoModal = {
        getMethods: () => globalDepositoModal.value.methods
      }
    }
    
    console.log('Deposito Modal:', depositoModal)
    
    if (depositoModal) {
      const methods = depositoModal.getMethods()
      methods.show()
    } else {
      console.warn('Deposito modal methods not available')
    }
  } catch (error) {
    console.error('Error in openDepositModal:', error)
  }
}

const showTransferir = () => {
    if(userInfo.value.pay_password == 0) {
        router.push('/personalCenter/changePayPassword')
    }
    else{
        transferirRef.value.show();
    }
}

const showDeposito = () => {
    //depositoRef.value.show();
    openDepositModal()
}


const active = ref(0);

function onDepositoClick() {
    showDeposito();
}

function onTransferirClick() {
    showTransferir();
}

function onReceberClick() {
    console.log("onReceberClick");
    isReceberDisabled.value = true;
    isCountDown.value = false;
    runApiAchieveInterest();
}

function getTime(time: string){
    const date = new Date(time * 1000);
    // console.log('time', time, date)
    return dayjs(getBrazilTime(time*1000)).format('MM/DD/YYYY HH:mm:ss')
    return date.toLocaleString(); // 转换为本地日期和时间
}

function getTypeName(type: number){
    let name = '';
    switch(type){
        case 0:
            name = "Transferir"
            break;
        case 1:
            name = "Depósito"
            break;
        case 2:
            name = "Resgatar lucro"
            break;
    }
    return name;
}


const tabData = ref([
    {
        label: 'Detalhes do registro',
        value: 0
    },
    {
        label: 'Regras de juros',
        value: 1
    },
])

function checkReceberState() {
    isDisabled.value = true;
    if (Number(userPiggyBankInfo.value?.PiggyAmount) > 0) {
        isDisabled.value = false;
    }

    isReceberDisabled.value = true
    if (userPiggyBankInfo.value?.CanAchieveInterest == 1){
        isReceberDisabled.value = false;
    }else{
        if (Number(userPiggyBankInfo.value?.Interest) == 0 && Number(userPiggyBankInfo.value?.PiggyAmount) == 0) {
            isReceberDisabled.value = true;
            isCountDown.value = false;

            //测试
            // isReceberDisabled.value = true;
            // isCountDown.value = true;
            // const now = new Date();
            // const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0);
            // const diffTime = midnight - now;
            // time.value = diffTime;
        }else if(Number(userPiggyBankInfo.value?.Interest) > 0 || Number(userPiggyBankInfo.value?.PiggyAmount) > 0){
            isReceberDisabled.value = true;
            isCountDown.value = true;
            // const now = new Date();
            // const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0);
            // const diffTime = midnight - now;
            // time.value = diffTime;

            const localTime = new Date().getTime();
            const localOffset  = getBrazilTime(localTime);
            const brazilTime = new Date(localOffset);

            const midnightBrazil = new Date(brazilTime.getFullYear(), brazilTime.getMonth(), brazilTime.getDate() + 1, 0, 0, 0);
            time.value = midnightBrazil - brazilTime;
        }
    }

}

function onTimeFinish() {
    console.log("onTimeFinish");
    isReceberDisabled.value = false;
    isCountDown.value = false;
    time.value = 0;
}

// 创建一个可以被外部调用的方法
const runApiGetPiggyBankInfo = () => {
    try {
        run()
    } catch (error) {
        console.error('Error calling runApiGetPiggyBankInfo:', error)
        // 可以添加额外的错误处理逻辑
    }
}

// 保留原有的 useRequest 配置
const { run, data: userPiggyBankInfo } = useRequest(() => ApiGetPiggyBankInfo(), {
    manual: true,
    onSuccess(data) {
        // console.log(userPiggyBankInfo?.value?.InterestCalcTime);
        // console.log(Math.floor(Date.now() / 1000))
        checkReceberState();
        setTimeout(() => {
            runApiGetPiggyBankChangeInfo({ flag: timeIndex.value, type: typeIndex.value });
        }, 300);
        
    },
    onError(data: any) {
        showErrorTip(data)
    }
})

const { run: runApiGetPiggyBankChangeInfo, data: piggyBankChangeDatas, loading: runApiGetPiggyBankChangeInfoLoading } = useRequest((datas:PiggyBankChangeInfoParam) => ApiGetPiggyBankChangeInfo(datas), {
    manual: true,
    onSuccess(res: any) {
        console.log(res);
        RecPiggyBankItemArray.value = [];
        if (res){
            RecPiggyBankItemArray.value = [...RecPiggyBankItemArray.value, ...res];
            console.log(RecPiggyBankItemArray.value);
            totalInterest.value = 0;
            RecPiggyBankItemArray.value.forEach((item) => {
                if (item.type == 2) {
                    totalInterest.value += Number(item.amount);
                }
            })
        }
    },
    onError(data: any){
        showErrorTip(data);
    }
})

const { run: runApiAchieveInterest } = useRequest(() => ApiAchieveInterest(), {
    manual: true,
    onSuccess(data) {
        if (data) {
            appStore.runGetUserBalance()
            // appStore.setIsShowMessage(true,"Bônus +R$ "+ data)
            appStore.setIsShowBonusMessage(true, "Resgatado com sucesso!")

            console.log(data)
            runApiGetPiggyBankInfo();
            // runApiGetPiggyBankChangeInfo({ flag: timeIndex.value, type: typeIndex.value });

        }
    },
    onError(data: any) {
        showErrorTip(data);
    }
})

const refresh = () => {
    pageDatas.value.pageIndex = 0;
    getQueryTeam();
}

const loadMoreData = () => {
    console.log("loadMoreData");
    if (pageDatas.value?.pageIndex == 0 || pageDatas.value?.pageIndex * pageDatas.value?.pageCount < queryTeamDatas.value?.TotalCount) {
        getQueryTeam();
        pageDatas.value.pageIndex++
    }
}

const getQueryTeam = () => {
    return;
    console.log("getQueryTeam", pageDatas.value.pageIndex);
    if (pageDatas.value.pageIndex == 0) {
        RecPiggyBankItemArray.value = []
    }
    runGetProxyChildDetailInfo({ pageIndex: pageDatas.value.pageIndex, pageCount: pageDatas.value.pageCount, Username: question.value, QueryType: timeIndex.value })
}


onMounted(() => {
    runApiGetPiggyBankInfo();
    // runApiGetPiggyBankChangeInfo({flag: timeIndex.value, type: typeIndex.value});
  // 监听存款成功事件
  eventBus.on((event) => {
    if (event === 'depositSuccess' || event === 'transferirSuccess') {
      runApiGetPiggyBankInfo()

    }
  })
});

onUnmounted(() => {
  clearCountDown();
});

// 清除倒计时的方法
const clearCountDown = () => {
    isCountDown.value = false;
};

// 暴露方法
defineExpose({
  runApiGetPiggyBankInfo,
  runApiGetPiggyBankChangeInfo,
  openDepositModal
})

</script>
<template>
    <div class="spin-container">
        <section class="detail-content">
            <div class="row-flex">
                <div class="col">
                    <span>Depositado</span>
                    <div>
                        <div class="deposit-amount">
                            <span>{{ transf(Number(userPiggyBankInfo?.PiggyAmount)) }}
                                <div class="tool-tips">
                                    <p class="tool-tips-box">
                                        <span>Taxa de Juros Anual {{ transf(Number(userPiggyBankInfo?.Return) * 100) }}%</span>
                                    </p>
                                    <p class="tool-tips-tail"></p>
                                </div>
                            </span>

                        </div>
                    </div>
                </div>
                <div class="col-end">
                    <button type="button" class="btn" :class="{ 'finance': true }" @click=onDepositoClick>
                        <span>Depósito</span>
                    </button>
                    <button type="button" class="btn" :class="{ 'primary': true, 'disabled': isDisabled }"
                        :disabled="isDisabled" @click=onTransferirClick>
                        <span>Transferir</span>
                    </button>
                </div>
            </div>
            <div class="row-flex">
                <div class="col">
                    <div class="curIncome">
                        <div class="curIncomeItem">
                            <div>Período de liquidação <span>1 horas</span> (Máximo <span>1 ciclos</span>)</div>
                        </div>
                        <div class="curIncomeItem">
                            <div>Pendente <strong>{{ transf(Number(userPiggyBankInfo?.Interest), 6) }}</strong> (Resgatado
                                {{ transf(Number(userPiggyBankInfo?.TotalInterest)) }})</div>
                        </div>
                    </div>
                </div>
                <div class="col-end">
                    <button type="button" class="btn" :class="{ 'btn-receber': true, 'disabled': isReceberDisabled }"
                        :disabled="isReceberDisabled" @click=onReceberClick>
                        <span>Receber</span>
                        <van-count-down v-if = "isReceberDisabled && isCountDown" class = "countdown" :time="time" @finish="onTimeFinish"/>
                    </button>
                </div>
            </div>
        </section>
    </div>
    <Transferir 
    ref="transferirRef" 
    :max-amount="userPiggyBankInfo?.PiggyAmount"
    :countdown-seconds="(() => {
        const timeDiff = Math.floor(Date.now() / 1000) - userPiggyBankInfo?.InterestCalcTime
        const oneHourInMs = 60 * 60;
        return timeDiff > oneHourInMs ? 0 : Math.max(0, oneHourInMs - Math.floor(timeDiff));
    })()"
    />

    <Deposito ref="depositoRef" />

    <section class="data-container">
        <van-tabs class="tabs-title" v-model:active="active" swipeable shrink background="var(--theme-main-bg-color)">
            <!-- <van-tab v-for="index in tabData" :title="index.label">
                内容 {{ index }}
            </van-tab> -->
            <van-tab title="Detalhes do registro" >
                <section class="data-container-main">
                    <div class="selection-container">
                        <div class="selection-component">
                            <van-popover class="content-body-top-popover" v-model:show="showPopover" :actions="actions"
                                @select="onSelect" @open="popoverStatus(true)" @close="popoverStatus(false)"
                                placement="bottom-start" :show-arrow=false :offset="[0, 2]">
                                <template #reference>
                                    <div class="content-body-top-popover-select box-border"
                                        :class="{ 'viewOpen': showPopover }">
                                        <!-- <van-text-ellipsis :content="selText" class="top-popover-select-title"/> -->
                                        <span class="top-popover-select-title">{{ selText }}</span>
                                        <span class="top-popover-select-icon"
                                            :class="{ 'rotate': showPopover, 'rotate1': !showPopover }">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                                viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                                aria-hidden="true" focusable="false" class="">
                                                <path id="comm_icon_fh"
                                                    d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                                    transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                            </svg>
                                        </span>
                                    </div>
                                </template>
                            </van-popover>
                            <van-popover class="content-body-top-popover" v-model:show="showTypePopover"
                                :actions="typeActions" @select="onTypeSelect" @open="typePopoverStatus(true)"
                                @close="typePopoverStatus(false)" placement="bottom-start" :show-arrow=false
                                :offset="[0, 2]" :style="{ marginLeft: `var(--app-px-20})` }">
                                <template #reference>
                                    <div class="content-body-top-popover-select box-border"
                                        :class="{ 'viewOpen': showTypePopover }">
                                        <!-- <van-text-ellipsis :content="selText" class="top-popover-select-title"/> -->
                                        <span class="top-popover-select-title">{{ selText1 }}</span>
                                        <span class="top-popover-select-icon"
                                            :class="{ 'rotate': showTypePopover, 'rotate1': !showTypePopover }">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                                viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                                aria-hidden="true" focusable="false" class="">
                                                <path id="comm_icon_fh"
                                                    d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                                    transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                            </svg>
                                        </span>
                                    </div>
                                </template>
                            </van-popover>
                        </div>
                        <div class="informations">
                            <div>Renda Acumulada</div>
                            <span class="currencyAmount">{{ transf(totalInterest) }}</span>
                        </div>
                    </div>
                    <div class="detail-container">
                        <div class="belowDiv"> <!--下面显示-->
                            <div class="below_itemTitle">
                                <label class="below_itemTitle_aposta">Horas</label>
                                <label class="below_itemTitle_aposta">Tipo</label>
                                <label class="below_itemTitle_aposta">Quantia</label>
                            </div>

                            <div v-if="RecPiggyBankItemArray?.length > 0">
                                <div v-for="(data, index) in RecPiggyBankItemArray" :key="index"
                                    class="below_itemDevPa">
                                    <label class="itemContent">{{ getTime(data.created_at) }}</label>
                                        <label class="itemContent">{{ getTypeName(data.type) }}</label>
                                        <label v-if="data.type == 0" class="itemContent gold">{{
                                            transf(-Number(data.amount)) }}</label>
                                        <label v-if="data.type == 1" class="itemContent gold">{{
                                            transf(Number(data.amount)) }}</label>
                                        <label v-if="data.type == 2" class="itemContent finance">{{
                                            transf(Number(data.amount)) }}</label>
                                </div>

                                <!-- <AppList class="app-List" :loading="runApiGetPiggyBankChangeInfoLoading" style="padding-top: 0px;">
                                    <div class="below_itemDevPa" v-for="(data, index) in RecPiggyBankItemArray"
                                        :key="index">
                                        <label class="itemContent">{{ getTime(data.created_at) }}</label>
                                        <label class="itemContent">{{ getTypeName(data.type) }}</label>
                                        <label v-if="data.type == 0" class="itemContent gold">{{
                                            transf(-Number(data.amount)) }}</label>
                                        <label v-if="data.type == 1" class="itemContent gold">{{
                                            transf(Number(data.amount)) }}</label>
                                        <label v-if="data.type == 2" class="itemContent finance">{{
                                            transf(Number(data.amount)) }}</label>
                                    </div>
                                </AppList> -->
                            </div>
                            <div v-else>
                                <div class="content">
                                    <app-empty text="Sem Registros" />
                                </div>
                            </div>

                        </div>
                    </div>
                </section>
            </van-tab>
            <van-tab title="Regras de juros">
                <section class="rule-container">
                    <div class="rule-component">
                        <div class="style">1.<strong>Introdução aos ganhos:</strong> O valor depositado no tesouro de
                            juros deve
                            cumprir um ciclo completo para gerar juros. Se for sacado antes, o ciclo não contará para os
                            lucros. Por
                            exemplo, se o período atual de liquidação for de 1 horas, o valor transferido em 2023-01-01
                            00:00:01
                            gerará o primeiro ciclo de juros em 2023-01-01 01:00:01;</div>
                        <div class="style">2.<strong>Ciclo de Liquidação:</strong> O período atual de liquidação de
                            juros é de 1
                            horas;</div>
                        <div class="style">3.<strong>Taxa de Juros Anual: </strong>A taxa percentual anual atual é{{(Number(userPiggyBankInfo?.Return) * 100)}}%;
                        </div>
                        <div class="style">4.<strong>Fórmula de cálculo:</strong> Lucro dos juros = valor do depósito *
                            taxa de
                            juros anual / período de liquidação;</div>
                        <div class="style">5.<strong>Exemplo:</strong>Um 10.000 depositado em 2023-01-01 00:00:01, a
                            taxa de juros
                            anual é {{ (Number(userPiggyBankInfo?.Return) * 100) }}% e o período de liquidação é de 1 horas, então a primeira receita de juros é
                            obtido em
                            2023-01-01 01:00:01 , o método de cálculo é o seguinte:<strong>Primeiros juros
                                =10.000*{{ (Number(userPiggyBankInfo?.Return) * 100) }}%/365 dias/24
                                horas*1 horas = 1.141552</strong>;</div>
                        <div class="style">6.<strong>Limiar de entrada:</strong> O valor de cada transferência deve ser
                            maior ou
                            igual a 20 (ou seja, ≥ 20), sem limite superior no valor da transferência. Quanto mais você
                            transfere,
                            maior o lucro;</div>
                        <div class="style">7.<strong>Limite de rendimentos: </strong>No momento, o limite de rendimentos
                            permite
                            acumular em até 1 ciclos. Após esse período, não haverá mais geração de rendimentos. Para
                            evitar a perda
                            de rendimentos, lembre-se de receber seus lucros periodicamente!</div>
                        <div class="style">8.<strong>Tempo de reivindicação:</strong> Pode resgatar no próximo dia, o
                            que significa
                            que os juros gerados no mesmo dia podem ser reivindicados após 00:00 do próximo dia.</div>
                        <div class="style">9.<strong>Múltiplo de auditoria:</strong> O múltiplo de auditoria atual é 1
                            vezes
                            (requisitos de volume de apostas), ou seja, os juros recebidos precisam ser colocados antes
                            que o
                            dinheiro possa ser sacado, aposta Nenhuma plataforma válida específica,Apenas os juros
                            recebidos requepx
                            auditoria, enquanto o valor principal transferido para dentro ou para fora não requer
                            auditoria;</div>
                        <div class="style">10.<strong>Declaração do evento: </strong> Esta função é exclusiva para
                            apostas em jogos
                            realizadas pelo próprio titular da conta. Alugar, usar bots, robôs, apostas entre contas
                            diferentes,
                            apostas mútuas, arbitragem, acesso à API, exploração de falhas, controle de grupo ou outros
                            meios
                            técnicos são proibidos.O controle de grupo ou outros meios técnicos são proibidos. Uma vez
                            verificado, a
                            plataforma reserva o direito de encerrar os logins dos membros, suspender o uso do site
                            pelos membros e
                            confiscar bônus e lucros impróprios sem aviso prévio;</div>
                        <div class="style">11.<strong>Explicação:</strong> Quando um membro recebe a recompensa Yibao, a
                            plataforma
                            presumirá que o membro concorda e cumpre as condições correspondentes e outros regulamentos
                            relevantes.
                            Para evitar ambiguidade na compreensão do texto, a plataforma reserva-se o direito. para a
                            interpretação
                            final deste evento.</div>
                    </div>
                </section>
            </van-tab>
        </van-tabs>

    </section>
    <!-- <div class = "content">
        <app-empty text="Sem Registros"/>
    </div> -->

</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.spin-container {
    .detail-content {
        background-color: var(--theme-main-bg-color);
        border-radius: 0;
        padding: 40px 20px 20px;

        .row-flex {
            margin-bottom: 10px;
            align-items: center;
            justify-content: space-between;
            display: flex;
            flex-flow: row wrap;

            .col {
                align-items: center;
                display: flex;
                justify-content: flex-start;
                box-sizing: border-box;
                font-size: 20px;
                line-height: normal;

                span {
                    color: var(--theme-text-color-lighten);
                    font-size: 20px;
                    line-height: 1.45;

                    &:first-child {
                        display: inline-block;
                        margin-right: 10px;
                        max-width: none;
                        white-space: pre-wrap;
                    }
                }

                .deposit-amount {
                    span {
                        color: var(--theme-text-color);
                        font-size: 30px;
                        font-weight: 700;
                        height: 43px;
                        margin-right: 10px;
                        box-sizing: border-box;
                        position: relative;
                    }

                    .tool-tips {
                        height: 32px;
                        left: 72%;
                        position: absolute;
                        right: unset;
                        top: -20px;
                        width: fit-content;
                        z-index: 99;
                        box-sizing: border-box;

                        &-box {
                            background-color: var(--theme-secondary-color-finance) !important;
                            font-size: 18px;
                            height: 26px;
                            line-height: 26px;
                            padding: 0 8px;
                            border-radius: 12.5px 12.5px 12.5px 0;
                            color: #fff;
                            width: 100%;

                            span {
                                margin-right: 10px;
                                color: var(--theme-font-on-background-color);
                                font-size: 14px;
                                font-weight: 400;
                                white-space: nowrap;
                                height: 26px;
                                line-height: 26px;
                                padding: 0 8px;
                            }
                        }

                        &-tail {
                            border-left-color: var(--theme-secondary-color-finance) !important;
                            border-bottom: 7px solid transparent;
                            border-left: 7px solid var(--theme-secondary-color-error);
                            border-right: 7px solid transparent;
                            height: 0;
                            width: 0;
                        }
                    }
                }

                .curIncomeItem {
                    color: var(--theme-text-color-lighten);
                    display: flex;
                    font-size: 20px;
                    line-height: 1.45;
                    margin-bottom: 21px;
                    width: 100%;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    span {
                        color: var(--theme-text-color);
                    }

                    strong {
                        color: var(--theme-secondary-color-finance);
                    }
                }

            }

            .col-end {
                display: flex;
                position: relative;
                justify-content: flex-end;

                .btn {
                    font-size: 20px;
                    height: auto;
                    min-height: 50px;
                    padding: 5px;
                    width: 120px;
                    word-break: break-word;
                    text-align: center;
                    white-space: nowrap;
                    display: inline-block;
                    border: 1px solid #d9d9d9;
                    box-shadow: 0 2px 0 rgba(0, 0, 0, .015);
                    font-weight: 400;
                    border-radius: 14px;
                    span{
                        line-height: 1.45!important;
                    }
                    &:nth-child(2) {
                        margin-left: 20px;
                    }
                    .countdown{
                        font-size: 14px;
                        letter-spacing: 1px;
                        line-height: 1.5;
                        margin-top: -4px;
                        color: var(--theme-disabled-font-color);
                    }
                }

                .finance {
                    background-color: var(--theme-secondary-color-finance);
                    border-color: var(--theme-secondary-color-finance);

                    span {
                        color: var(--theme-font-on-background-color) !important;
                    }
                }
                .btn-receber{
                    background-color: var(--theme-secondary-color-success);
                    border-color: var(--theme-secondary-color-success);

                    span {
                        color: var(--theme-font-on-background-color) !important;
                    }
                }
                .primary {
                    background-color: var(--theme-primary-color);
                    border-color: var(--theme-primary-color);
                    color: var(--theme-primary-font-color);
                }

                .disabled {
                    background-color: #999 !important;
                    border-color: #999 !important;
                    color: var(--theme-disabled-font-color) !important;
                }
            }
        }
    }
}

.data-container {
    .data-container-main {
        .selection-container {
            align-items: center;
            display: flex;
            box-sizing: border-box;
            justify-content: space-between;
            padding: 15px 20px;
            color: var(--theme-text-color);
            font-size: 14px;

            .selection-component {
                // width: 100%;
                // height: 90px;
                // padding: 20px;
                display: flex;
                align-items: center;
                // justify-content: space-between;

                .content-body-top-popover {
                    width: 180px;
                    height: 50px;
                }

                .content-body-top-popover-select {
                    max-width: 220px;
                    // width: 180px;
                    width: auto;
                    height: 50px;
                    border-radius: 25px;
                    display: flex;
                    align-items: center;
                    // justify-content: space-between;
                    flex-direction: row;
                    /* 水平排列 */
                    position: relative;
                    background-color: var(--theme-main-bg-color) !important;

                    &.viewOpen {
                        border-color: var(--theme-primary-color);
                    }

                    margin-right: 20px;
                }

                .top-popover-select-title {
                    color: var(--theme-text-color-lighten) !important;
                    font-size: 20px;
                    line-height: 48px;
                    padding-left: 18px;
                    padding-right: 40px;
                    padding-top: 2px;
                    width: auto;
                    height: 48px;
                    white-space: nowrap;
                    max-width: 180px;
                }

                .rotate {
                    animation: spin 0.3s linear 0s 1;
                    transform: rotate(180deg);
                }

                .rotate1 {
                    transform: rotate(180deg);
                    animation: spin1 0.3s linear 0s 1;
                    transform: rotate(0deg);
                }

                .top-popover-select-icon {
                    // position: absolute;
                    width: 20px;
                    height: 20px;
                    right: 15px;
                    margin-right: 18px;

                    svg {
                        width: 20px;
                        height: 20px;
                        color: var(--theme-text-color-lighten);
                        position: absolute;

                    }
                }
            }

            .informations {
                align-items: flex-end;
                font-size: 22px;
                color: var(--theme-text-color-lighten);
                display: flex;

                span {
                    padding-left: 4px;
                    color: var(--theme-secondary-color-finance);
                }
            }
        }

        .detail-container {
            
            .belowDiv {
                margin-top: 10px;
                width: 750px;
                position: relative;
                // background: var(--theme-main-bg-color);
                border-radius: 15px;
                margin-bottom: 50px;

                .below_title {
                    display: block;
                    margin-top: 25px;
                    margin-left: 25px;

                    font-family: Arial;
                    font-weight: 700;
                    color: var(--app-ratio-title-color);
                    font-size: 24px;
                }

                .below_itemTitle {
                    display: flex;
                    margin: 0 auto;
                    // margin-top: 20px;
                    width: 708px;
                    height: 80px;
                    background: var(--theme-main-bg-color);
                    border-radius: 8px;
                    border: 2px solid;
                    border-color: var(--theme-color-line);
                }

                .below_itemImg {
                    width: 41px;
                    height: 41px;
                    margin-left: 20px;
                    margin-top: 15px;
                }

                .below_itemTitle_aposta {
                    // margin-left: 105px;
                    margin-top: 28px;

                    font-family: Arial;
                    color: var(--theme-text-color);
                    // font-weight: 700;
                    font-size: 24px;
                    width: 354px;
                    text-align: center;
                }

                .below_itemDevPa {
                    display: flex;
                    // flex-direction: column;
                    align-items: center;
                    width: 708px;
                    height: 70px;
                    line-height: 70px;
                    margin: 0 auto;
                    // margin-top: 20px;
                    // margin-bottom: 20px;
                    // position: relative;

                    &:nth-child(2n) {
                        background: var(--theme-main-bg-color);
                        border-radius: 15px;
                        width: 708px;
                        height: 70px;
                        line-height: 70px;
                    }


                    .itemContent {
                        font-family: Arial;
                        color: var(--app-title-color);
                        font-size: 20px;
                        text-align: center;
                        transform: translateY(2px);
                        color: var(--theme-text-color-lighten);
                        width: 354px;
                        text-align: center;
                    }

                    .finance {
                        color: var(--theme-secondary-color-finance);
                    }

                    .gold {
                        color: var(--theme-text-color-darken);
                    }

                }
                .below-content {
                    height: calc(100% - 500px);
                }
            }
        }
    }
}

.rule-container {
    padding: 20px;
    box-sizing: border-box;
    display: block;
    font-size: 14px;
    color: var(--theme-text-color);

    .rule-component {
        // color: var(--theme-alt-neutral-2);
        box-sizing: border-box;
        font-size: 20px;
        margin-bottom: 40px;
        margin-top: 0;
        padding: 20px;
        background-color: var(--theme-main-bg-color);
        border: 1px solid var(--theme-color-line);
        border-radius: 10px;
        // height: 800px;
        // overflow-y: scroll;

        .style {
            word-wrap: break-word;
            line-height: 1.5;

            &:first-child {
                font-size: 22px;
            }

            &:not(:last-child) {
                margin-bottom: 30px;
            }

            strong {
                font-size: 22px;
            }
        }
    }
}

.box-border {
    border-style: solid;
    // border-color: var(--theme-main-bg-color);
    border-color: var(--theme-color-line);
    border-width: 2px;
    border-radius: 10px;
}

.content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45vh;
    /* 使用视口高度来使容器占满屏幕垂直空间 */
}
</style>
<style>
:root:root {
    --van-tab-text-color: var(--theme-btm-def-color);
    --van-tab-active-text-color: var(--theme-primary-font-color);
    --van-font-bold: 400;
    --van-tab-font-size: 22px;
    --van-tabs-line-height: 70px;
    --van-tabs-bottom-bar-color: #ffffff;
    --van-tabs-bottom-bar-height: 4px;

    --van-popover-action-width: 210px;
    --van-popover-action-height: 80px;

    .tabs-title .van-tabs__wrap {
        border-bottom: thin solid var(--theme-color-line);
        display: -ms-flexbox;
        /* display: flex; */
        /* height: auto; */
    }

    /* .tabs-title .van-tabs__line{
        width: auto !important;
    } */


}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>