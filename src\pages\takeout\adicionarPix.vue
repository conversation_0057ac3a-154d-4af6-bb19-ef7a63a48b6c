<script setup lang="ts" name="adicionarPix">

const router = useRouter()
const appStore = useAppStore();
const { adicionarPixDialogVisible, userInfo } = storeToRefs(appStore);

const inputRealNameRef = ref()
const inputPhoneRef = ref()
const inputEmailRef = ref()
const inputCNPJRef = ref()

const selectIndex = ref(1);
const showPopover = ref(false);


let actions = ref([{ text: "CPF", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "PHONE", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "EMAIL", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "CNPJ", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }])

let config = ref(15);
const isCPF = ref(false);
const isPhone = ref(false);
const isEmail = ref(false);
const isCNPJ = ref(false);
let selText = ref("");

function setConfigValue(){
    isCPF.value = false;
      isPhone.value = false;
      isEmail.value = false;
      isCNPJ.value = false;

      if ((config.value & 1) !== 0) {
        isCPF.value = true;
      } else if ((config.value & 2) !== 0) {
        isPhone.value = true;
      } else if ((config.value & 4) !== 0) {
        isEmail.value = true;
      } else if ((config.value & 8) !== 0) {
        isCNPJ.value = true;
      }

      // Update selText based on the boolean flags
      if (isCPF.value) {
        selText.value = "CPF";
      } else if (isPhone.value) {
        selText.value = "PHONE";
      } else if (isEmail.value) {
        selText.value = "EMAIL";
      } else if (isCNPJ.value) {
        selText.value = "CNPJ";
      }
}
setConfigValue()

const configOptions = [
    { text: "CPF", calssName: "1", bitValue: 1 },
    { text: "PHONE", calssName: "2", bitValue: 2 },
    { text: "EMAIL", calssName: "3", bitValue: 4 },
    { text: "CNPJ", calssName: "4", bitValue: 8 }
];

const popoverStatus = (isOpen: boolean, config: number) => {
    if (!isOpen) {
        actions.value = configOptions
            .filter(item => (config & item.bitValue) !== 0)
            .map(item => ({
                text: item.text,
                calssName: item.calssName,
                color: selectIndex.value == parseInt(item.calssName) ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)"
            }));
    }
}


const onSelect = (action: any) => {
    selText.value = action.text;
    isCPF.value = (action.text == "CPF")
    isPhone.value = (action.text == "PHONE")
    isEmail.value = (action.text == "EMAIL")
    isCNPJ.value = (action.text == "CNPJ")
    selectIndex.value = Number(action.calssName)

}

// const { userNameReg } = useRegExpUserName();
const { phoneReg } = useRegExpPhone();

const fromData = reactive({
    update_type: 2,   // 1 新增密码 2 修改旧密码 3 忘记密码，通过验证码重置
    ty: 1,           // 1 手机号 2 邮箱的验证码
    sid: '',
    ts: '',
    code: '',
    confirm_password: '',
    password: '',
    old: '',
    phone: '',
    real_name: '',
    show_name: '',
    real_cnpj: '',
    show_cnpj: '',
    email: ''
})

// showToast("111")
// watchEffect(() => {
//     if (userInfo.value.phone) {
//         fromData.phone = userInfo.value.phone
//     }
// })

// const { run: runResetPwd, loading: resetLoading } = useRequest(() => ApiResetPwd(fromData), {
//   manual: true,
//   onSuccess: () => {
//     appStore.setFindPasswordDialogVisible(false)
//     // localStorage.clear()
//     location.replace(location.origin)
//   }
// })

//关闭界面
const closePage = () => {
    appStore.setAdicionarPixDialogVisible(false)
}


const {run: runGetPlatformLinkData,data:platformLinkData } = useRequest(() => ApiGetPlatformLinkData(), {
  manual: true,
  onSuccess(res:any) {
    console.log("数据="+res.CfgWithdrawPixType)
    if (res && res.CfgWithdrawPixType !== undefined) {
        config.value = res.CfgWithdrawPixType;
    }
    setConfigValue()
  },
})
runGetPlatformLinkData();



const onClickCPF = () => {
    fromData.real_name = fromData.show_name.replace(/[.\-]/g, '')
    if (fromData.real_name.length != 11) return;
    runGetInsertCPF()
}

const onCLickCNPJ = () => {
    fromData.real_cnpj = fromData.show_cnpj.replace(/[.\/-]/g, '')
    console.log(fromData.real_cnpj)
    if (fromData.real_cnpj.length != 14) return;
    runGetInsertCNPJ()
}

const onClickPhone = () => {
    inputPhoneRef.value.validation()
    if (!inputPhoneRef.value.isValid) {
        return;
    }

    runGetInsertPhone()
}

const onClickEmail = () => {
    inputEmailRef.value.validation()
    if (!inputEmailRef.value.isValid) {
        return;
    }
    runGetInsertEmail()
}


const { run: runGetInsertCPF } = useRequest(() => ApiGetInsertBankParam({ realname: "", pix_id: fromData.real_name, flag: '1', pix_account: fromData.real_name }), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
        console.log(data)
        showToast('Adicionado com éxito');
        closePage()
        appStore.runGetUserBanks()

    }
})

const { run: runGetInsertPhone } = useRequest(() => ApiGetInsertBankParam({ realname: "", pix_id: fromData.phone, flag: '2', pix_account: fromData.phone }), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
        console.log(data)
        showToast('Adicionado com éxito');
        closePage()
        appStore.runGetUserBanks()
    }
})

const { run: runGetInsertEmail } = useRequest(() => ApiGetInsertBankParam({ realname: "", pix_id: fromData.email, flag: '3', pix_account: fromData.email }), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
        console.log(data)
        showToast('Adicionado com éxito');
        closePage()
        appStore.runGetUserBanks()
    }
})

const { run: runGetInsertCNPJ } = useRequest(() => ApiGetInsertBankParam({ realname: "", pix_id: fromData.real_cnpj, flag: '4', pix_account: fromData.real_cnpj }), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
        console.log(data)
        showToast('Adicionado com éxito');
        closePage()
        appStore.runGetUserBanks()
    }
})


const onInputChange = (data) => {

    if (fromData.show_name != "" && data.data) {
        const result = fromData.show_name.replace(/[.\-]/g, '')
        const a = result.replace(/(\w{3})|(\d{3})/g, '$1.')
        if (a.length > 11) {
            const s = a.slice(0, 11)
            const s2 = a.slice(12, 14)
            fromData.show_name = s + "-" + s2;
        }
        else {
            fromData.show_name = a;
        }
    }

}

const onInputCNPJChange = (data) => {
    console.log(data.data)
    if (fromData.show_cnpj != "" && data.data) {
        const result = fromData.show_cnpj.replace(/\D/g, '')
        let part1, part2, part3, part4, part5;
        if (result.length < 2) fromData.show_cnpj = result; // 如果长度小于2，直接返回
        else if (result.length < 5) {
            part1 = result.slice(0, 2); // 取前2位
            part2 = result.slice(2, 5); // 取接下来的3位
            fromData.show_cnpj = `${part1}.${part2}`;
        }
        else if (result.length < 8) {
            part1 = result.slice(0, 2); // 取前2位
            part2 = result.slice(2, 5); // 取接下来的3位
            part3 = result.slice(5, 8); // 取接下来的3位
            fromData.show_cnpj = `${part1}.${part2}.${part3}`;
        }
        else if (result.length < 12) {
            part1 = result.slice(0, 2); // 取前2位
            part2 = result.slice(2, 5); // 取接下来的3位
            part3 = result.slice(5, 8); // 取接下来的3位
            part4 = result.slice(8, 12); // 取接下来的4位
            fromData.show_cnpj = `${part1}.${part2}.${part3}/${part4}`;
        }
        else {
            part1 = result.slice(0, 2); // 取前2位
            part2 = result.slice(2, 5); // 取接下来的3位
            part3 = result.slice(5, 8); // 取接下来的3位
            part4 = result.slice(8, 12); // 取接下来的4位
            if (result.length < 14) {
                part5 = result.slice(12); // 取最后2位
            }
            else {
                part5 = result.slice(12, 14); // 取最后2位
            }
            fromData.show_cnpj = `${part1}.${part2}.${part3}/${part4}-${part5}`;
        }
    }

}




const themeVars = reactive({
    popoverActionWidth: '660px',
    ActionWidth: '660px',
    width: '660px',
});

watch(adicionarPixDialogVisible, () => {
    fromData.real_name = "";
    fromData.phone = "";
    fromData.email = "";
    fromData.show_name = "";
    fromData.show_cnpj = "";
    fromData.real_cnpj = "";
    //onSelect(actions);
    actions.value = configOptions
        .filter(item => (config.value & item.bitValue) !== 0)
        .map(item => ({
            text: item.text,
            calssName: item.calssName,
            color: selectIndex.value == parseInt(item.calssName) ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)"
        }));
    
});

</script>

<template>
    <van-config-provider :theme-vars="themeVars">
        <van-popup class="tip-poup" v-model:show="adicionarPixDialogVisible" round>
            <div class="content">
                <label style="color: var(--theme-text-color-lighten);">Adicionar Pix</label>
                <div class="page-tab-panel">
                    <section class="page-tab-panel-content">
                        <div class="panel-content-header">
                            <van-popover class="adicionarPix-content-header-popover" v-model:show="showPopover"
                                :actions="actions" @select="onSelect" @open="popoverStatus(true, config)"
                                @close="popoverStatus(false, config)" placement="bottom-start" :show-arrow=false>
                                <template #reference>
                                    <div class="content-header-popover-select box-border"
                                        :class="{ 'viewOpen': showPopover }">
                                        <van-text-ellipsis :content="selText" class="content-header-select-title" />
                                        <span class="content-header-select-icon"
                                            :class="{ 'rotate-svg': showPopover, 'rotate-svg1': !showPopover }">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                                viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                                aria-hidden="true" focusable="false" class="">
                                                <path id="comm_icon_fh"
                                                    d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                                    transform="translate(-************.68) rotate(-90)"></path>
                                            </svg>
                                        </span>
                                    </div>
                                </template>
                            </van-popover>
                            <AppImage class="icon_left" src="icons/input-user-icon.webp" alt="" />
                        </div>
                    </section>
                </div>


                <AppInput v-if="isPhone" class="idIputPIX" icon-with="25" v-model="fromData.phone" :pattern="phoneReg"
                    ref="inputPhoneRef" icon-left="icon-pix.webp" type="text" placeholder="Introduza a sua chave do PIX"
                    msg="A chave por telefone da conta PIX não pode começar com 0" :err-height="42" clearable
                    width="662" height="70" maxlength="11" :style-obj="{
                        background: 'transparent',
                        color: '#000',
                        borderRadius: 'var(--app-px-10)',

                    }">
                </AppInput>

                <AppInput v-if="isEmail" class="idIputPIX" icon-with="25" v-model="fromData.email" :pattern="emailReg"
                    ref="inputEmailRef" icon-left="icon-pix.webp" type="text" placeholder="Introduza a sua chave do PIX"
                    msg="Por favor, insira um formato válido de email" :err-height="42" clearable width="662"
                    height="70" maxlength="11" :style-obj="{
                        background: 'transparent',
                        color: '#000',
                        borderRadius: 'var(--app-px-10)',

                    }">
                </AppInput>

                <AppInput v-if="isCPF" class="idIput" icon-with="25" v-model="fromData.show_name" :pattern="pixExpReg"
                    ref="inputRealNameRef" icon-left="user-card-id.webp" type="text"
                    placeholder="Introduza o número de 11 dígitos do CPF" msg="Deve ser números puros de 11 bits"
                    :err-height="42" clearable width="662" height="70" maxlength="11" @input="onInputChange" :style-obj="{
                        background: 'transparent',
                        color: '#000',
                        borderRadius: 'var(--app-px-10)',

                    }">
                </AppInput>

                <AppInput v-if="isCNPJ" class="idIputPIX" icon-with="25" v-model="fromData.show_cnpj"
                    :pattern="cnpjExpReg" ref="inputCNPJRef" icon-left="user-card-id.webp" type="text"
                    placeholder="Introduza o número de 14 dígitos do CNPJ" msg="Deve ser números puros de 14 bits"
                    :err-height="42" clearable width="662" height="70" maxlength="18" @input="onInputCNPJChange"
                    :style-obj="{
                        background: 'transparent',
                        color: 'var(--theme-text-color-darken)',
                        borderRadius: 'var(--app-px-10)',

                    }">
                </AppInput>

                <p class="tip-poup-content">
                Verifique cuidadosamente, caso contrário, não será creditado.
                </p>
                <AppButton class="proximo" @click="() =>{
                    if(isCPF)onClickCPF()
                    if(isPhone)onClickPhone()
                    if(isEmail)onClickEmail()
                    if(isCNPJ)onCLickCNPJ()
                }" width="95%" height="70" yellow :radius="15"
                    fontSize="15px" color="var(--theme-bg-color)">
                    Confirmar
                </AppButton>
                <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp" alt="" @click="() => {
                    closePage()
                }" />
            </div>
        </van-popup>
    </van-config-provider>

</template>

<style lang="scss" scoped>
//@import '../theme/mixin.scss';
.idIput {
    margin-left: 20px;
}

.idIputPIX {
    margin-left: 20px;
    margin-bottom: 50px;
}

.tip-poup-content{
    color: var(--theme-text-color-lighten);
    font-size: 20px;
    text-align: left;
    margin-left: 22px;
    margin-top: 50px;
    line-height: 1.5;
}

.adicionarPix-content-header-popover {
    width: 660px;
    height: 150px;

}

.page-tab-panel {
    //display: block;
    width: 710px;
    height: 100px;
    color: var(--theme-text-color);
    font-size: 14px;
    margin-top: 30px;
    margin-bottom: 40px;
    //background-color: aqua;

    .page-tab-panel-content {
        width: 100%;
        height: 200px;
        padding: 0 20px 20px;

        //overflow: auto;
        //background-color: aqua;
        .icon_left {
            position: absolute;
            left: 38px;
            width: 25px;
        }
    }

    .panel-content-header {
        width: 680px;
        height: 90px;
        //overflow: auto;
        //overflow-x: scroll;
        color: var(--theme-text-color);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 22px;
        //background-color: aqua;
    }



    .box-border {
        border-style: solid;
        border-color: var(--theme-color-line);
        border-width: 2px;
        border-radius: 10px;

    }


    .content-header-popover-select {
        width: 660px;
        height: 70px;
        //margin-right: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        background-color: var(--theme-main-bg-color);

        &.viewOpen {
            border-color: var(--theme-primary-color);
        }

    }

    .content-header-select-title {
        color: var(--theme-text-color);
        font-size: 24px;
        line-height: 48px;
        padding-left: 60px;
        text-align: left;
        width: 401px;
        height: 48px;

        //background-color:aqua;
    }

    @-webkit-keyframes spin0 {
        from {
            -webkit-transform: rotate(0deg);
        }

        to {
            -webkit-transform: rotate(180deg);
        }
    }

    @keyframes spin0 {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(180deg);
        }
    }

    @-webkit-keyframes spin1 {
        from {
            -webkit-transform: rotate(180deg);
        }

        to {
            -webkit-transform: rotate(0deg);
        }
    }

    @keyframes spin1 {
        from {
            transform: rotate(180deg);
        }

        to {
            transform: rotate(0deg);
        }
    }

    .rotate-svg {
        animation: spin0 0.3s linear 0s 1;
        transform: rotate(180deg);
    }

    .rotate-svg1 {
        transform: rotate(180deg);
        animation: spin1 0.3s linear 0s 1;
        transform: rotate(0deg);
    }

    .content-header-select-icon {
        position: absolute;
        width: 20px;
        height: 20px;
        right: 15px;
        display: flex;

        svg {
            width: 20px;
            height: 20px;
            color: var(--theme-text-color-lighten);
            position: absolute;

        }
    }

    .content-header-total {
        //background-color: aqua;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--theme-text-color-lighten);
        text-align: right;
        margin-left: 20px;
        font-size: 22px;
        line-height: normal;
        -webkit-font-smoothing: antialiased;

        span {
            font-size: 22px;
            color: var(--theme-text-color-darken);
            margin-left: 5px;
        }
    }

    .content-header-total-img-box {
        width: 30px;
        height: 30px;
        display: inline-block;
        margin-left: 10px;

        .rotate {
            animation: spin 1s linear infinite;
        }
    }

    .content-header-total-img {
        height: 30px;
        width: 30px;
    }
}

.tip-poup {
    width: 750px;
    height: 100vh;
    display: flex;
    align-items: center;
    //flex-direction: column;
    position: absolute;
    justify-content: center;
    overflow: auto;

    //background-color: #0ED1F4;
    .content {
        width: 710px;
        //height: 'var(--app-px--${divHeight})';
        border-radius: 15px;
        background-color: var(--theme-main-bg-color);
        border: 2px var(--theme-text-color-lighten) solid;
        text-align: center;

        label {
            position: relative;
            top: 20px;
            color: white;
            font-size: 28px;
        }

        .levantamento {
            width: 660px;
            margin: 100px 0 0 30px;
            //margin-top: 80px;
            text-align: left;
            color: white;
            font-size: 20px;
            line-height: 1.5;
            display: flex;

            .Senha {
                color: var(--theme-text-color-lighten);
                position: relative;
                width: 270px;
                text-align: right;
                font-size: 22px;
            }
        }

        .proximo {
            margin: 60px 25px 30px 20px;
            background-color: var(--theme-primary-color);
        }

        .passwordInput {
            position: relative;
            top: 40px;
            //scale: 0.9;
        }

    }

    .tip {
        padding-top: 30px;
        font-size: 24px;
        color: var(--theme-text-color-lighten);
        //          display: block;
        text-align: center;

    }

    // .content {
    //     //width: 100%;
    //     height: 180px;
    //     position: relative;
    //     margin: 20px 40px;
    //     text-align: center;
    //     font-size: 21px;
    //     line-height: 30px;
    //     color: white;
    // }

    .close-btn {
        position: absolute;
        width: 56px;
        height: 56px;
        left: 45%;
        margin-top: 20px;
        //background-color: aqua;
        //top: 500px;
    }
}
</style>


<style lang="scss">
:root:root {
    .adicionarPix-content-header-popover .van-popover__action {
        color: var(--theme-text-color-lighten);
        font-size: 24px;
        border-style: none;
        align-items: left;
        width: 660px;
    }

    // --van-popover-action-width: 660px;
    .adicionarPix-content-header-popover .van-popover--light {
        background: var(--theme-main-bg-color) !important;
    }

    .adicionarPix-content-header-popover .van-popover__content {
        background: var(--theme-main-bg-color) !important;
        border-color: var(--theme-color-line);
        border-width: 2px;
        border-radius: 14px;
        border-style: solid;
    }

    .adicionarPix-content-header-popover .van-popover__action {
        color: var(--theme-text-color-lighten);
        font-size: 24px;
        border-style: none;
        align-items: left;
    }

    .adicionarPix-content-header-popover .van-popover__action-text {
        justify-content: left
    }

    .adicionarPix-content-header-popover .van-popover__action:active {
        background-color: var(--theme-text-color-lighten);
    }

    .adicionarPix-content-header-popover .van-hairline--bottom:after {
        border-bottom-width: 0px;
    }
}
</style>
