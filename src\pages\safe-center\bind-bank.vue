<script lang="ts" setup name="BindBank">
const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)

const formdata = reactive({
  pix_id: '',
  pay_password: '',
  flag: '1',
  realname: '',
  pix_account: '',
})
const pixIdRef = ref()
const realNameRef = ref()
const cpfidRef = ref()
const payPwdRef = ref()
const selectRef = ref()

const flagList = [
  { label: 'CPF', value: '1' },
  { label: 'Telefone(+55)', value: '2' },
  { label: 'E-mail', value: '3' },
]
const currentFlag = computed(() => flagList.find(a => a.value === formdata.flag)?.label)
const toggleSelect = () => {
  selectRef.value.btnClick()
}
const accountType = computed(() => formdata.flag === '3' ? 'email' : 'number')

const { run: runBindBank, loading: bindLoading } = useRequest(() => ApiBindBank({
  flag: formdata.flag,
  pix_id: formdata.pix_id,
  pay_password: formdata.pay_password,
  realname: formdata.realname,
  pix_account: formdata.pix_account,
}), {
  manual: true,
  onSuccess: () => {
    appStore.runGetUserBanks()
    router.go(-1)
  }
})

const confirmFn = () => {
  if (pixIdRef.value.validation() && realNameRef.value.validation() && cpfidRef.value.validation() && payPwdRef.value.validation()) {
    runBindBank()
  }
}
</script>

<template>
  <div class="bind-bank">
    <AppHeader left-arrow title="Número da conta bancária" placeholder />
    <section class="content">
      <div class="top">
        <h3>Nome completo: {{ userInfo.username }}</h3>
      </div>
      <AppInput icon-with="56"  ref="pixIdRef" v-model="formdata.pix_id" placeholder="Por favor, preencha o número da conta bancária"
        icon-left="icon_bank_card" width="650" height="75" msg="Informe o CPF no formato correto" required type="number" err-height="34"
        clearable :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }" />
      <div class="divider"></div>
      <div class="info-bar mb-34">
        <label>Nome do usuário:</label>
        <AppInput ref="realNameRef" v-model="formdata.realname" plain align="right" width="auto"
          placeholder="Insira o nome do titular do cartão" msg="Insira o nome do titular do cartão" required :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }" />
      </div>
      <div class="divider"></div>
      <div class="info-bar mb-34">
        <label>Código CPF:</label>
        <AppInput ref="cpfidRef" v-model="formdata.pix_account" plain align="right" width="auto"
          placeholder="Insira o seu código CPF" msg="Informe o CPF no formato correto" required :type="accountType"
          clearable  :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"/>
      </div>
      <div class="divider"></div>
      <div class="info-bar mb-34" @click="toggleSelect">
        <label>Tipo Pix:</label>
        <div class="unit-input">
          <span class="unit">{{ currentFlag }}</span>
          <AppImage class="arrow-down" src="/icons/i-arrow-white-down.png" />
        </div>
        <AppSelect ref="selectRef" v-model="formdata.flag" :list-data="flagList" plain  :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"/>
      </div>
      <div class="divider"></div>
      <AppInput icon-with="38"  ref="payPwdRef" v-model="formdata.pay_password" width="650" height="75" :pattern="payPasswordReg" msg="Senha (6 letras e números)"
        placeholder="Digite sua senha atual." icon-left="icon_password" type="payPassword" err-height="34" clearable
        :max-length="6" :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }" />
      <div class="btn-box">
        <AppButton :loading="bindLoading"
          fontSize="36"
          radius="15"
          whiteText
          blue
          width="240"
          height="80"
          center
          @click="confirmFn">Enviar</AppButton>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.info-bar {
  width: 650px;
  height: 75px;
  line-height: 75px;
  margin: 0 auto;
  border-radius: 15px;
  // border: 1px solid rgba(255, 255, 255, 0.10);
  background: #192841;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 0 24px;
  position: relative;

  label {
    color: #FFF;
    font-size: 28px;
    white-space: nowrap;
    width: 250px;
  }

  .app-input-outer {
    flex: 1;
  }

  .unit-input {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .unit {
      padding-left: 20px;
      color: #fff;
      font-size: 28px;
    }
  }

  .info-title {
    color: #fff;
    font-size: 28px;
  }

  .arrow-down {
    width: 20px;
    margin-left: 20px;
  }
}

.fake-select {
  width: 710px;
  line-height: 72px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.10);
  background: linear-gradient(0deg, #011A51 0%, #011A51 100%), #2A2E3E;
  color: rgba(255, 255, 255, 0.40);
  font-size: 28px;
  font-weight: 400;
  padding-left: 32px;
  padding-right: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  img {
    width: 16px;
  }
}

.btn-box {
  padding-top: 66px;
}

.divider {
  height: 34px;
}

.bind-bank {
  .content {
    margin: 0 auto;
    width: 710px;
    height: 808px;
    background-color: #28374d;
    border-radius: 50px;
    margin-top: 40px;
    .top{
      width: 710px;
      height: 80px;
      line-height: 80px;
      background-color: #324b6e;
      color: #679fea;
      border-radius: 50px 50px 0px 0px;
      margin-bottom: 25px;
      text-indent: 67px;
    }
    h3 {
      color: #679fea;
      font-size: 24px;
      font-weight: 400;
      margin: 0;
    }
  }
}
</style>
