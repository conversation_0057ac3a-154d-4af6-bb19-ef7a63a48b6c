<script setup lang="ts" name="GameShowcase">
import { ref, computed } from 'vue'
import AppGameCardNew from '~/components/AppGameCardNew.vue'

// 模拟游戏数据
const gameList = ref([
  {
    id: '1',
    name: 'FORTUNE TIGER',
    image: '/img/games/fortune-tiger.webp',
    bgColor: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
    isFavorite: false,
    isHot: true
  },
  {
    id: '2',
    name: 'FORTUNE MOUSE',
    image: '/img/games/fortune-mouse.webp',
    bgColor: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '3',
    name: 'FORTUNE SNAKE',
    image: '/img/games/fortune-snake.webp',
    bgColor: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
    isFavorite: false,
    isHot: true
  },
  {
    id: '4',
    name: 'FORTUNE RABBIT',
    image: '/img/games/fortune-rabbit.webp',
    bgColor: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '5',
    name: 'FORTUNE DRAGON',
    image: '/img/games/fortune-dragon.webp',
    bgColor: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '6',
    name: 'FORTUNE OX',
    image: '/img/games/fortune-ox.webp',
    bgColor: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '7',
    name: 'TIGER GOLD',
    image: '/img/games/tiger-gold.webp',
    bgColor: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
    isFavorite: false,
    isHot: true
  },
  {
    id: '8',
    name: 'CASH MANIA',
    image: '/img/games/cash-mania.webp',
    bgColor: 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '9',
    name: 'MR. TREASURE\'S FORTUNE',
    image: '/img/games/mr-treasures.webp',
    bgColor: 'linear-gradient(135deg, #1F2937 0%, #374151 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '10',
    name: 'LUCKY PIGGY',
    image: '/img/games/lucky-piggy.webp',
    bgColor: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '11',
    name: 'WINGS OF IGUAZU',
    image: '/img/games/wings-iguazu.webp',
    bgColor: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)',
    isFavorite: false,
    isHot: false
  },
  {
    id: '12',
    name: 'WILD BOUNTY SHOWDOWN',
    image: '/img/games/wild-bounty.webp',
    bgColor: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
    isFavorite: false,
    isHot: false
  }
])

const totalGames = computed(() => gameList.value.length)

// 切换收藏状态
const toggleFavorite = (gameId: string) => {
  const game = gameList.value.find(g => g.id === gameId)
  if (game) {
    game.isFavorite = !game.isFavorite
  }
}

// 启动游戏
const launchGame = (game: any) => {
  console.log('启动游戏:', game.name)
  // 这里可以添加启动游戏的逻辑
}

// 查看更多
const showMore = () => {
  console.log('查看更多游戏')
  // 这里可以添加查看更多的逻辑
}
</script>

<template>
  <div class="game-showcase">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left">
        <div class="pg-logo">PG</div>
      </div>
      <div class="header-center">
        <span class="game-count">Todos {{ totalGames }}</span>
      </div>
      <div class="header-right">
        <button class="more-btn" @click="showMore">Mais</button>
      </div>
    </div>

    <!-- 游戏网格 -->
    <div class="game-grid">
      <AppGameCardNew
        v-for="game in gameList"
        :key="game.id"
        :data="game"
        @launch="launchGame"
        @toggle-favorite="toggleFavorite"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.game-showcase {
  min-height: 100vh;
  background: linear-gradient(180deg, #E8E3FF 0%, #F5F3FF 100%);
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  backdrop-filter: blur(10px);

  .header-left {
    .pg-logo {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 16px;
    }
  }

  .header-center {
    .game-count {
      font-size: 18px;
      font-weight: 600;
      color: #374151;
    }
  }

  .header-right {
    .more-btn {
      background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

.game-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 0 10px;
}

// 响应式设计
@media (max-width: 480px) {
  .game-showcase {
    padding: 15px;
  }

  .header {
    padding: 12px 15px;
    margin-bottom: 15px;
  }

  .game-grid {
    gap: 12px;
    padding: 0 5px;
  }
}
</style>

<route lang="yaml">
meta:
  layout: home
</route>
