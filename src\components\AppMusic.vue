<script setup lang='ts'>
import { GameNavEnum, CycleModeEnum, GameHallTopEnum } from '~/types/common';
// import Bonus from '~/pages/record-list/bonus.vue';
// import AppTab from './AppTab.vue';
//import { userInfo } from 'node:os';

const router = useRouter()
const appStore = useAppStore();
const { isApp, userInfo,isPlayingMusic,curCycleMode,showMusicPlayer } = storeToRefs(appStore)

const audioPlayer = ref(null);
const progress = ref(0);
const duration = ref(0);

const currentTime = ref(0);
const durationTime = ref('00:00');
const audioSrc = ref('');
const curMusicIndex = ref(0);
const curMusicId = ref(1);
const curMyMusicIndex = ref(0);
let myMusicArray = [1];
const IsOnlyOne = ref(true);



function setMusicData() {
    // localStorage.removeItem('myMusicArray');
    const storedArrayString = localStorage.getItem('myMusicArray');
    if (storedArrayString) {
        const storedArray = JSON.parse(storedArrayString);
        musicNum.value = storedArray.length;
        myMusicArray.splice(0);
        musicData1.value.splice(0);
        for (let i = 0; i < storedArray.length; i++) {
            let item = musicData.value.find(item => item.id == Number(storedArray[i]));
            item.state = MUSIC_STATE.Downloaded;
            myMusicArray.push(Number(storedArray[i]));
            musicData1.value.push(item)
        }
    } else {
        musicData.value[0].state = MUSIC_STATE.Downloaded;
        musicData1.value.push(musicData.value[0]);
    }
    audioSrc.value = 'music/' + musicData1.value[0].name + musicData1.value[0].format;
    curMusicId.value = musicData1.value[0].id;
    curMusicIndex.value = musicData1.value[0].id - 1;

    IsOnlyOne.value = false;
    if (myMusicArray.length == 1) {
        IsOnlyOne.value = true;
    }
    console.log("setMusicData");
}

function togglePlay() {
    isPlayingMusic.value = !isPlayingMusic.value; // 切换播放状态
    // 这里可以添加实际控制音乐播放的逻辑
    if (isPlayingMusic.value == true) {
        if (audioPlayer.value) {
            audioPlayer.value.play();
        }
    } else {
        if (audioPlayer.value) {
            audioPlayer.value.pause();
        }
    }
}

const onChange = (e) => {
    audioPlayer.value.currentTime = (audioPlayer.value.duration * progress.value) / 100;
};

const updateProgress = () => {
    if (audioPlayer.value.duration) {
        const newTime = audioPlayer.value.currentTime;
        currentTime.value = newTime.toFixed(2);
        progress.value = (newTime / audioPlayer.value.duration) * 100;
    }
};

const updateTime = () => {
    duration.value = audioPlayer.value.duration;
    durationTime.value = formatTime(duration.value);
};

onMounted(() => {
    setMusicData();
    setCurSongName();
    audioPlayer.value = new Audio(audioSrc.value);
    audioPlayer.value.addEventListener('timeupdate', updateProgress);
    audioPlayer.value.addEventListener('loadedmetadata', updateTime);
});

// 格式化时间函数
const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};


function previousSong() {
    // 添加逻辑以播放前一首歌曲
    if (curCycleMode.value == 1 || curCycleMode.value == 3) {
        curMyMusicIndex.value = (curMyMusicIndex.value - 1 + musicData1.value.length) % musicData1.value.length;
    } else if (curCycleMode.value == 2) {
        getRandomMusicIndex();
    }
    curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
    curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
    loadSong(musicData1.value[curMyMusicIndex.value]);
    setCurSongName();
}

function nextSong() {
    // 添加逻辑以播放下一首歌曲
    if (curCycleMode.value == 1 || curCycleMode.value == 3) {
        curMyMusicIndex.value = (curMyMusicIndex.value + 1) % musicData1.value.length;
    } else if (curCycleMode.value == 2) {
        getRandomMusicIndex();
    }
    curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
    curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
    loadSong(musicData1.value[curMyMusicIndex.value]);
    setCurSongName();
}

const playNextSong = () => {
    if (curCycleMode.value == 1) {
        curMyMusicIndex.value = (curMyMusicIndex.value + 1) % musicData1.value.length;
    } else if (curCycleMode.value == 2) {
        getRandomMusicIndex();
    }
    curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
    curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
    loadSong(musicData1.value[curMyMusicIndex.value]);
    setCurSongName();
}

function getRandomMusicIndex() {
    if (musicData1.value.length == 1) return;
    let randomIndex;
    do {
        randomIndex = Math.floor(Math.random() * musicData1.value.length);
    } while (randomIndex === curMyMusicIndex.value);
    curMyMusicIndex.value = randomIndex;
}

function musicItemClick(index, state) {
    if (state == MUSIC_STATE.Downloaded) {
        isPlayingMusic.value = true;

        if (index != curMusicIndex.value) {
            curMusicIndex.value = index;
            curMusicId.value = musicData.value[curMusicIndex.value].id;
            curMyMusicIndex.value = musicData1.value.findIndex(item => item.id == curMusicId.value);
            loadSong(musicData.value[curMusicIndex.value]);
            setCurSongName();
        } else {
            if (isPlayingMusic.value == true) {
                if (audioPlayer.value) {
                    audioPlayer.value.play();
                }
            } else {
                if (audioPlayer.value) {
                    audioPlayer.value.pause();
                }
            }
        }
    } else {
        handleDownloadClick(index);
    }

}

const downloadTime = ref([1000, 1500, 2000, , 2500, 3000])

const handleDownloadClick = (index) => {
    const item = musicData.value[index];
    let time = downloadTime.value[Math.floor(Math.random() * 5)];
    // console.log("downloadTime", time)
    if (item) {
        item.isLoading = true;

        // 模拟下载过程
        setTimeout(() => {
            // 下载完成
            item.isLoading = false;
            item.state = MUSIC_STATE.Downloaded;
            // alert('下载' + item.name + '完成');
            myMusicArray.push(item.id);
            musicData1.value.push(item);
            console.log(myMusicArray, musicData1)

            saveMyMusicArray();
            //下载完播放
            myMusicItemClick(musicData1.value.length - 1)
        }, time); // 假设下载需要2秒
    }
};

function myMusicItemClick(index) {
    isPlayingMusic.value = true;

    if (index != curMyMusicIndex.value) {
        curMyMusicIndex.value = index;
        curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
        curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
        loadSong(musicData1.value[curMyMusicIndex.value]);
        setCurSongName();
    } else {
        if (isPlayingMusic.value == true) {
            if (audioPlayer.value) {
                audioPlayer.value.play();
            }
        } else {
            if (audioPlayer.value) {
                audioPlayer.value.pause();
            }
        }
    }
}

function deleteSong(deleteIndex) {
    if (IsOnlyOne.value) return;
    if (deleteIndex == curMyMusicIndex.value) {

        myMusicArray.splice(deleteIndex, 1);
        musicData1.value[deleteIndex].state = MUSIC_STATE.Not_Downloaded;
        musicData1.value.splice(deleteIndex, 1);
        console.log(myMusicArray, musicData1, musicData);
        saveMyMusicArray();

        if (musicData1.value.length > 0) {
            curMyMusicIndex.value = 0;
            curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
            curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
            loadSong(musicData1.value[curMyMusicIndex.value]);
            setCurSongName();
        }

    } else {
        myMusicArray.splice(deleteIndex, 1);
        musicData1.value[deleteIndex].state = MUSIC_STATE.Not_Downloaded;
        musicData1.value.splice(deleteIndex, 1);
        console.log(myMusicArray, musicData1, musicData);
        saveMyMusicArray();
        //删完歌之后重新设置index
        curMyMusicIndex.value = musicData1.value.findIndex(item => item.id == curMusicId.value);
    }
}

function saveMyMusicArray() {
    // 将数组转换为 JSON 字符串
    const arrayString = JSON.stringify(myMusicArray);
    // 存储字符串到 LocalStorage
    localStorage.setItem('myMusicArray', arrayString);

    musicNum.value = myMusicArray.length;

    IsOnlyOne.value = false;
    if (myMusicArray.length == 1) {
        IsOnlyOne.value = true;
    }
}

const loadSong = song => {
    audioPlayer.value.src = 'music/' + song.name + song.format;
    audioPlayer.value.load();
    duration.value = audioPlayer.value.duration;
    currentTime.value = 0;
    progress.value = 0;
    if (isPlayingMusic.value == true) {
        if (audioPlayer.value) {
            audioPlayer.value.play();
        }
    }
};

function switchCycleMode() {
    if (curCycleMode.value < 3) {
        curCycleMode.value++
    } else {
        curCycleMode.value = 1;
    }
}

function getCycleModeName(mode: number) {
    let name = ""
    switch (mode) {
        case CycleModeEnum.Sequential_Loop:
            name = "Ciclo";
            break;
        case CycleModeEnum.Random_Cycle:
            name = "Aleatório";
            break;
        case CycleModeEnum.Single_Loop:
            name = "Repetir";
            break;
    }
    return name;
}

function openMusicList() {
    showMusicPlayer.value = !showMusicPlayer.value;
}

const songName = ref("");

function setCurSongName() {
    songName.value = musicData1.value[curMyMusicIndex.value].name;
}

function truncatedText(text: string, length: number) {
    return text.length > length ? text.substring(0, length) + '...' : text;
}



const isHoveringDownload = ref(false); // 用于追踪鼠标是否悬停在按钮上
const isHoveringSupport = ref(false); // 用于追踪鼠标是否悬停在按钮上
const isHoveringHelp = ref(false); // 用于追踪鼠标是否悬停在按钮上

const musicNum = ref(1);


const SECLET_TYPE = readonly({
    System_Music: "0",
    My_Music: "1",
})

const MUSIC_STATE = readonly({
    Not_Downloaded: 0,
    Downloaded: 1,
})
const secletType = ref(SECLET_TYPE.System_Music);

const tabData = ref([
    {
        label: 'Sistema de Musica',
        value: SECLET_TYPE.System_Music
    },
    {
        label: 'Minhas musicas',
        value: SECLET_TYPE.My_Music
    },
])

const musicData = ref([
    {
        id: 1,
        name: 'Dept _ Ashley Alisha - Waiting for You(feat. Ashley Alisha)',
        size: '2M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.ogg'
    },
    {
        id: 2,
        name: 'Mariah Carey - Without You',
        size: '2M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.ogg'
    },
    {
        id: 3,
        name: 'Nicky Jam _ Will Smith _ Era Istrefi - Live It Up (Official Song 2018 FIFA World Cup Russia)',
        size: '2M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.ogg'
    },
    {
        id: 4,
        name: 'Sexy Love',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 5,
        name: 'Shakira - 6.Waka Waka',
        size: '2M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 6,
        name: 'Shakira - 7.Try Everything (From Zootopia) [Official Music Video]',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 7,
        name: 'Shakira - 8.Whenever, Wherever (Official Music Video)',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 8,
        name: 'Shape of You',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 9,
        name: 'Sia-53.Chandelier',
        size: '937K',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 10,
        name: 'Sia-54.Cheap Thrills',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 11,
        name: 'Sia-55.Dusk Till Dawn',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 12,
        name: 'Sia-55.Move Your Body',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 13,
        name: 'Silver Scrapes',
        size: '2M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 14,
        name: 'Skin',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 15,
        name: 'Someone Like You',
        size: '',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 16,
        name: 'Something Just Like This',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 17,
        name: 'Soviet March',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 18,
        name: 'Taylor Swift - 103.Blank Space',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 19,
        name: 'Taylor Swift - 104.You Belong With Me',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 20,
        name: 'Taylor Swift - 105.Shake It Off',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 21,
        name: 'Taylor Swift - 106.Bad Blood ft. Kendrick Lamar',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 22,
        name: 'That Girl',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 23,
        name: 'The Chainsmokers、Coldplay - 15.Something Just Like This',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 24,
        name: 'The Chainsmokers-67.Closer',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 25,
        name: 'The Chainsmokers-68.Don_t Let Me Down',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 26,
        name: 'The Chainsmokers-69.Paris',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 27,
        name: 'The Days',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 28,
        name: 'The Fox',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 29,
        name: 'The Nights(Remix)',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 30,
        name: 'The Phoenix',
        size: '937K',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 31,
        name: 'Titanium',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 32,
        name: 'Toni Braxton - Yesterday',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.ogg'
    },
    {
        id: 33,
        name: 'Victory',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 34,
        name: 'Wait Wait Wait',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 35,
        name: 'Waiting for Love',
        size: '4M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 36,
        name: 'Will Smith-9.Live It Up',
        size: '3M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.mp3'
    },
    {
        id: 37,
        name: 'Wiz Khalifa _ Charlie Puth - See You Again',
        size: '2M',
        state: MUSIC_STATE.Not_Downloaded,
        isLoading: false,
        format: '.ogg'
    },
])

const musicData1 = ref([

])

const onTabChange = () => {
    if (secletType.value == SECLET_TYPE.System_Music) {
        // scrollToSystemItem(curMusicIndex.value);
    } else if (secletType.value == SECLET_TYPE.My_Music) {
        // scrollToMyItem(curMyMusicIndex.value);
    }
}



function downloadMusic(state: number) {
    return;
    showToast(state)
}

const scrollContainer = ref(null);
const systemRefs = ref([]);
const myRefs = ref([]);
const scrollToSystemItem = (index) => {

    if (!scrollContainer.value || !systemRefs.value[index]) return;
    showToast(index)

    scrollContainer.value.scrollTo({
        top: systemRefs.value[index].offsetTop,
        behavior: 'smooth'
    });
};

const scrollToMyItem = (index) => {

    if (!scrollContainer.value || !myRefs.value[index]) return;
    showToast(index)

    scrollContainer.value.scrollTo({
        top: myRefs.value[index].offsetTop,
        behavior: 'smooth'
    });
};

</script>
<template>
    <van-popup class="music-player-poup" v-model:show="showMusicPlayer" round>
        <div class="music-player">
            <span>Música</span>
            <div class="music-control">
                <span class="name">{{ truncatedText(songName, 60) }}</span>
                <div class="slider-content">
                    <span class="time">{{ formatTime(currentTime) }}</span>
                    <van-slider class="slider" v-model="progress" bar-height="6px" active-color="var(--theme-text-color)"
                        button-size="18px" @change="onChange" />
                    <span class="time1">{{ durationTime }}</span>

                </div>
                <div class="operate-content">
                    <div class="cycle-mode" @click="switchCycleMode">
                        <AppImage class="img" :src="`/img/leftMenu/music_cycle_mode${curCycleMode}.webp`" alt="" />
                        <span>{{ getCycleModeName(curCycleMode) }}</span>
                    </div>
                    <AppImage class="img1" src="/img/musicPlayer/music_previous_song.webp" alt=""
                        @click="previousSong" />
                    <AppImage class="img2"
                        :src="isPlayingMusic ? '/img/musicPlayer/music_pause.webp' : '/img/musicPlayer/music_play.webp'"
                        alt="" @click="togglePlay" />
                    <AppImage class="img1" src="/img/musicPlayer/music_next_song.webp" alt="" @click="nextSong" />
                    <div class="music-num">
                        <span :style="{ fontSize: '15px' }">{{ musicNum }}</span>
                        <span>Baixado</span>
                    </div>
                </div>

            </div>
            <div class="music-list">
                <div class="music-list-content">
                    <AppTab class="music-list-tab" :list-data="tabData" v-model="secletType" @change="onTabChange">
                    </AppTab>
                    <AppImage src="/img/musicPlayer/line.webp" alt="" />
                </div>
                <div class="content" ref="scrollContainer">
                    <div v-if="secletType == SECLET_TYPE.System_Music" class="content-system">
                        <div class="music-item" v-for="(data, index) in musicData" :key="index" ref="systemRefs"
                            @click="musicItemClick(index, data.state)">
                            <div v-if="curMusicIndex == index" class="music-info">
                                <!-- <span>{{ (index + 1)+ "&nbsp&nbsp" + data.name + "&nbsp&nbsp" + data.size }}</span> -->
                                <!-- <span class="child-style-1">{{ index + 1 }}</span> -->
                                <AppImage :style="{ width: '13.5px', height: '15.5px' }"
                                    src="/img/musicPlayer/music_icon.webp" alt="" />
                                <span class="child-style-4">{{ truncatedText(data.name, 31) }}</span>
                                <span class="child-style-5">{{ data.size }}</span>
                            </div>
                            <div v-else class="music-info">
                                <span class="child-style-1">{{ index + 1 }}</span>
                                <span class="child-style-2">{{ truncatedText(data.name, 31) }}</span>
                                <span class="child-style-3">{{ data.size }}</span>
                            </div>
                            <div class="music-state">
                                <!-- <span>{{ data.state }}</span> -->
                                <div v-if="data.isLoading" class="loader" />
                                <div v-else>
                                    <div v-if="data.state == 0">
                                        <AppImage class="state1" src="/img/musicPlayer/music_download.webp" alt=""
                                            @click="downloadMusic(data.state)" />
                                    </div>
                                    <div v-else>
                                        <AppImage class="state2" src="/img/musicPlayer/music_gou.webp" alt="" />
                                    </div>
                                </div>
                            </div>
                            <!-- <span> {{ data.name }}</span> -->
                        </div>
                    </div>
                    <div v-else-if="secletType == SECLET_TYPE.My_Music" class="content-my">
                        <div class="music-item" v-for="(data, index) in musicData1" :key="index" ref="myRefs"
                            @click="myMusicItemClick(index)">
                            <div v-if="curMusicId == data.id" class="music-info">
                                <!-- <span>{{ (index + 1)+ "&nbsp&nbsp" + data.name + "&nbsp&nbsp" + data.size }}</span> -->
                                <!-- <span class="child-style-1">{{ index + 1 }}</span> -->
                                <AppImage :style="{ width: '13.5px', height: '15.5px' }"
                                    src="/img/musicPlayer/music_icon.webp" alt="" />
                                <span class="child-style-4">{{ truncatedText(data.name, 31) }}</span>
                                <span class="child-style-5">{{ data.size }}</span>
                            </div>
                            <div v-else class="music-info">
                                <span class="child-style-1">{{ index + 1 }}</span>
                                <span class="child-style-2">{{ truncatedText(data.name, 31) }}</span>
                                <span class="child-style-3">{{ data.size }}</span>
                            </div>
                            <div class="music-operation">
                                <div class="music-state">
                                    <AppImage class="state2" src="/img/musicPlayer/music_gou.webp" alt="" />
                                </div>
                                <div class="delete">
                                    <AppImage :style="{ width: '15px', height: '15px' }"
                                        src="/img/musicPlayer/music_delete.webp" alt=""
                                        @click.stop="() => deleteSong(index)" />
                                </div>
                            </div>
                            <!-- <span> {{ data.name }}</span> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp" alt=""
            @click="() => {
                appStore.setShowMusicState(false) 
            }" />
    </van-popup>

</template>

<style lang='scss' scoped>
@import '../theme/mixin.scss';

.music-player-poup {
    width: 690px;
    height: 1200px;
    display: flex;
    // align-items: center;
    // flex-direction: column;
    position: absolute  ;
    justify-content: center;

    .close-btn {
        position: absolute;
        width: 56px;
        height: 56px;
        top: 95%;
    }
}

.music-player {
    width: 680px;
    height: 980px;
    background-color: var(--theme-primary-font-color);
    background-size: 690px 980px;
    text-align: center;
    position: absolute;
    display: flex;
    // justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 20px;
    border-radius: 20px;
    border: 2px solid var(--theme-text-color-lighten);
    top: 8%;

    span {
        position: relative;
        color: white;
        font-size: 28px;
    }

    .music-control {
        margin-top: 20px;
        position: relative;
        width: 588px;
        height: 224px;
        background-color: var(--theme-main-bg-color);
        border-radius: 16px;
        display: flex;
        justify-content: top;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        .name {
            padding-top: 22px;
            position: relative;
            color: white;
            font-size: 21px;
        }

        .slider-content {
            margin-top: 26px;
            // padding-top: 40px;
            position: relative;
            width: 588px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            /* 垂直居中 */
            flex-direction: row;

            .slider {
                width: 300px;
            }

            .time {
                padding-right: 50px;
                color: var(--theme-text-color-lighten);
                font-size: 20px;
            }

            .time1 {
                padding-left: 50px;
                color: var(--theme-text-color-lighten);
                font-size: 20px;
            }
        }

        .operate-content {
            margin-top: 26px;
            position: relative;
            width: 588px;
            height: 80px;
            display: flex;
            flex-direction: row;
            /* 水平布局 */
            justify-content: space-around;
            /* 子节点之间的间距平均分布 */
            align-items: center;

            /* 垂直居中对齐 */
            .cycle-mode {
                width: 100px;
                display: flex;
                justify-content: center;
                align-items: center;
                /* 垂直居中 */
                flex-direction: column;

                span {
                    font-size: 22px;
                    top: 0px;
                }
            }

            .music-num {
                display: flex;
                justify-content: center;
                align-items: center;
                /* 垂直居中 */
                flex-direction: column;

                span {
                    color: var(--theme-text-color-lighten);
                    font-size: 22px;
                }
            }

            .img {
                width: 30px;
                height: 30px;
            }

            .img1 {
                width: 56px;
                height: 56px;
            }

            .img2 {
                width: 76px;
                height: 76px;
            }
        }
    }

    .music-list {
        margin-top: 20px;
        position: relative;
        width: 588px;
        height: 610px;
        background-color: var(--theme-main-bg-color);
        border-radius: 16px;
        display: flex;
        justify-content: top;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        .music-list-content {
            width: 588px;
            height: 40px;

            .music-list-tab {
                width: 588px;

                position: absolute;
                top: 0px;
            }

            img {
                position: absolute;
                width: 588px;
                height: 2px;
                top: 69px;
                left: 0px;
            }
        }

        .content {
            // padding-top: 36px;
            margin-top: 36px;
            width: 588px;
            height: 524px;
            position: relative;
            display: flex;
            justify-content: top;
            align-items: center;
            /* 垂直居中 */
            flex-direction: column;
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;

            .content::-webkit-scrollbar {
                display: none;
            }

            .content-system {
                // width: 588px;
                // height: 524px;
                width: 100%;
                position: relative;
                display: flex;
                justify-content: top;
                align-items: center;
                /* 垂直居中 */
                flex-direction: column;

                .music-item {
                    box-sizing: border-box;
                    width: 560px;
                    height: 80px;
                    position: relative;
                    list-style-type: none;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid var(--theme-text-color);
                    /* 子节点之间的分隔线 */
                    // margin-top: 30px;
                    // margin-bottom: 30px;

                    .music-info,
                    .music-state {
                        display: flex;
                        align-items: center;
                    }

                    .music-info {
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        flex-direction: row;

                        img {
                            margin-left: 10px;
                        }

                        .child-style-1 {
                            padding-left: 10px;
                            /* 子节点1样式 */
                            color: var(--theme-text-color);
                            font-size: 22px;
                        }

                        .child-style-2 {
                            padding-left: 10px;
                            /* 子节点2样式 */
                            color: white;
                            font-size: 24px;
                        }

                        .child-style-3 {
                            padding-left: 10px;
                            /* 子节点3样式 */
                            color: var(--theme-text-color);
                            font-size: 20px;
                        }

                        .child-style-4 {
                            padding-left: 12px;
                            /* 子节点3样式 */
                            color: #FFF0BB;
                            font-size: 24px;
                        }

                        .child-style-5 {
                            padding-left: 12px;
                            /* 子节点3样式 */
                            color: var(--theme-text-color);
                            font-size: 22px;
                        }
                    }

                    .music-state {
                        justify-content: flex-end;

                        .state1 {
                            width: 32px;
                            height: 32px;
                        }

                        .state2 {
                            width: 30px;
                            height: 22px;
                        }

                        .loader {
                            /* 加载指示器样式 */
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #3498db;
                            border-radius: 50%;
                            width: 20px;
                            height: 20px;
                            animation: spin 2s linear infinite;
                            margin-right: 5px;
                        }

                        @keyframes spin {
                            0% {
                                transform: rotate(0deg);
                            }

                            100% {
                                transform: rotate(360deg);
                            }
                        }
                    }
                }
            }

            .content-my {
                // width: 588px;
                // height: 524px;
                width: 100%;
                position: relative;
                display: flex;
                justify-content: top;
                align-items: center;
                /* 垂直居中 */
                flex-direction: column;

                .music-item {
                    box-sizing: border-box;
                    width: 560px;
                    height: 80px;
                    position: relative;
                    list-style-type: none;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid var(--theme-text-color);
                    /* 子节点之间的分隔线 */
                    // margin-top: 30px;
                    // margin-bottom: 30px;

                    .music-info,
                    .music-operation {
                        display: flex;
                        align-items: center;
                    }

                    .music-info {
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        flex-direction: row;

                        img {
                            margin-left: 10px;
                        }

                        .child-style-1 {
                            padding-left: 10px;
                            /* 子节点1样式 */
                            color: var(--theme-text-color);
                            font-size: 22px;
                        }

                        .child-style-2 {
                            padding-left: 10px;
                            /* 子节点2样式 */
                            color: white;
                            font-size: 24px;
                        }

                        .child-style-3 {
                            padding-left: 10px;
                            /* 子节点3样式 */
                            color: var(--theme-text-color);
                            font-size: 20px;
                        }

                        .child-style-4 {
                            padding-left: 12px;
                            /* 子节点3样式 */
                            color: #FFF0BB;
                            font-size: 24px;
                        }

                        .child-style-5 {
                            padding-left: 12px;
                            /* 子节点3样式 */
                            color: var(--theme-text-color);
                            font-size: 22px;
                        }
                    }

                    .music-operation {
                        img {
                            margin-right: 10px;
                            margin-left: 6px;
                        }

                        .music-state {
                            justify-content: flex-end;

                            .state1 {
                                width: 32px;
                                height: 32px;
                            }

                            .state2 {
                                width: 30px;
                                height: 22px;
                            }
                        }
                    }


                }
            }

        }

    }
}
</style>
