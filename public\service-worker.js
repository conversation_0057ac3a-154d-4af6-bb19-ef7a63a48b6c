// 优化的Service Worker - 解决性能问题
const CACHE_NAME = '777pgday-v1';
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24小时

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index2.html',
  '/js/app.js',
  '/manifest.json',
  '/ic_launcher1.png'
];

// 安装事件 - 预缓存关键资源
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        // 强制激活新的Service Worker
        return self.skipWaiting();
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      // 立即控制所有客户端
      return self.clients.claim();
    })
  );
});

// 网络请求拦截 - 优化的缓存策略
self.addEventListener('fetch', event => {
  const request = event.request;
  const url = new URL(request.url);

  // 只处理同源请求
  if (url.origin !== location.origin) {
    return;
  }

  // 对于HTML文件使用网络优先策略
  if (request.destination === 'document') {
    event.respondWith(
      fetch(request)
        .then(response => {
          // 如果网络请求成功，更新缓存
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(CACHE_NAME).then(cache => {
              cache.put(request, responseClone);
            });
          }
          return response;
        })
        .catch(() => {
          // 网络失败时从缓存获取
          return caches.match(request);
        })
    );
    return;
  }

  // 对于其他资源使用缓存优先策略
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        if (cachedResponse) {
          // 检查缓存是否过期
          const cachedDate = new Date(cachedResponse.headers.get('date'));
          const now = new Date();

          if (now - cachedDate < CACHE_EXPIRY) {
            return cachedResponse;
          }
        }

        // 缓存不存在或已过期，从网络获取
        return fetch(request)
          .then(response => {
            // 只缓存成功的响应
            if (response.status === 200) {
              const responseClone = response.clone();
              caches.open(CACHE_NAME).then(cache => {
                cache.put(request, responseClone);
              });
            }
            return response;
          })
          .catch(() => {
            // 网络失败时返回缓存（即使过期）
            return cachedResponse;
          });
      })
  );
});

// 推送通知处理
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/ic_launcher1.png',
      badge: '/ic_launcher1.png',
      vibrate: [200, 100, 200],
      data: data,
      actions: [
        {
          action: 'open',
          title: '打开应用'
        },
        {
          action: 'close',
          title: '关闭'
        }
      ]
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// 通知点击处理
self.addEventListener('notificationclick', event => {
  event.notification.close();

  if (event.action === 'close') {
    console.log('Notification closed');
    return;
  }

  // 打开或聚焦到应用窗口
  event.waitUntil(
    clients.matchAll({ type: 'window' })
      .then(clientList => {
        // 查找已打开的窗口
        for (const client of clientList) {
          if (client.url === '/' && 'focus' in client) {
            return client.focus();
          }
        }
        // 如果没有打开的窗口，打开新窗口
        if (clients.openWindow) {
          return clients.openWindow('/index2.html');
        }
      })
  );
});
