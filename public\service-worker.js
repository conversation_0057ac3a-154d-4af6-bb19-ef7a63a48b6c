// 按需加载优化的Service Worker
const CACHE_NAME = '777pgday-lazy-v1';
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24小时

// 只预缓存绝对必要的资源
const ESSENTIAL_ASSETS = [
  '/index2.html',  // 启动页面
  '/js/app.js'     // 核心逻辑
];

// 可选资源 - 按需缓存，不预加载
const OPTIONAL_ASSETS = [
  '/manifest.json',
  '/ic_launcher1.png',
  '/favicon1.ico',
  '/photo.png'
];

// 外部资源域名白名单
const ALLOWED_ORIGINS = [
  'https://www.777PGDAY.com',
  'https://ofs.cloudoss.org'
];

// 安装事件 - 只预缓存必要资源
self.addEventListener('install', event => {
  console.log('Service Worker installing with lazy loading strategy...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching only essential assets');
        return cache.addAll(ESSENTIAL_ASSETS);
      })
      .then(() => {
        console.log('Essential assets cached, skipping waiting');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Failed to cache essential assets:', error);
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      return self.clients.claim();
    })
  );
});

// 按需缓存策略
function shouldCache(request) {
  const url = new URL(request.url);

  // 同源资源
  if (url.origin === location.origin) {
    return true;
  }

  // 白名单外部资源
  return ALLOWED_ORIGINS.some(origin => url.origin === origin);
}

// 网络请求拦截 - 按需加载策略
self.addEventListener('fetch', event => {
  const request = event.request;
  const url = new URL(request.url);

  // 只处理GET请求
  if (request.method !== 'GET') {
    return;
  }

  // 跳过不需要缓存的资源
  if (!shouldCache(request)) {
    return;
  }

  // 检测是否为PWA独立模式启动
  const isPWAMode = event.request.mode === 'navigate' &&
                   (event.request.destination === 'document' ||
                    event.clientId === '');

  // 对于PWA启动页面，使用缓存优先策略
  if (isPWAMode || request.destination === 'document') {
    event.respondWith(
      caches.match(request)
        .then(cachedResponse => {
          if (cachedResponse) {
            console.log('Serving from cache:', request.url);

            // 后台更新缓存（stale-while-revalidate）
            fetch(request)
              .then(response => {
                if (response.status === 200) {
                  const responseClone = response.clone();
                  caches.open(CACHE_NAME).then(cache => {
                    cache.put(request, responseClone);
                  });
                }
              })
              .catch(() => {
                // 网络失败时忽略
              });

            return cachedResponse;
          }

          // 没有缓存时从网络获取并缓存
          console.log('Fetching and caching:', request.url);
          return fetch(request)
            .then(response => {
              if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(CACHE_NAME).then(cache => {
                  cache.put(request, responseClone);
                });
              }
              return response;
            });
        })
    );
    return;
  }

  // 对于其他资源使用按需缓存策略
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        // 如果有缓存，检查是否过期
        if (cachedResponse) {
          const cachedDate = new Date(cachedResponse.headers.get('date'));
          const now = new Date();

          // 对于静态资源，直接返回缓存
          if (request.destination === 'script' ||
              request.destination === 'style' ||
              request.destination === 'image' ||
              request.destination === 'font') {

            console.log('Serving static resource from cache:', request.url);

            // 后台更新（如果缓存过期）
            if (now - cachedDate > CACHE_EXPIRY) {
              fetch(request)
                .then(response => {
                  if (response.status === 200) {
                    const responseClone = response.clone();
                    caches.open(CACHE_NAME).then(cache => {
                      cache.put(request, responseClone);
                    });
                  }
                })
                .catch(() => {});
            }

            return cachedResponse;
          }

          // 对于其他资源，检查是否过期
          if (now - cachedDate < CACHE_EXPIRY) {
            console.log('Serving from cache (fresh):', request.url);
            return cachedResponse;
          }
        }

        // 缓存不存在或已过期，从网络获取
        console.log('Fetching from network:', request.url);
        return fetch(request)
          .then(response => {
            // 只缓存成功的响应
            if (response.status === 200 && shouldCache(request)) {
              const responseClone = response.clone();
              caches.open(CACHE_NAME).then(cache => {
                cache.put(request, responseClone);
                console.log('Cached on demand:', request.url);
              });
            }
            return response;
          })
          .catch(() => {
            // 网络失败时返回缓存（即使过期）
            if (cachedResponse) {
              console.log('Network failed, serving stale cache:', request.url);
              return cachedResponse;
            }
            throw new Error('Network failed and no cache available');
          });
      })
  );
});

// 推送通知处理
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/ic_launcher1.png',
      badge: '/ic_launcher1.png',
      vibrate: [200, 100, 200],
      data: data
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// 通知点击处理
self.addEventListener('notificationclick', event => {
  event.notification.close();

  if (event.action === 'close') {
    console.log('Notification closed');
    return;
  }

  event.waitUntil(
    clients.matchAll({ type: 'window' })
      .then(clientList => {
        for (const client of clientList) {
          if (client.url === '/' && 'focus' in client) {
            return client.focus();
          }
        }
        if (clients.openWindow) {
          return clients.openWindow('/index2.html');
        }
      })
  );
});
