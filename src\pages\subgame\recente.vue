<script setup lang="ts" name="app-index-game-container">
import { GameHallTopEnum } from "~/types/common";

// 分页的列表数据
const gameList = ref<any[]>([]);
const isShowVertical = ref(false); //切换横竖版
const isShowThreeRow = ref(false); //是否显示三行
isShowVertical.value = true;

const appStore = useAppStore();
const { isSmallGameIcon } = storeToRefs(appStore);

//取数据
getData();

function getData() {
  let recenteData = window.localStorage.getItem("recenteData");
  if (recenteData) {
    const loadedMap = JSON.parse(recenteData);
    gameList.value = loadedMap;
  }
}

function appGamePlatformby() {}
const gameItemInnerStyle: any = {
  margin: isSmallGameIcon.value
    ? `var(--app-npx-22) var(--app-npx-20)`
    : `var(--app-npx-15) 0`,
};
</script>

<template>
  <div class="app-index-game-container">
    
    <AppIndexTitle
      :id="3"
      :platform_id="GameHallTopEnum.Recente"
      :isCallback="true"
      @appGamePlatform-by="appGamePlatformby"
    />
    <div class="app-maps game-container">
      <div
        class="content"
        :class="{
          versionContent: isShowVertical,

          'is-empty-loading': !gameList?.length,
        }"
        :style="gameItemInnerStyle"
      >
        <template v-if="gameList && gameList.length > 0">
          <AppGameItem
            v-if="isSmallGameIcon"
            class="game-item"
            v-for="(item, idx) in gameList"
            :key="item.id + idx"
            :data="{ ...item }"
          />
          <AppGameItemBig
            v-else
            class="game-item"
            v-for="(item, idx) in gameList"
            :key="item.id + idx"
            :data="{ ...item }"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-index-game-container {
  color: var(--app-title-color);
  font-size: var(--app-gameTitle-fontSize);
  // font-weight: 700;
  padding: 26px 0;
  line-height: 40px;
  margin-bottom: 150px;
}
.app-maps.game-container {
  margin-left: -20px;
  margin-right: -20px;
  border-radius: 0;
  padding-left: 20px;
  padding-right: 20px;
}
.app-maps {
  // border-radius: 0px 30px 30px 30px;
  // background: linear-gradient(180deg, #044B9A 0%, #011A51 100%), #1B2E1B;
  // padding-top: 17px;
  // padding: 0 10px;
  // padding-bottom: 8px;

  .content {
    // height: 590px;
    // padding-bottom: 8px;
    // height: calc(100vh - 360px);
    display: flex;
    flex-wrap: wrap;
    // grid-auto-flow: row;
    // grid-column-gap: 10px;
    // grid-row-gap: 10px;
    scroll-snap-type: x;
    overflow: auto hidden;
    height: 100%;
    // grid-template-columns: repeat(auto-fill, 200px);
    min-height: 350px;
    &::-webkit-scrollbar {
      display: none;
    }
    &.one-row {
      grid-template-rows: auto;
      justify-content: flex-start;
      // height: 295px;
      // height: 295px;
    }
    .left {
      width: 12px;
      scroll-snap-align: start;
    }
    // .left {
    //   width: 12px;
    //   scroll-snap-align: start;
    // }
    &.is-empty-loading {
      align-items: center;
      justify-items: center;
      justify-content: center;
    }

    &.overflow-hidden {
      overflow: hidden;
    }
    // &.flow-row {
    //   grid-auto-flow: row;
    // }

    .last-loading {
      grid-row-start: 1;
      grid-row-end: 2;
      width: 580px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .more {
    // justify-items: center;
    // justify-content: center;
    text-align: center;

    border-radius: 10px;
    .more-text {
      display: block;
      color: var(--theme-text-color-lighten);
      font-size: 24px;
    }

    .more-text-carregar {
      display: block;
      position: absolute;
      margin: 0 auto;
      text-align: center;
      color: var(--theme-text-color);
      font-size: 24px;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50;
      margin-top: 5px;
    }

    .more-text-img {
      width: 14px;
    }
  }

  //热门竖版显示
  .versionContent {
    // grid-template-columns: repeat(4, 1fr); /* 创建三个等宽的列 */
    // grid-template-rows: repeat(4, 230px); /* 创建两行固定高度为100px的网格 */
  }
}
</style>
<route lang="yaml">
meta:
  layout: home
</route>
