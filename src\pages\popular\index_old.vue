<script setup lang="ts" name="polular">
import { GameNavEnum } from '~/types/common';

const appStore = useAppStore()
const { custService } = storeToRefs(appStore)

const gameStore = useGameStore()
const { gameNavData, gameNavInit } = storeToRefs(gameStore)

const openService = (url?: string) => {
  if (!url) return
  window.open(url, '_blank')
}

const filterType = ref('')

const setFilterGameParam = (v: string) => {
  filterType.value = v
}

</script>

<template>
  <div class="app-index-layout-container">
    <!-- <div class="g-filter">
      <AppGameFilterGlobal :id="+GameNavEnum.Quente" :setFilterGameParam="setFilterGameParam" />
    </div> -->
    <AppIndexGameContainer :id="GameNavEnum.Quente" class="game-container-list" :filter-type="filterType" />
  </div>
</template>

<style lang="scss" scoped>
.app-index-layout-container {
  position: relative;
  padding-bottom: 132px;
  .g-filter {
    position: absolute;
    right: 0;
    top: 28px;
  }
}

.game-container-list {
  // padding-top: 30px;
}

.index-latest-win {
  margin-top: 80px;
}



.index-title {
  color: #FFF;
  font-size: 32px;
  font-weight: 700;
  padding-top: 30px;
  padding-bottom: 27px;
}

.jobs-img {
  width: 100%;
}

.index-line {
  position: relative;
  width: 100%;
  margin-top: 39px;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed #828EB4;
    pointer-events: none;
    transform: scaleY(0.5);
    transform-origin: top left;
  }
}

.index-b-img {
  vertical-align: middle;
}

.index-b-img-lx {
  width: 76px;

  &:not(:last-child) {
    margin-right: 30px;
  }
}

.index-b-img-139 {
  width: 139px;
}

.index-b-img-92 {
  width: 92px;
}

.index-title-2 {
  color: rgba(255, 255, 255, 0.60);
  font-size: 26px;
  padding-top: 40px;
}

.bottom-link {
  color: #CCCED2;
  text-align: center;
  font-size: 22px;
  display: flex;
  justify-content: space-around;
  padding: 35px 0 50px 0;

  .link-img {
    height: 51px;
  }
}

.copyright {
  color: rgba(255, 255, 255, 0.60);
  text-align: center;
  font-size: 24px;
  height: 94px;
  background: #28374d;
  line-height: 94px;
}
</style>
<route lang="yaml">
meta:
  layout: home
</route>
