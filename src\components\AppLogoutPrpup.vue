<script setup lang="ts" name="app-find-pwd">
// const router = useRouter()
const appStore = useAppStore();
const { showLoginOut } = storeToRefs(appStore);
const router = useRouter()

//关闭界面
const closePage = () => {
  appStore.setShowLoginOut(false);
};
const submit = () => {
  runLogout()
};
const { run: runLogout } = useRequest(ApiLogout, {
  manual: true,
  loadingDelay: 500,
  onSuccess(data) {
    // localStorage.clear()
    closePage()
    appStore.clearToken();
    localStorage.setItem("logonOut", "0");
    router.push("/");
    // location.replace(location.origin)
  },
});
</script>

<template>
  <!-- @click-overlay="onClickOverlay" -->
  <div>
    <van-popup
      class="app-login-register"
      v-model:show="showLoginOut"
      teleport="body"
      :close-on-click-overlay="false"
    >
      <div class="content">
        <div class="title">Lembrete</div>
        <div class="message">Você se retira da conta corrente?</div>
        <div class="submit">
          <AppButton
            @click="submit()"
            class=""
            width="var(--app-px-253)"
            height="70"
            blue
            :radius="14"
            black-text
            font-size="var(--app-px-24)"
            color="var(--theme-primary-font-color)"
            background="rgba(0, 0, 0, 0)"
            border="1px solid var(--theme-primary-color)"
            :style="{ fontWeight:400 }"
          >
            Confrmar saida
          </AppButton>
          <AppButton
            @click="closePage()"
            class=""
            width="49%"
            height="70"
            blue
            :radius="14"
            black-text
            font-size="var(--app-px-24)"
            color="white"
            margin="0 0 0 20px"
          >
            Cancelar
          </AppButton>
        </div>
      </div>
      <!-- <img
        class="app-image img close-icon"
        src="/icons/close_black.png.webp"
        @click="closePage()"
        alt=""
        
      /> -->
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.content {
  width: 600px;
  height: 304px;
  border-radius: 16px;
  background-color: var(--theme-main-bg-color);
  border: thin solid;
  border-color: var(--theme-color-line);
  position: relative;
  .title {
    text-align: center;
    margin-top: 20px;
    font-size: 30px;
    color: var(--theme-text-color);
    font-weight: 400;
    line-height: 42px;
  }
  .message {
    text-align: center;
    margin-top: 30px;
    color: var(--theme-primary-font-color);
    font-size: 24px;
  }
}

.submit {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 230px;
  display: flex;
  width: 536px;
  justify-content: space-between;
}

.close-icon {
  width: 2.1rem;
  border: 5px solid white;
  border-radius: 100%;
  padding: 5px;
  margin: 40px auto;
  display: block;
}
</style>
