<script setup lang='ts' name='changeEmail'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const emailRef = ref()
//隐藏底下菜单
appStore.setFooterDialogVisble(false)
const fromData = reactive({
  email: '',
  code:'',
})

const sidData = reactive({
  sid: '',
  ts: ''
})

function onClickBtn(){
    if (emailRef.value.validation()) {
        const { email, code} = fromData;
        const { sid, ts } = sidData
        runBindEmail({sid,ts,code,email})
    }
}

// 绑定邮箱
const { run: runBindEmail, loading: bindEmailLoading } = useRequest(ApiBindEmail, {
  manual: true,
  onSuccess: () => {
    userInfo.value.email = fromData.email;
    router.go(-1)
  }
})

</script>

<template>
    <AppPageTitle left-arrow title="Endereço de e-mail" title-weight="700" />
    <div class="content">
        <span >Alterar o E-mail</span>
        <div class="email">
            <AppInput
            icon-with="23"
            ref="emailRef"
            v-model="fromData.email"
            icon-left="input-user-message"
            type="email"
            placeholder="Por favor insira seu e-mail"
            :err-height="42"
            clearable
            width="92%"
            height="70"
            :rules="[
            { pattern: emailReg, msg: 'O e-mail nao pode estar vazio' },
            ]"
            :readonly="isReadOnly"
            :style-obj="{
              background: '--theme-main-bg-colo',
              color: '#000',
              borderRadius: '5px',
            }"
          >
          </AppInput>
        </div>
        <div class="content-bottom">
            <AppButton @click="onClickBtn" :loading="loading" width="100%" height="70" :radius="15" fontSize="12px" color="var(--theme-primary-font-color)" 
              background="var(--theme-primary-color)"
            >
              Confirme
            </AppButton>
        </div>
    </div>
    
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.content {
    height: calc(100vh - 100px);
    font-size: 25px;
    background-color: var(--theme-bg-color);
    span{
        position:absolute;
        padding: 50px;
        color: white;
    }

   .email{
     padding: 120px 0 0 45px; 
   }

   &-bottom{
        position: absolute;
        bottom: 0;
        height: 120px;
        width: 100%;
        padding: 25px 25px;
        background-color:var(--theme-main-bg-color);
    }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>