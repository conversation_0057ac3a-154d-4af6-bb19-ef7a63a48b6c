<script lang="ts" setup>
import AppFooter from "~/components/AppFooter.vue";
import arrowIcon from "/icons/svg/arrow.svg?raw";
import copyIcon from "/icons/svg/copy.svg?raw";
const router = useRouter();
const appStore = useAppStore();
const { userInfo, showIndexModule } = storeToRefs(appStore);
const showInfo = ref(false);
const showPwd = ref(false);
const nextLevelNeedRunning = ref(0);
const curLevelNeedRunning = ref(0);
const validWaged = ref(0);
const RESET_TYPE = readonly({
  LOGIN: 0,
  PAY: 1,
});
appStore.setFooterDialogVisble(true);

const showPopup = ref(false);
const tabData = ref([
  {
    label: "Senha de login",
    value: RESET_TYPE.LOGIN,
  },
  {
    label: "Senha de saque",
    value: RESET_TYPE.PAY,
  },
]);
const hasPayPwd = computed(
  () =>
    userInfo.value &&
    userInfo.value.pay_password &&
    +userInfo.value.pay_password === 1
);
const pwdResetType = ref(RESET_TYPE.LOGIN);
const phoneRef = ref();
const emailRef = ref();
const formdata = reactive({
  username: userInfo.value.username || "",
  email: userInfo.value.email || "",
  phone: userInfo.value.phone || "",
});

const formdataLogin = reactive({
  old_password: "",
  password: "",
  confirm_password: "",
});
const formdataWithdrawal = reactive({
  update_type: 2,
  ty: 1,
  sid: "",
  ts: "",
  code: "",
  old: "",
  password: "",
  confirm_password: "",
});

const enum JumpViewTypeVip {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
}

const logout = () => {
  appStore.setShowLoginOut(true);
};

const { run: runUpdateInfo, loading: updateLoading } = useRequest(
  ApiUpdateUserInfo,
  {
    manual: true,
    onSuccess: () => {
      // router.go(-1)
      appStore.runGetMemberInfo();
    },
  }
);
watch(userInfo, (val) => {
  if (val) {
    formdata.phone = val.phone || "";
    formdata.email = val.email || "";
    formdata.username = val.username || "";
  }
});
const submit = () => {
  if (phoneRef.value.validation() && emailRef.value.validation()) {
    runUpdateInfo({
      phone: userInfo.value.phone ? "" : formdata.phone,
      email: userInfo.value.email ? "" : formdata.email,
      username: formdata.username,
    });
    showInfo.value = false;
  }
};
const closeInfo = () => {
  showInfo.value = false;
  formdata.phone = userInfo.value.phone || "";
  formdata.email = userInfo.value.email || "";
  formdata.username = userInfo.value.username || "";
};

const { run: runUpdatePwd, loading: loadingResetPwd } = useRequest(
  () => ApiUpdateLoginPwd(formdataLogin),
  {
    manual: true,
    onSuccess: () => {
      // localStorage.clear()
      showToast("sucesso");
      location.replace(location.origin);
    },
  }
);

const { run: runUpdatePayPwd, loading: loadingResetPayPwd } = useRequest(
  () => ApiUpdatePayPwd(formdataWithdrawal),
  {
    manual: true,
    onSuccess: () => {
      // localStorage.clear()
      // location.replace(location.origin)
      closePwd();
      formdataWithdrawal.old = "";
      formdataWithdrawal.password = "";
      formdataWithdrawal.confirm_password = "";
      showToast("sucesso");
    },
  }
);

const submitPwd = () => {
  if (pwdResetType.value == RESET_TYPE.LOGIN) {
    if (
      formdataLogin.old_password == "" ||
      formdataLogin.password == "" ||
      formdataLogin.confirm_password == ""
    ) {
      return showToast("请完成输入");
    }

    //密码一致性判断
    if (formdataLogin.password !== formdataLogin.confirm_password) {
      return showToast("As senhas inseridas não coincidem");
    }
    runUpdatePwd();
  } else {
    if (
      formdataWithdrawal.old == "" ||
      formdataWithdrawal.password == "" ||
      formdataWithdrawal.confirm_password == ""
    ) {
      return showToast("请完成输入");
    }
    //密码一致性判断
    if (formdataWithdrawal.password !== formdataWithdrawal.confirm_password) {
      return showToast("As senhas inseridas não coincidem");
    }
    runUpdatePayPwd();
  }
  // showPwd.value = false
};
const closePwd = () => {
  showPwd.value = false;
};

const enum JumpViewType {
  Suporte = 0,
  Noticia,
  Notificacao,
  PainelRolante,
  BonusDeSugestao,
}

const records = ref([
  // { icon: 'personal_stock', text: 'Transação', path: '/record-list/transaction', iconWidth: 0 }, // icon 图标从 figma 导出 x3 倍图
  // { icon: 'personal_info', text: 'Informações pessoais', path: '/safe-center/account-info', iconWidth: 0 },
  // { icon: 'personal_centro', text: 'Centro de Segurança', path: '/safe-center/reset-pwd', iconWidth: 0 },
  // { icon: 'personal_faq', text: 'Suporte', path: '/supper/supperIndex', iconWidth: 0 },
  // { icon: 'i-bet-record', text: 'Histórico de bônus', path: '/record-list/bonus', iconWidth: 0 },
  {
    icon: "icon-Convidar",
    text: "Convidar",
    path: "/agent",
    iconWidth: 0,
    svg: `<svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_6581_88290)">
  <path d="M7.33301 38.0002C5.93267 38.0119 4.58482 37.4679 3.58508 36.4872C2.58534 35.5066 2.01531 34.1695 2 32.7692V9.23114C2.01531 7.83084 2.58534 6.49374 3.58508 5.51313C4.58482 4.53251 5.93267 3.98845 7.33301 4.00019H20.667C21.109 4.00045 21.5329 4.17615 21.8455 4.48871C22.158 4.80128 22.3337 5.22514 22.334 5.66718C22.3337 6.10921 22.158 6.53308 21.8455 6.84564C21.5329 7.15821 21.109 7.33391 20.667 7.33417V7.34918H7.33301C6.80794 7.34465 6.30248 7.54858 5.92755 7.9162C5.55262 8.28382 5.33881 8.78518 5.33301 9.31024V32.7681C5.33881 33.2933 5.55259 33.7948 5.92749 34.1625C6.30239 34.5303 6.80784 34.7344 7.33301 34.7302H31.333C31.8583 34.7347 32.3638 34.5306 32.7388 34.1628C33.1138 33.7949 33.3275 33.2934 33.333 32.7681V22.6682C33.3329 22.4492 33.3759 22.2323 33.4596 22.03C33.5433 21.8276 33.6661 21.6437 33.8209 21.4888C33.9757 21.3339 34.1595 21.211 34.3618 21.1271C34.5642 21.0433 34.781 21.0002 35 21.0002C35.4419 21.0002 35.8656 21.1757 36.178 21.4881C36.4905 21.8005 36.666 22.2243 36.666 22.6662V32.7662C36.6509 34.1667 36.0809 35.5041 35.0808 36.4848C34.0808 37.4655 32.7326 38.0094 31.332 37.9971L7.33301 38.0002ZM16.403 26.3002C16.1842 26.295 15.9685 26.2467 15.7684 26.1581C15.5683 26.0696 15.3875 25.9426 15.2365 25.7841C15.0855 25.6257 14.9672 25.439 14.8884 25.2348C14.8095 25.0306 14.7717 24.813 14.777 24.5942C14.8834 22.9591 15.2368 21.3495 15.825 19.8201C16.7524 17.1363 18.4847 14.8037 20.786 13.1402C23.5657 11.2278 26.8367 10.1553 30.209 10.0502L28.154 7.99518C27.8418 7.68245 27.6664 7.25863 27.6664 6.81671C27.6664 6.3748 27.8418 5.95086 28.154 5.63813C28.4666 5.32565 28.8905 5.15021 29.3325 5.15021C29.7745 5.15021 30.1984 5.32565 30.511 5.63813L35.696 10.8232C36.0085 11.1358 36.184 11.5597 36.184 12.0017C36.184 12.4436 36.0085 12.8676 35.696 13.1802L30.511 18.3652C30.1984 18.6776 29.7745 18.8532 29.3325 18.8532C28.8905 18.8532 28.4666 18.6776 28.154 18.3652C27.8415 18.0526 27.666 17.6287 27.666 17.1867C27.666 16.7447 27.8415 16.3208 28.154 16.0082L30.806 13.3561C22.717 13.7011 19.924 18.1632 18.968 20.9272L18.956 20.9601C18.4943 22.1507 18.2087 23.4023 18.108 24.6752C18.0973 25.1101 17.9171 25.5236 17.6059 25.8276C17.2947 26.1315 16.877 26.3018 16.442 26.3022L16.403 26.3002Z" fill="var(--svg-icon-color)"></path>
  </g>
  <defs>
  <clipPath id="clip0_6581_88290">
  <rect width="40" height="40" fill="var(--svg-icon-color)"></rect>
  </clipPath>
  </defs>
  </svg>`,
  },
  {
    icon: "icon-Dados",
    text: "Dados",
    path: "/personalCenter/userInfo",
    iconWidth: 0,
    svg: `<svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_6581_88289)">
  <path d="M20.5069 38.6813H2.81393C2.37808 38.6808 1.96023 38.5074 1.65204 38.1992C1.34384 37.891 1.17046 37.4732 1.16993 37.0374V34.4163C1.16758 32.7946 1.49168 31.1891 2.12294 29.6953C2.73303 28.255 3.61592 26.9464 4.72294 25.8414C5.82801 24.7339 7.13712 23.851 8.57794 23.2413C10.0769 22.626 11.6796 22.3025 13.2999 22.2883H20.5079C20.9438 22.2891 21.3615 22.4627 21.6695 22.7711C21.9775 23.0795 22.1507 23.4975 22.1509 23.9333C22.1507 24.3691 21.9775 24.7869 21.6695 25.0952C21.3614 25.4034 20.9437 25.5768 20.5079 25.5773H13.2999C10.9562 25.58 8.70928 26.5122 7.05203 28.1694C5.39478 29.8267 4.46259 32.0737 4.45994 34.4174V35.3943H20.5079C20.9437 35.3949 21.3614 35.5683 21.6695 35.8765C21.9775 36.1847 22.1507 36.6026 22.1509 37.0383C22.1504 37.4741 21.977 37.8919 21.6688 38.1999C21.3606 38.5079 20.9427 38.6811 20.5069 38.6813ZM24.4709 38.4183C24.2728 38.4186 24.0766 38.3795 23.8936 38.3035C23.7106 38.2274 23.5446 38.1159 23.4049 37.9753C23.2566 37.827 23.1406 37.6494 23.0646 37.4538C22.9885 37.2582 22.9539 37.049 22.9629 36.8394L23.1359 32.8834C23.1518 32.5044 23.3096 32.1453 23.5779 31.8773L32.4619 22.9923C32.6025 22.8514 32.7697 22.7397 32.9536 22.6636C33.1376 22.5876 33.3349 22.5487 33.5339 22.5493C33.7329 22.5487 33.93 22.5876 34.1138 22.6636C34.2976 22.7397 34.4646 22.8513 34.6049 22.9923L38.3899 26.7773C38.6732 27.062 38.8323 27.4472 38.8323 27.8488C38.8323 28.2504 38.6732 28.6357 38.3899 28.9203L29.5049 37.8033C29.2364 38.0709 28.8776 38.2285 28.4989 38.2454L24.5429 38.4174L24.4709 38.4183ZM33.5329 26.2073L26.1329 33.6074L26.0599 35.3253L27.7779 35.2493L35.1719 27.8553L33.5299 26.2123L33.5329 26.2073ZM19.8559 20.3344C17.3354 20.3317 14.9189 19.3294 13.1364 17.5473C11.3539 15.7652 10.3511 13.3489 10.3479 10.8284C10.3506 8.3075 11.3532 5.89062 13.1357 4.10809C14.9182 2.32557 17.3351 1.32296 19.8559 1.32031C22.3766 1.32322 24.7932 2.32597 26.5755 4.10846C28.3579 5.89095 29.3603 8.30768 29.3629 10.8284C29.36 13.349 28.3573 15.7655 26.5748 17.5476C24.7922 19.3298 22.3755 20.332 19.8549 20.3344H19.8559ZM19.8559 4.60834C18.2069 4.61019 16.6258 5.26612 15.4598 6.43219C14.2937 7.59826 13.6378 9.17929 13.6359 10.8284C13.6381 12.4772 14.2941 14.0578 15.4602 15.2235C16.6263 16.3892 18.2071 17.0447 19.8559 17.0463C21.5045 17.0445 23.085 16.3888 24.2507 15.2231C25.4164 14.0574 26.0721 12.4769 26.0739 10.8284C26.0724 9.17938 25.4167 7.59835 24.2507 6.43225C23.0848 5.26615 21.5039 4.61019 19.8549 4.60834H19.8559Z" fill="var(--svg-icon-color)"></path>
  </g>
  <defs>
  <clipPath id="clip0_6581_88289">
  <rect width="40" height="40" fill="var(--svg-icon-color)"></rect>
  </clipPath>
  </defs>
  </svg>`,
  },
  {
    icon: "icon-seguranca",
    text: "Segurança",
    path: "/personalCenter/security",
    iconWidth: 0,
    svg: `<svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_6581_88336)">
  <path d="M19.504 38.4891C4.10904 33.1111 2.50404 20.5321 2.68204 11.2591C2.69904 10.4241 2.72004 9.38508 2.72004 8.44608C2.71845 8.02552 2.88385 7.62153 3.17991 7.32284C3.47597 7.02415 3.87849 6.85519 4.29904 6.85308C9.63604 6.85308 15.539 5.19008 18.92 1.87608C19.2131 1.58455 19.6097 1.4209 20.023 1.4209C20.4364 1.4209 20.833 1.58455 21.126 1.87608C24.505 5.18708 30.359 6.79108 35.698 6.79108C36.1189 6.79319 36.5216 6.96226 36.8179 7.26113C37.1141 7.56001 37.2796 7.96426 37.278 8.38508C37.278 9.24708 37.295 10.1381 37.315 11.1971C37.49 20.4701 35.937 33.1081 20.54 38.4871C20.2048 38.6054 19.8392 38.6054 19.504 38.4871V38.4891ZM5.86904 9.99808C5.86904 10.2791 5.85404 10.7571 5.84304 11.3221C5.99504 20.7091 7.31804 30.5851 20.023 35.2941C32.723 30.5851 33.946 21.0701 34.154 11.2601C34.144 10.7151 34.137 10.3231 34.131 9.93908C29.177 9.67808 23.464 8.48408 20.023 5.53908C16.58 8.48308 10.818 9.73808 5.86904 9.99808ZM17.829 24.6641L14.318 21.1221C14.0213 20.8225 13.8548 20.4178 13.8548 19.9961C13.8548 19.5744 14.0213 19.1697 14.318 18.8701C14.4642 18.7225 14.6382 18.6053 14.8299 18.5253C15.0216 18.4454 15.2273 18.4042 15.435 18.4042C15.6428 18.4042 15.8484 18.4454 16.0402 18.5253C16.2319 18.6053 16.4059 18.7225 16.552 18.8701L18.946 21.2811L24.846 15.3331C24.9919 15.185 25.1658 15.0676 25.3576 14.9876C25.5494 14.9076 25.7552 14.8666 25.963 14.8671C26.171 14.8665 26.3769 14.9075 26.5687 14.9877C26.7606 15.0679 26.9344 15.1857 27.08 15.3341C27.3768 15.6337 27.5433 16.0384 27.5433 16.4601C27.5433 16.8818 27.3768 17.2865 27.08 17.5861L20.063 24.6601C19.9166 24.8078 19.7424 24.925 19.5504 25.005C19.3584 25.085 19.1525 25.1262 18.9445 25.1262C18.7366 25.1262 18.5306 25.085 18.3387 25.005C18.1467 24.925 17.9725 24.8078 17.826 24.6601L17.829 24.6641Z" fill="var(--svg-icon-color)"></path>
  </g>
  <defs>
  <clipPath id="clip0_6581_88336">
  <rect width="40" height="40" fill="var(--svg-icon-color)"></rect>
  </clipPath>
  </defs>
  </svg>`,
  },
  //   { icon: "icon-musica", text: "Música", path: "/", iconWidth: 0, svg: `` },
  {
    icon: "icon-FAQ",
    text: "FAQ",
    path: "/serviceMessages",
    iconWidth: 0,
    svg: `<svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_6581_88379)">
  <path d="M1.66602 20C1.66621 16.3741 2.74177 12.8296 4.75635 9.81492C6.77092 6.8002 9.6342 4.45064 12.9841 3.06321C16.3341 1.67578 20.0202 1.31287 23.5764 2.02036C27.1326 2.72786 30.3991 4.47398 32.9629 7.03794C35.5267 9.6019 37.2727 12.8686 37.98 16.4248C38.6873 19.981 38.3241 23.6671 36.9365 27.0169C35.5489 30.3668 33.1992 33.23 30.1843 35.2444C27.1695 37.2588 23.6249 38.334 19.999 38.334C15.1383 38.3287 10.4782 36.3953 7.04126 32.9581C3.60427 29.5209 1.67104 24.8607 1.66602 20ZM5.00024 20C5.00024 22.9667 5.87986 25.8667 7.52808 28.3335C9.1763 30.8002 11.5189 32.7228 14.2598 33.8581C17.0007 34.9934 20.0168 35.2905 22.9265 34.7118C25.8362 34.133 28.5089 32.7043 30.6067 30.6065C32.7045 28.5088 34.1331 25.8361 34.7119 22.9264C35.2907 20.0167 34.9937 17.0006 33.8584 14.2597C32.7231 11.5189 30.8005 9.17615 28.3337 7.52793C25.867 5.87971 22.967 4.99998 20.0002 4.99998C16.0234 5.00421 12.2104 6.58589 9.39844 9.39805C6.58647 12.2102 5.00421 16.0231 5.00024 20ZM17.635 29.3929C17.6352 28.945 17.7683 28.5072 18.0173 28.1349C18.2663 27.7625 18.62 27.4724 19.0339 27.3011C19.4478 27.1298 19.9032 27.0851 20.3425 27.1726C20.7818 27.2601 21.1855 27.4759 21.5022 27.7927C21.8189 28.1095 22.0343 28.5132 22.1216 28.9525C22.2089 29.3918 22.1641 29.8471 21.9927 30.261C21.8212 30.6748 21.5309 31.0285 21.1584 31.2773C20.786 31.5262 20.3481 31.6589 19.9001 31.6589C19.2993 31.6586 18.7233 31.4199 18.2986 30.995C17.8738 30.57 17.635 29.9937 17.635 29.3929ZM17.9272 23.969C17.9272 23.969 17.8702 22.176 17.9272 21.391C18.0602 20.348 19.346 20.024 19.968 19.901C19.973 19.901 22.835 19.674 23.447 17.577C23.5507 17.1935 23.5777 16.7933 23.5261 16.3994C23.4746 16.0055 23.3456 15.6257 23.1467 15.2818C22.9478 14.938 22.6829 14.6368 22.3672 14.3957C22.0515 14.1546 21.6912 13.9783 21.3071 13.8769C20.5952 13.7174 19.8501 13.8059 19.1953 14.1278C18.5406 14.4497 18.0154 14.9857 17.707 15.6469C17.5178 16.1273 17.1584 16.5211 16.6973 16.7531C16.2361 16.9852 15.7055 17.0393 15.207 16.905C14.7639 16.7544 14.3966 16.4376 14.1826 16.0213C13.9686 15.6051 13.9247 15.1219 14.0601 14.6739C14.6147 13.055 15.7432 11.6955 17.2322 10.852C18.3984 10.2053 19.7291 9.91606 21.0586 10.0206C22.3881 10.1251 23.6575 10.6187 24.7083 11.4398C25.7591 12.2609 26.5449 13.3732 26.9678 14.6379C27.3906 15.9027 27.4318 17.264 27.0862 18.552C26.3772 21.185 24.216 22.342 21.696 22.781V23.967C21.696 24.4671 21.4974 24.9467 21.1438 25.3003C20.7902 25.6539 20.3106 25.8525 19.8105 25.8525C19.3105 25.8525 18.8309 25.6539 18.4773 25.3003C18.1237 24.9467 17.925 24.4671 17.925 23.967L17.9272 23.969Z" fill="var(--svg-icon-color)"></path>
  </g>
  <defs>
  <clipPath id="clip0_6581_88379">
  <rect width="40" height="40" fill="var(--svg-icon-color)"></rect>
  </clipPath>
  </defs>
  </svg>`,
  },
  {
    icon: "icon-Sugestao",
    text: "Bônus de Sugestão",
    path: "/serviceMessages",
    iconWidth: 0,
    svg: `<svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_1116_2)">
  <g clip-path="url(#clip1_1116_2)">
  <path d="M23.395 36.2069C22.4105 36.2056 21.4666 35.8141 20.7703 35.1181C20.0739 34.4221 19.6819 33.4785 19.68 32.4939V14.5549C19.6811 13.57 20.0728 12.6257 20.7693 11.9292C21.4657 11.2327 22.41 10.841 23.395 10.8399H34.285C35.2699 10.8413 36.214 11.2331 36.9104 11.9295C37.6069 12.6259 37.9987 13.5701 38 14.5549V32.4939C37.9989 33.4792 37.6071 34.4237 36.9104 35.1204C36.2138 35.817 35.2692 36.2089 34.284 36.2099L23.395 36.2069ZM22.755 14.5559V32.4939C22.7553 32.6636 22.8228 32.8262 22.9427 32.9462C23.0627 33.0662 23.2253 33.1337 23.395 33.1339H34.285C34.3691 33.1341 34.4525 33.1176 34.5302 33.0855C34.608 33.0534 34.6787 33.0063 34.7382 32.9468C34.7977 32.8874 34.845 32.8168 34.8772 32.7391C34.9094 32.6614 34.926 32.5781 34.926 32.4939V14.5549C34.9257 14.3851 34.8581 14.2223 34.7379 14.1023C34.6177 13.9823 34.4548 13.9149 34.285 13.9149H23.395C23.2252 13.9149 23.0623 13.9823 22.9421 14.1023C22.8219 14.2223 22.7543 14.3851 22.754 14.5549L22.755 14.5559ZM18.79 36.0489H11.67C11.2565 36.0487 10.86 35.8844 10.5676 35.5921C10.2751 35.2998 10.1105 34.9034 10.11 34.4899C10.1105 34.0764 10.2751 33.6799 10.5675 33.3874C10.8599 33.095 11.2564 32.9305 11.67 32.9299H13.125L13.742 29.9979H5.742C4.74952 29.9964 3.79821 29.6012 3.0967 28.8991C2.3952 28.1971 2.00079 27.2454 2 26.2529V7.53695C2.00132 6.54465 2.3961 5.59337 3.09776 4.89171C3.79943 4.19004 4.7507 3.79527 5.743 3.79395H31.321C32.3133 3.79527 33.2646 4.19004 33.9662 4.89171C34.6679 5.59337 35.0627 6.54465 35.064 7.53695V9.40795H31.945V7.53695C31.945 7.37145 31.8793 7.21273 31.7622 7.09571C31.6452 6.97869 31.4865 6.91295 31.321 6.91295H5.743C5.5775 6.91295 5.41879 6.97869 5.30177 7.09571C5.18474 7.21273 5.119 7.37145 5.119 7.53695V26.2529C5.119 26.4184 5.18474 26.5772 5.30177 26.6942C5.41879 26.8112 5.5775 26.8769 5.743 26.8769H18.078V29.9939H16.293L15.676 32.9259H18.1V34.2939C18.0993 34.9439 18.3465 35.5697 18.791 36.0439L18.79 36.0489ZM27.267 29.4669C27.267 29.0535 27.4313 28.6569 27.7236 28.3646C28.016 28.0722 28.4125 27.9079 28.826 27.9079C29.2396 27.9079 29.6362 28.0722 29.9287 28.3645C30.2213 28.6568 30.3857 29.0534 30.386 29.4669C30.386 29.6718 30.3457 29.8747 30.2673 30.0639C30.1889 30.2532 30.0739 30.4252 29.9291 30.57C29.7842 30.7149 29.6123 30.8298 29.423 30.9082C29.2337 30.9866 29.0309 31.0269 28.826 31.0269C28.4123 31.0269 28.0155 30.8626 27.7229 30.57C27.4304 30.2775 27.267 29.8807 27.267 29.4669Z" fill="var(--svg-icon-color)"></path>
  </g>
  </g>
  <defs>
  <clipPath id="clip0_1116_2">
  <rect width="40" height="40" fill="var(--svg-icon-color)"></rect>
  </clipPath>
  <clipPath id="clip1_1116_2">
  <rect width="40" height="40" fill="var(--svg-icon-color)"></rect>
  </clipPath>
  </defs>
  </svg>`,
  },
]);

const loadingRefresh = ref(false);

const refreshBalance = () => {
  if (loadingRefresh.value) {
    return;
  }
  loadingRefresh.value = true;
  appStore.runGetMemberInfo();
  setTimeout(() => {
    loadingRefresh.value = false;
  }, 1500);
};

const recordClick = (item: any) => {
  if (item.text === "Informações pessoais") {
    showInfo.value = true;
    return;
  }
  if (item.text === "Centro de Segurança") {
    showPwd.value = true;
    return;
  }

  if (item.text == "Música") {
    scrollToTop();
    appStore.setShowMusicState(true);

    return;
  }
  if (item.text == "Bônus de Sugestão") {
    router.push({
      path: "/serviceMessages",
      query: { key: JumpViewType.BonusDeSugestao },
    });
    return;
  }

  if (item.fn) {
    item.fn();
    return;
  }
  if (item.path) {
    router.push(item.path);
  }
};

const onClickMenu = (index) => {
  if (index == 0) {
    router.push({
      path: "/serviceMessages",
      query: { key: JumpViewType.Suporte },
    });
  } else if (index == 1) {
    router.push({
      path: "/serviceMessages",
      query: { key: JumpViewType.Notificacao },
    });
  } else if (index == 2) {
    router.push("/personalCenter/userInfo");
  }
};

const onClickRelatorio = (index) => {
  router.push({ path: "/report", query: { key: index } });
};

function scrollToTop() {}

const iconLoad = (e: any, item: any) => {
  for (let i = 0; i < records.value.length; i++) {
    if (item.icon === records.value[i].icon) {
      records.value[i].iconWidth = Math.ceil(e.target.naturalWidth);
      break;
    }
  }
};

const copyName = () => {
  copy(userInfo.value.uid || "");
  showToast("Copied!");
};

const onClickbox = () => {
  router.push({ path: "/advancement", query: { key: JumpViewTypeVip.VIP } });
};

function truncatedText(text: string, length: number) {
  return text.length > length ? text.substring(0, length) + "..." : text;
}

const getEditPhoneIsRead = computed(() => {
  return !!userInfo.value.phone && userInfo.value.phone.length > 0;
});

const getEditEmailIsRead = computed(() => {
  return !!userInfo.value.email && userInfo.value.email.length > 0;
});

const { run: runApiGetVipListData, data: vipdata } = useRequest(
  () => ApiGetVipListData(),
  {
    onError: () => {},
    onSuccess: (data) => {
      // console.log(data)
    },
  }
);

//获取玩家VIP信息
const { run: runGetUserVipInfo, data: userVipInfo } = useRequest(
  ApiGetVipMemberInfoData,
  {
    manual: true,
    onSuccess(data) {
      appStore.setUserVipInfo(userVipInfo);
      for (let i = 0; i < vipdata.value?.length; i++) {
        if (vipdata.value[i].level == userVipInfo.value.level) {
          validWaged.value = userVipInfo.value.validWaged;
          if (userVipInfo.value.level != 50) {
            curLevelNeedRunning.value = vipdata.value[i].needRunning;
            nextLevelNeedRunning.value = vipdata.value[i + 1].needRunning;
            break;
          }
        }
      }
    },
  }
);

runGetUserVipInfo();

function onClickSaque() {
  if (userInfo.value.pay_password == 0) {
    router.push("/personalCenter/changePayPassword");
  } else {
    router.push("/takeout");
  }
}
</script>

<template>
  <van-popup
    class="app-pay-popup"
    v-model:show="showPersonalCenter"
    position="bottom"
    teleport="body"
    :close-on-click-overlay="true"
    destroy-on-close
    z-index="998"
  >
    <div class="personal-page">
      <!--<AppIndexHeader /> -->
      <!-- <AppPageTitle left-arrow title="CONTA" background="#324b6e" title-weight="700" /> -->
      <!--<section class = "right-up">
        <div class="right-up-item"></div>
        <div class="right-up-item"></div>
        <div class="right-up-item"></div>
    </section> -->
      <div class="pagetitlebg"></div>

      <section class="content">
        <div class="user-info-box-outer">
          <div class="user-info-box">
            <div class="panel">
              <div class="top">
                <div class="top-head">
                  <AppImage src="/img/user/avatar-default.png" />
                </div>
                <div class="detail">
                  <div class="user-name">
                    <span class="copy-txt">
                      <!-- <p style="color: var(--theme-text-color)">Conta:</p> -->
                      <span>{{ userInfo.username }}</span>
                    </span>
                  </div>
                  <div class="nick-name">
                    <p style="color: var(--theme-text-color)">ID:</p>
                    <span>{{ userInfo.uid }}</span>
                    <AppButton
                      fontSize="24"
                      radius="22"
                      width="20"
                      height="38"
                      center
                      trans
                      @click="copyName"
                      style="margin-left: 5px"
                    >
                      <!-- <AppImage
                      class="copy"
                      overlayColor="var(--theme-primary-font-color)"
                      src="/icons/personal_copy.webp"
                      :style="{ scale: 1 }"
                    /> -->
                      <div v-html="copyIcon"></div>
                    </AppButton>
                    <div class="nick-name-line"></div>

                    <div class="right-balance">
                      <AppImage
                        class="right-logo"
                        src="icons/content.webp"
                        alt=""
                      />
                      <label class="right-text" @click="">
                        {{ userInfo.formatAmount }}</label
                      >
                      <div
                        class="right-refresh"
                        :class="{ rotate: loadingRefresh }"
                        @click="refreshBalance"
                      >
                        <svg
                          width="14"
                          height="15"
                          viewBox="0 0 14 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g clip-path="url(#clip0_6585_93480)">
                            <g clip-path="url(#clip1_6585_93480)">
                              <path
                                d="M5.24939 13.0677L7.83053 8.61198L8.90223 10.512C9.66287 10.0297 10.2867 9.37531 10.7181 8.60705C11.1495 7.83879 11.375 6.98057 11.3747 6.10888C11.3747 4.63371 10.3541 3.17073 9.33343 2.21148C11.3333 3.00996 13.125 5.02394 13.125 7.22207C13.1249 8.25425 12.8084 9.26385 12.2146 10.1264C11.6207 10.989 10.7755 11.6668 9.783 12.0763L10.7919 13.625L5.24939 13.0677ZM0.875 7.77793C0.875225 6.74578 1.19169 5.73622 1.78542 4.87362C2.37915 4.01102 3.22426 3.33307 4.21656 2.92324L3.20815 1.375L8.74862 1.93135L6.16642 6.38754L5.09733 4.48756C4.33637 4.96966 3.71225 5.62408 3.28066 6.39246C2.84907 7.16083 2.62339 8.01926 2.62383 8.89113C2.62383 10.3663 3.64445 11.8293 4.66508 12.7885C2.66772 11.9915 0.875561 9.97753 0.875561 7.7794L0.875 7.77793Z"
                                fill="currentColor"
                              ></path>
                            </g>
                          </g>
                          <defs>
                            <clipPath id="clip0_6585_93480">
                              <rect
                                width="14"
                                height="14"
                                fill="white"
                                transform="translate(0 0.5)"
                              ></rect>
                            </clipPath>
                            <clipPath id="clip1_6585_93480">
                              <rect
                                width="13.363"
                                height="14"
                                fill="white"
                                transform="translate(0.318359 0.5)"
                              ></rect>
                            </clipPath>
                          </defs>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <ul class="money-msg-box">
                <li
                  class="money-msg-box-item"
                  @click="() => router.push('/takeout')"
                >
                  <div>
                    <!-- <AppImage
                    src="/img/user/icon-saque"
                    overlayColor="var(--theme-primary-font-color)"
                    :style="{ width: 'var(--app-px-44)' }"
                  /> -->
                    <svg
                      width="40"
                      height="40"
                      fill="var(--svg-icon-color)"
                      class=""
                      viewBox="0 0 53 46.375"
                    >
                      <rect
                        id="7EdIBZie-comomn_tx_icon___69_69"
                        data-name="comomn / tx icon _ 69*69"
                        width="53"
                        height="46.375"
                        opacity="0"
                      ></rect>
                      <path
                        id="7EdIBZie-icon_"
                        data-name="icon "
                        d="M39.1,35.775H4.632A4.623,4.623,0,0,1,0,31.187v-5.9a7.9,7.9,0,0,0,5.117-7.376A7.9,7.9,0,0,0,0,10.531V4.586A4.623,4.623,0,0,1,4.633,0H39.1A4.625,4.625,0,0,1,42.37,1.347a4.543,4.543,0,0,1,1.355,3.239v5.877a7.876,7.876,0,0,0,0,14.89v5.836A4.618,4.618,0,0,1,39.1,35.774ZM11.925,10.96a1.99,1.99,0,0,0-1.987,1.987V24.872a1.99,1.99,0,0,0,1.987,1.988H31.8a1.99,1.99,0,0,0,1.987-1.988V12.947A1.99,1.99,0,0,0,31.8,10.96ZM26.84,9.037a1.988,1.988,0,0,1,1.489.673h2.693L30.463,7.47a1.984,1.984,0,0,0-2.409-1.448L13.265,9.71H23.7l2.691-.621A1.974,1.974,0,0,1,26.84,9.037Zm2.12,15.551H14.765a2.843,2.843,0,0,0-2.84-2.839V16.071a2.844,2.844,0,0,0,2.84-2.839h14.2a2.843,2.843,0,0,0,2.84,2.839v5.679a2.845,2.845,0,0,0-2.841,2.838Zm-7.1-8.328a2.65,2.65,0,1,0,2.65,2.65A2.653,2.653,0,0,0,21.863,16.26Z"
                        transform="translate(4.638 4.969)"
                      ></path>
                    </svg>
                  </div>
                  <div :style="{ height: 'var(--app-px-10)' }" />
                  <span>Saque</span>
                </li>
                <li
                  class="money-msg-box-item"
                  @click="() => appStore.setPayVisble(true)"
                >
                  <div>
                    <!-- <AppImage
                    src="/img/user/icon-deposite"
                    overlayColor="var(--theme-primary-font-color)"
                    :style="{
                      top: 'var(--app-px-0)',
                      width: 'var(--app-px-44)',
                    }"
                  /> -->
                    <svg
                      width="40"
                      height="40"
                      fill="var(--svg-icon-color)"
                      class=""
                      viewBox="0 0 53 46.375"
                    >
                      <rect
                        id="qktsPu6o-comomn_cz_icon___69_69"
                        data-name="comomn / cz icon _ 69*69"
                        width="53"
                        height="46.375"
                        opacity="0"
                      ></rect>
                      <path
                        id="qktsPu6o-cz"
                        d="M1030.33,659.489a6.633,6.633,0,0,1-6.625-6.625V630.339a6.633,6.633,0,0,1,6.625-6.625H1060.8a6.632,6.632,0,0,1,6.625,6.625v2.65H1050.58a5.962,5.962,0,1,0,0,11.925h16.849v7.95a6.632,6.632,0,0,1-6.625,6.625Zm.757-7.409v2.69a1.305,1.305,0,0,0,2.61,0v-2.69a1.305,1.305,0,0,0-2.61,0Zm0-7.8v2.69a1.305,1.305,0,1,0,2.61,0v-2.69a1.305,1.305,0,0,0-2.61,0Zm0-7.8v2.69a1.305,1.305,0,1,0,2.61,0v-2.69a1.305,1.305,0,1,0-2.61,0Zm0-7.8v2.69a1.305,1.305,0,0,0,2.61,0v-2.69a1.305,1.305,0,0,0-2.61,0Zm17.461,10.263a2.981,2.981,0,1,1,2.981,2.981A2.981,2.981,0,0,1,1048.548,638.951Z"
                        transform="translate(-1019.399 -618.745)"
                        stroke-width="1"
                      ></path>
                    </svg>
                  </div>
                  <div :style="{ height: 'var(--app-px-10)' }" />
                  <span>Depositar</span>
                </li>
                <!-- <li
                class="money-msg-box-item"
                @click="() => router.push('/promotion?key=6')"
              >
                <div>
                  
                  <svg
                    t="1745425736977"
                    class="icon"
                    viewBox="0 0 1137 1000"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="1742"
                    width="46.375"
                    height="46.375"
                  >
                    <path
                      d="M455.111111 512a56.888889 56.888889 0 0 1 0-113.777778h91.932445L471.324444 321.706667a55.523556 55.523556 0 0 1 0-77.994667 54.158222 54.158222 0 0 1 77.198223 0L625.777778 321.706667l77.198222-77.994667a54.215111 54.215111 0 0 1 77.255111 0 55.523556 55.523556 0 0 1 0 77.994667L704.512 398.222222H796.444444a56.888889 56.888889 0 0 1 0 113.777778h-113.777777v56.888889h113.777777a56.888889 56.888889 0 0 1 0 113.777778h-113.777777v113.777777a56.888889 56.888889 0 0 1-113.777778 0v-113.777777H455.111111a56.888889 56.888889 0 0 1 0-113.777778h113.777778V512H455.111111z m170.666667 512a510.008889 510.008889 0 0 1-366.307556-154.680889l80.668445-80.611555A397.653333 397.653333 0 1 0 227.555556 512v0.682667h97.507555a62.577778 62.577778 0 0 1 0 83.740444l-115.825778 125.553778a51.655111 51.655111 0 0 1-77.255111 0L16.099556 596.423111a62.919111 62.919111 0 0 1 0-83.740444H113.777778V512a512 512 0 1 1 512 512z"
                      p-id="1743"
                      fill="var(--svg-icon-color)"
                    ></path>
                  </svg>
                </div>
                <span>Poupança</span>
              </li> -->
              </ul>

              <div class="vip-box">
                <div class="vip-box-vipbg">
                  <span class="vip-box-vipbg-num">
                    vip{{ userVipInfo ? userVipInfo.level : 0 }}
                  </span>
                  <div class="vip-box-vipbg-info" @click="onClickbox">
                    <span>
                      Restantes
                      <span
                        style="color: var(--theme-font-on-background-color)"
                        >{{
                          "VIP" + (userVipInfo ? userVipInfo.level + 1 : 1)
                        }}</span
                      >
                      Valor restante para aposta
                      <p style="color: var(--theme-font-on-background-color)">
                        {{ UsAmountFormat(nextLevelNeedRunning) }}
                      </p>
                    </span>
                    <AppImage
                      class="vip-box-vipbg-info-img"
                      src="/img/user/icon-arrow-green"
                      :style="{ width: 'var(--app-px-16)' }"
                    />
                  </div>
                  <div class="vip-box-vipbg-line"></div>
                  <div class="vip-box-vipbg-info2">
                    <span>{{ userVipInfo?.level || 0 }}</span>
                  </div>
                  <span class="vip-box-vipbg-aposta">
                    Aposta para promoção
                  </span>
                  <div class="vip-box-vipbg-progress">
                    <AppProgress
                      :value="validWaged"
                      :max="nextLevelNeedRunning"
                      :width="400"
                      :height="18"
                      :type="1"
                      topcolor="#42b983"
                      bgcolor="black"
                      :op="0.08"
                    ></AppProgress>
                    <span>{{ UsAmountFormat(validWaged) }}</span>
                    <span>/</span>
                    <span>{{ UsAmountFormat(nextLevelNeedRunning) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ul class="reports">
          <li
            class="item"
            @click="() => router.push('/personalCenter/retrieve')"
          >
            <div class="icon">
              <!-- <AppImage
              src="/img/user/icon-Recuperar"
              :style="{ width: 'var(--app-px-45)' }"
            /> -->
              <svg
                width="30"
                height="30"
                viewBox="0 0 50 50"
                fill="var(--svg-icon-color)"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M40 23.75C40 32.7246 32.7246 40 23.75 40C14.7754 40 7.5 32.7246 7.5 23.75C7.5 14.7754 14.7754 7.5 23.75 7.5C32.7246 7.5 40 14.7754 40 23.75ZM36.9045 40.4401C33.2863 43.2957 28.7172 45 23.75 45C12.0139 45 2.5 35.486 2.5 23.75C2.5 12.0139 12.0139 2.5 23.75 2.5C35.486 2.5 45 12.0139 45 23.75C45 28.7172 43.2957 33.2863 40.4401 36.9045L46.7678 43.2322C47.7441 44.2085 47.7441 45.7915 46.7678 46.7678C45.7915 47.7441 44.2085 47.7441 43.2322 46.7678L36.9045 40.4401Z"
                  fill="var(--svg-icon-color)"
                ></path>
                <path
                  d="M24.375 22.5C29.5527 22.5 33.75 20.8211 33.75 18.75C33.75 16.6789 29.5527 15 24.375 15C19.1973 15 15 16.6789 15 18.75C15 20.8211 19.1973 22.5 24.375 22.5Z"
                  fill="var(--svg-icon-color)"
                ></path>
                <path
                  d="M31.597 23.6218C29.6402 24.3552 27.0754 24.7592 24.375 24.7592C21.6746 24.7592 19.1098 24.3552 17.153 23.6218C16.2574 23.2861 15.5525 22.9047 15 22.5V23.9861C15 24.9247 15.9751 25.8071 17.7459 26.4707C19.5166 27.1345 21.8708 27.5 24.375 27.5C26.8792 27.5 29.2334 27.1345 31.0041 26.4708C32.7748 25.8071 33.75 24.9247 33.75 23.9861V22.5C33.1975 22.9047 32.4926 23.2861 31.597 23.6218Z"
                  fill="var(--svg-icon-color)"
                ></path>
                <path
                  d="M31.597 28.5911C29.6402 29.3045 27.0754 29.6974 24.375 29.6974C21.6746 29.6974 19.1098 29.3045 17.153 28.591C16.2574 28.2645 15.5525 27.8936 15 27.4999V29.0822C15 30.9699 19.1973 32.5002 24.375 32.5002C29.5527 32.5002 33.75 30.9699 33.75 29.0822V27.5C33.1976 27.8935 32.4926 28.2646 31.597 28.5911Z"
                  fill="var(--svg-icon-color)"
                ></path>
              </svg>
            </div>
            <span>Recuperar o saldo</span>
            <!-- <AppImage class="arrow" src="/img/user/icon-arrow-green" /> -->
            <div class="arrow" v-html="arrowIcon"></div>
          </li>

          <li class="item" @click="onClickRelatorio(0)">
            <div class="icon">
              <!-- <AppImage
              src="/img/user/icon-Detalhes"
              :style="{ width: 'var(--app-px-45)' }"
            /> -->
              <svg
                width="30"
                height="30"
                viewBox="0 0 50 50"
                fill="var(--svg-icon-color)"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M29.8074 44.6718C27.194 43.5968 25.0343 41.6488 23.6963 39.1597C22.3583 36.6706 21.925 33.7946 22.4701 31.0218C23.0153 28.2489 24.5052 25.7508 26.6858 23.9534C28.8664 22.1559 31.6027 21.1702 34.4287 21.1643C37.6651 21.1572 40.7718 22.4354 43.0661 24.718C45.0699 26.7107 46.3208 29.3366 46.6052 32.1482C46.8897 34.9599 46.1902 37.7831 44.6262 40.1368C43.5115 41.8149 41.9992 43.1912 40.2237 44.143C38.4481 45.0948 36.4646 45.5925 34.4501 45.5918C32.8573 45.5914 31.28 45.2788 29.8074 44.6718ZM28.2526 27.1555C26.8144 28.5746 25.9128 30.4486 25.7019 32.458C25.4911 34.4673 25.9837 36.4876 27.0959 38.1743C28.2077 39.861 29.8704 41.1098 31.7999 41.708C33.7295 42.3061 35.8068 42.2164 37.6776 41.4543C39.5486 40.692 41.0973 39.3044 42.0596 37.5281C43.022 35.7517 43.3384 33.6966 42.955 31.713C42.5715 29.7295 41.512 27.9405 39.957 26.6508C38.402 25.3611 36.4477 24.6507 34.4275 24.6405H34.3851C32.0901 24.641 29.8875 25.5449 28.2538 27.1568L28.2526 27.1555ZM8.80493 44.8705C7.35648 44.8617 5.97002 44.2824 4.94568 43.2583C3.92133 42.2342 3.3417 40.8477 3.33252 39.3993V9.87553C3.34008 8.42657 3.91915 7.03913 4.94385 6.01467C5.96854 4.99021 7.35598 4.41152 8.80493 4.4043H33.2098C34.6586 4.41185 36.0459 4.9907 37.0703 6.01513C38.0947 7.03955 38.6738 8.42679 38.6813 9.87553V18.6468C37.4133 18.2624 36.0973 18.0579 34.7723 18.0393V9.78928C34.7732 9.58388 34.7335 9.38037 34.6552 9.19048C34.5768 9.0006 34.4615 8.8281 34.3161 8.68301C34.1712 8.53739 33.9989 8.42217 33.8089 8.34404C33.6189 8.26591 33.4152 8.22647 33.2098 8.22803H8.80493C8.59937 8.22658 8.39571 8.26607 8.20557 8.34419C8.01542 8.42231 7.84292 8.53747 7.69775 8.68301C7.5527 8.82828 7.43771 9.00085 7.35962 9.19071C7.28153 9.38057 7.24156 9.58399 7.24243 9.78928V39.3193C7.2417 39.5245 7.28154 39.728 7.35962 39.9178C7.4377 40.1076 7.55281 40.2802 7.69775 40.4255C7.8433 40.5706 8.01551 40.6854 8.20557 40.7635C8.39562 40.8416 8.59946 40.8814 8.80493 40.8805H21.0547C21.9251 42.3609 23.0082 43.7056 24.2688 44.8718L8.80493 44.8705ZM34.4037 39.2393C34.4037 39.2393 34.4038 39.2393 32.6163 39.223C33.4091 38.3913 33.9948 37.3846 34.3262 36.2843C33.7523 36.7466 33.1009 37.1034 32.4023 37.338C31.9622 37.4229 31.5083 37.4018 31.0779 37.2763C30.6475 37.1509 30.253 36.9248 29.9274 36.6168C29.6013 36.3089 29.3534 35.9278 29.2041 35.505C29.0548 35.0822 29.0082 34.6299 29.0686 34.1855C29.1449 31.7368 31.8849 30.6855 34.4174 27.5605C36.9099 30.7218 39.6987 31.8105 39.7424 34.248C39.7955 34.6925 39.742 35.1431 39.5862 35.5626C39.4303 35.9822 39.1765 36.3586 38.8461 36.6605C38.5162 36.9627 38.1192 37.1819 37.6877 37.2999C37.2562 37.418 36.8028 37.4314 36.3651 37.3393C35.6886 37.1028 35.0626 36.7411 34.52 36.273C34.8369 37.3828 35.4092 38.4029 36.1911 39.2518L34.4037 39.2393ZM20.4437 24.718H12.8186C12.2898 24.7128 11.7845 24.5004 11.4105 24.1265C11.0366 23.7525 10.8237 23.2469 10.8185 22.718C10.815 22.4552 10.8644 22.1943 10.9631 21.9506C11.0619 21.707 11.2085 21.4856 11.394 21.2993C11.5805 21.1134 11.8018 20.9667 12.0459 20.868C12.29 20.7692 12.5516 20.7204 12.8149 20.7243H20.4401C20.7027 20.7243 20.9628 20.776 21.2054 20.8765C21.4481 20.977 21.6685 21.1244 21.8542 21.3101C22.04 21.4958 22.1871 21.7163 22.2876 21.9589C22.3881 22.2016 22.4402 22.4616 22.4402 22.7243C22.4402 23.2547 22.2293 23.7634 21.8542 24.1385C21.4792 24.5136 20.9705 24.7243 20.4401 24.7243L20.4437 24.718ZM26.9501 18.1255H12.7002C12.1715 18.12 11.666 17.9075 11.2921 17.5336C10.9182 17.1598 10.7056 16.6542 10.7001 16.1255C10.6962 15.8621 10.7451 15.6006 10.8441 15.3565C10.9431 15.1124 11.0901 14.8906 11.2762 14.7043C11.4622 14.5181 11.6835 14.3712 11.9275 14.2724C12.1714 14.1736 12.4334 14.125 12.6965 14.1293H26.9464C27.4751 14.1348 27.9806 14.3473 28.3545 14.7212C28.7284 15.0951 28.9407 15.6005 28.9462 16.1293C28.9499 16.3924 28.9009 16.6537 28.8019 16.8976C28.7029 17.1415 28.5561 17.3631 28.3701 17.5493C28.1873 17.7319 27.97 17.8766 27.7313 17.9753C27.4926 18.074 27.2371 18.1246 26.9788 18.1243L26.9501 18.1255Z"
                  fill="var(--svg-icon-color)"
                ></path>
              </svg>
            </div>
            <span>Conta</span>
            <!-- <AppImage
            overlayColor="var(--theme-primary-font-color)"
            class="arrow"
            src="/img/user/icon-arrow-green"
          /> -->
            <div class="arrow" v-html="arrowIcon"></div>
          </li>

          <li class="item" @click="onClickRelatorio(1)">
            <div class="icon">
              <!-- <AppImage
              src="/img/user/icon-Recordes.webp"
              :style="{ width: 'var(--app-px-45)' }"
            /> -->
              <svg
                width="30"
                height="30"
                viewBox="0 0 50 50"
                fill="var(--svg-icon-color)"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M29.7825 44.5269C27.1693 43.4517 25.0098 41.5036 23.672 39.0145C22.3343 36.5255 21.9011 33.6495 22.4462 30.8768C22.9912 28.1038 24.481 25.6057 26.6617 23.8081C28.8424 22.0106 31.579 21.025 34.405 21.0194C37.6412 21.0132 40.7477 22.2913 43.0425 24.5731C45.0461 26.5659 46.2967 29.1917 46.5814 32.0032C46.8661 34.8147 46.1672 37.6379 44.6037 39.9918C43.4889 41.6698 41.9764 43.046 40.2009 43.9978C38.4254 44.9496 36.442 45.4473 34.4275 45.4469C32.8339 45.4465 31.2559 45.134 29.7825 44.5269ZM28.2288 27.0107C26.7905 28.4294 25.889 30.3032 25.6781 32.3124C25.4672 34.3216 25.9601 36.3417 27.0725 38.0281C28.1845 39.7149 29.847 40.9638 31.7767 41.562C33.7065 42.1601 35.7839 42.0704 37.655 41.3081C39.5258 40.5457 41.0743 39.158 42.0367 37.3817C42.999 35.6055 43.3156 33.5504 42.9325 31.5668C42.5487 29.5835 41.4891 27.7945 39.9342 26.5049C38.3792 25.2153 36.4251 24.5048 34.405 24.4944H34.3613C32.0658 24.4942 29.8625 25.3982 28.2288 27.0107ZM24.6387 44.6981H5.60249C5.00132 44.6978 4.42481 44.459 3.99948 44.0341C3.57415 43.6093 3.33473 43.033 3.33374 42.4319C3.3344 41.8305 3.57366 41.2539 3.99902 40.8288C4.42439 40.4036 5.0011 40.1647 5.60249 40.1643H20.9775C21.8994 41.8948 23.1433 43.4332 24.6425 44.6969L24.6387 44.6981ZM34.405 39.2393C34.405 39.2393 34.405 39.2456 32.6163 39.2231C33.4093 38.3915 33.9951 37.3847 34.3262 36.2843C33.7525 36.747 33.1011 37.1043 32.4025 37.3394C31.962 37.4243 31.5077 37.403 31.077 37.2774C30.6464 37.1517 30.2519 36.9254 29.9262 36.6169C29.6005 36.3091 29.3528 35.9281 29.2037 35.5055C29.0546 35.0829 29.0083 34.6309 29.0688 34.1869C29.145 31.7369 31.885 30.6869 34.4175 27.5619C36.91 30.7219 39.6988 31.8119 39.7425 34.2506C39.7955 34.6949 39.742 35.1454 39.5863 35.565C39.4307 35.9845 39.1775 36.3609 38.8475 36.6631C38.5173 36.9647 38.12 37.1835 37.6886 37.3013C37.2572 37.4191 36.8039 37.4325 36.3663 37.3406C35.6892 37.104 35.0628 36.7418 34.52 36.2731C34.8368 37.3833 35.4092 38.4038 36.1913 39.2531L34.405 39.2393ZM19.5175 36.2019H5.60249C5.00132 36.2015 4.42481 35.9627 3.99948 35.5379C3.57415 35.113 3.33473 34.5368 3.33374 33.9356C3.33473 33.3343 3.57411 32.758 3.9994 32.3329C4.4247 31.9079 5.0012 31.6688 5.60249 31.6681H19.2175C19.185 32.0781 19.1675 32.4981 19.1675 32.9181C19.167 34.0229 19.2848 35.1246 19.5187 36.2044L19.5175 36.2019ZM20.0175 27.8681H5.60249C5.00132 27.8678 4.42481 27.629 3.99948 27.2041C3.57415 26.7793 3.33473 26.2031 3.33374 25.6019C3.3344 25.0004 3.57362 24.4237 3.99895 23.9983C4.42428 23.573 5.00099 23.3338 5.60249 23.3331H22.5063C21.4245 24.6929 20.5813 26.2263 20.0125 27.8681H20.0175ZM26.925 19.5344H5.60249C5.00153 19.534 4.42523 19.2954 3.99994 18.8708C3.57464 18.4463 3.33506 17.8703 3.33374 17.2694C3.3344 16.6679 3.57362 16.0911 3.99895 15.6658C4.42428 15.2405 5.00099 15.0013 5.60249 15.0006H35.7275C36.3146 15.0022 36.8775 15.2353 37.2938 15.6493C37.5058 15.8625 37.6724 16.1164 37.7837 16.3956C37.8949 16.6749 37.9485 16.9739 37.9412 17.2744C37.941 17.4705 37.9158 17.6658 37.8662 17.8556C36.7864 17.6219 35.6848 17.5042 34.58 17.5043C31.8931 17.5025 29.2525 18.2032 26.92 19.5369L26.925 19.5344ZM5.60745 10.8619C5.00551 10.8626 4.42796 10.6242 4.00162 10.1993C3.57528 9.77435 3.33506 9.19752 3.33374 8.59558C3.3344 7.99419 3.57366 7.4177 3.99902 6.99257C4.42439 6.56744 5.0011 6.32846 5.60249 6.32812H35.7275C36.3135 6.32966 36.8752 6.56284 37.29 6.97685C37.502 7.19 37.6686 7.44389 37.7799 7.72316C37.8912 8.00243 37.9448 8.3013 37.9375 8.60184C37.9368 9.20301 37.6977 9.77937 37.2726 10.2045C36.8475 10.6296 36.2712 10.8687 35.67 10.8694L5.60745 10.8619Z"
                  fill="var(--svg-icon-color)"
                ></path>
              </svg>
            </div>
            <span>Apostas</span>

            <div class="arrow" v-html="arrowIcon"></div>
          </li>

          <li class="item" @click="onClickRelatorio(2)">
            <div class="icon">
              <!-- <AppImage
              src="/img/user/icon-Relatorio.webp"
              :style="{ width: 'var(--app-px-45)' }"
            /> -->
              <svg
                width="30"
                height="30"
                viewBox="0 0 50 50"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_11_484)">
                  <path
                    d="M43.5912 45.2413L40.8325 42.4825C38.8306 43.962 36.4054 44.7571 33.9162 44.75C32.3834 44.7542 30.865 44.4543 29.4489 43.8677C28.0327 43.2811 26.747 42.4194 25.6662 41.3325C24.5793 40.2517 23.7176 38.966 23.131 37.5499C22.5444 36.1337 22.2445 34.6153 22.2487 33.0825C22.2446 31.5497 22.5445 30.0313 23.1311 28.6152C23.7177 27.199 24.5794 25.9134 25.6662 24.8325C26.747 23.7456 28.0327 22.884 29.4489 22.2973C30.865 21.7107 32.3834 21.4108 33.9162 21.415C35.449 21.4109 36.9674 21.7109 38.3835 22.2975C39.7996 22.8841 41.0853 23.7457 42.1662 24.8325C43.253 25.9134 44.1147 27.199 44.7013 28.6152C45.2879 30.0313 45.5878 31.5497 45.5837 33.0825C45.589 35.5586 44.8005 37.9713 43.3337 39.9662L46.0962 42.7288C46.3291 42.9464 46.4992 43.2225 46.5887 43.5284C46.6782 43.8343 46.6838 44.1587 46.605 44.4675C46.5262 44.7765 46.3656 45.0585 46.1401 45.2839C45.9147 45.5094 45.6327 45.6699 45.3237 45.7487C45.1796 45.7859 45.0313 45.8049 44.8825 45.805C44.6401 45.8042 44.4005 45.7538 44.1785 45.6569C43.9564 45.5599 43.7565 45.4185 43.5912 45.2413ZM26.0025 33.0838C26.0048 35.1819 26.8393 37.1934 28.3229 38.677C29.8065 40.1606 31.8181 40.9952 33.9162 40.9975C36.0143 40.9952 38.0259 40.1606 39.5095 38.677C40.9931 37.1934 41.8276 35.1819 41.83 33.0838C41.8276 30.9856 40.9931 28.974 39.5095 27.4904C38.0259 26.0068 36.0143 25.1723 33.9162 25.17C31.8181 25.1723 29.8065 26.0068 28.3229 27.4904C26.8393 28.974 26.0048 30.9856 26.0025 33.0838ZM8.80371 44.8725C7.35583 44.8649 5.96933 44.2868 4.94505 43.2634C3.92076 42.2401 3.34132 40.8541 3.33246 39.4062V9.88248C3.3387 8.43287 3.91697 7.04438 4.94154 6.01888C5.9661 4.99337 7.35411 4.41381 8.80371 4.40625H33.2075C34.6562 4.4138 36.0434 4.99269 37.0679 6.01712C38.0923 7.04155 38.6711 8.42878 38.6787 9.87752V18.6475C37.4106 18.2634 36.0948 18.0593 34.77 18.0413V9.79123C34.7707 9.58585 34.7307 9.38242 34.6524 9.19255C34.5741 9.00269 34.459 8.83013 34.3137 8.68497C34.1685 8.53948 33.9958 8.42434 33.8057 8.34622C33.6155 8.26811 33.4118 8.22864 33.2062 8.23003H8.80121C8.59588 8.22888 8.39237 8.2685 8.20247 8.3466C8.01257 8.42471 7.84007 8.53969 7.69497 8.68497C7.54957 8.83005 7.43438 9.00259 7.35607 9.19247C7.27775 9.38236 7.23786 9.58583 7.23871 9.79123V39.3213C7.238 39.5266 7.27795 39.7301 7.35626 39.92C7.43456 40.1099 7.54968 40.2824 7.69497 40.4275C7.8404 40.5724 8.01296 40.687 8.20278 40.7651C8.3926 40.8432 8.59596 40.8831 8.80121 40.8825H21.0512C21.9216 42.3629 23.0043 43.7076 24.265 44.8738L8.80371 44.8725ZM20.4425 24.72H12.8175C12.2886 24.7148 11.783 24.5024 11.409 24.1285C11.0351 23.7545 10.8227 23.2488 10.8175 22.72C10.8142 22.4571 10.8634 22.1961 10.9624 21.9525C11.0613 21.7088 11.208 21.4874 11.3937 21.3012C11.5799 21.1152 11.8015 20.9686 12.0454 20.8698C12.2893 20.771 12.5506 20.7222 12.8137 20.7263H20.4387C20.9691 20.7263 21.4779 20.937 21.8529 21.3121C22.228 21.6871 22.4387 22.1958 22.4387 22.7263C22.4387 23.2567 22.228 23.7654 21.8529 24.1404C21.4779 24.5155 20.9691 24.7262 20.4387 24.7262L20.4425 24.72ZM26.9425 18.1263H12.6925C12.1636 18.1211 11.658 17.9086 11.284 17.5347C10.9101 17.1607 10.6977 16.655 10.6925 16.1262C10.6884 15.8628 10.7373 15.6013 10.8363 15.3571C10.9353 15.113 11.0823 14.8912 11.2687 14.705C11.4547 14.5189 11.6763 14.372 11.9203 14.2732C12.1642 14.1744 12.4256 14.1257 12.6887 14.13H26.9387C27.4674 14.1355 27.9729 14.348 28.3468 14.7219C28.7207 15.0958 28.9332 15.6013 28.9387 16.13C28.9427 16.3933 28.8937 16.6545 28.7947 16.8985C28.6957 17.1424 28.5487 17.364 28.3625 17.55C28.18 17.7326 27.9633 17.8774 27.7248 17.976C27.4862 18.0747 27.2306 18.1253 26.9725 18.125L26.9425 18.1263Z"
                    fill="var(--svg-icon-color)"
                  ></path>
                </g>
                <defs>
                  <clipPath id="clip0_11_484">
                    <rect width="50" height="50" fill="white"></rect>
                  </clipPath>
                </defs>
              </svg>
            </div>
            <span>Relatório</span>
            <!-- <AppImage
            class="arrow"
            overlayColor="var(--theme-primary-font-color)"
            src="/img/user/icon-arrow-green"
          /> -->
            <div class="arrow" v-html="arrowIcon"></div>
          </li>

          <li class="item" @click="() => onClickSaque()">
            <div class="icon">
              <!-- <AppImage
              src="/img/user/icon-Gestao.webp"
              :style="{ width: 'var(--app-px-45)' }"
            /> -->
              <svg
                width="30"
                height="30"
                viewBox="0 0 50 50"
                fill="red"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_11_483)">
                  <path
                    d="M30.5838 43.28L30.3337 41.7062L30.2513 41.6238L28.76 42.2038C28.2056 42.4268 27.5891 42.4413 27.0249 42.2446C26.4606 42.0478 25.9868 41.6531 25.6913 41.1337L23.7001 37.7338C23.4172 37.2225 23.3204 36.6289 23.4262 36.0543C23.5319 35.4797 23.8337 34.9596 24.28 34.5825L25.6163 33.5825V33.4062L24.3737 32.4062C23.8973 32.0511 23.5728 31.5289 23.4652 30.9445C23.3577 30.36 23.475 29.7565 23.7937 29.255L25.785 25.855C26.0721 25.3485 26.5314 24.9619 27.0795 24.7656C27.6276 24.5692 28.2279 24.5761 28.7712 24.785L30.345 25.365L30.4288 25.2812L30.6787 23.6212C30.7598 23.0426 31.0488 22.5133 31.4916 22.1321C31.9344 21.751 32.5008 21.544 33.0851 21.55H37.065C37.6645 21.5363 38.2467 21.7513 38.6934 22.1513C39.1401 22.5514 39.4178 23.1064 39.47 23.7038L39.72 25.2812L39.8025 25.365L41.2937 24.785C41.8482 24.562 42.4646 24.5475 43.0289 24.7442C43.5932 24.9409 44.067 25.3356 44.3625 25.855L46.3538 29.255C46.6364 29.7663 46.7331 30.3598 46.6273 30.9344C46.5216 31.509 46.22 32.0291 45.7738 32.4062L44.4375 33.4062V33.5726L45.7738 34.5725C46.2302 34.9412 46.5454 35.4561 46.6661 36.0302C46.7868 36.6044 46.7056 37.2026 46.4363 37.7238L44.445 41.1987C44.1308 41.6988 43.6558 42.077 43.0981 42.2713C42.5404 42.4655 41.9332 42.4642 41.3763 42.2675L39.8025 41.6875L39.72 41.7713L39.47 43.4275C39.3376 43.9899 39.0204 44.4917 38.5693 44.8526C38.1181 45.2136 37.5591 45.4129 36.9813 45.4188L36.9712 45.4375H32.94C32.3486 45.4376 31.7787 45.2161 31.3425 44.8168C30.9063 44.4174 30.6356 43.8691 30.5838 43.28ZM31.2538 38.7288C31.5912 38.9793 31.9492 39.2009 32.3238 39.3913L33.0738 39.7225L33.4988 42.3688H36.4012L36.8163 39.6325L37.5663 39.3012C37.9405 39.1108 38.298 38.8892 38.6351 38.6388L39.2975 38.1387L41.8688 39.1387L43.3688 36.5675L41.2113 34.9113L41.2937 34.0825V32.8325L41.2113 32.0038L43.3688 30.3438L41.8688 27.7751L39.2975 28.775L38.6351 28.275C38.298 28.0245 37.9405 27.803 37.5663 27.6125L36.8163 27.2775L36.4012 24.5438H33.4838L33.07 27.2775L32.3201 27.6125C31.9285 27.7714 31.5667 27.9953 31.25 28.275L30.5875 28.775L28.0162 27.7751L26.525 30.3438L28.6825 32.0038L28.6 32.8325V34.1688L28.6825 34.9975L26.525 36.6538L28.0162 39.225L30.5875 38.225L31.2538 38.7288ZM41.6288 27.275C41.62 27.3575 41.7088 27.3576 41.6201 27.2738L41.6288 27.275ZM20.7238 37.9388H8.17127C7.5356 37.9405 6.90588 37.816 6.31862 37.5727C5.73136 37.3294 5.19821 36.972 4.75002 36.5212C3.84331 35.6084 3.33424 34.3742 3.33377 33.0876V11.5163C3.33143 10.8787 3.45561 10.247 3.69914 9.65783C3.94268 9.06863 4.30074 8.53358 4.75254 8.08374C5.20172 7.6328 5.73575 7.2753 6.32381 7.0318C6.91187 6.78829 7.54227 6.66367 8.17874 6.66505H40.1563C40.7926 6.66363 41.4228 6.78821 42.0107 7.03172C42.5985 7.27523 43.1324 7.63278 43.5813 8.08374C44.0331 8.53353 44.3912 9.06854 44.6347 9.65776C44.8783 10.247 45.0024 10.8787 45 11.5163V22.2738C43.838 21.2433 42.5226 20.3999 41.1013 19.7738V19.1488H39.405C37.992 18.7217 36.5237 18.5052 35.0475 18.5063C33.5713 18.5053 32.1031 18.7218 30.69 19.1488H7.23255V33.2738C7.23641 33.5249 7.33788 33.7646 7.51544 33.9421C7.69301 34.1197 7.9327 34.2211 8.18378 34.225L20.0588 34.1087C20.109 35.4122 20.3292 36.7035 20.7138 37.95L20.7238 37.9388ZM8.20377 10.6525C7.95177 10.6538 7.71046 10.7544 7.53215 10.9325C7.35384 11.1106 7.25288 11.3518 7.25124 11.6038V15.2287H41.1V11.6038C41.0955 11.3527 40.9937 11.1131 40.816 10.9357C40.6383 10.7582 40.3986 10.6567 40.1475 10.6525H8.19499H8.20377ZM32.175 33.4125C32.175 32.6646 32.4721 31.9473 33.001 31.4185C33.5298 30.8896 34.2471 30.5925 34.995 30.5925C35.7429 30.5925 36.4602 30.8896 36.9891 31.4185C37.5179 31.9473 37.8151 32.6646 37.8151 33.4125C37.8076 34.1581 37.508 34.871 36.9808 35.3983C36.4535 35.9255 35.7406 36.2251 34.995 36.2325C34.6238 36.2337 34.2561 36.1616 33.9128 36.0203C33.5696 35.879 33.2577 35.6714 32.9949 35.4093C32.7321 35.1471 32.5236 34.8357 32.3814 34.4928C32.2392 34.15 32.1661 33.7824 32.1662 33.4113L32.175 33.4125ZM11.8938 26.4362C11.657 26.4341 11.4231 26.3839 11.2062 26.2888C10.9892 26.1933 10.7933 26.0558 10.6298 25.8842C10.4663 25.7125 10.3385 25.5102 10.2538 25.2888C10.1685 25.069 10.1281 24.8344 10.135 24.5988C10.1421 24.1133 10.3381 23.6497 10.6814 23.3064C11.0247 22.9631 11.4883 22.7671 11.9738 22.76H17.755C17.9897 22.7602 18.2222 22.8052 18.44 22.8925C18.6641 22.9845 18.8676 23.12 19.0388 23.2913C19.2107 23.4619 19.3455 23.6661 19.435 23.8912C19.5264 24.1162 19.5714 24.3572 19.5675 24.6C19.5591 25.0854 19.3618 25.5485 19.0176 25.8909C18.6734 26.2333 18.2093 26.4279 17.7238 26.4337L11.8938 26.4362Z"
                    fill="var(--svg-icon-color)"
                  ></path>
                </g>
                <defs>
                  <clipPath id="clip0_11_483">
                    <rect width="50" height="50" fill="white"></rect>
                  </clipPath>
                </defs>
              </svg>
            </div>
            <span>Gestão de saque</span>
            <!-- <AppImage
            class="arrow"
            overlayColor="var(--theme-primary-font-color)"
            src="/img/user/icon-arrow-green"
          /> -->
            <div class="arrow" v-html="arrowIcon"></div>
          </li>
        </ul>

        <ul class="reports">
          <li
            v-for="(item, idx) in records"
            :key="item.icon"
            class="item"
            @click="() => recordClick(item)"
          >
            <div class="icon">
              <!-- <AppImage
              :src="'/img/user/' + item.icon"
              :style="{ width: 'var(--app-px-40)' }"
              @load="($event) => iconLoad($event, item)"
            /> -->
              <!-- <template v-html="item.svg">{{123}}</template> -->
              <div class="svg-icon" v-html="item.svg"></div>
            </div>
            <span>{{ item.text }}</span>
            <!-- <AppImage
            class="arrow"
            overlayColor="var(--theme-primary-font-color)"
            src="/img/user/icon-arrow-green"
          /> -->
            <div class="arrow" v-html="arrowIcon"></div>
          </li>
        </ul>
        <ul class="reports reports-second">
          <li class="item" @click="() => logout()">
            <div class="icon">
              <!-- <AppImage src="/img/user/personal_sair" /> -->
              <svg
                width="30"
                height="30"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_6581_88424)">
                  <path
                    d="M8.00195 37.333C6.58779 37.3319 5.23199 36.7696 4.23193 35.7698C3.23187 34.7699 2.66929 33.4142 2.66797 32V8C2.66956 6.58601 3.2319 5.23037 4.23193 4.23071C5.23197 3.23106 6.58796 2.66903 8.00195 2.66797H24.002C25.4159 2.66929 26.7715 3.23136 27.7715 4.23096C28.7715 5.23056 29.3341 6.58609 29.3359 8V9.33398C29.3359 9.55285 29.2928 9.76949 29.209 9.97168C29.1252 10.1739 29.0025 10.3577 28.8477 10.5125C28.6928 10.6672 28.5089 10.7899 28.3066 10.8735C28.1044 10.9572 27.8878 11.0001 27.6689 11C27.4502 11 27.2334 10.957 27.0312 10.8733C26.8291 10.7896 26.6454 10.6667 26.4907 10.512C26.336 10.3573 26.2136 10.1736 26.1299 9.97144C26.0462 9.76931 26.0029 9.55277 26.0029 9.33398V8C26.0029 7.46957 25.7921 6.96077 25.417 6.58569C25.0419 6.21062 24.5334 6 24.0029 6H8.00293C7.4725 6 6.96394 6.21062 6.58887 6.58569C6.21379 6.96077 6.00293 7.46957 6.00293 8V32C6.00293 32.5304 6.21379 33.0392 6.58887 33.4143C6.96394 33.7894 7.4725 34 8.00293 34H24.0029C24.5334 34 25.0419 33.7894 25.417 33.4143C25.7921 33.0392 26.0029 32.5304 26.0029 32V30.667C26.0028 30.4481 26.0457 30.2313 26.1294 30.0291C26.2131 29.8268 26.336 29.6431 26.4907 29.4883C26.6454 29.3335 26.8291 29.2107 27.0312 29.127C27.2334 29.0432 27.4501 29 27.6689 29C28.1111 29 28.535 29.1757 28.8477 29.4883C29.1603 29.8009 29.3359 30.2249 29.3359 30.667V32C29.3343 33.4141 28.772 34.7697 27.772 35.7695C26.772 36.7693 25.416 37.3317 24.002 37.333H8.00195ZM29.8628 25.656C29.4879 25.281 29.2773 24.7723 29.2773 24.2419C29.2773 23.7116 29.4879 23.2029 29.8628 22.8279L30.6919 21.999H21.335C20.8045 21.999 20.296 21.7882 19.9209 21.4131C19.5458 21.038 19.335 20.5295 19.335 19.999C19.335 19.4686 19.5458 18.9598 19.9209 18.5847C20.296 18.2096 20.8045 17.999 21.335 17.999H30.6919L29.8628 17.1699C29.4879 16.7949 29.2773 16.2864 29.2773 15.7561C29.2773 15.2258 29.4879 14.7171 29.8628 14.342C30.2378 13.9671 30.7465 13.7563 31.2769 13.7563C31.8072 13.7563 32.3159 13.9671 32.6909 14.342L36.9341 18.584C37.1198 18.7699 37.2668 18.9906 37.3672 19.2334C37.4676 19.4762 37.5192 19.7363 37.519 19.999C37.5196 20.2616 37.4681 20.5217 37.3677 20.7644C37.2673 21.0071 37.12 21.2276 36.9341 21.4131L32.6909 25.655C32.5057 25.8414 32.2852 25.9891 32.0425 26.0898C31.7998 26.1906 31.5396 26.2423 31.2769 26.2419C31.015 26.2416 30.7559 26.1897 30.5142 26.0891C30.2724 25.9886 30.053 25.8415 29.8682 25.656H29.8628Z"
                    fill="var(--svg-icon-color)"
                  ></path>
                </g>
                <defs>
                  <clipPath id="clip0_6581_88424">
                    <rect
                      width="40"
                      height="40"
                      fill="var(--svg-icon-color)"
                    ></rect>
                  </clipPath>
                </defs>
              </svg>
            </div>
            <span>Sair</span>
            <!-- <AppImage
            class="arrow"
            overlayColor="var(--theme-primary-font-color)"
            src="/img/user/icon-arrow-green"
          /> -->
            <div class="arrow" v-html="arrowIcon"></div>
          </li>
        </ul>
      </section>
    </div>
  </van-popup>

  <!-- <AppMusic/> -->
</template>

<style lang="scss" scoped>
// @import "../../theme/mixin.scss";

.pt-30 {
  padding-top: 30px;
}

.refresh-img {
  &.rotate {
    animation: spin 0.5s linear infinite;
  }
}

.personal-page {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  //padding-top: 112px;
  padding-bottom: 132px;
  background: var(--theme-bg-color);
  overflow: auto;
  // background: url() no-repeat, ;
  //background-color: #131a2c;

  position: relative;

  .pagetitlebg {
    position: absolute;
    width: 100%;
    height: 52.666667vw;
    background-image: url("/img/user/top-bg.png");
    // @include webp("/img/user/icon-bg");
    // background-color: var(--theme-main-bg-color);
    background-size: 100%;
    // pointer-events: none;
  }

  .right-up {
    position: absolute;
    width: 360px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    right: 0;
    font-size: 20px;
    //pointer-events: none;
    //background-color: #172554;

    &-item {
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 120px;
      height: 80px;
      margin: 5px;
      //background-color: #BD0C36;
      color: var(--theme-text-color);
    }

    .b {
      position: absolute;
      width: 900px;
      height: 900px;
      background-color: #000;
    }
  }

  .user-info-box-outer {
    position: relative;
    top: 64px;
    width: 700px;
    // height: 455px;
    border-radius: 20px;
    //background-color: var(--app-box-bg-color);
    margin: 0 auto;
  }

  .user-info-box {
    position: relative;
    align-items: stretch;
    min-height: 490px;
    background-size: 251px auto;
    background-position: 100% 52px;
    //background-color: #BD0C36;
    .tag {
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 0px 0px 0px 100px;
      background: linear-gradient(143deg, #fa3a68 0%, #bd0c36 100%);
      width: 220px;
      height: 54px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 24px;
      gap: 28px;
      color: #fff;
      font-size: 24px;

      img:nth-of-type(1) {
        width: 28px;
      }

      img:nth-of-type(2) {
        width: 12px;
      }
    }

    .content-top {
      width: 700px;
      background-color: #324b6e;
      border-radius: 50px 50px 0px 0px;
    }

    .panel {
      flex: 1;
      // background: #fff;
      border-radius: 15px;
      padding: 0px 20px 15px;
      // overflow: hidden;

      .top {
        width: 100%;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 12px;
        // background-color: #212d40;
        border-radius: 10px;
        margin: 0 auto;
        margin-top: 30px;
        left: -40px;
        padding: 20px 20px;
        // padding: 10px 30px;
        position: relative;

        &-head {
          img {
            width: 90px;
            border-radius: 50%;
          }
        }

        .profile {
          .avatar {
            width: 86px;
            margin-top: 37px;
            display: block;
          }

          .vipimg {
            width: 128px;
            display: block;
            margin-top: -16px;
          }
        }

        .detail {
          color: #b3c2d8;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          margin-left: 13px;

          > div {
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }

          .nick-name {
            width: 100%;

            // font-weight: 700;
            padding-top: 4px;
            font-family: Arial;
            color: var(--theme-text-color);
            font-size: 30px;
            padding-bottom: 5px;
            // font-weight: bold;
            .copy {
              position: relative;
              top: 1px;
              left: -7px;
            }
            .nick-name-line {
              position: absolute;
              //top: 70px;
              left: 350px;
              width: 1px;
              height: 20px;
              opacity: 0.5;
              background-color: #ffffff;
            }

            .right-balance {
              display: inline;
              position: absolute;
              left: 355px;
              scale: 0.9;
              //right:10px;
              min-width: 100px;
              max-width: 300px;
              height: 44px;
              // line-height: 72px;
              font-size: 30px;

              .right-logo {
                display: inline-block; /* 将元素设置为行内块元素 */
                vertical-align: middle; /* 垂直居中对齐 */

                width: 33px;
                height: 33px;
                margin-left: 15px;
              }

              .right-text {
                display: inline-block; /* 将元素设置为行内块元素 */
                vertical-align: middle; /* 垂直居中对齐 */
                // vertical-align: middle; /* 垂直居中对齐 */
                // width: 100px;
                font-size: 30px;
                // text-align: right;
                // line-height: 44px;
                // word-wrap: break-word; /* 旧版浏览器支持 */
                // overflow-wrap: break-word; /* 标准属性 */
                padding-top: 8px;
                padding-left: 60px;
                padding-right: 10px;
                color: var(--theme-secondary-color-finance);
                //text-decoration: underline
                // padding-right: 5px;
              }

              .right-refresh {
                display: inline; /* 将元素设置为行内块元素 */
                // vertical-align: middle; /* 垂直居中对齐 */
                margin-top: 8px;
                margin-right: 10px;
                float: right;
                // width: 29px;
                // height: 31px;
                // @include webp("/icons/refresh");
                // background-repeat: no-repeat;
                // background-size: 29px 31px;

                &.rotate {
                  // animation: spin 1s linear infinite;
                  -webkit-transform: rotate(360deg);
                  transform: rotate(360deg);

                  -webkit-transition: -webkit-transform 0.5s linear;
                  transition: transform 0.5s linear;
                }

                .flex-sb {
                  // display: flex;
                }

                .right-dev {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  height: 74px;
                  width: 355px;
                  margin-left: 70px;
                  // background-color: #D9D9D9;
                }

                .right-add {
                  width: 60px;
                  margin-right: 75px;
                }
              }
            }

            button {
              position: absolute;
              left: 310px;

              //right: 0;
              //text-indent: 30px;
            }

            img {
              width: 24px;
              position: absolute;
              top: 8px;
              //left: 21px;
              // position: absolute;
              // right: 20px;
            }
          }

          .help {
            width: 100%;
            font-size: 30px;
            // font-weight: 700;
            padding-top: 4px;
            margin-top: 30px;
            color: #000;
            position: relative;

            img {
              width: 40px;
              margin-left: 12px;
              position: absolute;
              right: 20px;
            }
          }

          .user-name {
            width: 100%;
            color: #000;
            font-size: 50px;
            font-weight: normal;
            position: relative;
            margin-top: -0.666667vw;

            .label {
              margin-right: 8px;
              color: #b3c2d8;
            }

            .copy-txt {
              display: flex;
              align-items: center;
              //border-radius: 50px;
              // background: #28374d;
              // padding: 0 18px;
              white-space: nowrap;
              overflow: hidden;
              font-family: Arial;
              font-weight: 400;
              color: var(--app-top_username-color);
              font-size: 24px;
              padding-bottom: 5px;

              span {
                display: block;
                flex: 1;
                text-overflow: ellipsis;
                overflow: hidden;
                font-size: 5vw;
              }
            }
          }

          .money-line {
            margin-top: 40px;
            height: 150px;

            .money-line-item {
              width: 330px;
              height: 142px;
              border-radius: 20px;
              background-color: #dbe4ef;
              text-align: center;
              color: #000;
              font-size: 30px;

              &-icon {
                margin-top: -20px;
                height: 90px;

                img {
                  margin-top: -10px;
                  width: 105px;
                }
              }

              &-name {
                line-height: 30px;
              }

              &-amount {
                line-height: 35px;
              }

              &:last-child {
                margin-left: 10px;

                .money-line-item-icon {
                  img {
                    margin-top: 0px;
                  }
                }
              }
            }
          }

          .money {
            width: 273px;
            height: 56px;
            // @include webp("/icons/frame_purses.png");
            background-size: 100% 100%;
            background-position: 0 0;
            position: relative;
            margin-top: 15px;
            color: #fff;
            font-size: 28px;

            .unit {
              text-indent: 80px;
            }

            .num {
              // padding-left: 10px;
              // padding-right: 8px;
            }

            img {
              width: 34px;
              height: 32px;
              position: absolute;
              right: 15px;
              // margin-right: 10px;
            }
          }

          .tip {
            position: absolute;
            right: 20px;
            bottom: 30px;
            font-size: 24px;
            display: flex;
            align-items: center;

            .right {
              width: 58px;
              height: 58px;
              border-radius: 30px;
              line-height: 58px;
              text-align: center;
              margin-left: 10px;
              background-color: #192332;

              img {
                width: 25px;
                height: 40px;
                margin-top: 10px;
                margin-left: 5px;
              }
            }
          }

          .vip-level {
            position: absolute;
            top: 3px;
            right: 40px;

            img {
              width: 38px;
              height: 33px;
            }

            &-num {
              font-size: 18px;
              color: #7d8aa2;
              margin-top: 20px;
              margin-left: -5px;
            }
          }
        }
      }

      .money-msg-box {
        position: relative;
        width: 678px;
        height: 130px;
        top: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 25px;
        // color: #FFF;
        &-item {
          text-align: center;
          width: 300px;
          //height: 80px;
          font-size: 22px;
          margin: 25px;
          //background-color: #BD0C36;
          color: var(--theme-text-color);
        }
        span {
          position: relative;
          top: 0px;
        }
      }

      .vip-box {
        width: 690px;
        height: 240px;
        padding: 20px 20px;
        background-size: 100% 100%;
        background-position: center center;
        margin: 14px -15px 18px -15px;
        background-color: var(--theme-primary-color);
        border-radius: 20px;
        position: relative;
        font-size: 25px;
        &-vipbg {
          position: absolute;
          width: 70px;
          height: 30px;
          top: 28px;
          //left: 25px;
          background-color: #24b299;
          border-top-left-radius: 12px;
          border-bottom-right-radius: 12px;
          text-align: center;
          padding: 0px 0;

          &-num {
            font-style: italic;
            color: #e2b54a;
          }
          &-info {
            position: absolute;
            width: 530px;
            height: 50px;

            left: 100px;
            top: -10px;
            padding: 0px, 20px;
            display: inline;
            color: var(--theme-bg-color2);
            //background-color: #BD0C36;
            text-align: left;
            font-size: 24px;
            &-img {
              //background-color: #000;
              position: absolute;
              width: 16px;
              height: 28px;
              right: -15px;
              top: 15px;
              opacity: 0.5;
            }
            p {
              position: relative;
              left: -10px;
            }
          }

          &-line {
            position: absolute;
            top: 60px;
            width: 646px;
            height: 1px;
            background-color: var(--app-red-line-color);
          }

          &-info2 {
            width: 88px;
            height: 88px;
            // @include webp("/img/user/icon-vip-round");
            background-size: 100%;
            position: absolute;
            top: 82px;
            left: 5px;
            text-align: center;
            color: #e2b54a;
            padding: 23px 0;
            font-size: 40px;

            //background-color: #000;
          }

          &-aposta {
            color: var(--theme-bg-color2);
            position: absolute;
            width: 180px;
            left: 110px;
            top: 100px;
            text-align: left;
            font-size: 20px;
          }

          &-progress {
            position: absolute;
            top: 100px;
            left: 250px;
            color: white;
            span {
              position: relative;
              top: -25px;
              font-size: 22px;
            }
          }
        }
      }

      .money-msg {
        width: 678px;
        height: 285px;
        padding: 18px 33px;
        // @include webp("/img/personal_bg1.png");
        background-size: 100% 100%;
        background-position: center center;
        margin: 0 auto;
        position: relative;

        &-top {
          position: relative;

          img {
            width: 32px;
          }

          span {
            position: absolute;
            font-family: Arial;
            color: #d6d7f9;
            font-size: 24px;
            margin-left: 13px;
            line-height: 40px;
          }
        }

        &-middle {
          font-family: Arial;
          color: #ffffff;
          font-size: 56px;
          padding-bottom: 6px;
          position: absolute;
          top: 74px;
          left: 0;
          width: 100%;
          text-align: center;
        }

        &-bottom {
          width: 100%;
          position: absolute;
          top: 181px;
          left: 0;
          padding: 0 34px;
          display: flex;
          gap: 31px;
        }
      }

      .menu-list {
        width: 675px;
        height: 425px;
        // @include webp("/img/personal_bg2.png");
        background-size: 100% 100%;
        background-position: center center;
        margin: 0 auto;
        margin-top: 47px;
        position: relative;
        padding: 30px 0px 0px 0px;
        display: grid;
        grid-template-rows: auto auto;
        grid-auto-flow: column;
        grid-column-gap: 0px;
        grid-row-gap: 0px;
        // color: #fff;
        color: #8a8a8a;

        &-item {
          margin: 0 auto;
          width: 150px;
          height: 150px;

          img {
            display: block;
            // width:55px;
            margin: 0 auto;
          }

          span {
            display: block;
            font-family: Arial;
            font-size: 24px;
            text-align: center;
          }

          &:nth-child(1) {
            padding-top: 20px;

            img {
              width: 54px;
            }

            span {
              margin-top: 23px;
            }
          }

          &:nth-child(2) {
            padding-top: 44px;

            img {
              width: 66px;
            }

            span {
              // width: 146px;
              margin-top: 10px;
              // color: #fff;
            }
          }

          &:nth-child(3) {
            img {
              width: 90px;
            }

            span {
              margin-top: 0;
            }
          }

          &:nth-child(4) {
            margin-top: 44px;

            img {
              width: 60px;
            }

            span {
              margin-top: 20px;
              // color: #fff;
            }
          }

          &:nth-child(5) {
            margin-top: 10px;

            img {
              width: 67px;
            }

            span {
              margin-top: 20px;
            }
          }

          &:nth-child(6) {
            margin-top: 44px;

            img {
              width: 60px;
            }

            span {
              margin-top: 20px;
              // color: #fff;
            }
          }
        }
      }
    }
  }
}

.reports {
  position: relative;
  margin: 8px 0;
  padding: 0px 0px;
  list-style: none;
  //border-radius: 15px;
  width: 100%;
  top: 15px;
  color: var(--theme-text-color);
  font-size: 22px;
  margin-top: 20px;
  background: var(--theme-main-bg-color);

  &-second {
    //padding: 0;
    margin-top: -20px;
    img {
      width: 40px;
    }
  }

  .item {
    width: 632px;
    position: relative;
    margin: 0 auto;
    // border-bottom: 1px solid #152237;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 100px;
    // background: #fff;
    border-radius: 15px;
    span {
      position: relative;
      left: 15px;
      font-size: 24px;
    }

    &:nth-child(1) {
      border-top-left-radius: 15px;
      border-top-right-radius: 15px;
    }

    &:nth-child(4) {
      border-bottom-left-radius: 15px;
      border-bottom-right-radius: 15px;
    }

    .icon {
      //   width: 40px;
      margin-left: -20px;
      //margin-right: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:nth-child(2) {
      .icon {
        //margin-left: 12px;
      }
    }

    &:last-child {
      border: 0;
    }

    .arrow {
      width: 30px;
      position: absolute;
      top: 50%;
      right: -5px;
      transform: translateY(-50%);
    }
  }

  .item:last-child {
    // color: rgba(255, 255, 255, 0.50);
  }
}
.svg .edit-info {
  // background: #999;
  .van-popup__close-icon--top-right {
    right: 50px !important;
  }
}

.pop-content {
  width: 674px;
  height: 610px;
  border-radius: 10px;
  margin: 0 auto;
  margin-top: 50%;
  background-color: var(--app-box-bg-color);
  position: relative;

  &-inner {
    width: 615px;
    display: flow;
    margin: 0 auto;

    .d1 {
      display: block;
      width: 615px;
      height: 155px;
    }
  }

  .d {
    height: 100px;
  }

  .close {
    width: 40px;
    position: absolute;
    top: 25px;
    right: 20px;
  }

  .pop-title {
    width: 674px;
    height: 97px;
    line-height: 97px;
    background: var(--app-psw-top-color);
    border-radius: 10px 10px 0px 0px;
    margin-top: 20px;
    margin-bottom: 10px;
    font-family: Arial;
    font-weight: 700;
    color: #ffffff;
    text-align: center;

    &-top {
      width: 615px;
      height: 35px;
      font-family: Arial;
      font-weight: 700;
      color: #172554;
      font-size: 30px;
      margin: 0 auto;
      padding-left: 5px;
    }

    &-input {
      width: 615px;
      height: 35px;
      font-family: Arial;
      color: var(--app-psw-inputtop-color);
      font-size: 24px;
      margin: 0 auto;
      padding-left: 5px;
    }
  }

  button {
    margin-top: 28px;
  }
}

.edit-pwd {
  .pop-content {
    width: 674px;
    height: 800px;

    .pop-title-top-2 {
      margin-top: 30px;
    }

    .pop-title-input {
      margin-top: 20px;
    }
  }
}
</style>
<route lang="yaml">
meta:
  auth: true
</route>
