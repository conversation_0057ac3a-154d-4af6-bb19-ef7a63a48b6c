<script lang="ts" setup name="AppActivePopup">
const router = useRouter();
const appStore = useAppStore();
const { isFirstChargePopup, depositBonusConf, activeDialogVisble } = storeToRefs(appStore);

const isRemember = ref(false);
const isRemember1 = ref(false);

const datalist = computed(() => {
  // depositBonusConf.value = [...new Set(depositBonusConf.value), ...new Set(depositBonusConf.value), ...new Set(depositBonusConf.value)]
  return depositBonusConf.value?.sort((a, b) => a.min_amount - b.min_amount);
})
const curObj = ref({});
const close = () => {
  // if (itemAll.value.length > curIndex.value + 1) {
  //   curIndex.value += 1;
  //   curItem.value[0] = itemAll.value[curIndex.value];
  // } else {
  appStore.setFirstChargePopupVisble(false);
  // showPopup.value = false;
  // }
};

// const showPopup = ref(false);
// const { run: runGetPlatformLinkData, data: platformLinkData } = useRequest(() => ApiGetPlatformLinkData(), {
//   manual: true,
//   onSuccess(res: any) {
//     // console.log(res)
//     // showPopup.value = isFirstChargePopup && platformLinkData.value.send_firstcharge_bonus_data;
//     console.log(isFirstChargePopup.value, platformLinkData.value.send_firstcharge_bonus_data)
//     showPopup.value = isFirstChargePopup.value && platformLinkData.value.send_firstcharge_bonus_data == 1;
//   }
// })
// runGetPlatformLinkData();

const goDetail = () =>{
  console.log("goDetail");
  router.push("/promotion-detail/first-recharge-rewards")
  close();
}

const clickDoNotShowToday = () =>{
  isRemember.value = !isRemember.value;
  // localStorage.setItem("doNotShowToday", "true");
  storeSettingForToday('doNotShowToday');
}

const clickDoNotForever = () =>{
  isRemember1.value = !isRemember1.value;
  // localStorage.setItem("doNotShowToday", "true");
  storeSettingForToday('doNotShowToday');
}

// 存储设置，有效期为当天午夜12点
function storeSettingForToday(key: string) {
  if(isRemember.value || isRemember1.value){
    const todayMidnight = new Date();
    todayMidnight.setHours(0, 0, 0, 0);
    todayMidnight.setDate(todayMidnight.getDate() + 1); // 设置为下一天的午夜
    localStorage.setItem(key, JSON.stringify({ value: 'true', expiry: todayMidnight.getTime() }));
  }else{
    localStorage.removeItem(key);
  }

  // console.log(JSON.stringify({ value, expiry: todayMidnight.getTime() }))
}

</script>

<template>
  <van-popup class="app-login-register" v-model:show="isFirstChargePopup" teleport="body" round :close-on-click-overlay="false">
    <div class="content">
      <div class="title">Bônus Extra De Depósito Inicial</div>
      <div body>
        <div class="belowDiv"> <!--下面显示-->
          <!-- <label class="below_title">CASHBACK E REGRAS</label> -->
          <div class="below_itemTitle">
            <!-- <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp" /> -->
            <label class="below_itemTitle_aposta">Primeiro depósito</label>
            <label class="below_itemTitle_aposta below_itemTitle_cashback">Recompensa</label>
          </div>

          <div v-for="(data, index) in datalist" :key="index" class="below_itemDevPa">
            <label class="itemContent">{{ "≥" + data.min_amount }}</label>
            <!-- <label class="itemContent copy">{{ new Intl.NumberFormat('pt-BR', { style: 'percent' }).format(data.bonus)
            }}</label> -->
            <label class="itemContent copy">{{ transf(data.bonus) + "%" }}</label>
          </div>

        </div>
        <div class="bottomDiv">
          <div class="inputDiv">
            <div class="inputItem">
              <div class="remember" @click="clickDoNotShowToday">
                <div class="toggle">
                  <AppImage style="width:var(--app-px-26);" :src="`/img/login/${!isRemember ? 'remember-not-select' : 'remember-select'
                    }.webp`" alt="" />
                </div>
                <div class="text">Não mostrar novamente hoje</div>
              </div>
            </div>
            <div class="inputItem">
              <div class="remember" @click="clickDoNotForever">
                <div class="toggle">
                  <AppImage style="width:var(--app-px-26);" :src="`/img/login/${!isRemember1 ? 'remember-not-select' : 'remember-select'
                    }.webp`" alt="" />
                </div>
                <div class="text">Ocultar permanentemente</div>
              </div>
            </div>
          </div>
          <button type="button" class="goDetailBtn" @click="goDetail"><span>Prosseguir</span></button>
        </div>
      </div>
    </div>
    <img class="app-image img close-icon" src="/icons/close_black.png.webp" @click="close" alt="" />
  </van-popup>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.content {
  width: 686px;
  // height: 636px;
  border-radius: 16px;
  background-color: var(--theme-bg-color);
  flex-direction: column;
  overflow: hidden;
  margin-top: 100px;

  .title {
    text-align: center;
    font-size: 30px;
    font-weight: revert;
    color: var(--theme-text-color-lighten);
    padding: 20px 30px;
  }

  .body {
    width: 646px;
    background-color: var(--theme-main-bg-color);
    border-radius: 10px;
    box-shadow: 0 3px 9px 0 rgba(0, 0, 0, .06);
    word-wrap: break-word;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    display: block;
  }

  .belowDiv {
    padding-top: 10px;
    margin-left: 20px;
    width: 646px;
    max-height: 794px;
    position: relative;
    background: var(--theme-main-bg-color);
    border-radius: 15px;
    padding-bottom: 20px;
    overflow-y: auto;
    scrollbar-width: thin;
    &::-webkit-scrollbar {
        display: block;
        width: 2px;
    }
    
    .below_title {
      display: block;
      margin-top: 25px;
      margin-left: 25px;

      font-family: Arial;
      font-weight: 700;
      color: var(--app-ratio-title-color);
      font-size: 24px;
    }

    .below_itemTitle {
      display: flex;
      margin: 0 auto;
      margin-top: 10px;
      width: 602px;
      height: 80px;
      background: var(--theme-bg-color);
      border-radius: 8px;
      border: 2px solid;
      border-color: var(--theme-color-line);
    }

    .below_itemImg {
      width: 41px;
      height: 41px;
      margin-left: 20px;
      margin-top: 15px;
    }

    .below_itemTitle_aposta {
      // margin-left: 105px;
      margin-top: 28px;

      font-family: Arial;
      font-weight: 700;
      color: var(--theme-text-color-lighten);
      font-size: 24px;
      width: 354px;
      text-align: center;
    }

    .below_itemDevPa {
      display: flex;
      // flex-direction: column;
      align-items: center;
      width: 602px;
      height: 70px;
      line-height: 70px;
      margin: 0 auto;
      // margin-top: 20px;
      // margin-bottom: 20px;
      // position: relative;

      &:nth-child(2n + 1) {
        background: var(--theme-bg-color);
        border-radius: 15px;
        width: 602px;
        height: 70px;
        line-height: 70px;
      }


      .itemIcon {
        position: absolute;
        width: 28px;
        height: 32px;
        margin-left: -550px;
        margin-top: 20px;
      }

      .itemIconLev {
        position: absolute;
        margin-left: -550px;
        margin-top: 2px;

        font-family: Arial;
        font-weight: 700;
        font-style: italic;
        color: #f43f5e;
        font-size: 20px;
      }

      .itemContent {
        font-family: Arial;
        color: var(--app-title-color);
        font-size: 20px;
        text-align: center;
        transform: translateY(2px);
        color: var(--theme-text-color-lighten);
        width: 354px;
        text-align: center;
      }

      .copy {
        color: var(--theme-secondary-color-finance);
      }

    }

  }

  .bottomDiv {
    width: 646px;
    align-items: center;
    border-top: none;
    display: flex;
    height: 120px;
    justify-content: space-between;
    margin-left: 20px;

    .inputDiv {
      width: 516px;

      .inputItem {
        margin-top: 10px;
        align-items: center;
        color: var(--theme-text-color);
        cursor: pointer;
        display: flex;
        text-align: left;
        line-height: 1.2;
        font-size: 11px;
      }

      .remember {
        display: flex;
        align-items: center;

        .text {
          margin-top: -5px;
          color: var(--theme-text-color);
          margin-left: 15px;
          font-size: 22px;
        }
      }
    }

    .goDetailBtn {
      background-color: var(--theme-primary-color)!important;
      align-items: center;
      border: none;
      border-radius: 10px;
      display: flex;
      font-size: 20px;
      height: 50px;
      justify-content: center;
      line-height: 1.4;
      padding: 0 !important;
      white-space: pre-wrap;
      width: 120px;
      word-break: break-word;
      span{
        color: var(--theme-primary-font-color)
      }
    }
  }
}

.close-icon {
  width: 60px;
  height: 60px;
  border: 5px solid white;
  border-radius: 100%;
  padding: 5px;
  margin: 30px auto;
  display: block;
}
</style>
