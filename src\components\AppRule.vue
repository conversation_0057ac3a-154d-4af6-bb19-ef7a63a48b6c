<!-- 通用规则 -->

<script lang="ts" setup>
interface Props {
  title?: string
  content?: string
}

// const isShowPwd = ref(false)
// const isFocus = ref(false)

const props = withDefaults(defineProps<Props>(), {
  title: 'TERMOS E REGRAS',
  content: '',
})

</script>

<template>
  <div class="rule">
  <div v-if="title.length > 0">
    <span>{{ title }}</span><br>
  </div>
  <pre  v-html="content" ></pre>
  </div>
</template>

<!-- <style lang="scss">
[theme='blue']:root {
  // --van-padding-md:0px;
  
}
</style> -->

<style lang="scss" scoped>
.rule{
  padding:20px 30px 20px 30px;
  color:var(--app-title-color);
  // max-width: 680px;
  width: 100%;
  font-size:24px;
  line-height:40px;
  // white-space:pre-wrap;
  span{
    font-weight:700;
    font-size:30px;
    line-height: 70px;
  }
  pre{
    // font-size:inherit;
    // line-height:inherit;
    font-family: inherit;
    white-space: pre-wrap;
  }
}
</style>
