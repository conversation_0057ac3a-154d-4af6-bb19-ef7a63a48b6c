<script setup lang='ts' name='apostas'>


const router = useRouter()
const appStore = useAppStore()
const { isApp } = storeToRefs(appStore)
const showPopover1 = ref(false);
const showPopover2 = ref(false);
const showPopover3 = ref(false);
const showPopover4 = ref(false);
const showPopover5 = ref(false);
const selectIndex1 = ref(2);
const selectIndex2 = ref(2);
const selectIndex3 = ref(2);
const selectIndex4 = ref(2);
const selectIndex5 = ref(2);
const selText1 = ref("Hoje")
const selText2 = ref("Todos os Status")
const selText3 = ref("Todos os Tipos")
const selText4 = ref("Plataformas")
const selText5 = ref("Todos os Jogos")
let actions1 = [{ text: "Hoje", calssName: "2", color: selectIndex1.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Ontem", calssName: "1", color: selectIndex1.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 7 Dias", calssName: "3", color: selectIndex1.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 15 Dias", calssName: "4", color: selectIndex1.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 30 Dias", calssName: "5", color: selectIndex1.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]

let actions2 = [{ text: "Todos os Status", calssName: "2", color: selectIndex2.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Não Liquidado", calssName: "1", color: selectIndex2.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Liquidado", calssName: "3", color: selectIndex2.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Pedido cancelado", calssName: "4", color: selectIndex2.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]

let actions3 = [{ text: "Todos os Tipos", calssName: "2", color: selectIndex3.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Slots", calssName: "1", color: selectIndex3.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }
]
let actions4 = [{ text: "Plataformas", calssName: "2", color: selectIndex4.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }
]

let actions5 = [{ text: "Todos os Jogos", calssName: "2", color: selectIndex5.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }
]


const popoverStatus1 = (isOpen: boolean) => {
    if (!isOpen) {
        actions1 = [{ text: "Hoje", calssName: "2", color: selectIndex1.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Ontem", calssName: "1", color: selectIndex1.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 7 Dias", calssName: "3", color: selectIndex1.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 15 Dias", calssName: "4", color: selectIndex1.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 30 Dias", calssName: "5", color: selectIndex1.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]
    }
}

const popoverStatus2 = (isOpen: boolean) => {
    if (!isOpen) {
        actions2 = [{ text: "Todos os Status", calssName: "2", color: selectIndex2.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Não Liquidado", calssName: "1", color: selectIndex2.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Liquidado", calssName: "3", color: selectIndex2.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Pedido cancelado", calssName: "4", color: selectIndex2.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]
    }
}


const popoverStatus3 = (isOpen: boolean) => {
    if (!isOpen) {
        actions3 = [{ text: "Todos os Tipos", calssName: "2", color: selectIndex3.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Slots", calssName: "1", color: selectIndex3.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }
        ]
    }
}

const popoverStatus4 = (isOpen: boolean) => {
    if (!isOpen) {
        actions4 = [{ text: "Plataformas", calssName: "2", color: selectIndex4.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }
        ]
    }
}

const popoverStatus5 = (isOpen: boolean) => {
    if (!isOpen) {
        actions5 = [{ text: "Todos os Jogos", calssName: "2", color: selectIndex5.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }
        ]
    }
}


const onSelect1 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText1.value = action.text;
    selectIndex1.value = Number(action.calssName)
    //runUserWitdrawList();
}




const onSelect2 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText2.value = action.text;
    selectIndex2.value = Number(action.calssName)
    //runUserWitdrawList();
}

const onSelect3 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText3.value = action.text;
    selectIndex3.value = Number(action.calssName)
    //runUserWitdrawList();
}

const onSelect4 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText4.value = action.text;
    selectIndex4.value = Number(action.calssName)
    //runUserWitdrawList();
}

const onSelect5 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText5.value = action.text;
    selectIndex5.value = Number(action.calssName)
    //runUserWitdrawList();
}


// <AppImage src="/img/finance/finance-icon.png" class="list-item-conetent-top-left-img" />
</script>
<template>
    <div class="conta">
        <div class="page-tab-panel">
            <section class="page-tab-panel-content">
                <div class="panel-content-header">
                    <van-popover class="content-header-popover" v-model:show="showPopover1" :actions="actions1"
                        @select="onSelect1" @open="popoverStatus1(true)" @close="popoverStatus1(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select1 box-border"
                                :class="{ 'viewOpen': showPopover1 }">
                                <van-text-ellipsis :content="selText1" class="content-header-select-title" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover1, 'rotate-svg1': !showPopover1 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                    </van-popover>
                    <van-popover class="content-header-popover" v-model:show="showPopover2" :actions="actions2"
                        @select="onSelect2" @open="popoverStatus2(true)" @close="popoverStatus2(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select2 box-border"
                                :class="{ 'viewOpen': showPopover2 }">
                                <van-text-ellipsis :content="selText2" class="content-header-select-title1" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover2, 'rotate-svg1': !showPopover2 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                    </van-popover>
                    <van-popover class="content-header-popover" v-model:show="showPopover3" :actions="actions3"
                        @select="onSelect3" @open="popoverStatus3(true)" @close="popoverStatus3(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select3 box-border"
                                :class="{ 'viewOpen': showPopover3 }">
                                <van-text-ellipsis :content="selText3" class="content-header-select-title2" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover3, 'rotate-svg1': !showPopover3 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                    </van-popover>
                    <van-popover class="content-header-popover" v-model:show="showPopover4" :actions="actions4"
                        @select="onSelect4" @open="popoverStatus4(true)" @close="popoverStatus4(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select4 box-border"
                                :class="{ 'viewOpen': showPopover4 }">
                                <van-text-ellipsis :content="selText4" class="content-header-select-title3" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover4, 'rotate-svg1': !showPopover4 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                    </van-popover>
                    <van-popover class="content-header-popover" v-model:show="showPopover5" :actions="actions5"
                        @select="onSelect5" @open="popoverStatus5(true)" @close="popoverStatus5(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select5 box-border"
                                :class="{ 'viewOpen': showPopover5 }">
                                <van-text-ellipsis :content="selText5" class="content-header-select-title4" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover5, 'rotate-svg1': !showPopover5 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                    </van-popover>
                </div>
            </section>
        </div>
    </div>
    <div class="content-body-middle">
            <app-empty />
        </div>
</template>

<style lang='scss' scoped>
.conta {
    height: 400px;
    .conta-content {
        width: 100%;
        position: relative;
        height: 200px;
    }

    .page-tab-panel {
        display: block;
        width: 100%;
        height: 100%;
        color: var(--theme-text-color);
        font-size: 14px;
        //background-color: aqua;

        .page-tab-panel-content {
            width: 100%;
            height: 100%;
            padding: 0 20px 20px;
            overflow: auto;
        }

        .panel-content-header {
            width: 1400px;
            height: 90px;
            overflow: auto;
            overflow-x: scroll;
            color: var(--theme-text-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 22px;
        }

        .content-header-popover {
            width: 160px;
            height: 150px;

        }

        .box-border {
            border-style: solid;
            border-color: var(--theme-color-line);
            border-width: 2px;
            border-radius: 10px;
        }


        .content-header-popover-select1 {
            width: 160px;
            height: 50px;
            //margin-right: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-popover-select2 {
            width: 200px;
            height: 50px;
            //margin-right: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            //left: -100px;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-popover-select3 {
            width: 300px;
            height: 50px;
            //margin-right: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            //left: -100px;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-popover-select4 {
            width: 300px;
            height: 50px;
            //margin-right: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            //left: -100px;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-popover-select5 {
            width: 300px;
            height: 50px;
            //margin-right: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            //left: -100px;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-select-title {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 101px;
            height: 48px;
        }

        .content-header-select-title1 {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 180px;
            height: 48px;
        }

        .content-header-select-title2 {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 251px;
            height: 48px;
        }

        .content-header-select-title3 {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 180px;
            height: 48px;
        }

        .content-header-select-title4 {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 180px;
            height: 48px;
        }

        @-webkit-keyframes spin0 {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(180deg);
            }
        }

        @keyframes spin0 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(180deg);
            }
        }

        @-webkit-keyframes spin1 {
            from {
                -webkit-transform: rotate(180deg);
            }

            to {
                -webkit-transform: rotate(0deg);
            }
        }

        @keyframes spin1 {
            from {
                transform: rotate(180deg);
            }

            to {
                transform: rotate(0deg);
            }
        }

        .rotate-svg {
            animation: spin0 0.3s linear 0s 1;
            transform: rotate(180deg);
        }

        .rotate-svg1 {
            transform: rotate(180deg);
            animation: spin1 0.3s linear 0s 1;
            transform: rotate(0deg);
        }

        .content-header-select-icon {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 15px;

            svg {
                width: 20px;
                height: 20px;
                color: var(--theme-text-color-lighten);
                position: absolute;

            }
        }

        .content-header-total {
            //background-color: aqua;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--theme-text-color-lighten);
            text-align: right;
            margin-left: 20px;
            font-size: 22px;
            line-height: normal;
            -webkit-font-smoothing: antialiased;

            span {
                font-size: 22px;
                color: var(--theme-text-color-darken);
                margin-left: 5px;
            }
        }

        .content-header-total-img-box {
            width: 30px;
            height: 30px;
            display: inline-block;
            margin-left: 10px;

            .rotate {
                animation: spin 1s linear infinite;
            }
        }

        .content-header-total-img {
            height: 30px;
            width: 30px;
        }
    }

    .content-body-middle {
        width: 100%;
        height: calc(100vh - 375px);
        padding: 10px 0px;
        //background-color: aquamarine;
    }

    .content-body-bottom {
        width: 100%;
        position: fixed;
        display: flex;
        bottom: 0px;
        height: 95px;
        left: 0px;
        font-size: 24px;
        background-color: var(--theme-main-bg-color);

        &-a {
            position: absolute;
            width: 220px;
            padding: 2px 0 0 20px;
        }

        &-b {
            color: var(--app-red-vip-down);
            position: absolute;
            padding: 2px 0 0 200px;
        }

        &-c {
            position: absolute;
            padding: 0px 200px 0 0px;
            right: 2px;
        }

        &-d {
            color: var(--theme-secondary-color-error);
            position: absolute;
            padding: 2px 100px 0 0px;
            right: 0px;
        }

        &-e {
            position: absolute;
            width: 180px;
            padding: 35px 0 0 20px;
        }

        &-f {
            color: var(--theme-secondary-color-finance);
            position: absolute;
            padding: 50px 0 0 200px;
        }
    }
}
</style>

<route lang="yaml">
    meta:
      auth: true
  </route>