<script setup lang="ts" name="agente">
import LinkDeConvite from "../pages/agent/linkDeConvite.vue";
import MeusDados from "../pages/agent/meusDados.vue";
import TodosOsDados from "../pages/agent/todosOsDados.vue";

import AppComissao from "../pages/agent/appComissao.vue";
import DadosDoSub from "../pages/agent/appDadosDoSu.vue";
import ApostasDosSub from "../pages/agent/appApostasDosSub.vue";

const router = useRouter();
const appStore = useAppStore();
const { isApp, showIndexModule } = storeToRefs(appStore);
//隐藏底下菜单
// appStore.setFooterDialogVisble(false);
const currentType = ref("0");
const SECLET_TYPE = readonly({
  Convite: 0,
  MenuDados: 1,
  TodosOsDados: 2,

  <PERSON>em<PERSON>ho: 3,
  Co<PERSON><PERSON>ão: 4,
  Dad<PERSON>_do_Subordinado: 5,

  Apost<PERSON>_dos_Subordinados: 6,
});

const secletType = ref(SECLET_TYPE.Convite);

if (router.currentRoute.value.query.tab != null) {
  secletType.value = Number(router.currentRoute.value.query.tab);
}

const tabData = ref([
  {
    label: "Link de convite",
    value: SECLET_TYPE.Convite,
  },
  {
    label: "Meus dados",
    value: SECLET_TYPE.MenuDados,
  },
  {
    label: "Todos os dados",
    value: SECLET_TYPE.TodosOsDados,
  },
  //未开发
  {
    label: "Desempenho",
    value: SECLET_TYPE.Desempenho,
  },
  {
    label: "Comissão",
    value: SECLET_TYPE.Comissão,
  },
  {
    label: "Dados do Subordinado",
    value: SECLET_TYPE.Dados_do_Subordinado,
  },
  {
    label: "Apostas dos Subordinados",
    value: SECLET_TYPE.Apostas_dos_Subordinados,
  },
]);

const onTabChange = () => {
  // showToast(secletType.value)
  // secletType.value =
  // router.push("/agent/?tab="+secletType.value)
};

// watch(router.currentRoute, () => {
//     if(router.currentRoute.value.query.tab !=null ){
//         secletType.value= Number(router.currentRoute.value.query.tab)
//     }
// });

//返回
function clickLeft() {
  router.push("/");
  appStore.setShowIndexModule(false);

  //   appStore.setFooterDialogVisble(true);
}

//跳转
function mussDadosClick(data: any) {
  console.log(data);
  secletType.value = data;
}

const showModuleVisible = computed({
  get: () => showIndexModule.value === 2,
  set: (val: boolean) => {
    appStore.setShowIndexModule(val ? 2 : false);
  },
});
</script>
<template>
  <van-popup
    v-model:show="showModuleVisible"
    position="bottom"
    teleport="body"
    :close-on-click-overlay="true"
    destroy-on-close
    z-index="998"
    @update:show="(val) => !val && appStore.setShowIndexModule(false)"
  >
    <div class="agente">
      <AppPageTitle
        left-arrow
        title="Convidar"
        title-weight="700"
        @clickLeft="clickLeft"
        style="position: sticky; top: 0; z-index: 10"
      />
      <div class="tab" style="position: relative; height: 222px">
        <img class="tab-bg" src="/img/invite/afiliado_1.png" />

        <div
          style="
            position: absolute;
            bottom: 0px;
            background: rgba(105, 82, 235, 0.5);
            backdrop-filter: blur(8px);
          "
        >
          <AppTab
            :list-data="tabData"
            v-model="secletType"
            @change="onTabChange"
          ></AppTab>
        </div>
      </div>
      <div class="agente-content" style="overflow: auto">
        <LinkDeConvite v-if="secletType == SECLET_TYPE.Convite" />
        <MeusDados
          v-else-if="secletType == SECLET_TYPE.MenuDados"
          @clickDados="mussDadosClick"
        />
        <TodosOsDados v-else-if="secletType == SECLET_TYPE.TodosOsDados" />
        <AppComissao v-else-if="secletType == SECLET_TYPE.Comissão" />
        <DadosDoSub
          v-else-if="secletType == SECLET_TYPE.Dados_do_Subordinado"
        />
        <ApostasDosSub
          v-else-if="secletType == SECLET_TYPE.Apostas_dos_Subordinados"
        />
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.tab-bg {
  width: 100vw;
}
.agente {
  .agente-content {
    position: relative;
    min-height: calc(100vh - 163px);
    // background-color: var(--theme-bg-color);

    background-image: url(/img/index/main_bg.webp);
    background-size: cover;
    background-repeat: repeat;
  }
}
</style>

<route lang="yaml">
meta:
  auth: true
</route>
