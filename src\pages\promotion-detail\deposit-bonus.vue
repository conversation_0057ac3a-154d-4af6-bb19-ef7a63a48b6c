<script setup lang='ts' name='deposit-bonus'>
const router = useRouter()
const { isApp } = storeToRefs(useAppStore())

const clickHandler = () => {
  if (isApp.value) return window.open('brazilapp://event?type=deposit')

  router.push('/finance?tab=deposit')
}
</script>
<template>
  <div class="deposit-bonus">
    <AppIndexHeader />
    <AppPageTitle left-arrow title="Recarga benefícios" title-weight="700" />
    <AppImage src="/img/promotion/deposit-bonus.png" alt="" class="banner" />
    <p class="des">A partir de agora, a recarga pode obter recompensas extras em dinheiro.<br />
      Quanto mais você recarregar, maior será a taxa de recompensa, até 10%. Após a recarga, o dinheiro extra também
      será
      transferido diretamente para a sua conta.</p>
    <div class="text-box">
      <p>Nota especial:</p>
      <p>Certifique-se de que o seu número de conta, número de telemóvel e CPF são únicos.<br />
        Se o mesmo usuário registrar várias contas para obter bônus, consideraremos isso trapaceando e as contas
        relevantes serão congeladas permanentemente.
        Não faremos qualquer compensação pelas perdas causadas por trapaça.
      </p>
    </div>
    <AppButton @click="clickHandler" width="580" height="90" radius="15" blue whiteText center>Recarregue agora
    </AppButton>
  </div>
</template>

<style lang='scss' scoped>
.deposit-bonus {
  // background: #000A1D;
  padding: 30px 20px 120px;
  color: #fff;
  font-size: 26px;
  // min-height: calc(100vh - 110px);
  padding-top: var(--app-navbar-height);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.banner {
  width: 710px;
  display: block;
  margin-bottom: 30px;
}

.text-box {
  border-radius: 20px;
  border: 1px solid #28374d;
  background: #28374d;
  width: 100%;
  padding: 20px 25px;
  line-height: 40px;
  margin-bottom: 50px;
}

.des {
  line-height: 40px;
  margin-bottom: 30px;
  color: #28374d;
}
</style>
