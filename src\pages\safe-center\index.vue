<script lang="ts" setup name="SafeCenter">
const router = useRouter()

const appStore = useAppStore()
const { userInfo, userBanks } = storeToRefs(appStore)
appStore.runGetMemberInfo()

const menus = computed(() => {
  const arr = [
    { id: '1', path: '/safe-center/account-info', icon: 'icon_personal_information', title: 'Informações pessoais', subTitle: 'Informações pessoais completas.', done: userInfo.value.phone && userInfo.value.email && userInfo.value.telegram, edit: false },
    { id: '2', path: '/safe-center/reset-pwd', icon: 'icon_cryptographic', title: 'Alterar senha de entrar', subTitle: 'Combinação recomendada de letras e números', done: true, edit: true },
    { id: '3', path: '/safe-center/bind-pay-pwd', icon: 'icon_payment_password', title: '<PERSON><PERSON> de pagamento', subTitle: 'Definir a senha de pagamento para melhorar a segurança da sua conta', done: userInfo.value.pay_password && +userInfo.value.pay_password === 1, edit: true },
    { id: '4', path: '/safe-center/banks', icon: 'icon_banking_card', title: 'Número da conta bancária', subTitle: 'Adicionar número de conta bancária', done: userBanks.value.length >= 5, edit: false },
    { id: '5', path: '/safe-center/verify-check', icon: 'icon_phone_verification', title: 'Verificação de telefone', subTitle: 'Verificação de telefone', done: userInfo.value.phone_verify && +userInfo.value.phone_verify === 1, edit: false },
    { id: '6', path: '/safe-center/verify-check', icon: 'icon_email_verification', title: 'Verificação de email', subTitle: 'Verificação de email', done: userInfo.value.email_verify && +userInfo.value.email_verify === 1, edit: false },
  ]
  return arr
})

const menuClick = (item: any) => {
  if (item.id === '5' || item.id === '6') {
    if (item.done) {
      showToast('Se você precisar modificar a encadernação, entre em contato com o atendimento ao cliente')
      return
    }
  }
  if (item.path) {
    router.push(item.path)
  }
}

</script>

<template>
  <div class="safe-center-page">
    <AppHeader left-arrow title="Centro de Segurança" placeholder />
    <section>
      <ul class="panel">
        <li v-for="item in menus" class="item" @click="() => menuClick(item)">
          <div class="left">
            <AppImage :src="'/icons/'+item.icon+'.png'" />
          </div>
          <div class="mid">
            <h3>{{ item.title }}</h3>
            <p>{{ item.subTitle }}</p>
          </div>
          <div class="right">
            <AppImage class="check" :class="{done: item.done}" src="/icons/icon_fulfil.png" />
            <AppImage class="edit" :class="{done: item.done, 'show-edit': item.edit}" src="/icons/icon_compiler.png" />
            <AppImage class="arrow" :class="{done: item.done, 'show-arrow': item.edit}" src="/icons/icon_jump.png" />
          </div>
        </li>
      </ul>
    </section>
  </div>
</template>

<style lang="scss" scoped>
  .safe-center-page {
    width: 710px;
    height: auto;
    border-radius: 20px;
    margin: 0 auto;
    margin-top: 30px;
    .panel {
      border-radius: 20px;
      background: #28374d;
      .item {
        height: 130px;
        border-bottom: 2px solid #152237;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &:nth-child(1) {
          .left {
            padding: 34px 56px 34px 56px;
            img {
              width: 60px;
            }
          }
        }
        &:nth-child(2) {
          .left {
            padding: 34px 63px 34px 60px;
            img {
              width: 50px;
            }
          }
        }
        &:nth-child(3) {
          .left {
            padding: 34px 57px 34px 52px;
            img {
              width: 63px;
            }
          }
        }
        &:nth-child(4) {
          .left {
            padding: 34px 55px 34px 52px;
            img {
              width: 65px;
            }
          }
        }
        &:nth-child(5) {
          .left {
            padding: 34px 66px 34px 63px;
            img {
              width: 43px;
            }
          }
        }
        &:nth-child(6) {
          border-bottom: 0;
          .left {
            padding: 34px 60px 34px 56px;
            img {
              width: 56px;
            }
          }
        }
        .mid {
          width: 388px;
          h3 {
            color: #FFF;
            font-size: 30px;
            font-weight: 400;
            line-height: 25px;
            margin-bottom: 8px;
          }
          p {
            color: #7d8aa2;
            font-size: 20px;
            font-weight: 400;
            line-height: 20px;
          }
        }
        .right {
          flex: 1;
          padding-right: 25px;
          display: flex;
          align-items: center;
          gap: 14px;
          justify-content: flex-end;
          img {
            width: 32px;
          }
          img.arrow {
            width: 20px;
          }
          .check {
            opacity: 0;
          }
          .check.done {
            opacity: 1;
            width: 55px;
          }
          .arrow {
            margin-left: 10px;
            opacity: 1;
          }
          .arrow.done {
            opacity: 0;
          }
          .arrow.show-arrow {
            opacity: 1;
          }
          .edit.done {
            margin-left: 20px;
            opacity: 0;
          }
          .edit.show-edit {
            opacity: 1;
          }
        }
      }
    }
  }
</style>
