<!DOCTYPE html>
<html lang="pt">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no"
    />
    <meta name="screen-orientation" content="portrait" />
    <meta name="full-screen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="version" content="1.0.0" />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="144x144"
      href="favicon1.ico"
    />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta
      http-equiv="Cache-control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Cache" content="no-cache" />
    <style>
      html {
        /* font-size: 13.3333333vw !important; */
        -webkit-text-size-adjust: none;
        /* scrollbar-width: none; */
        scroll-behavior: smooth;
      }

      html,
      body {
        touch-action: manipulation;
        -webkit-touch-callout: manipulation;
        --webkit-tap-highlight-color: transparent;
      }
      ::-webkit-scrollbar {
        display: none;
      }
    </style>

    <link rel="icon" href="/favicon1.ico" type="image/svg+xml" />
    <link rel="manifest" href="/manifest.json" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <title>cagadoslot</title>
    <!-- <script src="/nobug.js"></script> -->
  </head>

  <body class="">
    <button id="install" hidden>Install</button>
    <div id="app"></div>
    <noscript>
      <div>Please enable JavaScript to use this application.</div>
    </noscript>
    <script src="https://fastly.jsdelivr.net/npm/@vant/touch-emulator"></script>
    <script type="module" src="/src/main.ts"></script>

    <script>
      console.log("im here");
      let deferredPrompt = null;

      window.addEventListener("beforeinstallprompt", (e) => {
        e.preventDefault();
        deferredPrompt = e;
        console.log(879329329239);

        console.log(deferredPrompt);

        console.log("Service beforeinstallprompt");
      });

      window.installButtonclick = async function () {
        console.log("installButtonclick11111");
        console.log("deferredPrompt", deferredPrompt);

        const result = await deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
          if (choiceResult.outcome === "accepted") {
            console.log("User accepted the install prompt");
          } else {
            console.log("User dismissed the install prompt");
          }
          deferredPrompt = null;
        });
      };

      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("./service-worker.js")
            .then((reg) => console.log("Service Worker registered"))
            .catch((err) =>
              console.log("Service Worker registration failed", err)
            );
        });
      }

      window.addEventListener("appinstalled", (evt) => {
        window.open("/index2.html", "_self");
      });
    </script>

    <script>
      (function() {
        // 获取当前URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const isApp = urlParams.get('isApp') || urlParams.get('is-app');
        
        // 检查是否已经在GooglePlay页面
        const isGooglePlayPage = window.location.pathname.includes('/googlePlayApp/');
        
        // 如果不在GooglePlay页面且没有isApp=1参数，则跳转
        if (!isGooglePlayPage && isApp !== '1') {
          console.log('No isApp=1 parameter found, redirecting to GooglePlay page');
          window.location.href = '/googlePlayApp/index.html';
          return;
        }
        
        // 如果有isApp=1参数，记录到sessionStorage供后续使用
        if (isApp === '1') {
          sessionStorage.setItem('isApp', '1');
        }
      })();
    </script>
  </body>
</html>
