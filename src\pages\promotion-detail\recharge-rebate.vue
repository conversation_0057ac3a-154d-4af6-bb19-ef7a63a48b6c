<script setup lang='ts' name='promotion'>
import { ShareRecordItem, ShareRecordTotal, ShareRecordtemParam } from "~/core/http/api"

const router = useRouter()
const isReceber = ref(false)
const isShowSelect = ref(false)
const showDatePicker = ref(false)
const showViewType = ref(false)
const ValidInviteCount = ref(0) //有效下级
const ChildCount= ref(0)   //直属下级人数

const isShowDetails = ref(false);
const isShowHistory = ref(false);
const isShowPersonalDetails = ref(false);
const swipeWidth = window.innerWidth;
const swipeHeight = window.innerHeight;
const showPopover = ref(false);
const selText = ref("Inteiro")
const selectIndex = ref(0);
const selectLevel = ref(0);
const inputForce = ref(false)
const question= ref()
const finished = ref(false);
const pageDatas = ref({
  pageIndex:0,
  pageCount:30,
});
// const currentDate = ref({
//   start: dayjs().subtract(0, 'day'),
//   end: dayjs()
// })

const currentDate = ref({
  start: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  end: dayjs().format('YYYY-MM-DD')
})

const ShareRecordItemArray = ref<ShareRecordItem[]>([]);
const ShareRecordTotalArray = ref<ShareRecordTotal[]>([]);

// let i=0
// for( i;i<15; i++){
//   const temp:ShareRecordItem={
//     nickname:'1112222',
//     level: 1,//对父级的贡献
//     create_at: 4544444,//首冲金额
//     parent_id: 148851444,//绑定时间
//     recharge_rate: 0.01,
//     recharge_share: 10,
//     amount: 1000,
//     uid:465464156 + i,
//   }

//   ShareRecordItemArray.value.push(temp)
// }
const ValidInviteWagedReq = ref(0);
const ValidInviteMinDeposit = ref(0);
const showDetailsIndex = ref(-1);

const appStore = useAppStore();
const { userInfo } = storeToRefs(appStore)
appStore.setFooterDialogVisble(false)

const shareUrl = ref("")
const share=[{icon:"/icons/agent_share_Partilhar",name:"Partilhar",url:location.origin},
             {icon:"/icons/agent_share_Facebook",name:"Facebook",url:"https://m.facebook.com/"},  
             {icon:"/icons/agent_share_Instagram",name:"Instagram",url:"https://www.instagram.com/"},  
             {icon:"/icons/agent_share_Telegram",name:"Telegram",url:"https://t.me/telegram"},  
             {icon:"/icons/agent_share_WhatsApp",name:"WhatsApp",url:"https://api.whatsapp.com/"},  
             {icon:"/icons/agent_share_TikTok",name:"TikTok",url:"https://vt.tiktok.com/"},  
             {icon:"/icons/agent_share_Kwai",name:"Kwai",url:"https://www.kwai.com/"},  
             {icon:"/icons/agent_share_Line",name:"Line",url:"https://line.me/"},]

const li = computed(()=>{
  if(userInfo.value.uid && userInfo.value.uid.length < 9){
    return [ location.origin + '/register' + `?id=${userInfo.value.uid}` + '&currency=BRL&type=2' ]
  }else{
    return [""]
  }
 
})

let actions = [{ text: "Inteiro", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Nível 1", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Nível 2", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Nível 3", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },]

const popoverStatus = (isOpen: boolean) => {
    if(!isOpen) {
      actions = [{ text: "Inteiro", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      { text: "Nível 1", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      { text: "Nível 2", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      { text: "Nível 3", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },]
    }   
}

const onSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selText.value = action.text;
   
    selectIndex.value = Number(action.calssName)
    pageDatas.value.pageIndex = 0;
    finished.value = false;
    selectLevel.value = Number(action.calssName)
    loadMoreData();
    // pageDatas.value.pageIndex=1
}

const eventFocus=()=>{
  inputForce.value = true
}

const eventBlur=()=>{
  inputForce.value = false
}

const onConfirmID = ()=>{
  console.log("++++++++++++++++")
  // showSelectTime(false)
  pageDatas.value.pageIndex=0
  refresh();
  // pageDatas.value.pageIndex=1
}

function closeSelect(){
  isShowSelect.value =false
}

function openSelect(){
  isShowSelect.value =true
}

function liClick(item:any){
  shareUrl.value = li.value[0]
}

//日期查找
const dateStr = computed(() => dayjs(currentDate.value.start).format('YYYY-MM-DD') + ' - ' + dayjs(currentDate.value.end).format('YYYY-MM-DD'))

//日期查找
const onConfirm = (data: any) => {
  showSelectTime(false)
  currentDate.value = data;
  pageDatas.value.pageIndex=0

  pageDatas.value.pageIndex = 0;
  finished.value = false;

  loadMoreData();
}

// const onConfirmID = ()=>{
//   showSelectTime(false)
//   pageDatas.value.pageIndex=0
//   refresh();
//   pageDatas.value.pageIndex=1
// }


const onCancel=()=>{
  showSelectTime(false)
}

const showSelectTime=(isFlag:boolean)=>{
  showViewType.value = isFlag
}

//复制分享地址
function copyUrl(){
  showToast('Copiado com sucesso')
  copy(li.value[0])
}
//复制id
function copyID(){
  showToast('Copiado com sucesso')
  copy(userInfo.value.uid || '')
}

function openShareUrl(item:any){
  window.open(item.url, '_blank')
}


function showDetails(){
  finished.value = false;
  ShareRecordItemArray.value = [];
  selectLevel.value = 0;
  pageDatas.value.pageIndex = 0;
  isShowDetails.value = true;

  // loadMoreData();

  document.documentElement.style.setProperty('--overflow-value', 'hidden');
}

function closeDetails(){
  isShowDetails.value = false;
  document.documentElement.style.setProperty('--overflow-value', 'auto');
}
function clickLeft() {
  appStore.setFooterDialogVisble(true)
}

function clickHistory() {
  selectLevel.value = selectIndex.value;
  finished.value = false;
  ShareRecordItemArray.value = [];
  pageDatas.value.pageIndex = 0;
  isShowHistory.value = true;
  document.documentElement.style.setProperty('--overflow-value', 'hidden');
}

function closeHistory(){
  isShowHistory.value = false;
  document.documentElement.style.setProperty('--overflow-value', 'auto');
}

function clickBack(){
  // router.push('/advancement')
  router.go(-1)
  appStore.setFooterDialogVisble(true)
}

//按钮点击
const isRunAni = ref(false)
function getMoney() {
  if (isRunAni.value) return;
  if (rechargeShareInfo.value?.EnableRechargeShare > 0) {
    runApiRechargeShare();
  } else {
    isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  }
  console.log("getMoney click")
}

//数据请求

// const refresh = () => {
//   pageDatas.value.pageIndex = 0;
//   getQueryTeam();
// }

const refresh = () => {
  // pageDatas.value.pageIndex=0;
  // finished.value = false;
  // ShareRecordItemArray.value.length = 0
  // getQueryTeam();
}

const loadMoreData = () => {
  console.log("loadMoreData", pageDatas.value.pageIndex);
  if(pageDatas.value?.pageIndex == 0 || (pageDatas.value?.pageIndex) * pageDatas.value?.pageCount <= rechargeShareRecords.value?.recordCount){
    getQueryTeam();
    pageDatas.value.pageIndex++
  }
}

const getQueryTeam = () => {
  let tempStartTime = currentDate.value.start + " 00:00:00"
  let tempEndTime = currentDate.value.end + " 23:59:59"
  console.log("getQueryTeam",pageDatas.value.pageIndex);
  console.log("start", dayjs(tempStartTime).unix(), tempStartTime);
  console.log("end", dayjs(tempEndTime).unix(), tempEndTime);

  if(pageDatas.value.pageIndex == 0){
    ShareRecordItemArray.value = [];
  }
  if(finished.value == false && rechargeShareRecordloading.value == false){
    if(isShowDetails.value){
      runGetRechargeShareRecord({ beginTime: 0, endTime: 0, level: selectLevel.value, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount})
    }else{
      runGetRechargeShareRecord({ beginTime: dayjs(tempStartTime).unix(), endTime: dayjs(tempEndTime).unix(), level: selectLevel.value, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount})
    }
  }
}

//获取分成比例配置
const { run: runGetRechargeShareCfg, data: rechargeShareDatas } = useRequest(() => ApiRechargeShareList(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
  }
})

//获取充值分成的信息
const { run: runGetRechargeShareInfo, data: rechargeShareInfo } = useRequest(() => ApiRechargeShareInfo(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
  }
})

const RecordCount = ref(0);
const Amount = ref(0);
const RechargeShare = ref(0);
/*
查询充值分成记录
*/
const {run: runGetRechargeShareRecord , data:rechargeShareRecords, loading:rechargeShareRecordloading } = useRequest((datas:ShareRecordtemParam) => ApiGetRechargeShareRecord(datas), {
  manual: true,
  onSuccess(res:any) {
    // console.log(rechargeShareRecords)
    if (res?.Total){
      RecordCount.value = res?.recordCount;
    }
    if(res?.Total){
      ShareRecordTotalArray.value = [...ShareRecordTotalArray.value,...res?.Total!]
    }
    if(res?.Items){
      ShareRecordItemArray.value = [...ShareRecordItemArray.value,...res?.Items!]
    }
    if(ShareRecordItemArray.value.length >= res?.recordCount){
      finished.value = true;
    } else {
      finished.value = false;
    }
    console.log(ShareRecordTotalArray)
  },
  onError(data:any){
    showErrorTip(data)
  }
  
})

// runGetRechargeShareRecord({ beginTime: currentDate.value.start.unix(), endTime: currentDate.value.end.unix(), level: 0, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount})

/*
领取充值分成
*/
const { run: runApiRechargeShare, data: rechargeShareData } = useRequest(() => ApiRechargeShare(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    console.log(res);
    // appStore.setIsShowMessage(true, "Bônus +R$ " + res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    
    runGetRechargeShareInfo();
    appStore.runGetUserBalance();
  }
})


const {run: runQueryChildRunningReturnData,data:queryDatas } = useRequest(() => ApiQueryChildRunningReturnData(), {
  manual: true,
  onSuccess(res:any) {
    ChildCount.value = res.ChildCount;
  }
})

const {run: runGetValidInviteBonusInfo,data:ValidInviteBonusInfo } = useRequest(() => ApiGetValidInviteBonusInfo(), {
  manual: true,
  onSuccess(res:any) {
    ValidInviteCount.value = ValidInviteBonusInfo.value.ValidInviteCount;
  }
})

onMounted(() => {
  runGetValidInviteBonusInfo();
  runQueryChildRunningReturnData();
  runGetRechargeShareInfo();
  runGetRechargeShareCfg();
});

</script>
<template>
  <AppPageTitle left-arrow showHistory title="Indique amigos para obter bônus de depósito" 
  @clickLeft="clickLeft" @clickHistory="clickHistory"/>
  <div class="linkDeConvite">
      <!-- {{ swipeWidth  }}
      {{swipeHeight}} -->
      <!-- {{console.log(currentDate)}} -->
      <div class="info">
          <div class="line"></div>
          <label class="modo">Informações da promoção</label>
          <!-- 二维码 -->
          <AppImage src="/icons/agent_qrcodebg" class="QRCodebg" />
          <AppImage src="/agent_qrcode" class="QRCode" />
          <div class="agent_bottom_right" >
            <label class="men">Meu Link</label>
            <label class="alt" @click="openSelect">Alterar</label>
            <div class="httpAddress" :class={active:isShowSelect}>
              <label class="test">{{li[0]}}</label>
              <AppImage src="/icons/agent_copy" class="copy" @click="copyUrl" />
            </div>
          </div>

          <div class="conviteNum">
            <label class="subord">Subordinados diretos</label>
            <label class="subord_num">{{ ChildCount }}</label>
            <br>
            <label class="convite">Código de Convite</label>
            <label class="convite_id">{{userInfo.uid || ''}}</label>
            <AppImage src="/icons/agent_copy" class="copy2" @click="copyID"/>
          </div>
        
          <div class="share">
            <div v-for=" item in share" class="item" @click="openShareUrl(item)">
              <AppImage :src="item.icon" class="itemImg" />
              <label class="itemName">{{item.name}}</label>
            </div>

          </div>
          <div class="detail">
            <label class="one">Subordinados válidos </label>
            <label class="two"> {{ ValidInviteCount }} </label>
            <label class="one"> pessoas</label>
            <label class="two" @click="showDetails"> Detalhes</label>
            <!-- <label class="two"> Detalhes</label> -->
          </div>
      </div>
      <div class="condition" v-if="rechargeShareDatas?.validWaged > 0">

        <span>O que é um número válido promocional?(Cumprir todos os requisitos indicados abaixo)</span>
        
        <div>
          <div class="one">
            <div class="text1">Apostas acumuladas do subordinado</div>
            <div class="text2">&nbsp{{rechargeShareDatas?.validWaged}} ou mais</div>
          </div>
        </div>
      </div>

      <div class="topDiv">
        <div>
          <span class="first">Pode ser afirmado:</span>
          <span class="second"> {{ "&nbsp" + (transf(rechargeShareInfo?.EnableRechargeShare) || "0,00") }} </span>
        </div>
        <div>
          <span class="first">Bônus total:</span>
          <span class="third"> {{ "&nbsp" + (transf(rechargeShareInfo?.HasRechargeShare) || "0,00") }}</span>
        </div>
      </div>

      <div class="belowDiv"> <!--下面显示-->
        <!-- <label class="below_title">CASHBACK E REGRAS</label> -->
        <div class="below_itemTitle">
          <!-- <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp" /> -->
          <label class="below_itemTitle_aposta">Relação </label>
          <label class="below_itemTitle_aposta below_itemTitle_cashback">Prêmios Extra(%)</label>
        </div>

        <div v-for="(data, index) in rechargeShareDatas?.cfgList" :key="index" class="below_itemDevPa">
          <label class="itemContent">Nível {{ data.level }}</label>
          <label class="itemContent copy">{{ transf(Number((data.rate * 100).toFixed(2))) + "%" }}</label>
        </div>

      </div>

      <div class="desc">
          <span>Instruções Do Evento:</span>
          <div class="rule">1. Convide amigos para depositar e ganhe um bônus proporcional ao valor do depósito. Quanto mais amigos você convidar, mais depósitos seus amigos farão e maior será a recompensa;<br>2. Esta promoção é uma recompensa adicional para a plataforma, e você também pode desfrutar de outras recompensas e comissões do agente, o que significa que você pode experimentar diretamente a felicidade dobrada;<br>3. As recompensas só poderão ser coletadas manualmente em APP/iOS, APP/Android, PC/Windows, e serão emitidas automaticamente após a expiração;<br>4. Os bônus deste evento (excluindo o prêmio principal) devem atender a condições válidas antes de serem obtidos;<br>5. Esta atividade está limitada às operações normais dos correntistas. É proibido leasing, utilização de plug-ins, robôs, apostas com contas diferentes, escovação mútua, arbitragem, interfaces, protocolos, exploração de brechas, controle de grupo e outros meios técnicos. Caso contrário, as recompensas serão canceladas ou deduzidas, a conta será congelada ou mesmo colocada na lista negra;<br>6. Para evitar diferenças na compreensão do texto, a plataforma reserva-se o direito de interpretação final deste evento.</div>
      </div>

      <div class="footer-space"></div>

      <div class="selectAddress" v-if="isShowSelect" @click="closeSelect">
          <div class="ul">
              <ul>
                <li v-for="item in li" @click="liClick(item)" :class="{active:item==shareUrl}">
                  <span>{{item}}</span>
                </li>
              </ul>
          </div>
      </div>

      <div class="bottomButton">
        <div class="back" @click="clickBack">
          <span>Retornar</span>
        </div>
        <div class="receive" @click="getMoney" :class="{active: rechargeShareInfo?.EnableRechargeShare > 0}">
          <span>Receber</span>
        </div>
      </div>
  </div>
  <van-popup class="details-poup" v-model:show="isShowDetails" round :close-on-click-overlay="true" @click-overlay="closeDetails">
        <div class="details-content">
          <span>Detalhes</span>
          <div class="invite-list">
            <AppList class="app-List" :loading="rechargeShareRecordloading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData"
              style="padding-top: 0px;">
              <div class="invite-list-item" v-for="(item, index) in ShareRecordItemArray" :key="item.nickname">
                <div class="invite-list-item-body">
                  <div class="invite-list-item-body-id">  
                    <!-- <label class="invite-list-item-body-id-name"> ID: </label> -->
                    <label> {{ item.uid }}</label>
                    <AppImage class="lower-list-item-body-id-copy" src="/icons/personal_copy.webp" :style="{scale:1}" @click="()=>{
                          copy( item.uid.toString() || '')
                          showToast('Copied!')
                    }"/>
                  </div>
                  <!-- <div class="invite-list-item-body-time">  
                    <label>Tempo:</label>
                  </div> -->
                  <div class="invite-list-item-body-timeAt">  

                    <label>{{ dayjs(getBrazilTime(item.create_at*1000)).format('YYYY-MM-DD HH:mm:ss') }}</label>
                  </div>
                  <div class="invite-list-item-body-apos">  
                    <!-- <label class="invite-list-item-body-apos-level">Relação:  </label> -->
                    <label> Nível {{ item.level }} </label>
                  </div>
                </div>
              </div>
            </AppList>
          </div>
          <app-empty class="onne_jl"  text="Sem Registros" v-show="ShareRecordItemArray.length==0"></app-empty>

        </div>
        <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp" alt=""
          @click="closeDetails" />
  </van-popup>
  <van-popup class="history-poup" v-model:show="isShowHistory" round :close-on-click-overlay="true" @click-overlay="closeHistory">
        <div class="details-content">
          <span>Histórico</span>
          <div class="content-body-top">
              <!-- 查下框 -->
              <div class="invite-query">
                <div class="invite-query-input">
                    <div class="date" @click="showSelectTime(true)">{{ dateStr }}</div>

                </div>
              </div>
              <!--时间选择框  -->
              <AppDatePickerNew class="selectTime" v-show="showViewType" v-model="showDatePicker" :startDate="currentDate.start" :endDate="currentDate.end" group @confirm="onConfirm" @cancel="onCancel" />

              <van-popover class="content-body-top-popover" v-model:show="showPopover" :actions="actions" 
                        @select="onSelect" @open="popoverStatus(true)" @close="popoverStatus(false)" placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-body-top-popover-select box-border"
                                :class="{ 'viewOpen': showPopover }">
                                <van-text-ellipsis :content="selText" class="top-popover-select-title"/>
                                <span class="top-popover-select-icon"
                                    :class="{ 'rotate': showPopover, 'rotate1': !showPopover }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                        
              </van-popover>
          </div>

          <div class="statistics">
            <div class="content1"/>
            <div class="content2"/>
            <div class="content3"/>

            <div class="level1-amount">
              <label>Nível 1 Depositar </label>
              <label class="data">{{ transf(Number(ShareRecordTotalArray[0]?.amount)) || "0,00" }}</label>
            </div>

            <div class="level1-recharge-share">
              <label>Nível 1 Bônus total </label>
              <label class="data">{{ transf(Number(ShareRecordTotalArray[0]?.recharge_share)) || "0,00" }}</label>
            </div>

            <div class="level2-amount">
              <label>Nível 2 Depositar </label>
              <label class="data">{{ transf(Number(ShareRecordTotalArray[1]?.amount)) || "0,00" }}</label>

            </div>

            <div class="level2-recharge-share">
              <label>Nível 2 Bônus total </label>
              <label class="data">{{ transf(Number(ShareRecordTotalArray[1]?.recharge_share)) || "0,00" }}</label>

            </div>

            <div class="level3-amount">
              <label>Nível 3 Depositar </label>
              <label class="data">{{ transf(Number(ShareRecordTotalArray[2]?.amount)) || "0,00" }}</label>

            </div>

            <div class="level3-recharge-share">
              <label>Nível 3 Bônus total </label>
              <label class="data">{{ transf(Number(ShareRecordTotalArray[2]?.recharge_share)) || "0,00" }}</label>

            </div>

          </div>

          <div class="lower-list">
            <AppList class="app-List" :loading="rechargeShareRecordloading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData"
              style="padding-top: 0px;">
              <div class="lower-list-item" v-for="(item, index) in ShareRecordItemArray" :key="item.nickname">
                <div class="lower-list-item-body">
                  <div class="lower-list-item-body-id">  
                    <!-- <label class="lower-list-item-body-id-name"> Nome: </label> -->
                    <label> {{ item.uid }}</label>
                    <AppImage class="lower-list-item-body-id-copy" src="/icons/personal_copy.webp" :style="{scale:1}" @click="()=>{
                          copy( item.uid.toString() || '')
                          showToast('Copied!')
                    }"/>
                  </div>

                  <div class="lower-list-item-body-apos">  
                    <!-- <label class="lower-list-item-body-apos-level">Relação:  </label> -->
                    <label> Nível {{ item.level }} </label>
                  </div>

                  <div class="lower-list-item-body-amount">
                    <label class="lower-list-item-body-amount-desc">Depositar  </label>
                    <label> {{ transf(item.amount) }} </label>
                  </div>

                  <div class="lower-list-item-body-recharge-rate">
                    <label class="lower-list-item-body-recharge-rate-desc">Proporcao  </label>
                    <label> {{ transf(Number((item.recharge_rate * 100).toFixed(2))) + "%" }} </label>
                    
                  </div>

                  <div class="lower-list-item-body-recharge-share">
                    <label class="lower-list-item-body-recharge-share-desc">Bônus  </label>
                    <label> {{ transf(item.recharge_share) }} </label>
                  </div>

                  <!-- <div class="lower-list-item-body-time">  
                    <label>Tempo:</label>
                  </div> -->
                  <div class="lower-list-item-body-timeAt">  
                    <label>{{ dayjs(getBrazilTime(item.create_at*1000)).format('YYYY-MM-DD HH:mm:ss') }}</label>
                  </div>
                </div>
              </div>
            </AppList>
          </div>
          <app-empty class="onne_jl"  text="Sem Registros" v-show="ShareRecordItemArray.length==0"></app-empty>

        </div>
        <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp" alt=""
          @click="closeHistory" />
  </van-popup>
</template>

<style lang='scss' scoped>
.linkDeConvite{
  position: relative;
  height: calc(100vh - 211px);
  overflow: auto;
  background-color: var(--theme-bg-color);

}

.all-promotion {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--theme-bg-color);
}

.info{
  margin: auto;
  margin-top: 20px;
  width:710px;
  height:592px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  font-size:23px;

  .modo{
    position: absolute;
    color:var(--theme-text-color-darken);
    font-size: 24px;
    left:60px;
    top: 60px;
    width: 300px;
  }

  .difere{
    position: absolute;
    color: var(--theme-text-color-darken);
    left:340px;
    top: 108px;
    width: 380px
  }

  .receber{
    position: absolute;
    left:435px;
    top: 43px;

    width:120px;
    height:48px;
    background:var(--theme-disabled-bg-color);
    border-radius:8px;
    color:var(--theme-disabled-font-color);
    text-align: center;
    line-height: 50px;
    &.active{
      background:var(--theme-filter-active-color);
      color:var(--theme-main-bg-color);
    }
  }

  .hist{
    position: absolute;
    left:583px;
    top: 43px;

    width:120px;
    height:48px;
    background:var(--theme-primary-color);
    color:var(--theme-main-bg-color);
    border-radius:8px;
    text-align: center;
    line-height: 50px;
  
  }

  .line{
    position: absolute;
    top: 110px;
    left: 37px;
    width:676px;
    height:1px;
    background: var(--theme-color-line);
  }

  .QRCodebg{
    position: absolute;
    width: 154px;
    left:40px;
    top: 130px;
  }

  .QRCode{
    position: absolute;
    width: 140px;
    left:45px;
    top: 137px;
  }

  .agent_bottom_right{
    position: absolute;
    width: 485px;
    height: 220px;
    left:220px;
    top: 130px;
    .men{
      color:var(--theme-text-color-lighten);
    }

    .alt{
      color:var(--theme-primary-color);
      float: right;
    }

    .httpAddress{
      display: flex;
      align-items: center; /* 垂直居中 */
      margin-top:15px;
      width:494px;
      height:92px;
      background:var(--theme-main-bg-color);
      border:1px solid;
      border-color:var(--theme-color-line);
      border-radius:8px;
      &.active{
        border-color:var(--theme-filter-active-color);
      }
      .test{
        // display: inline-block;
        color:var(--theme-text-color);
        margin-left: 30px;
        margin-top: 4px;
        width: 400px;
        white-space: normal; /* 允许自动换行 */
        word-wrap: break-word; /* 允许在单词内换行 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制为3行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .copy{
        position: absolute;
        width: 26px;
        top: 80px;
        right: 20px;
      }
    }
  }

  .conviteNum{
    position: absolute;
    width: 485px;
    height: 90px;
    left:220px;
    top: 270px;
    color:var(--theme-text-color-lighten);

    .subord{
      display: inline-block;
      padding-top: 10px;
      // left:220px;
      // top: 350px;
    }

    .subord_num{
      padding-left: 10px;
    }

    .convite{
      display: inline-block;
      padding-top:15px;
      // left:220px;
      // top: 390px;
    }

    .convite_id{
      padding-left: 10px;
      color: var(--theme-text-color-darken);
    }
  }
 
  .copy2{
    margin-left: 20px;
    width: 26px;
  }

  .share{
    position: absolute;
    left:40px;
    top: 400px;
    width: 90%;


    display: flex;
    align-items: flex-start;
    // height: 120px;
    overflow: auto;
    overflow-x: scroll;
    .item{
      width: 100px;
      height: 120px;
      margin-right: 20px;
    }

    .itemImg{
      display: block;
      margin: 0 auto;
      width:72px ;
    }
    .itemName{
      width: 100px;
      margin-top: 10px;
      display: inline-block;
      text-align: center;
      color:var(--theme-text-color);
    }

  }

  .detail{
    position: absolute;
    left:40px;
    top: 560px;
    width: 90%;
    .one{
      color: var(--theme-text-color-lighten);
      font-size: 22px;
    }
    .two{
      color: var(--theme-primary-color);
      font-size: 22px;
    }
  }
  // .share::-webkit-scrollbar{
  //   background-color: rgb(122, 178, 238);
  // }
}

.condition{
  margin: auto;
  margin-top: 20px;
  width:710px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  text-align: center;
  padding-bottom: 8px;
  padding-top: 10px;
  line-height: 22px;
  span{
    width: 670px;
    margin-left: 20px;
    color:var(--theme-text-color-lighten);
    font-size: 21px;
  }

  .one{
    margin-left: 20px;
    margin-top: 10px;
    width:670px;
    height: 70px;
    background: var(--theme-bg-color);
    border-radius:6px;
    text-align: center;
    position: relative;
    display: flex;
    // grid-template-columns: repeat(2, 1fr);
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    flex-direction: row;
    .text1{
      // width: 370px;
      color:var(--theme-text-color-lighten);
      font-size: 24px;
      white-space: nowrap; /* 防止文本换行 */

    }
    .text2{
      // width: 370px;
      color:var(--theme-text-color-darken);
      font-size: 24px;
      white-space: nowrap; /* 防止文本换行 */
    }

    &:nth-child(2n){
      background:var(--theme-main-bg-color);
      // margin-top: 0px;
      padding-bottom: 10px;
      margin-bottom: 6px;
    }

  }

}

.topDiv {
  width: 750px;
  display: grid;
  padding-top: 30px;
  padding-left: 80px;
  grid-template-columns: repeat(2, 1fr);

  .first {
    color: var(--theme-text-color-lighten);
    font-size: 24px;
  }

  .second {
    color: var(--theme-secondary-color-finance);
    font-size: 24px;

  }

  .third {
    color: var(--theme-secondary-color-finance);
    font-size: 24px;

  }

  .bonus {
    text-align: center;
  }
}

.belowDiv {
  margin-top: 10px;
  width: 750px;
  position: relative;
  // background: var(--theme-main-bg-color);
  border-radius: 15px;

  .below_title {
    display: block;
    margin-top: 25px;
    margin-left: 25px;

    font-family: Arial;
    font-weight: 700;
    color: var(--app-ratio-title-color);
    font-size: 24px;
  }

  .below_itemTitle {
    display: flex;
    margin: 0 auto;
    margin-top: 20px;
    width: 708px;
    height: 80px;
    background: var(--theme-main-bg-color);
    border-radius: 8px;
    border: 2px solid;
    border-color: var(--theme-color-line);
  }

  .below_itemImg {
    width: 41px;
    height: 41px;
    margin-left: 20px;
    margin-top: 15px;
  }

  .below_itemTitle_aposta {
    // margin-left: 105px;
    margin-top: 28px;

    font-family: Arial;
    color: var(--theme-text-color-darken);
    font-weight: 700;
    font-size: 24px;
    width: 354px;
    text-align: center;
  }

  .below_itemDevPa {
    display: flex;
    // flex-direction: column;
    align-items: center;
    width: 708px;
    height: 70px;
    line-height: 70px;
    margin: 0 auto;
    // margin-top: 20px;
    // margin-bottom: 20px;
    // position: relative;

    &:nth-child(2n + 1) {
      background: var(--theme-main-bg-color);
      border-radius: 15px;
      width: 708px;
      height: 70px;
      line-height: 70px;
    }


    .itemContent {
      font-family: Arial;
      color: var(--app-title-color);
      font-size: 20px;
      text-align: center;
      transform: translateY(2px);
      color: var(--theme-text-color-lighten);
      width: 354px;
      text-align: center;
    }
    .copy {
      color: var(--theme-secondary-color-finance);
    }

  }

}

.desc {
  margin: auto;
  margin-top: 20px;
  width:710px;
  // height:592px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  // font-size:23px;
  padding: 24px;
  span{
    color: var(--theme-text-color);
    font-size:24px;
    line-height: 40px;

  }
  .rule{
    color: var(--theme-text-color);
    font-size:24px;
    line-height: 40px;
  }
}
.row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.footer-space {
  height: 23px; /* 或者任何你需要的高度 */
}

.bottomButton{
  position: fixed;
  width: 100%;
  height: 116px;
  bottom:0px;
  background:var(--theme-main-bg-color);
  box-shadow:0px -1px 3px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  flex-direction: column;
  padding-left: 20px;
  .back{
    width: 344px;
    height: 72px;
    background: var(--theme-main-bg-color);
    border: 1px solid;
    border-color: var(--theme-ant-primary-color-0);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: var(--theme-ant-primary-color-0);
      font-size: 26px;
    }
  }
  .receive{
    width: 344px;
    height: 72px;
    background:var(--theme-disabled-bg-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: var(--theme-disabled-font-color);
      font-size: 26px;
    }
    &.active{
      background:var(--theme-primary-color);
      span{
        color: var(--theme-primary-font-color);
      }
    }
  }
}

.box-info{
  margin-left: 20px;
  width: 710px;


}
.selectAddress{
  position: absolute;
  width: 100vw;
  height: 100%;
  left:0;
  top: -70px;
  .ul{
    position: absolute;
    width:494px;
    // height: 220px;
    left:220px;
    top: 340px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:8px;

    li{
      height: auto;
      color:#7cb39d;
      font-size: 23px;
      padding-left: 30px;
      // background-color: var(--theme-text-color);
      padding-top: 10px;
      padding-bottom: 10px;
      &.active{
        color:var(--theme-filter-active-color);
      }
      span{
        width: 400px;
        white-space: normal; /* 允许自动换行 */
        word-wrap: break-word; /* 允许在单词内换行 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制为3行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

 
}

.box-border {
            border-style: solid;
            // border-color: var(--theme-main-bg-color);
            border-color: var(--theme-color-line);
            border-width: 2px;
            border-radius: 10px;
        }

.details-poup {
  width: 100%;
  // height: 1200px;
  height: 100%;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: fixed !important;
  justify-content: center;
  top: 45vh;
  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    bottom: 0%;
  }

  .details-content {
    width: 596px;
    height: 1000px;
    background-color: var(--theme-main-bg-color);
    background-size: 596px 900px;
    text-align: center;
    position: absolute;
    display: flex;
    // justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 20px;
    border-radius: 20px;
    border: 2px solid var(--theme-color-line);
    top: 14%;
    span{
      color: var(--theme-text-color);
    }

    .content-body-top {
        width: 100%;
        height: 90px;
        padding: 20px 20px 10px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .content-body-top-popover {
            width: 180px;
            height: 50px;
        }

        .content-body-top-popover-select {
            width: 180px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .top-popover-select-title {
            color: var(--theme-text-color);
            font-size: 22px;
            line-height: 48px;
            padding-left: 18px;
            padding-top: 2px;
            width: 101px;
            height: 48px;
        }
        .top-popover-select-icon {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 15px;
            margin-right: 10px;
            svg {
                width: 20px;
                height: 20px;
                color: var(--theme-text-color-lighten);
                position: absolute;

            }
        }
    }

    .invite-query{
      // margin-top: 20px;
      // background-color: aliceblue;
      .invite-query-input{
        margin-left: 0px;
        width:356px;
        height:50px;
        background:var(--theme-main-bg-color);
        border:1px solid;
        border-color:var(--theme-color-line);
        border-radius:26px;
        .date{
          font-size: 24px;
          display: inline-block;
          color:var(--theme-primary-color);
          width: 100%;
          height: 100%;
          text-align: center;
          padding-top: 12px;
          // padding-left: 20px;
        }
      }
    }

    .selectTime{
      position: absolute;
      margin-top: 420px; 
      left: -60px;
      // height: 100px;
      z-index: 1;
      scale: 0.8;
    }
  }
}

.invite{
  &-list{
    width: 596px;
    margin: 0 auto;
    margin-top: 20px;
    height: calc(100% - 100px);
    .app-List{
      width: 90%;
      height: calc(100% - 100px);
      left: 30px;
      position: absolute;
    }
    &-item{
      width: 100%;
      height: 120px;
      margin-bottom: 10px;

      border-radius:14px;
      color: var(--theme-text-color-darken);
      font-size: 21px;
      &:nth-child(2n + 1){
        background-color: var(--theme-bg-color);
      }
      &-body{
        width: 100%;
        height: 100%;
        position: absolute;

        // display: flex;
        // padding-top: 25px;
        // -----------------
        &-id{
          display: inline-block;
          position: absolute;
          left: 40px;
          top: 26px;
          color:var(--theme-text-color-darken);

          &-name{
            color:var(--theme-text-color-lighten);
          }
          &-copy{
            position: absolute;
            width: 23px;
            margin-left:25px;
          }
        }
        // time
        &-time{
          position: absolute;
          width: 300px;
          left: 20px;
          top: 66px;
          color:var(--theme-text-color-lighten);
          text-align: left;
        }
        &-timeAt{
          position: absolute;
          // width: 300px;
          left: 40px;
          top: 66px;
          color:var(--theme-text-color-darken);
          text-align: left;
        }
        //apos
        &-apos{
          position: absolute;
          color:var(--theme-text-color-darken);
          top: 26px;
          right: 100px;
          &-level{
            color:var(--theme-text-color-lighten);
          }
          &-num{
            padding-left: 10px;
            color:var(--theme-text-color-darken);
          }
        }
      }
    }
  }   
}

.history-poup {
  width: 100%;
  // height: 1200px;
  height: 100%;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: fixed !important;
  justify-content: center;
  top: 50vh;
  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    bottom: 5%;
  }

  .details-content {
    width: 640px;
    height: 1100px;
    background-color: var(--theme-main-bg-color);
    background-size: 596px 1000px;
    text-align: center;
    position: absolute;
    display: flex;
    // justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 20px;
    border-radius: 20px;
    border: 2px solid var(--theme-color-line);
    top: 6%;
    span{
      color: var(--theme-text-color);
    }

    .content-body-top {
        width: 100%;
        height: 90px;
        padding: 20px 20px 10px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .content-body-top-popover {
            width: 180px;
            height: 50px;
        }

        .content-body-top-popover-select {
            width: 180px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .top-popover-select-title {
            color: var(--theme-text-color-lighten);
            font-size: 22px;
            line-height: 48px;
            padding-left: 18px;
            padding-top: 2px;
            width: 101px;
            height: 48px;
        }
        .top-popover-select-icon {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 15px;
            margin-right: 10px;
            svg {
                width: 20px;
                height: 20px;
                color: var(--theme-text-color-lighten);
                position: absolute;

            }
        }
    }

    .statistics {
        width: 100%;
        height: 180px;
        font-size: 20px;
        background-color: var(--theme-bg-color);

        .content1{
          position: relative;
          width: 90%;
          height: 44px;
          background-color: var(--theme-main-bg-color);
          left: 5%;
          border-radius:10px;
          top: 12px;
        }
        .content2{
          position: relative;
          width: 90%;
          height: 44px;
          background-color: var(--theme-main-bg-color);
          left: 5%;
          border-radius:10px;
          top: 22px;
        }
        .content3{
          position: relative;
          width: 90%;
          height: 44px;
          background-color: var(--theme-main-bg-color);
          left: 5%;
          border-radius:10px;
          top: 32px;
        }
        .level1-amount{
          width: 280px;
          position: relative;
          // width: 270px;
          left: 46px;
          top: -110px;
          color:var(--theme-text-color-lighten);
          text-align: left;
          white-space: nowrap;
          .data{
            color:var(--theme-text-color-darken);
          }
        }
        .level1-recharge-share{
          width: 280px;
          position: relative;
          // width: 270px;
          left: 340px;
          top: -132px;
          color:var(--theme-text-color-lighten);
          text-align: left;
          white-space: nowrap;
          .data{
            color:var(--theme-text-color-darken);
          }
        }
        .level2-amount{
          width: 280px;
          position: relative;
          // width: 270px;
          left: 46px;
          top: -100px;
          color:var(--theme-text-color-lighten);
          text-align: left;
          white-space: nowrap;
          .data{
            color:var(--theme-text-color-darken);
          }
        }
        .level2-recharge-share{
          width: 280px;
          position: relative;
          // width: 270px;
          left: 340px;
          top: -122px;
          color:var(--theme-text-color-lighten);
          text-align: left;
          white-space: nowrap;
          .data{
            color:var(--theme-text-color-darken);
          }
        }
        .level3-amount{
          width: 280px;
          position: relative;
          // width: 270px;
          left: 46px;
          top: -92px;
          color:var(--theme-text-color-lighten);
          text-align: left;
          white-space: nowrap;
          .data{
            color:var(--theme-text-color-darken);
          }
        }
        .level3-recharge-share{
          width: 280px;
          position: relative;
          // width: 270px;
          left: 340px;
          top: -114px;
          color:var(--theme-text-color-lighten);
          text-align: left;
          white-space: nowrap;
          .data{
            color:var(--theme-text-color-darken);
          }
        }
    }

    .invite-query{
      // margin-top: 20px;
      // background-color: aliceblue;
      .invite-query-input{
        margin-left: 0px;
        width:356px;
        height:50px;
        background:var(--theme-main-bg-color);
        border:1px solid;
        border-color:var(--theme-color-line);
        border-radius:26px;
        .date{
          font-size: 24px;
          display: inline-block;
          color:var(--theme-text-color-lighten);
          width: 100%;
          height: 100%;
          text-align: center;
          padding-top: 12px;
          // padding-left: 20px;
        }
      }
    }

    .selectTime{
      position: absolute;
      margin-top: 420px; 
      left: -60px;
      // height: 100px;
      z-index: 1;
      scale: 0.8;
    }
  }
}

.lower{
  &-list{
    width: 596px;
    margin: 0 auto;
    margin-top: 10px;
    height: calc(100% - 100px);
    .app-List{
      width: 90%;
      height: calc(100% - 320px);
      left: 30px;
      position: absolute;
    }
    &-item{
      width: 100%;
      height: 140px;
      margin-bottom: 10px;

      border-radius:12px;
      color: var(--theme-text-color-darken);
      font-size: 20px;
      &:nth-child(2n+1){
        background-color: var(--theme-bg-color);
      }
      &-body{
        width: 100%;
        height: 100%;
        position: absolute;

        // display: flex;
        // padding-top: 25px;
        // -----------------
        &-id{
          display: inline-block;
          position: absolute;
          left: 20px;
          top: 20px;
          color:var(--theme-text-color-darken);

          &-name{
            color:var(--theme-text-color-lighten);
          }
          &-copy{
            position: absolute;
            width: 23px;
            margin-left:16px;
          }
        }
        // time
        &-time{
          position: absolute;
          width: 280px;
          left: 20px;
          top: 100px;
          color:var(--theme-text-color-lighten);
          text-align: left;
        }
        &-timeAt{
          position: absolute;
          // width: 300px;
          left: 360px;
          top: 100px;
          color:var(--theme-text-color-darken);
          text-align: left;
        }
        //apos
        &-apos{
          position: absolute;
          color:var(--theme-text-color-darken);
          top: 20px;
          left: 360px;
          &-level{
            color:var(--theme-text-color-lighten);
          }
          &-num{
            padding-left: 10px;
            color:var(--theme-text-color-darken);
          }
        }

        &-amount{
          display: inline-block;
          position: absolute;
          left: 20px;
          top: 60px;
          color:var(--theme-text-color-darken);

          &-desc{
            color:var(--theme-text-color-lighten);
          }
        }

        &-recharge-rate{
          display: inline-block;
          position: absolute;
          left: 20px;
          top: 100px;
          color:var(--theme-text-color-darken);

          &-desc{
            color:var(--theme-text-color-lighten);
          }
        }

        &-recharge-share{
          display: inline-block;
          position: absolute;
          left: 360px;
          top: 60px;
          color:var(--theme-text-color-darken);

          &-desc{
            color:var(--theme-text-color-lighten);
          }
        }
      }
    }
  }   
}

.detail-personal{
  width: 596px;
  height: 312px;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: fixed !important;
  justify-content: center;
  top: 50vh;
  .content {
    width: 596px;
    height: 312px;
    background-color: var(--theme-main-bg-color);
    background-size: 596px 312px;
    text-align: center;
    position: absolute;
    display: flex;
    // justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 40px;
    border-radius: 16px;
    border: 2px solid var(--theme-color-line);
    // top: 8%;
    span{
      color: var(--theme-text-color);
    }
    .detail-condition{
      // margin-left: 0px;
      margin-top: 30px;
      // padding-top: 10px;
      width:540px;
      height: 52px;
      background:var(--theme-bg-color);
      // border-radius:6px;
      text-align: center;
      position: relative;
      display: flex;
      // grid-template-columns: repeat(2, 1fr);
      justify-content: left;
      /* 水平居中 */
      align-items: center;
      flex-direction: row;
      .text{
        margin-left: 26px;
        // width: 370px;
        color:white;
        font-size: 20px;
        white-space: nowrap; /* 防止文本换行 */
        &.active{
          color:#04be02;
        }
      }


      &:nth-child(2n){
        background:var(--theme-main-bg-color);

        // margin-top: 0px;
        padding-bottom: 10px;
        margin-bottom: 6px;
        // .text1{
        // color:white;
        // }
        // .text2{
        //   color:white;
        // }
      }

    }
  }
}

.onne_jl{
  width: 100%;
  height: 450px;
  position: absolute;
  top: 25%;

  // .onne_jl_img{
    // position: absolute;
    // transform: translate(-50%,-50%);
    // left:50%;
    // top:50%;
    // width: 300px;
  // }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>
