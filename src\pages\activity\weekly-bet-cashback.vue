<script lang="ts" setup name="weeklyBet">
import { RunningRecordItem } from "~/core/http/api"

const router = useRouter()

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const apostasArr = [{ name: "slots", "dataKey": "slots_running", icon:"slots"},
{ name: "Pescaria", "dataKey": "pes_running", icon:"fishing" },
{ name: "Casino Ao Vivo", "dataKey": "cas_running", icon:"live" },
{ name: "Bing<PERSON>", "dataKey": "bingo_running", icon:"bingo" },
{ name: "Jogo de vido", "dataKey": "jogo_running", icon:"game" },
{ name: "Esportes", "dataKey": "esport_running", icon:"sports" }]
enum TabActive {
  thisweek = 'thisweek',
  lastweek = 'lastweek',
}
const tabData = ref([
  {
    label: 'ESSA SEMANA',
    value: TabActive.thisweek
  },
  {
    label: 'SEMANA PASSADA',
    value: TabActive.lastweek
  }
])
const tabActive = ref(TabActive.thisweek)

const themeVars = reactive({
      tabsCardHeight: 'var(--app-px-67)',
      paddingMd:'var(--app-px-48)',
      buttonNormalPadding: 0
      // sliderBarHeight: '4px',
      // sliderButtonWidth: '20px',
      // sliderButtonHeight: '20px',
      // sliderActiveBackground: '#07c160',
      // buttonPrimaryBackground: '#07c160',
      // buttonPrimaryBorderColor: '#07c160',
    });



const { run: runGetWeekRunningReturn, data: weekRunningReturnItemDatas } = useRequest(() => ApiGetWeekRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    

  }
})
runGetWeekRunningReturn();

const lastLv = ref(0)
const curLv = ref(0)

const getViewLv = () => {

  if (weekRunningReturnData.value && weekRunningReturnItemDatas.value) {
    const getLv = (value: number) => {
      for (let index = 0; index < weekRunningReturnItemDatas.value.length; index++) {
        if (value > weekRunningReturnItemDatas.value[index].running) {
          if (weekRunningReturnItemDatas.value.length == index + 1) {
            return index + 1;
            break;
          } else if (weekRunningReturnItemDatas.value[index + 1].running < value) {
            continue;
          } else {
            return index + 1;
          }
        }
      }

    }
    lastLv.value = getLv(weekRunningReturnData.value.LastWeekRunning) || 0;
    curLv.value = getLv(weekRunningReturnData.value.ThisWeekRunning) || 0;
  }
}
const { run: runQueryWeekRunningReturn, data: weekRunningReturnData } = useRequest(() => ApiGetQueryWeekRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)

    getViewLv();
  }
})
runQueryWeekRunningReturn();

const { run: runAchieveWeekRunningReturn, data: achieveWeekRunningReturnData } = useRequest(() => ApiAchieveWeekRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runQueryWeekRunningReturn();
    appStore.runGetUserBalance();
  }
})

const WeekRunningRecordData = ref();
const { run: runGetLastWeekRunningRecord, data: lastWeekRunningRecordData } = useRequest(() => ApiGetLastWeekRunningRecord(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    
    WeekRunningRecordData.value = lastWeekRunningRecordData?.value || [];
  }
})

const { run: runGeThisWeekRunningRecord, data: thisWeekRunningReturnData } = useRequest(() => ApiGetThisWeekRunningRecord(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    WeekRunningRecordData.value = thisWeekRunningReturnData?.value || [];
  }
})
runGeThisWeekRunningRecord();

const isRunAni = ref(false)
const getCashBack = ()=>{
  if(isRunAni.value) return;
  if (weekRunningReturnData.value?.WeekRunningReturn > 0) {
    runAchieveWeekRunningReturn();
  } else {
    isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  }
}

const thisWeekStr =ref(getWeekDate());
const lastWeekStr =ref(getWeekDate(true))

const onClickTab = (node:any) => {
      console.log(" title = ",node)
      if(node.name == "thisweek") {
        runGeThisWeekRunningRecord();
      } else {
        runGetLastWeekRunningRecord();
      }
};
</script>

<template>
  <van-config-provider :theme-vars="themeVars">
      <div class="root-page">
      <AppIndexHeader />
      <AppPageTitle left-arrow title="CASHBACK EM APOSTAS" title-weight="700" />
      <section class="content">
        <div class="topInfo">
          <div class="balance">{{ "R$" + (weekRunningReturnData?.WeekRunningReturn || 0) }}  </div>
          <van-button class="btn_recv" type="primary" color="#f43f5e"  text="Obter agora" @click="getCashBack"></van-button>
          <div class="period">
            <div class="title">
              Periodo
            </div>
            <br>
            {{ thisWeekStr }}  
            <br>
            <div class="title">
              Tempo de reivindicação
            </div>
            <br >
            <div class="lastWeekStr" :class="{ 'scaleAni': isRunAni }">{{ lastWeekStr }}  </div>
            <br>
          </div>
          <!-- <div class="balance">R$0</div> -->
        </div>
        <div class="middleInfo">
          <div class="bg-inner"></div>
          <div class="details">
            <span style="color: #3b82f6">Total de apostas na semana passada</span>
            <div class="balance-box">
              <div class="icon-left" ></div>
              <div class="center">{{ weekRunningReturnData?.LastWeekRunning || 0 }} </div>
              <div class="icon-right" >
                {{ lastLv }}
              </div>
            </div>
            <span style="color: #2ec6a0">Total de apostas esta semana</span>
            <div class="balance-box">
              <div class="icon-left" ></div>
              <div class="center" >{{ weekRunningReturnData?.ThisWeekRunning || 0 }}</div>
              <div class="icon-right" >
                {{ curLv }}
              </div>
            </div>
            <div></div>
          </div>
        </div>
        <span class="main-title">Detalhes das apostas</span>
        <div class="bet-details">
          <!-- <AppTab :list-data="tabData" v-model="tabActive" :height="84" active-color="#0ED1F4" /> -->
          <van-tabs v-model:active="tabActive" type="card" :ellipsis="false" color="var(--app-toggle-selbg-color)" background="var(--app-toggle-norbg-color)" title-inactive-color="#FFFFFF" @click-tab="onClickTab">
              <van-tab :title="tabData[0].label" :name="TabActive.thisweek"></van-tab>
              <van-tab :title="tabData[1].label" :name="TabActive.lastweek"></van-tab>
          </van-tabs>
          <div class="info_container">
            <div class="item" v-for="(data, index) in apostasArr" :key="index">
              <div class="left">
                <van-image
                  fit="contain"
                  width="var(--app-px-50)"
                  :src="`/img/activity/weeklybetcashback/${data.icon}.webp`"
                />
                <!-- <AppImage src="/img/activity/weeklybetcashback/billiard.webp"></AppImage> -->
              </div>
              <div class="right">
                {{ data.name }}
                <div>
                  {{ WeekRunningRecordData && WeekRunningRecordData[0]?WeekRunningRecordData[0][data.dataKey] : "0.00"}}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rule-table">
          <span class="main-title" style="padding-left: 0">CASHBACK E REGRAS</span>
          <div class="item title">
            <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp"/>
            <div class="table-label">  <span style="margin-left: 10px;">APOSTA </span> </div>
            <div class="table-label"> CASHBACK </div>
          </div>
          <div class="item" v-for="(data, index) in weekRunningReturnItemDatas" :key="index">
            <div class="icon-right"><div>{{index + 1 }}</div></div>
            <div class="table-label"> <span>{{ data.running }}</span> </div>
            <div class="table-label"> {{ (data.return_rate / 10) + "%" }}</div>
          </div>
        </div>
        <AppRule title="TERMOS E REGRAS" 
          content="1. O tempo de cálculo do bônus semanal é de segunda-feira às 00:00 até domingo às 23:59
2. Horário de aplicação do bônus: segunda-feira às 06h00 até domingo às 23h59 da próxima semana. Se não for resgatado, ele expirará.
3.O número real de apostas multiplicado pela percentagem de reembolso é o reembolso/cashback daquela semana.
4. Se você não fizer uma aposta durante o evento de desconto, você não receberá o desconto.
5. Limite máximo de recompensa de cashback por pessoa por semana: R$ 10.000
6. O valor do cashback pode ser sacado diretamente ou usado para continuar jogando"></AppRule>
      </section>
    </div>
  </van-config-provider>

</template>

<style lang="scss" scoped>
@import '/src/theme/mixin.scss';
.root-page {
  background: var(--theme-main-bg-color);
  padding-top: var(--app-navbar-height);
  padding-bottom: var(--app-footer-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  // font-family: Arial;
  color:#192756;
  font-size:24px;

}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  font-family: Arial;
}

.topInfo {
    @include webp("/img/activity/weeklybetcashback/bg_top_info");
    background-size: 100% 100%;
    width: 719px;
    height: 406px;
    transform: translateX(7px);
    position: relative;

    .balance {
      position: absolute;
      width: 380px;
      height: auto;
      // height: 127px;
      top: 85px;
      left: 20px; /* 从左侧开始 */
      text-align: center;
      // font-family:Montserrat;
      font-weight:700;
      color:#3b82f6;
      font-size:40px;
    }

    .btn_recv {
      width: 212px;
      height: 55px;
      line-height:normal;
      font-family:Montserrat;
      font-weight:700;
      color:#ffffff;
      font-size:23px;
      margin-top: 140px;
      margin-left: 108px;
    }

    .period{
      margin-left: 40px;
      margin-top: 4px;
      color:#192756;
      font-size:20px;
      .title{
        margin-top: 16px;
        margin-bottom: 6px;
        // margin: auto;
        display: inline-block;
        padding: 10px 15px;
        background:#9567b5;
        border-radius:10px;
        color:#fff;
      }
      
      .lastWeekStr{
        display: inline-block;
        &.scaleAni {
          // animation: spin 1s linear infinite;
          // transform: scale(1.1); /* 放大1.2倍 */
          // transition:transform 0.1s linear;
          animation:sacleAniFrames 0.8s ease;
        }
      }
      
      
    }
    
  }
  @keyframes sacleAniFrames {
      25% {
        transform: scale(1.08);
      }

      50% {
        transform: scale(1);
      }

      75% {
        transform: scale(1.08);
      }

      100% {
        transform: scale(1);
      }
    }
.middleInfo{
  position: relative;
  margin-top: 17px;
  margin-bottom: 33px;
  width:678px;
  height:224px;
  background: white;
  border-radius: 15px;

  .bg-inner{
    @include webp('/img/activity/weeklybetcashback/bg_middle_info');
    background-size: 100%;
    width: 365px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
  }
  .details{
    position: relative;
    padding: 17px 0 0 45px;
    font-size:24px;
    z-index: 1;
    .balance-box{
      width:310px;
      height:50px;
      margin: 12px 0;
      // opacity:20%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      background:#829aab33;
      border-radius:10px;

      .center{
        flex: 1;
        text-align: right;
        font-weight:700;
        color:#182655;
        font-size:34px;
        line-height: 100%;
      }
      // img {
      //   width: 45px;
      //   margin: 0px 10px;
      // }

      

      .icon-left{
        @include webp('/icons/personal_apostas');
        box-sizing: border-box;
        background-size: 100%;
        margin: 0px 15px;

        width: 35px;
        height: 35px;
      }
    }
  }
}

.icon-right{
  @include webp('/img/activity/weeklybetcashback/icon_level');
  box-sizing: border-box;
  background-size: 100%;
  margin: 0px 10px;
  width: 45px;
  height: 40px;
  text-align: center;
  padding-top: 8px;
  font-weight:700;
  font-style:italic;
  color:#fffcb1;
  font-size:20px;
  div{
    transform: translateX(-2px);
  }
}

.below_itemImg{
  width:41px;
  height:41px;
  margin-left: 11.5px;
  // margin-top: 15px;
}


.main-title{
  padding-left: 47px;
  width: 100%;
  font-weight:700;
  color:var(--app-ratio-title-color);
  font-size:24px;
}

.bet-details{
  margin-top: 16px;
  padding: 26px 0 0 0;
  background: var(--app-box-bg-color);
  border-radius:10px;
  width:702px;
  height:315px; 

  .info_container{
    display: grid;
    grid-template-columns: 224px 224px 224px; /* 3列 */
    grid-template-rows: 94px 94px; /* 2行 */
    gap: 5px; /* 网格间隙 */
    padding: 15px 9px;
  }

  .item {
    background-color: var(--theme-main-bg-color);
    border-radius:10px;
    position: relative;

    .left {
      // background-color: #f43f5e;
      position: absolute;
      left: 0;
      width: 70px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .right{
      // background-color: #9567b5;
      position: absolute;
      width: 73%;
      height: 100%;
      right: 0;
      text-align: center;
      display: flex;
      flex-direction: column;
      // justify-content: center;
      color: var(--app-title-color);
      align-items: center;
      padding: 10px 0;
      // line-height: 100%;
      div{
        // position: relative;
        margin-top: 8px;
        width:149px;
        height:39px;
        line-height: 39px;
        background:#829aab36;
        border-radius:10px;
        font-weight:700;
        color:#3b82f6;
        font-size:28px;
      }
    }


    // border: 1px solid blue;
  }



}

.rule-table{
  width: 678px;
  background:var(--app-box-bg-color);
  border-radius:15px;
  padding: 24px 23px 30px 23px;
  margin: 42px 0;
  // display: flex;
  // flex-direction: column;


  .item {
    // background:#eff6ff;
    border-radius:15px;
    width: 100%;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;

    line-height: 100%;
    color: var(--app-title-color);
    font-size:24px;
    
    div:nth-child(2){
      flex: 1;
      margin-left: 100px;
    }

    div:nth-child(3){
      width: 260px;
    }
    
  }

  .item:nth-child(2n){
    background:var(--theme-main-bg-color)
  }
  

  .title {
    background:#f8e5f6 !important;
    color:#f43f5e;
    margin-top: 12px;
    font-weight:700;
  }
  
}

.table-label {
  padding-top: 4px;
}


</style>

<route lang="yaml">
  meta:
    auth: true
</route>
