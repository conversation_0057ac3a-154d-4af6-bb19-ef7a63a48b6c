<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed,provide,inject } from 'vue'
import { showToast } from 'vant'
import { useRequest } from 'vue-request'
import Poupanca from './Poupanca.vue'
import { eventBus } from './eventBus'

const appStore = useAppStore()
const visible = ref(false)
const amount = ref('')
let numAmount = 0
const loading = ref(false)
const animatedBalance = ref('0,00')
const currentTime = ref('')
let timeTimer: ReturnType<typeof setInterval> | null = null
const { userInfo } = storeToRefs(appStore)

// 添加倒计时状态控制
const isCountdownRunning = ref(false)

const depositSuccessCallback = () => {
  // 通过事件总线触发
  eventBus.emit('depositSuccess')
}

// 计算是否禁用
const isDisabled = computed(() => {
  // 直接使用 currentBalance 的值
  return currentBalance.value < 20
})

// 添加输入框验证状态和交互标记
const showAmountError = ref(false)
const hasInteracted = ref(false)

// 添加新的状态和函数
const isRefreshing = ref(false)

// 添加响应式的当前余额变量
const currentBalance = ref(0)

const animateBalance = (isFirstShow = false) => {
  const rawAmount = String(userInfo.value?.formatAmount || '0,00')
  // 移除所有非数字字符，除了逗号
  const cleanAmount = rawAmount.replace(/[^\d,]/g, '')
  
  // 处理可能的NaN情况
  const targetBalance = parseFloat(cleanAmount.replace(',', '.'))
  if (isNaN(targetBalance)) {
    return
  }

  const formatBalance = (balance: number) => {
    // 将数字转换为字符串，保留两位小数
    const numStr = balance.toFixed(2)
    
    // 分隔整数和小数部分
    const [integerPart, decimalPart] = numStr.split('.')
    
    // 格式化整数部分（添加千位分隔符）
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
    
    // 组合整数和小数部分
    return `${formattedInteger},${decimalPart}`
  }

  const steps = 20
  const increment = targetBalance / steps

  const updateBalance = () => {
    currentBalance.value = Math.min(currentBalance.value + increment, targetBalance)
    
    animatedBalance.value = formatBalance(currentBalance.value)

    if (currentBalance.value < targetBalance) {
      requestAnimationFrame(updateBalance)
    } else {
      // 确保最终值精确
      currentBalance.value = targetBalance
      animatedBalance.value = formatBalance(targetBalance)
    }
  }

  // 只有在第一次显示时才从0开始
  if (isFirstShow) {
    currentBalance.value = 0
  }
  
  updateBalance()
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${month}/${day} ${hours}:${minutes}:${seconds}`
}

// 开始时间更新
const startTimeUpdate = () => {
  if(isCountdownRunning.value){
    console.log('startTimeUpdate')
    updateCurrentTime() // 立即更新一次
    timeTimer = setInterval(updateCurrentTime, 1000)
  }
}

const show = () => {
  visible.value = true
  // 触发刷新动画
  handleRefreshClick()
  // 重置表单和状态
  amount.value = ''
  loading.value = false
  showAmountError.value = false
  hasInteracted.value = false
  // 开始余额动画
  animateBalance(true)
  // 开始时间更新
  isCountdownRunning.value = true
  startTimeUpdate()
}

const hide = () => {
  visible.value = false
  // 清除时间定时器
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }

  isCountdownRunning.value = false
}

// 获取依赖注入
const depositoModal = inject('depositoModal')


// 组件卸载时清除定时器
onUnmounted(() => {
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }
})

const validateForm = () => {
  // 标记输入框已交互
  hasInteracted.value = true

  // 验证金额
  if (!amount.value) {
    showAmountError.value = true
    showToast('Por favor, insira o valor')
    return false
  }

  const numAmounts = parseFloat(amount.value.replace(',', '.'))
//   const rawMaxAmount = String(userInfo.value?.formatAmount || '0,00')
//   const maxAmount = parseFloat(rawMaxAmount.replace(',', '.'))

  if (isNaN(numAmounts) || numAmounts <= 0 || numAmounts > Number(currentBalance.value)) {
    showAmountError.value = true
    //showToast(`O valor deve ser entre 0 e ${currentBalance}`)
    return false
  }

  // 验证过，清除错误状态
  showAmountError.value = false
  return true
}

const onSubmit = async () => {
  if (!validateForm()) return
  
  try {
    loading.value = true
    // TODO: 调用存款API
    //depositSuccessCallback()   
    runGetDeposit()
  } catch (error) {
    showToast('Falha no depósito')
  } finally {
    loading.value = false
  }
}


// 定义 poupancaRef
//const poupancaRef = ref<InstanceType<typeof Poupanca> | null>(null)

const { run: runGetDeposit } = useRequest(() => ApiGetDepositParam({amount:numAmount > userInfo.value.brl! ? userInfo.value.brl! : numAmount}), {
    manual: true,
    onError(data) {
      console.error('Deposit error:', data)
    },
    onSuccess(data) {  
      // 提供一个回调方法，用于触发 Poupanca.vue 中的 runApiGetPiggyBankInfo
      depositSuccessCallback()
      showToast('Depósito bem-sucedido')
      hide()
      appStore.runGetMemberInfo()
    }
})


// 处理Tudo按钮点击
const handleTudoClick = () => {
  // 只有当 currentBalance >= 20 时才能点击
  if (currentBalance.value >= 20) {
    // 使用 currentBalance 的值
    const balanceValue = currentBalance.value
    
    // 将最大可用金额转换为带逗号的字符串
    const formattedAmount = balanceValue.toFixed(2).replace('.', ',')
    
    // 更新输入框的值
    amount.value = formattedAmount
    
    // 更新 numAmount
    numAmount = balanceValue
    
    // 重置错误状态
    showAmountError.value = false
    hasInteracted.value = true
    
    console.log(`Tudo clicked. Current balance: ${balanceValue}, Formatted: ${formattedAmount}`)
  }
}

// 处理刷新图标点击
const handleRefreshClick = async () => {
  if (isRefreshing.value) return
  
  isRefreshing.value = true
  // 执行5圈旋转动画
  await new Promise(resolve => setTimeout(resolve, 1500)) // 动画持续1.5秒
  
  // 动画结束后更新余额
  animateBalance()
  isRefreshing.value = false
}

// Handle amount input
const handleAmountInput = (event: Event) => {
  const input = event.target as HTMLInputElement
  const rawValue = input.value
  
  // 移除非数字和逗号的字符
  const cleanedValue = rawValue.replace(/[^\d,]/g, '')
  
  // 更新响应式变量
  amount.value = cleanedValue
  hasInteracted.value = true
  
  // 解析数值
  numAmount = parseFloat(cleanedValue.replace(',', '.'))
  
  // 显示错误
  showAmountError.value = hasInteracted.value && (isNaN(numAmount) || numAmount < 20)
}

// 添加新的响应式变量用于实时更新
const currentDateTime = ref(new Date())

// 计算利息时间显示
const interestTimeDisplay = computed(() => {
  const nextTime = new Date(currentDateTime.value)
  nextTime.setHours(nextTime.getHours() + 1)
  return nextTime.toLocaleTimeString('pt-BR', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
})

// 添加定时器更新currentDateTime
const startInterestTimeUpdate = () => {
  setInterval(() => {
    currentDateTime.value = new Date()
  }, 1000)
}

// 组件挂载时初始化
onMounted(() => {
  updateCurrentTime()
  startTimeUpdate()
  startInterestTimeUpdate()
  animateBalance(true)
    // 确保 depositoModal 存在
    if (depositoModal) {
      depositoModal.setMethods({ show, hide })
    } else {
      console.warn('Deposito modal not provided')
    }
})

defineExpose({
  show,
  hide,
})
</script>

<template>
  <div>
    
    <van-dialog
      v-model:show="visible"
      :show-confirm-button="false"
      class="transfer-dialog"
      close-on-click-overlay
      @close="hide"
      width="100%"
    >
      <div class="dialog-content">
        <div class="dialog-header">
          <div class="title">Depósito</div>
          <div class="balance">
            <div class="balance-left">
              <span class="balance-label">Saldo da Conta</span>
              <div class="balance-amount-wrapper">
                <span class="balance-amount">{{ animatedBalance }}</span>
                <div 
                  class="refresh-icon" 
                  :class="{ 'refreshing': isRefreshing }"
                  @click="handleRefreshClick"
                >
                  <img src="/icons/refresh-green.webp" alt="refresh">
                </div>
              </div>
            </div>
            <div class="period-wrapper">
              <span class="period-text">Período de liquidação</span>
              <span class="period-hours">1 horas</span>
            </div>
          </div>
          <div class="withdraw-row">
            <div class="withdraw-label">Valor do depósito</div>
            <div class="current-time">
              <span class="time-label">Horário atual</span>
              <span class="time-value">{{ currentTime }}</span>
            </div>
          </div>
          <div class="withdraw-input-row">
            <div class="input-wrapper">
              <span class="prefix">R$</span>
              <van-field
                v-model="amount"
                type="text"
                placeholder="Mínimo 20, apenas números inteiros"
                :readonly="loading"
                :disabled="isDisabled"
                class="withdraw-input"
                @input="handleAmountInput"
              >
                <template #button>
                  <span 
                    class="max-btn" 
                    :class="{ 'disabled': isDisabled }" 
                    @click="handleTudoClick"
                  >
                    Tudo
                  </span>
                </template>
              </van-field>
            </div>
            <div class="amount-hint" v-if="hasInteracted && showAmountError">
              <img src="/icons/icon_tips.webp" alt="tips" width="14" height="14">
              <span>Valor do depósito campo não pode estar vazio</span>
            </div>
            <div class="warning-text">
              <span>Após esse depósito, os primeiros juros vão ocorrer as: </span>
              <span class="interest-time">{{ interestTimeDisplay }}</span>
            </div>
          </div>
        </div>
        
        <div class="dialog-footer">
          <van-button 
            block 
            type="primary" 
            @click="onSubmit"
            :loading="loading"
            :disabled="isDisabled"
            loading-text="Processando..."
          >
            Transferir de
          </van-button>
        </div>

      </div>
      <div v-if="visible" class="close-wrap">
          <div class="close-btn" @click="hide">
            <img src="/icons/dialog-close.png" alt="close">
          </div>
        </div>

    </van-dialog>

  </div>
</template>

<style lang="scss" scoped>
.van-dialog {
  position: relative;
  overflow: visible !important;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  
  // 增加内部内容的可滚动区域
  .van-dialog__content {
    max-height: 95vh;
    width: 100%;
    border-radius: 16px;
    overflow-y: auto;
    margin: auto;
  }
}

:deep(.van-popup), :deep(.van-dialog) {
  height: 100vh !important;  // 全屏高度
  width: 100vw !important;   // 全屏宽度
  max-height: none !important;  // 移除最大高度限制
  max-width: none !important;   // 移除最大宽度限制
  top: 0 !important;            // 顶部对齐
  left: 0 !important;           // 左侧对齐
  transform: none !important;   // 移除任何平移
  background: transparent;
  border-radius: 0;             // 移除圆角
  margin: 0;                    // 移除边距
  padding: 0;                   // 移除内边距
  overflow: auto;               // 允许滚动
  display: flex;
  align-items: center;
  justify-content: center;
}

.transfer-dialog {
  :deep(.van-dialog) {
    background: transparent;
    overflow: visible;
    width: 100% !important;
  }

  :deep(.van-dialog__content) {
    height: 100%;
    width: 100%;
  }
}

.dialog-content {
  width: 90%;
  max-width: 800px;
  margin: 0 auto;
  background: var(--theme-main-bg-color);
  border-radius: 16px;
  overflow: hidden;
  padding: 0 10px;
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dialog-header {
  position: relative;
  padding: 28px 22px 12px;
  text-align: center;
  background: var(--theme-main-bg-color);
  
  .title {
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 22px;
    color: var(--theme-text-color-darken);
  }

  .balance {
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 22px;
    
    .balance-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .period-wrapper {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }

    .period-text {
      color: var(--theme-text-color-lighten);
      font-size: 22px;
    }

    .period-hours {
      color: var(--theme-text-color-darken);
      font-size: 22px;
    }

    .balance-label {
      color: var(--theme-text-color-lighten);
      font-size: 22px;
    }
    
    .balance-amount-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .balance-amount {
      color: var(--theme-text-color-darken);
      font-weight: 500;
      font-size: 28px;
    }

    .refresh-icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
      transition: transform 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      transform-origin: center center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &.refreshing {
        animation: spin 0.4s linear infinite;
        transform-origin: center center;
      }
    }
  }

  .withdraw-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    margin-bottom: 12px;
    
    .withdraw-label {
      text-align: left;
      color: var(--theme-text-color-darken);
      font-size: 22px;
      line-height: 1.2;
    }

    .current-time {
      display: flex;
      align-items: center;
      gap: 4px;

      .time-label {
        color: var(--theme-text-color-lighten);
        font-size: 22px;
      }

      .time-value {
        color: var(--theme-text-color-lighten);
        font-size: 22px;
      }
    }
  }
}

.dialog-body {
  padding: 0 22px;
  background: var(--theme-main-bg-color);
  
  .form-item {
    margin-bottom: 0;
    
    .label-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .label {
      margin-bottom: 0;
      font-size: 16px;
      color: var(--theme-text-color-lighten);
    }

    .password-toggle {
      width: 32px;
      height: 32px;
      cursor: pointer;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}

.dialog-footer {
  padding: 2px 22px 22px;
  background: var(--theme-main-bg-color);
  height: 100px;
  :deep(.van-button--primary) {
    background: var(--theme-primary-color);
    border-color: var(--theme-primary-color);
    color: var(--theme-primary-font-color);
    height: 70px;
    font-size: 24px;
    &.van-button--disabled {
      opacity: 0.5;
      background: var(--theme-primary-color);
      border-color: var(--theme-primary-color);
    }
  }
}

.withdraw-input-row {
  margin-top: 0;
  padding: 0;
  margin-bottom: 8px;

  .input-wrapper {
    position: relative;
    width: 100%;
  }

  .prefix {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    color: var(--theme-text-color-lighten);
    font-size: 20px !important;
  }

  :deep(.van-cell) {
    background-color: transparent !important;
    border: 1px solid var(--theme-text-color-darken);
    border-radius: 8px;
    &::after {
      display: none;
    }
  }

  .withdraw-input {
    background-color: transparent !important;
    position: relative;

    :deep(.van-field__control) {
      background-color: transparent !important;
      padding-left: 20px;
      text-align: left;
      color: var(--theme-text-color-darken) !important;
      font-size: 20px !important;
      height: 20px;
      line-height: 20px;
      padding-right: 100px;  // 增加右边距，给数字更多空间
      width: 100%;

      &::placeholder {
        font-size: 20px !important;
        width: 100%;
        white-space: nowrap;
        overflow: visible;
      }
    }

    :deep(.van-cell) {
      background-color: transparent !important;
      padding: 10px;
      padding-left: 35px;
      padding-right: 5px;
      font-size: 20px !important;
      display: flex;
      align-items: center;
    }

    :deep(.van-field__value) {
      background-color: transparent !important;
      font-size: 20px !important;
      height: 20px;
      line-height: 20px;
      display: flex;
      align-items: center;
      width: 100%;
    }

    ::placeholder {
      font-size: 20px !important;
      width: 100%;
      white-space: nowrap;
      overflow: visible;
    }
  }

  .max-btn {
    color: var(--theme-primary-color);
    font-size: 20px !important;
    cursor: pointer;
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
    height: 20px;
    line-height: 20px;
    z-index: 3;
    padding: 0 10px;
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &:active {
      opacity: 0.8;
    }
  }

  .amount-hint {
    font-size: 18px;
    color: #FF6B6B;
    text-align: left;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      flex-shrink: 0;
      margin-top: -2px;
    }

    span {
      line-height: 1.2;
    }
  }

  .warning-text {
    color: var(--theme-text-color-darken);
    font-size: 18px;
    margin-top: 32px;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 4px;

    .interest-time {
      color: var(--theme-text-color-darken);
    }
  }
}

.refresh-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  transition: transform 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &.refreshing {
    animation: spin 0.8s linear infinite;
    transform-origin: center center;
  }
}

.close-wrap {
  position: fixed;
  left: 50%;
  top: calc(50% + 250px);
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  z-index: 2999;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-btn {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  img {
    width: 60px;
    height: 60px;
    object-fit: contain;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>