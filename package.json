{"private": true, "packageManager": "pnpm@8.6.0", "scripts": {"build:t": " rimraf dist && vite build --mode test", "build:p": " rimraf dist && vite build --mode production", "dev": "vite --port 3333 --host --open", "lint": "eslint .", "typecheck": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest", "up": "taze major -I", "postinstall": "npx simple-git-hooks", "genImg": "./genImg.sh ./public", "removeImg": "./removeImg.sh ./public"}, "dependencies": {"@lucky-canvas/vue": "^0.1.11", "@vueuse/core": "^10.2.0", "axios": "^1.4.0", "cbor-js": "^0.1.0", "cg-utils": "^0.0.47", "clipboard": "^2.0.11", "currency.js": "^2.0.4", "dayjs": "^1.11.8", "gsap": "^3.13.0", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "pinia": "^2.1.4", "precompiled-mqtt": "4.3.14-beta", "qrcode": "^1.5.4", "vant": "^4.6.0", "vue": "^3.3.4", "vue-i18n": "9", "vue-request": "^2.0.3", "vue-router": "^4.2.2", "vue3-lottie": "^3.3.0"}, "devDependencies": {"@antfu/eslint-config": "^0.39.5", "@iconify-json/carbon": "^1.1.18", "@types/node": "^20.3.1", "@unocss/eslint-config": "^0.53.1", "@unocss/preset-rem-to-px": "^0.53.1", "@unocss/reset": "^0.53.1", "@vitejs/plugin-vue": "^4.2.3", "@vue-macros/volar": "^0.10.2", "@vue/test-utils": "^2.3.2", "eslint": "^8.43.0", "jsdom": "^22.1.0", "lint-staged": "^13.2.2", "pnpm": "^8.6.3", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.63.6", "simple-git-hooks": "^2.8.1", "taze": "^0.10.2", "typescript": "^5.1.3", "unocss": "^0.53.1", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "unplugin-vue-macros": "^2.3.0", "vite": "^4.3.9", "vite-plugin-pages": "^0.31.0", "vite-plugin-pwa": "^1.0.1", "vite-plugin-vue-layouts": "^0.8.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vitest": "^0.31.4", "vue-tsc": "^1.8.0"}, "simple-git-hooks": {}, "lint-staged": {"*": "eslint --fix"}, "eslintConfig": {"extends": ["@antfu", "@unocss"]}}