<template>
  <div class="pwa-download-container">
    <!-- Android Chrome WebAPK下载按钮 -->
    <button 
      v-if="showAndroidInstall" 
      @click="handleInstallClick"
      class="download-btn android-btn"
      :disabled="installing"
    >
      <div class="btn-content">
        <img src="/ic_launcher1.png" alt="App Icon" class="btn-icon">
        <div class="btn-text">
          <div class="btn-title">{{ installing ? '安装中...' : '下载应用' }}</div>
          <div class="btn-subtitle">WebAPK版本</div>
        </div>
        <div class="btn-arrow">→</div>
      </div>
    </button>

    <!-- iOS Safari添加到主屏幕提示 -->
    <div v-if="showIOSInstall" class="ios-install-guide">
      <div class="guide-content">
        <img src="/ic_launcher1.png" alt="App Icon" class="guide-icon">
        <div class="guide-text">
          <h3>添加到主屏幕</h3>
          <p>点击分享按钮 <span class="share-icon">⬆️</span> 然后选择"添加到主屏幕"</p>
        </div>
      </div>
    </div>

    <!-- 通用下载按钮 -->
    <button 
      v-if="showGenericDownload" 
      @click="handleGenericDownload"
      class="download-btn generic-btn"
    >
      <div class="btn-content">
        <img src="/ic_launcher1.png" alt="App Icon" class="btn-icon">
        <div class="btn-text">
          <div class="btn-title">下载应用</div>
          <div class="btn-subtitle">获得更好体验</div>
        </div>
        <div class="btn-arrow">→</div>
      </div>
    </button>

    <!-- 安装状态提示 -->
    <div v-if="installStatus === 'installed'" class="install-status installed">
      <div class="status-content">
        <div class="status-icon">✓</div>
        <div class="status-text">应用已安装</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { deviceDetection, pwaInstaller, installAnalytics, type PWAInstallPromptEvent } from '../utils/pwa'

const installing = ref(false)
const installStatus = ref<'not_installed' | 'installable' | 'installed'>('not_installed')
let deferredPrompt: PWAInstallPromptEvent | null = null

// 计算显示状态
const showAndroidInstall = computed(() => {
  return deviceDetection.isAndroid() &&
         deviceDetection.isSupportedAndroidBrowser() &&
         installStatus.value === 'installable' &&
         !pwaInstaller.isInstalled()
})

const showIOSInstall = computed(() => {
  return deviceDetection.isIOS() && 
         deviceDetection.isSafari() && 
         !pwaInstaller.isInstalled()
})

const showGenericDownload = computed(() => {
  return !showAndroidInstall.value && 
         !showIOSInstall.value && 
         !pwaInstaller.isInstalled() &&
         deviceDetection.isMobile()
})

// 处理Android Chrome安装
const handleInstallClick = async () => {
  if (!deferredPrompt) {
    // 如果没有安装提示事件，尝试触发手动安装流程
    handleManualInstall()
    return
  }

  installing.value = true
  installAnalytics.trackInstallEvent('prompt_accepted')

  try {
    await deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('用户接受了安装')
      installAnalytics.trackInstallEvent('installed')
    } else {
      console.log('用户拒绝了安装')
      installAnalytics.trackInstallEvent('prompt_dismissed')
    }
    
    deferredPrompt = null
  } catch (error) {
    console.error('安装失败:', error)
  } finally {
    installing.value = false
  }
}

// 手动安装流程（当没有beforeinstallprompt事件时）
const handleManualInstall = () => {
  if (deviceDetection.isAndroid()) {
    let instruction = ''

    if (deviceDetection.isChrome()) {
      instruction = '请点击浏览器菜单（三个点）→ "安装应用" 来安装到桌面'
    } else if (deviceDetection.isSamsungInternet()) {
      instruction = '请点击菜单 → "添加页面到" → "主屏幕" 来安装应用'
    } else if (deviceDetection.isEdge()) {
      instruction = '请点击菜单（三个点）→ "应用" → "将此站点安装为应用" 来安装'
    } else if (deviceDetection.isFirefox()) {
      instruction = '请点击菜单 → "安装" 来将此网站添加到主屏幕'
    } else if (deviceDetection.isOpera()) {
      instruction = '请点击菜单 → "主屏幕" → "添加到主屏幕" 来安装应用'
    } else if (/MiuiBrowser|XiaoMi|MI\s/i.test(navigator.userAgent)) {
      instruction = '请点击底部菜单（三条横线）→ "工具箱" → "添加到桌面" 来安装应用'
    } else {
      instruction = '请在浏览器菜单中查找"安装应用"或"添加到主屏幕"选项'
    }

    alert(instruction)
  }
}

// 处理通用下载
const handleGenericDownload = () => {
  // 可以跳转到应用商店或显示安装指引
  if (deviceDetection.isAndroid()) {
    // 可以跳转到Google Play或显示APK下载
    window.open('https://play.google.com/store/apps', '_blank')
  } else if (deviceDetection.isIOS()) {
    // 可以跳转到App Store
    window.open('https://apps.apple.com/', '_blank')
  } else {
    // 桌面端可以显示二维码或其他下载方式
    alert('请使用手机访问以获得更好的体验')
  }
}

// 监听安装提示事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault()
  deferredPrompt = e as PWAInstallPromptEvent
  installStatus.value = 'installable'
  installAnalytics.trackInstallEvent('prompt_shown')
}

// 监听安装完成事件
const handleAppInstalled = () => {
  installStatus.value = 'installed'
  installAnalytics.trackInstallEvent('installed')
  deferredPrompt = null
}

onMounted(() => {
  // 检查初始安装状态
  if (pwaInstaller.isInstalled()) {
    installStatus.value = 'installed'
  }

  // 监听事件
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.addEventListener('appinstalled', handleAppInstalled)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.removeEventListener('appinstalled', handleAppInstalled)
})
</script>

<style scoped>
.pwa-download-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.download-btn {
  width: 100%;
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
  border: none;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
  margin-bottom: 16px;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
}

.download-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  color: white;
}

.btn-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 16px;
}

.btn-text {
  flex: 1;
  text-align: left;
}

.btn-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.btn-subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.btn-arrow {
  font-size: 24px;
  font-weight: bold;
}

.ios-install-guide {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.guide-content {
  display: flex;
  align-items: center;
}

.guide-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 16px;
}

.guide-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.guide-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.share-icon {
  display: inline-block;
  background: #007AFF;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.install-status {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 12px;
  padding: 16px;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #155724;
}

.status-icon {
  font-size: 24px;
  margin-right: 12px;
}

.status-text {
  font-size: 16px;
  font-weight: 600;
}

.generic-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.generic-btn:hover {
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* 移动端适配 */
@media (max-width: 480px) {
  .download-btn {
    padding: 14px;
  }
  
  .btn-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }
  
  .btn-title {
    font-size: 16px;
  }
  
  .btn-subtitle {
    font-size: 13px;
  }
}
</style>
