<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    src: string;
    alt?: string;
    isGame?: boolean;
    width?:number;
    overlayColor?: string;
  }>(),
  {
    isGame: false,
    isGoogleCloud: false,
    overlayColor: undefined,
  }
);

const isWebp = ref(props.src.includes('.webp'))
// const isPng = ref(props.src.includes('.png'))
const isGif = ref(props.src.includes('.gif'));
const isSvg = ref(props.src.includes('.svg'));
const isHttp = ref(props.src.includes('http://'))
const isHttps = ref(props.src.includes('https://'))
const suffixWebp = Array.from(document.documentElement.classList).includes('webp');

const src = computed(() => {
  let _src = props.src
  // console.log("isWebp.value = " + isWebp.value + "  --props.src =" + props.src)  || isPng.value
  if (!suffixWebp || isWebp.value || isGif.value || isSvg.value || isHttp.value || isHttps.value ) return _src
  // console.log("_src.value = " + _src)
  if (props.isGame) {
    _src = import.meta.env.VITE_GAMEIMG_URL + _src
  }

  if(_src.includes(".webp")){
    return _src;
  }
  return `${_src}.webp`;
});

const imgStyle = computed(() => {
  const style: Record<string, string> = {};
  if (props.width) {
    style.width = `var(--app-px-${props.width})`;
  }
  if (props.overlayColor) {
    style['--overlay-color'] = props.overlayColor;
    style['--mask-url'] = `url("${src.value}")`;
  }
  return style;
});

// Calculate CSS filters to transform white to target color
const colorToFilter = computed(() => {
  if (!props.overlayColor) return {};
  
  // Extract RGB values from CSS variable if necessary
  let colorValue = props.overlayColor;
  
  // Simple case: if it's a direct color like #RRGGBB
  if (colorValue.startsWith('#')) {
    const r = parseInt(colorValue.slice(1, 3), 16) / 255;
    const g = parseInt(colorValue.slice(3, 5), 16) / 255;
    const b = parseInt(colorValue.slice(5, 7), 16) / 255;
    return getFilterValuesForColor(r, g, b);
  }
  
  // If it's a CSS variable, we'll need to use a different approach
  // We'll use a preset filter for known theme colors
  
  if (colorValue.includes('--theme-primary-font-color')) {
    // Preset filter for green color (approximation for var(--theme-primary-font-color))
    return {
      filter: 'brightness(0) saturate(100%) invert(46%) sepia(97%) saturate(427%) hue-rotate(116deg) brightness(97%) contrast(93%)'
    };
  }
  
  // Default filter for unknown colors (converts to grayscale)
  return {
    filter: 'brightness(0) saturate(100%)'
  };
});

// Combine the imgStyle and filter style
const combinedStyle = computed(() => {
  if (props.overlayColor) {
    return { ...imgStyle.value, ...colorToFilter.value };
  }
  return imgStyle.value;
});

function getFilterValuesForColor(r: number, g: number, b: number) {
  // This is a simplified version that works for some colors
  // A complete implementation would be more complex
  return {
    filter: `brightness(0) saturate(100%) invert(${Math.round(r * 100)}%) sepia(${Math.round(g * 100)}%) saturate(${Math.round(b * 100)}%)`
  };
}
</script>

<template>
  <!-- Use combinedStyle that includes filters -->
  <img
    class="app-image"
    :src="src"
    :alt="props.alt"
    loading="lazy"
    :style="combinedStyle"
  />
</template>

<style lang="scss" scoped>
  .app-image {
    display: inline-block;
    vertical-align: middle;
    max-width: 100%;
    height: auto;
    position: relative; // Needed for absolute positioning of ::before

    &.has-overlay::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      // Apply the background color from the CSS variable
      background-color: var(--overlay-color);
      // Use the CSS variable for the mask image URL
      mask-image: var(--mask-url); 
      -webkit-mask-image: var(--mask-url);
      mask-size: contain; 
      -webkit-mask-size: contain;
      mask-repeat: no-repeat;
      -webkit-mask-repeat: no-repeat;
      mask-position: center;
      -webkit-mask-position: center;
      pointer-events: none; // Ensure the overlay doesn't interfere with interactions
    }
  }

  // Remove .app-image-mask style
  /*
  .app-image-mask {
    background-color: currentColor;
  }
  */
</style>

