<script setup lang="ts" name="app-login-register">
console.log("打开登录页")
const appStore = useAppStore();
const {
  loginDialogVisible,
  loginDialogType
} = storeToRefs(appStore);
enum TabActive {
  login = 'login',
  register = 'register'
}
const tabData = ref([
  {
    label: 'Entrar',
    value: TabActive.login
  },
  {
    label: 'Cadastre-se',
    value: TabActive.register
  }
])

</script>

<template>
  <AppHeader leftText="登录"></AppHeader>
  <van-popup class="app-login-register" closeable close-icon="cross" v-model:show="loginDialogVisible" position="bottom"
    teleport="body" :style="{ height: '100%', width: '100%' }">
    <div class="content">
      <div class="bg-img" :class="{ 'bg-img-active': loginDialogType === TabActive.register }"></div>
      <div class="title">
        <AppLoginTab :list-data="tabData" v-model="loginDialogType" :height="116" active-color="#F43F5E" />
      </div>
      <div class="login-img">
        <AppImage src="/icons/logo_login.png" alt="" />
      </div>
      <div v-show="loginDialogType === TabActive.login">
        <AppLogin />
      </div>
      <div v-show="loginDialogType === TabActive.register">
        <AppRegister />
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.content {
  width: 100%;
  height: 100%;
  position: relative;

  .bg-img {
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    // @include webp('/img/bg_sign_in.png');
    background-color: #eff6ff;
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &-active {
      transform: scaleX(-1);
    }
  }

  .title {
    // padding: 20px 0;
  }

  .login-img {
    margin-top: 30px;
    text-align: center;
    // padding: 0 0 50px 0;

    img {
      scale: 0.6;
      // width: 186px;
      // height: 88px;
      vertical-align: middle;
    }
  }
}</style>
