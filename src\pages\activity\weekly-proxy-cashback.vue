<script lang="ts" setup name="weeklyProxy">



const router = useRouter()

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)

enum TabActive {
  thisweek = 'thisweek',
  lastweek = 'lastweek'
}

const tabData = ref([])
const tabActive = ref(TabActive.thisweek)

const themeVars = reactive({
      tabsCardHeight: 'var(--app-px-67)',
      paddingMd:'var(--app-px-48)',
      buttonNormalPadding: 0,
      cellTextColor:'#ffffff',
      cellBackground:"#f43f5eff",
      cellActiveColor:"#f43f5eff",
      cellRightIconColor:'#ffffff',
      // sliderBarHeight: '4px',
      // sliderButtonWidth: '20px',
      // sliderButtonHeight: '20px',
      // sliderActiveBackground: '#07c160',
      // buttonPrimaryBackground: '#07c160',
      // buttonPrimaryBorderColor: '#07c160',
    });

  const thisWeekStr =ref(getWeekDate());
  const lastWeekStr =ref(getWeekDate(true));

  const {run: runGetWeekProxyCommi,data:proxyCommiReturnDatas } = useRequest(() => ApiGetWeekProxyCommi(), {
  manual: true,
  onSuccess(res:any) {
    console.log(res)
   
  }
  })
  runGetWeekProxyCommi();
  const lastLv= ref(0)
  const curLv= ref(0)
 
  const getViewLv  = ()=>{
    
    if(queryProxyWeekDeposit.value && proxyCommiReturnDatas.value) {
      const getLv = (value:number) =>{
        for(let index = 0; index < proxyCommiReturnDatas.value.length; index++) {
        if(value > proxyCommiReturnDatas.value[index].deposit) {
          if(proxyCommiReturnDatas.value.length == index + 1) {
            return index + 1;
            break;
          } else if(proxyCommiReturnDatas.value[index + 1].deposit < value){
            continue;
          } else {
            return index + 1;
          }
        }
      }
     
      }
      lastLv.value = getLv(queryProxyWeekDeposit.value.LastDeposit) || 0;
      curLv.value = getLv(queryProxyWeekDeposit.value.WeekDeposit) || 0;
    }
  }
  const { run: runGetQueryProxyWeekDeposit, data: queryProxyWeekDeposit } = useRequest(() => ApiGetQueryProxyWeekDeposit(), {
    manual: true,
    onSuccess(res: any) {
      console.log(res)
      getViewLv();
    }
  })
  runGetQueryProxyWeekDeposit();

  const {run: runAchieveProxyWeekDeposit,data:AchieveProxyWeekDepositData } = useRequest(() => ApiAchieveProxyWeekDeposit(), {
  manual: true,
  onSuccess(res:any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runGetQueryProxyWeekDeposit();
    appStore.runGetUserBalance();
  }
  })

  const isRunAni = ref(false)
  const getCashBack = ()=>{
    if(isRunAni.value) return;
    if(queryProxyWeekDeposit.value?.LastWeekDepositReturn > 0){
      runAchieveProxyWeekDeposit();
      } else {
        isRunAni.value = true
        setTimeout(() => {
          isRunAni.value = false
        }, 800);
      }
      console.log("dfdsfasf")
  }


</script>

<template>
  <van-config-provider :theme-vars="themeVars">
      <div class="root-page">
      <AppIndexHeader />
      <AppPageTitle left-arrow title="Retorne ao agente toda semana" title-weight="700" />
      <section class="content">
        <div class="topInfo">
          <div class="caishen">
            <van-image fit="contain" width="var(--app-px-355)" src="/img/activity/weeklyproxycashback/caishen.webp"/>
          </div>
          <div class="bg_red">
            <van-image fit="contain" width="var(--app-px-355)" src="/img/activity/weeklyproxycashback/bg_red.webp"/>
          </div>
          <div class="data">
            <div class="period">
              <div class="title">Periodo</div><label > {{ thisWeekStr }}</label>
            </div>
            <div class="text">
              Ganhe altas comissões todas as <br>
              semanas sem limite máximo de bônus
            </div>
            <div class="panel-bonus">
              <div class="bonus">Bonus</div>
              <div>{{"R$" + (queryProxyWeekDeposit?.LastWeekDepositReturn || 0 )}}</div>
              <div :style="{height: $toPx(8),paddingBottom:$toPx(10)}"></div>
              <AppButton @click="getCashBack"  width="258" height="60" radius="10" fontSize="24" red whiteText>Obter agora</AppButton>
            </div>
            <div class="period" :class="{ 'scaleAni': isRunAni }">
              <div class="title">Tempo de reivindicação</div><label  > {{ lastWeekStr }}  </label>
            </div>

            <!-- <div class="balance">R$0</div>
            <van-button class="btn_recv" type="primary" color="#f43f5e"  text="Obter agora" @click="getCashBack"></van-button> -->
            
          </div>
          <!-- <div class="balance">R$0</div> -->
        </div>
        <div style="width: var(--app-px-678); margin: var(--app-px-24) 0;">
         <van-cell title="  Lista de detalhes de depósito" is-link to="list-of-proxy-details" style="border-radius:var(--app-px-15);"/>
        </div>
        <div class="middleInfo">
          <div class="bg-inner"></div>
          <div class="details">
            <span style="color: #3b82f6">Depósitos totais na semana passada</span>
            <div class="balance-box">
              <div class="icon-left" ></div>
              <div class="center">{{ queryProxyWeekDeposit?.LastDeposit || 0 }}</div>
              <div class="icon-right" >
                {{ lastLv }}
              </div>
            </div>
            <span style="color: #2ec6a0">Depósitos totais esta semana</span>
            <div class="balance-box">
              <div class="icon-left" ></div>
              <div class="center">{{ queryProxyWeekDeposit?.WeekDeposit || 0 }}</div>
              <div class="icon-right" >
                {{ curLv }}
              </div>
            </div>
          </div>
        </div>
        <!-- <span class="main-title">Detalhes das apostas</span> -->
        <!-- <div class="bet-details">
          <van-tabs v-model:active="tabActive" type="card" :ellipsis="false" color="#f43f5e" background="#829aab" title-inactive-color="#FFFFFF">
              <van-tab :title="tabData[0].label" :name="TabActive.thisweek"></van-tab>
              <van-tab :title="tabData[1].label" :name="TabActive.lastweek"></van-tab>
          </van-tabs>
          <div class="info_container">
            <div class="item" v-for="i in 6" :key="i">
              <div class="left">
                <van-image
                  fit="contain"
                  width="var(--app-px-50)"
                  lazy-load
                  src="/img/activity/weeklybetcashback/billiard.webp"
                />
              </div>
              <div class="right">
                Casino Ao Vivo
                <div>
                  99
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <div class="rule-table">
          <span class="main-title" style="padding-left: 0">BÔNUS E REGRAS</span>
          <div class="item title">
            <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp"/>
            <div class="table-label"> DEPÓSITOS  </div>
            <div class="table-label"> BÔNUS </div>
          </div>
          <div class="item" v-for="(data, index) in proxyCommiReturnDatas" :key="index">
            <div class="icon-right"><div>{{index + 1}}</div></div>
            <div class="table-label">{{ data.deposit  }} </div>
            <div class="table-label"> {{ (data.return_rate / 10) + "%" }} </div>
          </div>
        </div>
        <AppRule title="TERMOS E REGRAS" 
          content="1. O tempo de cálculo do bônus semanal é de segunda-feira às 00:00 até domingo às 23:59
2. Horário de aplicação do bônus: segunda-feira às 06h00 até domingo às 23h59 da próxima semana. Se não for resgatado, ele expirará.
3.Horário de aplicação do bônus: segunda-feira às 06h00 até domingo às 23h59 da próxima semana. Se não for resgatado, ele expirará.
4. Se os seus membros subordinados não depositarem durante a atividade de desconto, você não receberá o bônus
5. O valor do cashback pode ser sacado diretamente ou usado para continuar jogando"></AppRule>
      </section>
    </div>
  </van-config-provider>

</template>

<style lang="scss" scoped>
@import '/src/theme/mixin.scss';
.root-page {
  background: var(--theme-main-bg-color);
  padding-top: var(--app-navbar-height);
  padding-bottom: var(--app-footer-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  // font-family: Arial;
  color:#192756;
  font-size:24px;
  // justify-content: center;

  // flex-wrap: wrap;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  font-family: Arial;
}

.topInfo {
    // @include webp("/img/activity/weeklybetcashback/bg_top_info");
    background-color: #ffe5cf;
    border-radius:15px;
    // background-size: 100% 100%;
    width: 678px;
    height: 323px;
    // transform: translateX(7px);
    position: relative;
    padding-top: 14px;

    .caishen{
      display: inline-block;
      position: absolute;
      right: -16px;
      top: 43px;
    }

    .bg_red{
      display: inline-block;
      position: absolute;
      left: -6px;
      top: 122px;
    }

    .data{
      position: relative;
      z-index: 1;
    }

    .balance {
      position: absolute;
      width: 380px;
      height: auto;
      // height: 127px;
      top: 85px;
      left: 20px; /* 从左侧开始 */
      text-align: center;
      // font-family:Montserrat;
      font-weight:700;
      color:#3b82f6;
      font-size:40px;
    }

    .btn_recv {
      width: 212px;
      height: 55px;
      line-height:normal;
      font-family:Montserrat;
      font-weight:700;
      color:#ffffff;
      font-size:23px;
      margin-top: 140px;
      margin-left: 108px;
    }

    .period{
      display: inline-block;
      background:#6d543fBA;
      border-radius:10px;
      height:32px;
      line-height: 34px;
      padding-right: 15px;
      margin-left: 15px;
      font-family: Arial;
      // padding-top: 5px;
      // margin-top: 4px;
      color:#fff;
      font-size:20px;
      .title{
        height: 100%;
        margin-right: 10px;
        // margin-bottom: 6px;
        // margin: auto;
        display: inline-block;
        padding: 0px 15px;
        background:#3b82f6;
        border-radius:10px;
      }
      .period_label1{
        line-height: 32px;
        
      }
      &.scaleAni {
        // animation: spin 1s linear infinite;
        // transform: scale(1.1); /* 放大1.2倍 */
        // transition:transform 0.1s linear;
        animation:sacleAniFrames 0.8s ease;
      }
    }
    @keyframes sacleAniFrames {
      25% {
        transform: scale(1.08);
      }

      50% {
        transform: scale(1);
      }

      75% {
        transform: scale(1.08);
      }

      100% {
        transform: scale(1);
      }
    }
    .text{
      margin-left: 15px;
      margin-top: 14px;
      line-height: 32px;
      font-weight:700;
      color:#5b7bd4;
      font-size:24px;
    }

    .panel-bonus{
      margin-top: 15px;
      margin-bottom: 10px;
      display: flex;
      width: 360px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-weight:700;
      font-size:34px;
      .bonus{
        width:128px;
        height:26px;
        line-height: 26px;
        // background:#1e2027;
        color: #ffe66b;
        font-size:24px;
        text-align: center;
        // margin-bottom: 9px;
      }
      
    }
    
  }

.middleInfo{
  position: relative;
  margin-top: 17px;
  margin-bottom: 33px;
  width:678px;
  height:224px;
  background: white;
  border-radius: 15px;

  .bg-inner{
    @include webp('/img/activity/weeklyproxycashback/bg_middleinfo');
    background-size: 100%;
    width: 380px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
  }
  .details{
    position: relative;
    padding: 17px 0 0 45px;
    font-size:24px;
    z-index: 1;
    .balance-box{
      width:310px;
      height:50px;
      margin: 12px 0;
      // opacity:20%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      background:#829aab33;
      border-radius:10px;

      .center{
        flex: 1;
        text-align: right;
        font-weight:700;
        color:#182655;
        font-size:34px;
        line-height: 100%;
      }
      // img {
      //   width: 45px;
      //   margin: 0px 10px;
      // }

      

      .icon-left{
        @include webp('/icons/personal_semanal');
        box-sizing: border-box;
        background-size: 100%;
        margin: 0px 12px;

        width: 60px;
        height: 60px;
      }
    }
  }
}

.icon-right{
  @include webp('/img/activity/weeklyproxycashback/icon_level');

  background-size: 100%;
  margin:0 10px;
  width: 45px;
  height: 51px;
  text-align: center;
  padding-top: 10px;
  font-weight:700;
  font-style:italic;
  color:#676eff;
  font-size:20px;
  // display: flex;
  // justify-content: center;
  div{
    transform: translateX(-2px);
  }
}


.main-title{
  padding-left: 47px;
  width: 100%;
  font-weight:700;
  color:var(--app-ratio-title-color);
  font-size:24px;
}

.below_itemImg{
  width:41px;
  height:41px;
  margin-left: 11.5px;
  // margin-top: 15px;
}

.bet-details{
  margin-top: 16px;
  padding: 26px 0 0 0;
  background:#d0e3fe;
  border-radius:10px;
  width:702px;
  height:315px; 

  .info_container{
    display: grid;
    grid-template-columns: 224px 224px 224px; /* 3列 */
    grid-template-rows: 94px 94px; /* 2行 */
    gap: 5px; /* 网格间隙 */
    padding: 15px 9px;
  }

  .item {
    background-color: #ffffff;
    border-radius:10px;
    position: relative;

    .left {
      // background-color: #f43f5e;
      position: absolute;
      left: 0;
      width: 70px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .right{
      // background-color: #9567b5;
      position: absolute;
      width: 73%;
      height: 100%;
      right: 0;
      text-align: center;
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      padding: 10px 0;
      // line-height: 100%;
      div{
        // position: relative;
        margin-top: 8px;
        width:149px;
        height:39px;
        line-height: 39px;
        background:#829aab33;
        border-radius:10px;
        font-weight:700;
        color:#3b82f6;
        font-size:28px;
      }
    }


    // border: 1px solid blue;
  }



}

.rule-table{
  width: 678px;
  background:var(--app-box-bg-color);
  border-radius:15px;
  padding: 24px 23px 30px 23px;
  // margin: 42px 0;
  margin-bottom: 00px;
  // display: flex;
  // flex-direction: column;


  .item {
    // background:#eff6ff;
    border-radius:15px;
    width: 100%;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    // padding-left: 10px;

    line-height: 100%;
    color:var(--app-title-color);
    font-size:24px;
    
    div:nth-child(2){
      flex: 1;
      margin-left: 100px;
    }

    div:nth-child(3){
      width: 260px;
    }
    
  }

  .item:nth-child(2n){
    background: var(--theme-main-bg-color);
  }
  

  .title {
    background:#f8e5f6 !important;
    color:#f43f5e;
    margin-top: 12px;
    font-weight:700;
  }

  .table-label {
  padding-top: 4px;
  }

}




</style>

<route lang="yaml">
  meta:
    auth: true
</route>
