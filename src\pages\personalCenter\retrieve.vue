<script setup lang='ts' name='recuperar'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const showLoadingSpin = ref(false);
const gamebtnSeclet = ref(0)
const question = ref()
const inputForce = ref(false)
//隐藏底下菜单
appStore.setFooterDialogVisble(false)

const games = ref([
    { icon: 'nav-all-normal', iconSeclet: 'nav-all-seclet',text: 'Tudos', path: '', iconWidth: '30' },
   // { icon: 'nav-777-normal',iconSeclet: 'nav-777-seclet', text: 'Slots', path: '', iconWidth: 0 },
])

// 图片大小处理
const iconLoad = (e: any, item: any, type: number) => {
    if (type === 0) {
        for (let i = 0; i < games.value.length; i++) {
            if (item.icon === games.value[i].icon) {
                games.value[i].iconWidth = Math.ceil(e.target.naturalWidth)
                break
            }
        }
    }
}


const btnClick = (index) => {
    gamebtnSeclet.value = index;
}

const onConfirm = () => {

}

const setLoadingSpin = () => {

    console.log("setLoadingSpin")
    showLoadingSpin.value = true
    setTimeout(() => {
        showLoadingSpin.value = false
    }, 1500);
}


</script>

<template>
    <AppPageTitle left-arrow title="Recuperar o saldo" title-weight="700" />
    <div class="content">
        <div class="content-top">
            <div class="content-top-rect">
                <p class="recuperar-top-left-title">
                    <span class="recuperar-top-left-title-text">Saldo atual</span>
                    <netretry>
                        <div class="recuperar-top-left-title-money">
                            <section class="currency-count">
                                <div class="countTextBox">
                                    <span :style="{ height: `var(--app-px-24)` }">
                                        <span v-if="!showLoadingSpin" class="count-to">
                                            {{ UsAmountFormat(userInfo.unlock_amount!) }}
                                        </span>
                                        <span v-else class="count-loading">
                                            loading
                                        </span>
                                    </span>
                                    <i class="countTextBox-img-box " @click="setLoadingSpin">
                                        <AppImage src="/img/withdraw/refresh-money" class="countTextBox-img"
                                            :class="{ 'animate__spin': showLoadingSpin }" />
                                    </i>
                                </div>

                            </section>
                        </div>
                    </netretry>
                </p>
                <button class="content-cliquepara" @click="setLoadingSpin">Um clique para...</button>
                <span class="content-label">Você sō pode recuperar o múltiplo inteiro do equilibrio (ou seja,sem ponto
                    decimal)</span>
            </div>
        </div>
        <div class="content-middle">
            <section class="content-middle-games">
                <ul class="reports">
                    <li v-for="(item, idx) in games" :key="item.icon" class="item" :class="{'active': gamebtnSeclet==idx}" @click="() => btnClick(idx)">
                        <div v-if="gamebtnSeclet==idx" class="icon">
                            <AppImage :src="'/icons/' + item.iconSeclet + '.png'"
                                :style="{ width: item.iconWidth ? 'var(--app-px-' + item.iconWidth + ')' : '0'}"
                                />
                        </div>
                        <div v-else class="icon">
                            <AppImage :src="'/icons/' + item.icon + '.png'"
                                :style="{ width: item.iconWidth ? 'var(--app-px-' + item.iconWidth + ')' : '0' }"
                                @load="$event => iconLoad($event, item, 0)" />
                        </div>
                        <span v-if="gamebtnSeclet==idx" :style="{
                            color:'var(--theme-font-on-background-color)'
                        }" class="name">{{ item.text }}</span>
                         <span v-else :style="{
                            color:'#c5e2d2'
                        }" class="name">{{ item.text }}</span>
                    </li>
                </ul>
            </section>

            <div v-if="gamebtnSeclet == 0" class="content-right">
                <!-- 查下框 -->
                <div class="invite-query">
                    <div class="invite-query-input">
                        <div class="searchParent" :class="{ active: inputForce }">
                            <input v-model="question" class="search" placeholder="Pesquisa de plataforma"
                                @focus="eventFocus" @blur="eventBlur" />
                            <AppImage class="select-img" src="/icons/nvite_search1.webp" alt="" @click="onConfirm"
                                maxlength="30" />
                        </div>
                    </div>
                </div>
                <!--
                <div class="pg-game">
                    <AppImage class="icon" src="/icons/nav_101.png.webp" alt="" @click="onConfirm"
                                maxlength="30" />
                    <span>PG</span>
                    <div class = "line"></div>
                    <label>0.00</label>
                </div>
                -->
            </div>
            <div v-else-if="gamebtnSeclet == 1" class="content-right">
                <div class="pg-game">
                    <AppImage class="icon" src="/icons/nav_101.png.webp" alt="" @click="onConfirm"
                                maxlength="30" />
                    <span>PG</span>
                    <div class = "line"></div>
                    <label>0.00</label>
                </div>
            </div>
        </div>
        <div class="content-bottom"></div>

    </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.content {
    height: calc(100vh - 100px);
    font-size: 22px;
    background-color: var(--theme-bg-color);
    display: block;

    .content-top {
        height: 190px;
        width: 750px;
        
        .content-top-rect {
            position: relative;
            height: 150px;
            width: 700px;
            top: 18px;
            left: 25px;
            //padding: 25px 25px;
            border-radius: 10px;
            background-color: var(--theme-main-bg-color);
            font-size: 18px;

            .content-cliquepara {
                position: absolute;
                width: 160px;
                height: 50px;
                right: 20px;
                top: 20px;
                color: var(--theme-main-bg-color);
                border-radius: 15px;
                text-align: center;
                //font-size: 18px;
                background-color: var(--theme-primary-color);
            }

            .content-label {
                font-size: 20px;
                position: absolute;
                color: var(--theme-text-color-lighten);
                width: 700px;
                text-align: right;
                right: 15px;
            }
        }
    }


    .content-middle {
        width: 100%;
        height: 300px;
        position: relative;
        display: flex;

        .content-middle-games {
            width: 200px;
            //background-color: black;
            //margin: 0 auto;
            margin: 0px 22px;
            //padding: 30px 10px;
            list-style: none;
            border-radius: 15px;
            // width: 678px;
            color: #000;
            font-size: 28px;

            //margin-top: 24px;
            //background-color: var(--app-box-bg-color);
            .item {
                position: relative;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                height: 70px;
                width: 150px;
                //padding: 10px;
                margin-bottom: 20px;
                border-radius: 10px;

                background-color: var(--theme-main-bg-color);

                // background: #fff;
                .icon {
                    width: 42px;
                    margin-left: 20px;
                    margin-right: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                &.active {
                    background-color: var(--theme-primary-color) ;

                }
            }

            

            .name {
                position: relative;
                //color: var(--theme-text-color-lighten);
                font-size: 25px;
                left: -15px;
                text-align: center;
            }
        }

        .content-right {
            position: absolute;
            right: 0px;
            width: 550px;
            height: 400px;
            display: block;
            //background-color: aqua;
        }

        .invite-query {
            .searchParent {
                position: relative;
                width: 530px;
                height: 52px;
                top: -3px;
                //background: #164633;
                border: 1px solid;
                border-color: var(--theme-color-line);
                border-radius: 26px;

                &.active {
                    border-color: #f8fc4e;
                }

                .search {
                    display: inline;
                    margin: 0 auto;
                    padding-left: 20px;
                    width: 240px;
                    height: 52px;
                    background-color: transparent;
                    color: var(--theme-text-color);

                }

                .search::placeholder {
                    color: var(--theme-text-color-lighten);
                    font-size: 22px;
                }

                .select-img {
                    position: absolute;
                    width: 27px;
                    top: 15px;
                    right: 20px;
                }

            }

        }

        .pg-game{
            position: relative;
            width: 250px;
            height: 95px;
            margin-top: 20px;
            //top: 70px;
            color: white;
            border-radius: 10px;
            background-color: var(--theme-main-bg-color);
            .icon{
                width: 64px;
                height: 48px;
            }

            span{
                position: relative;
                height: 20px;
                top: -15px;
                //background-color: #000;
                //top: 13px;
            }

            .line{
                //display: block;
                position: relative;
                width: 100%;
                top: -6px;
                //padding: 0 10px;
                text-align: center;
                //left: -20px;
                height: 1px;
                //left: 20px;
                background-color: #2a815f;
            }

            label{
                position: relative;
                padding-left:15px;
                top: 6px; 
                font-size: 18px;
                color: var(--theme-text-color-lighten);
            }
        }

    }





    .recuperar-top-left-title {
        display: flex;
        align-items: center;
        font-size: 20px;
        padding: 35px 0 0 20px;
        color: var(--theme-text-color-darken);
        justify-content: flex-start;
        margin-bottom: 15px;
    }

    .recuperar-top-left-title-text {
        color: var(--theme-text-color-lighten);
        font-size: 23px;
    }

    .recuperar-top-left-title-money {
        font-size: 18px;
        color: var(--theme-text-color-darken);
        display: flex;
        align-items: center;
        justify-content: start;
        line-height: normal;
        padding-left: 2px;
        padding-right: 8px;
        width: auto;
        height: 30px;
    }

    .currency-count {
        display: flex;
        align-items: center;
        font-size: 22px;
        height: 28px;
        padding-left: 2px;
        padding-right: 8.4px;
    }

    .countTextBox {
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        font-size: 18px;
        line-height: 18px;
        height: 28px;
    }

    .count-to {
        color: var(--theme-secondary-color-finance) !important;
        position: relative;
        display: block;
        top: -2px;
        margin: 0 7.3px -3px 10px;
        max-width: 164px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 24px;
        line-height: 24px;
    }

    .count-loading {
        font-size: 18px;
        color: var(--theme-text-color-lighten);
        padding: 0 13px;
    }

    .countTextBox-img-box {
        height: 26px;
        width: 26px;
        position: relative;
        z-index: 10;
        display: block;

        .rotate {
            animation: spin 1s linear infinite;
        }
    }

    .countTextBox-img {
        height: 22px;
        width: 24px;
    }

    .animate__spin {
        -webkit-animation-name: spin;
        animation-name: spin;
        -webkit-animation-timing-function: linear;
        animation-timing-function: linear;
        animation-duration: 0.3s;
        animation-fill-mode: both;
        animation-iteration-count: infinite;
    }

    @-webkit-keyframes spin {
        0% {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg)
        }

        to {
            -webkit-transform: rotate(1turn);
            transform: rotate(1turn)
        }
    }

    @keyframes spin {
        0% {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg)
        }

        to {
            -webkit-transform: rotate(1turn);
            transform: rotate(1turn)
        }
    }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>