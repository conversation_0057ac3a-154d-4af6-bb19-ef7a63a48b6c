<script setup lang="ts" name="app-game-tab">
// import { GameNavEnum } from '~/types/common';
import { GameTypeEnum ,GameNavEnum} from "~/types/common"
defineProps({
  text: String
})


const tabList = ref<any[]>([
  { src: '/img/pg1.png',platform_id: GameTypeEnum.platform_pg ,cateName:"0 games",lastName:"PG"},
  { src: '/img/pp1.png',platform_id: GameTypeEnum.platform_pp ,cateName:"0 games",lastName:"PP"},
  { src: '/img/TADA1.png',platform_id: GameTypeEnum.platform_tada ,cateName:"0 games",lastName:"Tada"},
  { src: '/img/habanero_1.png',platform_id: GameTypeEnum.platform_haba ,cateName:"0 games",lastName:"Haba"},
  { src: '/img/Yesbingo_1.png',platform_id: GameTypeEnum.platform_yesbingo ,cateName:"0 games",lastName:"YesBingo"},
  { src: '/img/FC1.png',platform_id: GameTypeEnum.platform_fc ,cateName:"0 games",lastName:"Fc"},
  { src: '/img/Evolution.png',platform_id: GameTypeEnum.platform_evo ,cateName:"0 games",lastName:"EVO"},
  { src: '/img/Hacksaw.png',platform_id: GameTypeEnum.platform_hacksaw ,cateName:"0 games",lastName:"Hacksaw"},
  { src: '/img/JDB1.png',platform_id: GameTypeEnum.platform_jdb ,cateName:"0 games",lastName:"JDB"},
 
])



const router = useRouter()
const clickId = ref(-1)
const gameTabRef = ref()
const isShowLayer = ref(false)
const isShowPlatform = ref(false)//是否显示三方平台
const activeIndex = ref(0)
const gameList = ref<any[]>([])
const isFinish = ref(true)
const curPage = ref(1)
const platformId = ref("0")
//重连数据
const isGetDataFinish  =ref(false)
const reconnectionTims  =ref(0)
const reconnectionMax  = ref(3)

//大厅按钮选中
const liClick = (item: any) => {
  console.log(JSON.stringify(item))
  isShowLayer.value = true

  if(item.platform_id!=clickId.value){
    getData(item)
    setTimeout(() => {
      clickId.value = item.platform_id
    }, 200);
  }
}

//隐藏界面
function closePage(){
  isShowLayer.value = false
}

function closePage2(){
  isShowPlatform.value =false
}

//获取数据
function getData(item: any){
  curPage.value = 1
  platformId.value = item.platform_id
  gameList.value=[]
  isFinish.value = true
  clearReconnectionData()
  getGameList()
}

//下一页
function goPage(){
  curPage.value +=1
  clearReconnectionData()
  getGameList()
}

function clearReconnectionData(){
  isGetDataFinish.value = false
  reconnectionTims.value = 0
}




// // 获取游戏列表
// const { run: getGameList } = useRequest(() => ApiGameList({ page: curPage.value,
//   page_size: 48,  platform_id: platformId.value, tag_id: '0',is_new:0 }), {
//   manual: true,
//   onSuccess: (data) => {
//      isGetDataFinish.value =true
//     if (data.d && data.d.length) {
//       gameList.value = gameList.value.concat(data.d)
//       isFinish.value = gameList.value.length>=data.t
//     }
//   },
//   onError: () => {
//     gameList.value = []
//     isFinish.value= true
//   },
//   onAfter:()=>{
//     if(isGetDataFinish.value==false  && reconnectionTims.value<reconnectionMax.value ){
//       reconnectionTims.value +=1
//       setTimeout(function() {
//         console.log("获取数据ApiGameList-最后执行="+reconnectionTims.value)
//         getGameList()
//       }, 500);

    
//     }
//   }
// })

// 本来走 ApiGameList 后来换ApiGameRecList
const { run: getGameList} = useRequest(() => ApiGameRecList({ 
  platform_id: platformId.value,
  page_size: 30, 
  page:curPage.value}), {
  manual: true,
  onSuccess: (data) => {
    isGetDataFinish.value =true
    if (data.d && data.d.length) {
      gameList.value = gameList.value.concat(data.d)
      isFinish.value = gameList.value.length>=data.t
    }

  },
  onError: () => {
    gameList.value = []
    isFinish.value= true
  },
  onAfter:()=>{
    if(isGetDataFinish.value==false  && reconnectionTims.value<reconnectionMax.value ){
      reconnectionTims.value +=1
      setTimeout(function() {
        console.log("获取数据ApiGameList-最后执行="+reconnectionTims.value)
        getGameList()
      }, 500);

    
    }
  }
})
	 
//游戏的数量
const { run: runGetUserBanks } = useRequest(ApiPlatformNum, {
    onSuccess: (data) => {
      if (data) {
        data.forEach((item) => {
          tabList.value.forEach((tab)=>{
              if(tab.lastName ==item.platform_id){
                tab.cateName = item.number+" games"
              }
          })
      })
    }
    }
  })
  
//Tudos调用
function appGamePlatform(){
  // if(clickId.value==-1){
  //   liClick(tabList.value[0])
  // }else{
  //   isShowLayer.value = true
  // }

  isShowPlatform.value = true

}

function gotoLeft(){
  console.log("滚动左边")
  gameTabRef.value.scrollTo(GameTypeEnum.platform_pg)
}

function gotoRight(){
  console.log("滚动右边")
  gameTabRef.value.scrollTo(GameTypeEnum.platform_hacksaw)
  
}

</script>

<template>
 
  <div class="app-game-tab-sec">
    <div >
      <AppIndexTitle :id="GameNavEnum.Platform" :isCallback="true" @appGamePlatform-by="appGamePlatform"     @gotoLeft-by="gotoLeft"  @gotoRight-by="gotoRight"/>
    </div>
    <div class="down">
      <van-tabs ref="gameTabRef" :swipe-threshold="4" line-height="0" background="transparent" line-width="0" class="game-type-tabs" >
        <van-tab v-for="(item, classid) in tabList" :key=item.platform_id   :name=item.platform_id  >
          <template #title>
            <div class="n-tab-item" @click="liClick(item)" :class="{active:clickId === item.platform_id}">
              <AppImage class="icon" :class="`icon_${classid}`" :src="item.src" alt="" />
              <div class="text">{{ item.cateName }}</div>
            </div>
          </template>
        </van-tab>
      </van-tabs>
    </div>
  </div>

  <!-- 游戏列表弹窗 -->
   <van-popup teleport="body" v-model:show="isShowLayer" >
      <div class="content-box">
        <div class="top">
          <AppImage class="nav_1000" src="/icons/nav_1000-active.png.webp" alt="" />
          <span>Provedor do jogo</span>
          <AppImage class="close"  src="/icons/close_black.png.webp" @click="closePage" />
        </div>
        <div class="bottom">

          <van-tabs :swipe-threshold="4" line-height="0" background="transparent" line-width="0" class="game-type-tabs" v-model:active="clickId">
            <van-tab v-for="(item, classid) in tabList" :key=item.platform_id   :name=item.platform_id  >
              <template #title>
                <div class="n-tab-item2" @click="getData(item)" :class="{active:clickId === item.platform_id}">
                  <AppImage class="icon" :class="`icon_${classid}`" :src="item.src" alt="" />
                </div>
              </template>
            </van-tab>
          </van-tabs>
          
          <div class="app-maps game-container">
            <div class="content" >
              <AppGameItem class="game-item" v-for="(item, idx) in gameList" :key="item.id + idx" :data="item" />
            </div>
            <div @click="goPage" class="more" v-if="!isFinish">
              <label class="more-text">Mostrar mais</label>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

  <!-- 三方游戏切换 -->
  <van-popup teleport="body" v-model:show="isShowPlatform" >
      <div class="content-box">
        <div class="top">
          <AppImage class="nav_1000" src="/icons/nav_1000-active.png.webp" alt="" />
          <span>Provedor do jogo</span>
          <AppImage class="close"  src="/icons/close_black.png.webp" @click="closePage2" />
        </div>
        <div class="bottom">
          <div class="app-maps game-container">
            <div class="content2" >
              <div v-for="(item, classid) in tabList" :key=item.platform_id   :name=item.platform_id  >
                  <div class="n-tab-item n-tab-item2" @click="liClick(item)" :class="{active:clickId === item.platform_id}">
                    <AppImage class="icon" :class="`icon_${classid}`" :src="item.src" alt="" />
                  <div class="text">{{ item.cateName }}</div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </van-popup>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';
.app-game-tab-sec {
  display: block;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 50px;

  .down {
    flex: 1;
    overflow: hidden;
    width: 710px;
    height: 200px;
    line-height: 52px;
    border-radius: 27px;
    // background: #1c2533;
    padding: 0 0px 0 0px;
  }
}

.text{
  position: absolute;
  text-align: center;
  color:#ce57e5;
  font-size:24px;
  transform: translate(-50%,-50%);
  left:50%;
  top:77%;
}


.icon_0{
  width: 199px;
}
.icon_1{
  width: 189px;
}
.icon_2{
  width: 178px;
}
.icon_3{
  width:195px;
}
.icon_4{
  width: 178px;
}
.icon_5{
  width:206px;
}
.icon_6{
  width: 189px;
}
.icon_7{
  width:239px;
}
.icon_8{
  width: 166px;
  
}





.n-tab-item {
    flex: 1;
    flex-basis: auto;
    width: 254px;
    height: 179px;
    border-radius:15px;
    line-height: 37px;
    flex-shrink: 0;
    margin-top: 8px;
    position: relative;
    font-weight: normal;
    // background-color:var(--app-box-bg-color) ;
    background-color:var(--app-box-bg-color) ;

    &.active {
      // @include webp('/icons/nav_bg-active.png');
      background-color:var(--app-platform-color) ;
      color: #fff;
    }
    img{
      position: absolute;
      transform: translate(-50%,-50%);
      left:50%;
      top:35%;
    }

    
  }



  .n-tab-item2 {
    flex: 1;
    flex-basis: auto;
    width: 240px;
    height: 84px;
    border-radius:15px;
    line-height: 37px;
    flex-shrink: 0;
    margin-top: 8px;
    position: relative;
    font-weight: normal;
    // background-color:var(--app-box-bg-color) ;
    background-color:var(--app-box-bg-color) ;

    &.active {
      // @include webp('/icons/nav_bg-active.png');
      background-color:var(--app-platform-color) ;
      color: #fff;
    }
    img{
      position: absolute;
      transform: translate(-50%,-50%);
      left:50%;
      top:50%;
    }
    .icon_8{
      width: 105px;
    }
  }

//-----------------------------------------------------------------

.van-popup--center{
  max-width: 100%;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.content-box{
  width: 100%;
  height: 100%;
  margin: 0;
  background: var(--theme-main-bg-color);;
  .top {
    background-color:var(--theme-top-nav-bg) ;
    color: var(--app-title-color);
    text-align: center;
    width: 100%;
    height: 140px;
    line-height: 140px;
    font-size: 45px;
    font-weight: 700;
    position: relative;
    span {
      margin-left: 25px;
    }
    .nav_1000{
      width: 54px;
    }
    .close{
      position: absolute;
      right: 30px;
      top: 30px;
      width: 80px;
    }
  }
  .bottom{
    width: 100%;
    margin: 0;
    background: var(--theme-main-bg-color);
    padding: 0 20px;
  }



  .app-maps.game-container {
    // margin-left: 5px;
    // margin-right: 5px;
    border-radius: 0;
    height: calc(100% - 273px);
    padding-bottom: 40px;
    // overflow: auto;
  }
  .app-maps {
    border-radius: 0px 30px 30px 30px;
    // background: linear-gradient(180deg, #044B9A 0%, #011A51 100%), #1B2E1B;
    // padding-top: 17px;
    // padding: 0 18px;
    padding-bottom: 8px;

    .content {
      // height: 590px;
      display: grid;
      grid-template-columns:auto auto auto;
      grid-template-rows: auto auto;
      // grid-auto-flow: row;
      grid-column-gap: 21px;
      grid-row-gap: 17px;
      scroll-snap-type: x mandatory;
      // overflow: auto hidden;
      align-items: start;
      justify-items: start;
      // grid-template-columns: repeat(auto-fill, 200px);
      &.one-row {
        grid-template-rows: auto;
        justify-content: flex-start;
        // height: 295px;
      }
      .left {
        width: 12px;
        scroll-snap-align: start;
      }

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .more {
      margin: 0 auto;
      margin-top: 50px;
      width: 190px;
      height: 60px;
      // display: flex;
      // align-items: center;
      text-align: center;
      // justify-content: center;
      background-color: #0E3D8F;
      border-radius: 10px;
      &-text{
        color: white;
        line-height: 60px;
        font-size: 24px;
      }
    }

    .img2{
      // transform: rotate(180deg);
      width: 16px;
      height: 27px;
      margin-top: 5px;
      right: 20px;
    }
  }
}
//--------------------------------------------------
//三方tab 选项
.content2 {
    display: grid;
    grid-template-columns:auto auto;
    grid-template-rows: auto auto;
    grid-column-gap:0px;
    grid-row-gap: 17px;
    scroll-snap-type: x mandatory;
    align-items: start;
    justify-items: center;
  
    &.one-row {
      grid-template-rows: auto;
      justify-content: flex-start;
    }
  
    &::-webkit-scrollbar {
      display: none;
    }

    .n-tab-item2{
      width: 320px;
      height: 179px;
    } 

    img{
      position: absolute;
      transform: translate(-50%,-50%);
      left:50%;
      top:35%;
    }
}




// .app-game-tab {
//   height: 148px;
//   flex-shrink: 0;
//   border-radius: 20px;
//   background: #a6bad5;
//   display: grid;
//   grid-template-columns: repeat(4, 1fr);
//   grid-template-rows: repeat(2, 1fr);
//   overflow: hidden;
// }
</style>
