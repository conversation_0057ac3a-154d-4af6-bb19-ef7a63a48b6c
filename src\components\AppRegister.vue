<script setup lang="ts" name="app-login">
const router = useRouter();
const appStore = useAppStore();
const { loginDialogVisible, loginDialogType } = storeToRefs(appStore);
import userIcon from "/icons/svg/user.svg?raw";
import lockIcon from "/icons/svg/lock.svg?raw";
import cardIcon from "/icons/svg/card.svg?raw";
const fromData = reactive({
  phone: "",
  password: "",
  repassword: "",
  username: "",
  real_name: "",
  currency: "1",
  //代理
  link_id: "",
  business_id: "", ///业务员id 对应业务员链接注册
  channel_id: "", //渠道id 对应渠道链接注册
});
const isPhone = ref(false);
const { phoneReg } = useRegExpPhone();
const emailRef = ref();
const phoneRef = ref();
const passwordRef = ref();
const passwordRef2 = ref();
const loginRef = ref();
const inputRealNameRef = ref();
const codeRef = ref();
const isCheckd = ref(true);
const passwordStrength = ref(0);
const changeRegType = (bool: boolean) => {
  isPhone.value = bool;
  fromData.code = "";
  fromData.password = "";
};

watch(loginDialogVisible, () => {
  console.log("show register");

  fromData.phone = "";
  fromData.password = "";
  fromData.repassword = "";
  fromData.username = "";
  fromData.real_name = "";
});

const { run: runSendCode, loading: codeLoading } = useRequest(
  () => ApiSendOfflineSms({ flag: "text", tel: fromData.phone, ty: 1 }),
  {
    manual: true,
    onSuccess(data: string) {
      const [sid, ts] = data.split(":");
      fromData.sid = sid;
      fromData.ts = ts;
      startCountdown();
      showToast("Código de verificação enviado");
    },
  }
);
const goKf = () => {
  router.push("/serviceMessages");
  appStore.setLoginDialogVisible(false);
  appStore.setActiveDialogVisble(false);
  appStore.setFindPasswordDialogVisible(false);
};
const switchTabActiveType = () => {
  appStore.setRegisterDialogVisible(false);
  appStore.setLoginDialogVisible(true);
};
// 邮箱注册验证码获取
const { run: runEmailCode, loading: emailCodeLoading } = useRequest(
  () => ApiEmailCode({ ty: "1", mail: fromData.email }),
  {
    manual: true,
    onSuccess: (data) => {
      const [sid, ts] = data.split(":");
      fromData.sid = sid;
      fromData.ts = ts;
      startCountdown();
      showToast("Código de verificação enviado");
    },
  }
);

const countdowm = ref(0);
const startCountdown = () => {
  countdowm.value = 60;
  const timer = setInterval(() => {
    countdowm.value--;
    sessionStorage.setItem("countdowm", countdowm.value.toString());
    if (countdowm.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 发送验证码按钮是否禁用
const enviarDisabled = computed(() => {
  return countdowm.value > 0;
});

const { run: runLogin } = useRequest(
  () =>
    ApiLogin({
      username: fromData.username, // isPhone.value ? fromData.phone : fromData.email,
      password: fromData.password,
      device_no: "asdfasdf234",
    }),
  {
    manual: true,
    onSuccess(data) {
      //如果有保存的账户，则替换保存的账户
      if (appStore.storedAccount) {
        appStore.setStoredAccount(
          JSON.stringify({
            username: fromData.username, //isPhone.value ? fromData.phone : fromData.email,
            password: fromData.password,
          })
        );
      }
      openLoginDialog(false);
      appStore.setActiveDialogVisble(false);
      setTimeout(() => {
        appStore.setShowRecharge(true);
      }, 500);
    },
  }
);

const { run: runRegister, loading: registerLoading } = useRequest(
  () =>
    ApiRegister({
      phone: isPhone.value ? fromData.phone : undefined,
      password: fromData.password,
      real_name: fromData.real_name,
      currency: fromData.currency,
      username: fromData.username,
      // sid: fromData.sid,
      // ts: fromData.ts,
      // verify_code: fromData.code,
      // device_no: 'asdfasdf234',
      // reg_url: window.location.href,

      link_id: fromData.link_id,
      business_id: fromData.business_id,
      channel_id: fromData.channel_id,
    }),
  {
    manual: true,
    onSuccess() {
      // sessionStorage.setItem('LinkId',"")
      runLogin();
    },
  }
);

//判断手机号第三位是不是9
function isThirdChar9(str: string) {
  return str.length > 2 && str.charAt(2) === "9";
}

const submit = () => {
  console.log("register");

  loginRef.value.validation();
  inputRealNameRef.value.validation();
  passwordRef.value.validation();
  passwordRef2.value.validation();

  if (
    !loginRef.value.isValid ||
    !inputRealNameRef.value.isValid ||
    !passwordRef.value.isValid ||
    !passwordRef2.value.isValid
  ) {
    return;
  }
  fromData.phone = fromData.phone.replace(/\s*/g, "");
  if (isPhone.value) {
    phoneRef.value.validation();
    console.log(isPhone.value);

    // if (!isThirdChar9(fromData.phone) || !usernameReg.test(fromData.phone)) {
    //   return showToast("Erro de formato do celular");
    // }
    const regex = /^\d{1,11}$/;
    if (
      fromData.phone !== null &&
      fromData.phone !== undefined &&
      fromData.phone !== "" &&
      !regex.test(fromData.phone)
    ) {
      return showToast("Erro de formato do celular");
    }
  } else {
    // emailRef.value.validation();
  }
  if (fromData.password !== fromData.repassword) {
    return showToast("As senhas inseridas não coincidem");
  }
  // codeRef.value.validation();
  // 是否验证通过
  // const isPass = [isPhone.value ? phoneRef.value.isValid : emailRef.value.isValid, passwordRef.value.isValid, codeRef.value.isValid].every(item => item)
  const isPass = [
    isPhone.value ? phoneRef.value.isValid : true,
    passwordRef.value.isValid,
  ].every((item) => item);
  if (!isCheckd.value) {
    showToast("Por favor, leia e concorde com a Política de Privacidade");
    return;
  }
  console.log(isPass);
  if (isPass) {
    // if (!fromData.sid) {
    //   showToast('Por favor, envie o código de verificação')
    // } else {
    //   const obj = sessionStorage.getItem('LinkId')
    //   if (!!obj) {
    //     fromData.id = JSON.parse(obj).id
    //   }
    //   runRegister()
    // }
    const obj = sessionStorage.getItem("LinkId");
    if (!!obj) {
      fromData.link_id = JSON.parse(obj).id;
      fromData.business_id = JSON.parse(obj).business_id;
      fromData.channel_id = JSON.parse(obj).channel_id;
      // sessionStorage.setItem('LinkId',"")
    } else {
      if (router.currentRoute.value.query.id) {
        fromData.link_id = router.currentRoute.value.query.id + "";
      }

      if (router.currentRoute.value.query.business_id) {
        fromData.business_id = router.currentRoute.value.query.business_id + "";
      }

      if (router.currentRoute.value.query.channel_id) {
        fromData.channel_id = router.currentRoute.value.query.channel_id + "";
      }
    }

    // const obj2 = sessionStorage.getItem('businessId')
    // if (!!obj2) {
    //   fromData.id = JSON.parse(obj2).id
    // }

    runRegister();
  }
};
// 同时验证 phoneReg 和  emailReg，只要有一个通过就行
const usernameReg = new RegExp("(?:" + phoneReg.value.source + ")\\b");
const handleSendCode = () => {
  if (isPhone.value) {
    phoneRef.value.validation();
  } else {
    emailRef.value.validation();
  }
  const isPass = isPhone.value
    ? phoneRef.value.isValid
    : emailRef.value.isValid;
  if (isPass) {
    if (isPhone.value) {
      runSendCode();
    } else {
      runEmailCode();
    }
  }
};

const goPrivacy = () => {
  // appStore.setLoginDialogVisible(false);
  appStore.setShowPrivacy(true);
  // router.push("/privacy");
};

//关闭界面
const closePage = () => {
  appStore.setLoginDialogVisible(false);
};

const authPassword = () => {
  let strength = 0;
  let pd = fromData.password;

  // 检查大写字母
  if (/[A-Z]/.test(pd)) strength++;

  // 检查小写字母
  if (/[a-z]/.test(pd)) strength++;

  // 检查数字
  if (/\d/.test(pd)) strength++;

  // 检查特殊字符
  if (/[\W_]/.test(pd)) strength++;
  passwordStrength.value = strength;
};
// watch(
//   () => fromData.real_name,
//   (n) => {
//     fromData.real_name = String(n).replace(/^[a-zA-Z]+$/,'')
//   }
// );
</script>

<template>
  <div
    class="app-login"
    v-if="loginDialogType == 'register' && loginDialogVisible"
  >
    <!-- <div class="login-img"> -->
    <!-- <div class="reg-tab" :class="{
        'active': !isPhone
      }" @click="changeRegType(false)">
        <div class="email" :class="{
          'active': !isPhone
        }"></div>
      </div> -->
    <!-- <div class="line"></div> -->
    <!-- <div class="reg-tab" :class="{
        'active': isPhone
      }" @click="changeRegType(true)">
        <div class="phone" :class="{
          'active': isPhone
        }"></div>
      </div> -->
    <!-- </div> -->
    <div class="tab">
      <div class="tab-item">
        <div class="svg-icon"></div>
        <span>Registre sua conta</span>
      </div>
      <div class="bottom-line"></div>
    </div>

    <!-- v-show="isPhone"   icon-left="input-user-icon.webp"-->
    <div class="phone">
      <AppInput
        icon-with="25"
        v-model="fromData.username"
        ref="loginRef"
        :pattern="usernameExpReg"
        :required="true"
        :svg-icon-left="userIcon"
        type="text"
        placeholder="Nome de Usuário"
        msg="4-16 caráter bit, suporte em inglês/números/símbolos"
        :err-height="42"
        clearable
        width="100%"
        height="70"
        maxlength="16"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      >
      </AppInput>
    </div>
    <!--    <div class="email" v-show="!isPhone">-->
    <!--      <AppInput icon-with="43"  v-model="fromData.email" ref="emailRef" :pattern="emailReg" icon-left="icon_email_1" clearable placeholder="Email"-->
    <!--        msg="Erro de e-mail"  width="694" height="120" :style-obj="{-->
    <!--          background:'var(&#45;&#45;app-box-bg-color)',-->
    <!--          color: '#000',-->
    <!--          borderRadius: '15px'-->
    <!--        }" />-->
    <!--    </div>-->
    <div class="password">
      <AppInput
        icon-with="25"
        width="100%"
        height="70"
        v-model="fromData.password"
        ref="passwordRef"
        :required="true"
        double-action="true"
        :pattern="passwordReg3"
        :err-height="42"
        placeholder="Senha"
        @change="authPassword"
        msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos"
        clearable
        :svg-icon-left="lockIcon"
        type="password"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      />
    </div>
    <div :class="`ant-space-item passwordStrength-${passwordStrength}`">
      <span>Força</span><span class="step"></span><span class="step"></span
      ><span class="step"></span><span class="step"></span>
    </div>
    <div class="password">
      <AppInput
        icon-with="25"
        width="100%"
        height="70"
        v-model="fromData.repassword"
        ref="passwordRef2"
        :required="true"
        double-action="true"
        :pattern="passwordReg3"
        :err-height="42"
        placeholder="Confirme a senha novamente, o mesmo que a senha!"
        msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos"
        clearable
        :svg-icon-left="lockIcon"
        type="password"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      />
    </div>
    <!-- :pattern="usernameReg" -->
    <!-- <div class="phone">
      <AppSelectCodeAndInput
        v-model="fromData.phone"
        ref="phoneRef"
        type="phone"
        :pattern="/^\d{1,11}$/"
        placeholder="Digite o Número do Celular "
        msg="Por favor, insira um número com no máximo 11 dígitos."
        clearable
        width="100%"
        height="70"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      />
    </div> -->
    <!--  onkeyup="value=value.replace(/[^a-zA-Z]/g,'')" -->
    <div class="phone">
      <AppInput
        icon-with="25"
        v-model="fromData.real_name"
        :pattern="realnameExpReg"
        ref="inputRealNameRef"
        :svg-icon-left="cardIcon"
        type="text"
        placeholder="Preencha o nome verdadeiro e torne -o conveniente para a retirada posterior!"
        msg="Indique o seu nome verdadeiro"
        :err-height="42"
        clearable
        width="100%"
        height="70"
        maxlength="64"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      >
      </AppInput>
    </div>

    <div
      style="
        border: 1px solid var(--theme-color-line);
        border-radius: var(--app-px-14);
      "
    >
      <AppSelect
        :model-value="'1'"
        down-icon="down-pull-select.webp"
        :list-data="[
          {
            label: 'BRL（BRL）',
            value: '1',
          },
        ]"
        bc="var(--theme-main-bg-color)"
        flag="bank-icon.webp"
        :flag-size="36"
        flag-style=""
      >
      </AppSelect>
    </div>

    <div class="info">
      A moeda determina os jogos de terceiros e os métodos de pagamento e não
      pode ser modificada.
    </div>

    <!-- <div class="code">
      <AppInput icon-with="31"  v-model="fromData.code" ref="codeRef" :pattern="verifyCodeReg" placeholder="Código de verificação"
        msg="Erro no código de verificação" icon-left="icon_verification_code" width="506" height="63" :style-obj="{
          background:'#192841',
          color: '#fff',
          borderRadius: '32px'
        }">
        <template #right>
          <div :style="{ paddingRight: 'var(--app-px-18)' }">
            <AppButton :loading="codeLoading || emailCodeLoading" :disabled="enviarDisabled" @click="handleSendCode"
              fontSize="26" radius="23" whiteText blue width="125" height="47" center>{{ countdowm > 0 ?
                countdowm + 's' : 'Enviar' }}
            </AppButton>
          </div>
        </template>
      </AppInput>
    </div> -->

    <div class="remember-items">
      <div class="remember" @click="isCheckd = !isCheckd">
        <div class="toggle">
          <AppImage
            :width="28"
            :src="`/img/login/${
              isCheckd ? 'remember-select.webp' : 'remember-not-select.webp'
            }`"
            alt=""
          />
        </div>
        <div class="text">Tenho mais de 18 anos, li e aceito</div>
      </div>
      <span @click.stop="goPrivacy">《Acordo de Usuário》</span>
    </div>

    <div class="submit">
      <AppButton
        @click="submit"
        :loading="registerLoading"
        fontSize="var(--app-px-24)"
        width="100%"
        height="70"
        green
        whiteText
        :radius="14"
      >
        Register
      </AppButton>
    </div>

    <div class="ant-row-flex-middle">
      <button type="button" @click="goKf()">Suporte</button>
      <!-- <button type="button">Jogar Grátis</button> -->
      <button type="button" @click="switchTabActiveType">Login Agora</button>
    </div>
    <!-- <AppImage class="close"  src="/icons/close_black.png.webp" @click="closePage" /> -->
  </div>
</template>

<style lang="scss" scoped>
.svg-icon {
  display: inline-block;
  width: var(--app-px-26);
  height: var(--app-px-26);
  background-color: var(--theme-primary-font-color);
  mask-image: url("/img/login/icon_reg.svg");
  -webkit-mask-image: url("/img/login/icon_reg.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
}

.info {
  font-size: 18px;
  color: var(--theme-text-color-lighten);
  margin-top: 2px;
  margin-bottom: 18px;
  line-height: 22px;
}
.ant-space-item {
  color: var(--theme-primary-font-color);
  font-size: 22px;
  margin: 5px 0 15px 0;
  display: flex;
  align-items: center;
  flex-direction: row;
  &.passwordStrength-0 .step {
    background-color: var(--theme-color-line);
  }
  &.passwordStrength-1 span:nth-child(2) {
    background-color: var(--theme-secondary-color-error);
  }
  &.passwordStrength-2 span:nth-child(2),
  &.passwordStrength-2 span:nth-child(3) {
    background-color: var(--theme-secondary-color-finance);
  }
  &.passwordStrength-3 .step {
    background-color: var(--theme-secondary-color-success);
  }
  .step {
    border-radius: 0.4rem;
    height: 0.5rem;
    width: 3rem;
    display: block;
    margin-left: 0.4rem;
    background-color: var(--theme-color-line);
  }
}
.ant-row-flex-middle {
  padding: 0 30px 20px 30px;
}

.remember-items,
.ant-row-flex-middle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22px;
  color: white;
  > button {
    background: transparent;
    color: var(--theme-primary-font-color);
    font-size: 22px;
    line-height: 50px;
  }
  .remember {
    display: flex;
    align-items: center;
    .text {
      padding-left: 0.2rem;
    }
  }
}
.remember-items {
  display: inline-block;
  vertical-align: middle;
  font-size: 21px;
  > span {
    display: inline-block;
    vertical-align: middle;
    color: var(--theme-primary-font-color);
    line-height: 50px;
  }
  .toggle {
    display: inline-block;
    vertical-align: middle;
  }
  .remember {
    display: inline-block;
    vertical-align: middle;
    .text {
      color: var(--theme-primary-font-color);
      display: inline-block;
      vertical-align: middle;
      padding-left: 0.2rem;
    }
  }
}
.tab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px 15px 12px;
    > span {
      padding-left: 10px;
      color: var(--theme-primary-font-color);
      font-size: 26px;
    }
  }
  .bottom-line {
    margin: 5px 0 20px 0;
    width: 300px;
    height: 6px;
    border-radius: 3px;
    background-color: var(--theme-primary-font-color);
  }
}

@import "../theme/mixin.scss";
.email,
.phone {
  // padding-top: 10px;
  padding-bottom: 40px;
}
.submit {
  padding-top: 10px;
  padding-bottom: 20px;
  button {
    margin: 0 auto;
  }
}
.ly {
  color: #fff;
  // padding: 36px 40px 25px 40px;
  // padding-top: 40px;
  display: flex;
  align-items: center;
  // justify-content: center;

  .radio {
    width: 40px;
    // height: 40px;
    border-radius: 50%;
    margin-right: 10px;

    img {
      width: 40px;
    }
  }

  .scale {
    font-size: 24px;
    line-height: 28px;
    color: var(--app-title-color);

    .privacy {
      // margin-left: 20px;
      // color: black;
      // text-decoration: underline;
    }
  }
}

.password {
  padding-bottom: 40px;
}

.code {
  padding-bottom: 50px;
}

.app-login {
  padding: 30px 27px 0 27px;
}

.login-img {
  text-align: center;
  padding-top: 40px;
  padding-bottom: 35px;
  // padding: 0 0 40px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .line {
    width: 1px;
    height: 72px;
    background: rgba(255, 255, 255, 0.25);
    margin: 0 36px;
  }

  img {
    width: 72px;
    height: 72px;
    vertical-align: middle;
  }

  .reg-tab {
    width: 229px;
    height: 70px;
    border-radius: 10px;
    background: #192841;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s;
    &:last-child {
      margin-left: 30px;
    }
    &.active {
      background: #679fea;
    }

    .email {
      width: 45px;
      height: 45px;
      // background: url() no-repeat center center;
      @include webp("/icons/icon_email_1.png");
      background-position: center center;
      background-size: 100% 100%;

      &.active {
        // background: url() no-repeat center center;
        @include webp("/icons/icon_email_1-active.png");
        background-position: center center;
        background-size: 100% 100%;
      }
    }

    .phone {
      width: 48px;
      height: 48px;
      // background: url() no-repeat center center;
      @include webp("/icons/icon_phone_1.png");
      background-position: center center;
      background-size: 100% 100%;

      &.active {
        // background: url() no-repeat center center;
        @include webp("/icons/icon_phone_1-active.png");
        background-size: 100% 100%;
        background-position: center center;
      }
    }
  }
}

.retrieve {
  width: 260px;
  height: 46px;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid #0ed1f4;
  color: #0ed1f4;
  text-align: center;
  font-size: 26px;
  line-height: 46px;
}

.close {
  display: block;
  margin: 0 auto;
  margin-top: 200px;
}
</style>
