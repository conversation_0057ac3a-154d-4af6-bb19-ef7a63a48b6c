<script setup lang="ts" name="promotion">
import Bonus from "~/pages/record-list/bonus.vue";
import AppTab from "./AppTab.vue";
//import { userInfo } from 'node:os';

const router = useRouter();
const appStore = useAppStore();
//appStore.setFooterDialogVisble(false)
const { isApp, userInfo } = storeToRefs(appStore);
const currentType = ref("0");
const nextLevelNeedRunning = ref(0);
const curProgress = ref(0);
const isCanBonus = ref(0);
const validWaged = ref(0);
const showVipTip = ref(false);
const SECLET_TYPE = readonly({
  Aumento: 0,
  Semanal: 1,
  Mensal: 2,
  Privilegio: 3,
});

const enum JumpViewType {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
}

const emits = defineEmits(["onclickVip"]);
//
const showAumento = ref(false);
const showSemanal = ref(false);
const showMensal = ref(false);
const showPrivilegio = ref(false);

function onclickHistory() {
  emits("onclickVip", JumpViewType.HISTORY);
}

const {
  run: runApiGetVipListData,
  data: vipdata,
  loading,
} = useRequest(() => ApiGetVipListData(), {
  onError: () => {},
  onSuccess: (data) => {
    // console.log(data)
    runGetUserVipInfo();
  },
});

const { run: runGetVipMemberGroupData, data: vipMemberGroup } = useRequest(
  ApiGetVipMemberGroupData,
  {
    manual: true,
    onSuccess: (data) => {
      isCanBonus.value = 0;
      showToast("Colete todo o sucesso");
    },
  }
);

//获取玩家VIP信息
const { run: runGetUserVipInfo, data: userVipInfo } = useRequest(
  ApiGetVipMemberInfoData,
  {
    manual: true,
    onSuccess(data) {
      appStore.setUserVipInfo(userVipInfo);
      validWaged.value = userVipInfo.value.validWaged;
      for (let i = 0; i < vipdata.value?.length; i++) {
        if (vipdata.value[i].level == userVipInfo.value.level) {
          isCanBonus.value = userVipInfo.value.canBonus;

          if (userVipInfo.value.level != 50) {
            nextLevelNeedRunning.value = vipdata.value[i + 1].needRunning;
            break;
          }
        }
      }
      //console.log(userVipInfo)
    },
  }
);

//
const secletType = ref(SECLET_TYPE.Aumento);

const vipData_privilegio = ref([
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
  { totalda: "llimitado", superiorda: "llimitado", caneta: "—" },
]);

const tabData = ref([
  {
    label: "Bônus De Aumento De Nível",
    value: SECLET_TYPE.Aumento,
  },
  {
    label: "Bônus Semanal",
    value: SECLET_TYPE.Semanal,
  },
  {
    label: "Bônus Mensal",
    value: SECLET_TYPE.Mensal,
  },
  {
    label: "Privilégio VIP",
    value: SECLET_TYPE.Privilegio,
  },
]);

const onTabChange = () => {
  showAumento.value = true;
  showSemanal.value = true;
  showMensal.value = true;
  showPrivilegio.value = true;
  if (secletType.value == SECLET_TYPE.Aumento) {
    showAumento.value = false;
  } else if (secletType.value == SECLET_TYPE.Semanal) {
    showSemanal.value = false;
  } else if (secletType.value == SECLET_TYPE.Mensal) {
    showMensal.value = false;
  } else if (secletType.value == SECLET_TYPE.Privilegio) {
    showPrivilegio.value = false;
  }
};

const banners = computed(() => {
  if (list.value && list.value.length) {
    return list.value.map((i) => {
      let images = i.images.split("&");
      let h5str = images.filter((m: any) => m.indexOf("h5=") != -1)[0];
      i.redirect_url = decodeURIComponent(decodeURIComponent(i.redirect_url));
      if (h5str) {
        i.h5img = h5str.split("=")[1];
      }
      return i;
    });
  }
  return [];
});

function imgClick(type: number, url: string) {
  console.log("imgClick =", type, url);
  if (!type || !url) return;
  switch (type) {
    case 1:
      router.push(url);
      break;
    case 2:
      window.open(url, "_blank");
      break;
  }
}

function showTip() {
  showVipTip = true;
  showToast(showVipTip);
}

function btnColetarClick() {
  runGetVipMemberGroupData();
}
</script>
<template>
  <div class="vip">
    <div class="vip-info">
      <!-- <div class="vip-info-atual">Nível Atual</div> -->

      <div class="vip-info-level">
        <span>{{ userVipInfo?.level || 0 }}</span>
      </div>
    </div>

    <div class="vip-card">
      <div class="vip-info-right">
        <button
          class="vip-info-right-up"
          :style="{
            cursor: isCanBonus == 0 ? 'not-allowed' : '',
            'pointer-events': isCanBonus == 0 ? 'none' : '',
            'background-color':
              isCanBonus == 0 ? '#999999' : 'var(--theme-primary-color)',
            color: isCanBonus == 0 ? 'white' : '#22674b',
          }"
          @click="btnColetarClick"
        >
          Resgatar<br />Tudo
        </button>
        <button class="vip-info-right-down" @click="onclickHistory">
          Histórico
        </button>
      </div>

      <div class="vip-info-label">
        <label class="vip-info-label-a">Restantes </label>
        <label class="vip-info-label-b"
          >VIP{{ userVipInfo?.level + 1 || 1 }}</label
        >
        <br />
        <label class="vip-info-label-a">Aposte Mais </label>
        <label class="vip-info-label-c">{{
          UsAmountFormat(nextLevelNeedRunning - validWaged)
        }}</label>
      </div>
    </div>
    <div class="vip-title">Lista De Níveis VIP</div>
    <div>
      <AppTab
        class="vip-tab"
        :list-data="tabData"
        v-model="secletType"
        @change="onTabChange"
      ></AppTab>
    </div>
    <div v-if="!showAumento" class="aumento">
      <div class="vip-info-title">
        <span class="vip-info-title-a">Nível</span>
        <span class="vip-info-title-b">Aposta Para Promoção</span>
        <div
          class="vip-info-title-d"
          @click="
            () => {
              showVipTip = true;
            }
          "
        >
          ?
        </div>
        <span class="vip-info-title-c">Bônus De Aumento De Nível</span>
      </div>
      <div style="height: calc(100vh - 330px); overflow: auto">
        <div
          class="vip-info-data"
          v-for="(data, index) in vipdata"
          :key="index"
          :style="{
            border:
              index == userVipInfo?.level
                ? 'thin solid var(--theme-primary-color)'
                : '',
          }"
        >
          <div v-if="index <= 10" class="vip-info-data-level">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 20" class="vip-info-data-level1">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 30" class="vip-info-data-level2">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 40" class="vip-info-data-level3">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 50" class="vip-info-data-level4">
            <span>{{ index }}</span>
          </div>
          <div
            v-if="index == userVipInfo?.level + 1"
            class="vip-info-data-promocao-up"
          >
            <span>{{
              data.needRunning !== 0 ? UsAmountFormat(data.needRunning) : "—"
            }}</span>
            <!-- <AppProgress
              :value="validWaged"
              :max="data.needRunning"
              :width="300"
              :height="18"
              :type="1"
              topcolor="#42b983"
              bgcolor="var(--theme-text-color-lighten)"
              class="vip-info-data-promocao-up-progress"
            >
            </AppProgress> -->
            <!-- <div class="value">
              <p>{{ UsAmountFormat(validWaged) }}</p>
              <p>/</p>
              <p>{{ UsAmountFormat(data.needRunning) }}</p>
            </div> -->
          </div>
          <div v-else>
            <div class="vip-info-data-promocao-down">
              <span>{{
                data.needRunning !== 0 ? UsAmountFormat(data.needRunning) : "—"
              }}</span>
            </div>
          </div>

          <div class="vip-info-data-bonus">
            <div>
              {{ data.upBonus !== 0 ? UsAmountFormat(data.upBonus) : "—" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="!showSemanal" class="semanaldata">
      <div class="vip-info-title">
        <span class="vip-info-title-a">Nível</span>
        <span class="vip-info-title-b">Bônus</span>
      </div>
      <div style="height: calc(100vh - 330px); overflow: auto">
        <div
          class="vip-info-semanaldata"
          v-for="(data, index) in vipdata"
          :key="index"
          :style="{
            border:
              index == userVipInfo?.level
                ? 'thin solid var(--theme-primary-color)'
                : '',
          }"
        >
          <div v-if="index <= 10" class="vip-info-semanaldata-level">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 20" class="vip-info-semanaldata-level1">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 30" class="vip-info-semanaldata-level2">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 40" class="vip-info-semanaldata-level3">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 50" class="vip-info-semanaldata-level4">
            <span>{{ index }}</span>
          </div>
          <div class="vip-info-semanaldata-bonus">
            <div>
              {{
                data.monthBonus !== 0 ? UsAmountFormat(data.monthBonus) : "—"
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="!showMensal" class="mensaldata">
      <div class="vip-info-title">
        <span class="vip-info-title-a">Nível</span>
        <span class="vip-info-title-b">Bônus</span>
      </div>
      <div style="height: calc(100vh - 330px); overflow: auto">
        <div
          class="vip-info-mensaldata"
          v-for="(data, index) in vipdata"
          :key="index"
          :style="{
            border:
              index == userVipInfo?.level
                ? 'thin solid var(--theme-primary-color)'
                : '',
          }"
        >
          <div v-if="index <= 10" class="vip-info-mensaldata-level">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 20" class="vip-info-mensaldata-level1">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 30" class="vip-info-mensaldata-level2">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 40" class="vip-info-mensaldata-level3">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 50" class="vip-info-mensaldata-level4">
            <span>{{ index }}</span>
          </div>
          <div class="vip-info-mensaldata-bonus">
            <div>
              {{ data.weekBonus !== 0 ? UsAmountFormat(data.weekBonus) : "—" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="!showPrivilegio" class="privilegio">
      <div class="vip-info-title">
        <span class="vip-info-title-a">Nível</span>
        <span class="vip-info-title-b">O Valor Total Da Retirada Diária</span>
        <span class="vip-info-title-c"
          >O Limite Superior Da Retirada Diária</span
        >
        <span class="vip-info-title-d">Taxas De Isencão Diária Caneta</span>
      </div>
      <div style="height: calc(100vh - 330px); overflow: auto">
        <div
          class="vip-info-privilegio"
          v-for="(data, index) in vipData_privilegio"
          :key="index"
          :style="{
            border:
              index == userVipInfo?.level
                ? 'thin solid var(--theme-primary-color)'
                : '',
          }"
        >
          <div v-if="index <= 10" class="vip-info-privilegio-level">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 20" class="vip-info-privilegio-level1">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 30" class="vip-info-privilegio-level2">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 40" class="vip-info-privilegio-level3">
            <span>{{ index }}</span>
          </div>
          <div v-else-if="index <= 50" class="vip-info-privilegio-level4">
            <span>{{ index }}</span>
          </div>

          <div class="vip-info-privilegio-totalda">
            <div>{{ data.totalda }}</div>
          </div>
          <div class="vip-info-privilegio-superiorda">
            <div>{{ data.superiorda }}</div>
          </div>
          <div class="vip-info-privilegio-caneta">
            <div>{{ data.caneta }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="line"></div>
    <div class="vip-instruction-title">Instruções Sobre Regras VIP</div>
    <div class="line2"></div>
    <div class="vip-instruction-rect">
      <div class="vip-instruction-content">
        1.Padrão de promoção: atenda aos requisitos da promoção VIP (ou seja, a
        recarga ou apostas eficazes podem atender às condições), você pode
        avançar para o nível VIP correspondente e obter o bônus de promoção
        correspondente.O bônus pode ser recebido de tempos em tempos;
      </div>
      <div class="vip-instruction-content">
        2.Salário diário: Se a recarga diária e as apostas válidas atenderem aos
        requisitos salariais diários do nível atual, você poderá obter o bônus
        salarial diário correspondente. Se você avançar para vários níveis
        consecutivos, só poderá obter o bônus salarial diário do atual nível.O
        bônus pode ser recebido de tempos em tempos;
      </div>
      <div class="vip-instruction-content">
        3.Salário Semanal: Se a recarga semanal e as apostas válidas atenderem
        ao nível atual de requisitos salariais semanais, você poderá obter o
        bônus salarial semanal correspondente. Se você avançar para vários
        níveis consecutivos, poderá obter apenas o nível atual de bônus salarial
        semanal.O bônus pode ser recebido de tempos em tempos;
      </div>
      <div class="vip-instruction-content">
        4.Lulu mensal: recarga mensal e apostas efetivas para atender ao nível
        atual do Lulu mensal, e você pode obter o bônus de prêmio mensal
        correspondente.O bônus pode ser recebido de tempos em tempos;
      </div>
      <div class="vip-instruction-content">
        5.Tempo de Expiração da Recompensa: O bônus recebido fica disponível por
        %d dias. Se não for resgatado ativamente durante esse período, ele será
        automaticamente creditado na conta. Por exemplo: se uma recompensa for
        obtida em 1º de janeiro e retida por %d dias, será automaticamente
        creditada na conta em %d de janeiro às 00:00:00.
      </div>
      <div class="vip-instruction-content">
        6.Instruções para auditoria: o bônus VIP oferecido pode ser levantado
        apenas após o cumprimento do requisito de rollover 1x (ou seja,
        auditoria, apostas ou apostas válidas), independentemente da plataforma
        em que joga;
      </div>
      <div class="vip-instruction-content">
        7.Declarações: Esta função está limitada às operações normais dos
        titulares de contas. É proibido alugar contas, efetuar apostas sem risco
        (apostas com contas diferentes, swiping mútuo e swiping de odds baixas),
        arbitragem viciosa, utilizar plug-ins, robôs, exploração de acordos,
        lacunas, interfaces, controlo de grupo ou outros meios técnicos de
        participação; caso contrário, uma vez provado, a plataforma tem o
        direito de proibir os membros de iniciar sessão, suspender a utilização
        do nosso website, e confiscar o bônus e os ganhos indevidos, sem
        qualquer aviso especial;
      </div>
      <div class="vip-instruction-content">
        8.Instruções: Ao reclamar o bônus VIP, considera-se que os membros
        aceitam e cumprem as regras correspondentes. A fim de evitar diferenças
        na compreensão do texto, a plataforma reserva o direito final de
        interpretar esta atividade.
      </div>
    </div>

    <van-popup class="tip-poup" v-model:show="showVipTip" round>
      <div class="rect">
        <div class="tip">
          <span>Lembrete</span>
        </div>
        <div class="content">
          Para avançar para o próximo nível, é necessário fazer apostas
          adicionais além do total acumulado de apostas válidas. Por exemplo: se
          o requisito de apostas válidas para avançar para o VIP1 é de 1000, e o
          requisito para avançar para o VIP2 é de 2000, então o membro precisa
          acumular um total de 1000 + 2000 = 3000 apostas válidas para avançar
          para o VIP2, e assim por diante.
        </div>
      </div>
      <AppImage
        class="close-btn"
        src="/img/musicPlayer/music_close.webp"
        alt=""
        @click="
          () => {
            showVipTip = false;
          }
        "
      />
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";
.vip-card {
  position: absolute;
  background: linear-gradient(143deg, #612bf7b6 20%, #09720586 80%);
  border-radius: 20px;
  left: 50%; 
  transform: translateX(-50%);
  width: 88%;
  height: 300px;
  top: 400px;
  .vip-info-label{
    position: absolute;
    font-size: 50px;
    color: white;
    top: 30px;
    left: 30px;
    line-height: 2;
    
    &-b{
      color: #fcff5f;
    }
    &-c{
      color: #fcff5f;
    }
  }
}
.vip {
  // background-color: var(--theme-bg-color);
  background-image: url("/img/index/main_bg.webp");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.vip-instruction-title {
  position: relative;
  color: var(--theme-text-color);
  font-size: 30px;
  padding: 0px 10px;
  //margin-top: 25px;
  margin-bottom: 16px;
  height: 100%;
  text-indent: 1em;
}

.vip-instruction-rect {
  //width: 100%;
  //height: 100%;
  padding-bottom: 120px;
  background-color: #f0f1fc;
  .vip-instruction-content {
    position: relative;
    width: 680px;
    margin-left: 30px;
    padding: 0px 0 0 10px;
    //text-indent: 2em;
    color: var(--theme-text-color);
    font-size: 21px;
    line-height: 1.6;
  }
}

.vip-info {
  margin: 0px 25px;
  top: 18px;
  height: 88vw;
  border-radius: 15px;

  margin-bottom: 55px;

  // background-color: var(--theme-main-bg-color);
  background-image: url("/img/vipImg/vipv2_top_bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;

  &-atual {
    width: 95px;
    height: 30px;
    padding: 4px 4px;
    border-radius: 8px 8px 8px 0;
    background-color: var(--theme-secondary-color-error);
    color: white;
    font-size: 17px;
  }

  &-level {
    width: 100px;
    height: 100px;
    @include webp("/img/user/icon-vip-round");
    background-size: 100%;
    position: absolute;
    top: 20vw;
    // left: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: #e2b54a;
    padding: 26px 20px;
    font-size: 38px;
  }

  &-label {
    position: absolute;
    left: 140px;
    top: 65px;
    color: var(--theme-text-color-lighten);
    font-size: 23px;

    &-b {
      color: var(--theme-secondary-color-error);
      font-style: italic;
      font-weight: bold;
    }

    &-c {
      color: var(--theme-text-color);
      font-weight: bold;
    }
  }

  &-right {
    position: absolute;
    right: 32px;
    top: 32px;
    width: auto;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 14px;
    z-index: 2;

    .vip-info-right-up {
      width: 128px;
      height: 48px;
      border-radius: 16px;
      font-size: 18px;
      font-weight: 700;
      border: none;
      background: linear-gradient(90deg, #7f5fff 0%, #32e6b7 100%);
      color: #fff;
      box-shadow: 0 4px 16px 0 rgba(127,95,255,0.12);
      transition: all 0.18s;
      cursor: pointer;
      margin-bottom: 0;
      letter-spacing: 1px;
      line-height: 1.2;
      &:hover:enabled {
        background: linear-gradient(90deg, #32e6b7 0%, #7f5fff 100%);
        box-shadow: 0 6px 24px 0 rgba(127,95,255,0.18);
        filter: brightness(1.08);
      }
      &:disabled, &[style*='not-allowed'] {
        background: #bdbdbd !important;
        color: #fff !important;
        cursor: not-allowed;
        opacity: 0.7;
        box-shadow: none;
      }
    }
    .vip-info-right-down {
      width: 128px;
      height: 44px;
      border-radius: 16px;
      font-size: 16px;
      font-weight: 600;
      border: 1.5px solid #7f5fff;
      background: #fff;
      color: #7f5fff;
      box-shadow: 0 2px 8px 0 rgba(127,95,255,0.08);
      transition: all 0.18s;
      cursor: pointer;
      margin-bottom: 0;
      letter-spacing: 1px;
      &:hover {
        background: #f3f0ff;
        color: #32e6b7;
        border-color: #32e6b7;
      }
    }
  }
}

.privilegio {
  height: 100%;
  font-size: 22px;

  .vip-info-title {
    position: relative;
    margin: 20px 25px 0 25px;
    height: 100px;
    border-radius: 15px;
    border: 1px solid;
    border-color: var(--theme-color-line);
    color: white;
    font-size: 20px;

    &-a {
      position: relative;
      left: 30px;
      top: 40px;
    }

    &-b {
      width: 180px;
      position: absolute;
      left: 130px;
      top: 30px;
      text-align: center;
    }

    &-c {
      width: 155px;
      position: absolute;
      left: 340px;
      top: 20px;
      text-align: center;
    }

    &-d {
      width: 150px;
      position: absolute;
      left: 520px;
      top: 20px;
      text-align: center;
    }
  }

  .vip-info-privilegio {
    position: relative;
    width: 700px;
    height: 100px;
    border-radius: 15px;
    margin: 5px 25px;
    font-size: 18px;

    &-level {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round0");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level1 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round1");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level2 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round2");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level3 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round3");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level4 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round4");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-totalda {
      width: 200px;
      position: absolute;
      color: var(--theme-text-color-lighten);
      left: 120px;
      padding: 40px 0px;
      text-align: center;
    }

    &-superiorda {
      width: 200px;
      position: absolute;
      color: var(--theme-text-color-lighten);
      left: 320px;
      padding: 40px 0px;
      text-align: center;
    }

    &-caneta {
      width: 200px;
      position: absolute;
      color: var(--theme-text-color-lighten);
      left: 500px;
      padding: 40px 0px;
      text-align: center;
    }

    &:nth-child(2n-1) {
      background-color: var(--theme-main-bg-color);
    }
  }
}

.mensaldata {
  height: 100%;
  font-size: 22px;

  .vip-info-title {
    position: relative;
    margin: 20px 25px 0 25px;
    height: 100px;
    border-radius: 15px;
    border: 1px solid;
    border-color: var(--theme-color-line);
    color: white;
    font-size: 20px;

    &-a {
      position: relative;
      left: 30px;
      top: 40px;
    }

    &-b {
      position: relative;
      left: 320px;
      top: 40px;
    }
  }

  .vip-info-mensaldata {
    position: relative;
    width: 700px;
    height: 100px;
    border-radius: 15px;
    margin: 5px 25px;

    &-level {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round0");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level1 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round1");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level2 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round2");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level3 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round3");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level4 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round4");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-bonus {
      width: 200px;
      position: absolute;
      color: var(--theme-secondary-color-finance);
      left: 300px;
      padding: 40px 0px;
      text-align: center;
      font-size: 18px;
    }

    &:nth-child(2n-1) {
      background-color: var(--theme-main-bg-color);
    }
  }
}

.semanaldata {
  height: 100%;
  font-size: 22px;

  .vip-info-title {
    position: relative;
    margin: 20px 25px 0 25px;
    height: 100px;
    border-radius: 15px;
    border: 1px solid;
    border-color: var(--theme-color-line);
    color: white;
    font-size: 20px;

    &-a {
      position: relative;
      left: 30px;
      top: 40px;
    }

    &-b {
      position: relative;
      left: 320px;
      top: 40px;
    }
  }

  .vip-info-semanaldata {
    position: relative;
    width: 700px;
    height: 100px;
    border-radius: 15px;
    margin: 5px 25px;

    &-level {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round0");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level1 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round1");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level2 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round2");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level3 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round3");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level4 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round4");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-bonus {
      width: 200px;
      position: absolute;
      color: var(--theme-secondary-color-finance);
      left: 300px;
      padding: 40px 0px;
      text-align: center;
      font-size: 18px;
    }

    &:nth-child(2n-1) {
      background-color: var(--theme-main-bg-color);
    }
  }
}

.aumento {
  height: 100%;
  font-size: 25px;

  .vip-info-title {
    position: relative;
    margin: 20px 25px 0 25px;
    height: 100px;
    border-radius: 15px;
    border: 1px solid;
    border-color: var(--theme-color-line);
    color: white;
    font-size: 20px;

    &-a {
      position: relative;
      left: 30px;
      top: 40px;
      color: var(--theme-text-color);
    }

    &-b {
      position: relative;
      left: 100px;
      top: 40px;
      color: var(--theme-text-color);
    }

    &-c {
      position: absolute;
      text-align: center;
      width: 230px;
      left: 430px;
      top: 30px;
      color: var(--theme-text-color);
    }

    &-d {
      position: relative;
      left: 360px;
      top: 16px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      text-align: center;
      color: var(--theme-primary-font-color);
      font-size: 15px;
      background: var(--theme-primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .vip-info-data {
    position: relative;
    width: 700px;
    height: 100px;
    border-radius: 15px;
    margin: 5px 25px;

    &-level {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round0");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level1 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round1");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level2 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round2");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level3 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round3");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-level4 {
      width: 70px;
      height: 70px;
      @include webp("/img/user/icon-vip-round4");
      background-size: 100%;
      position: absolute;
      left: 20px;
      text-align: center;
      color: #e2b54a;
      padding: 19px 0px;
      font-size: 30px;
      top: 16px;
    }

    &-promocao-down {
      width: 300px;
      position: absolute;
      color: var(--theme-text-color-lighten);
      left: 100px;
      padding: 40px 50px;
      text-align: center;
      font-size: 18px;
    }

    &-promocao-up {
      width: 300px;
      position: absolute;
      color: var(--theme-text-color-lighten);
      left: 100px;
      padding: 25px 50px;
      text-align: center;
      font-size: 18px;

      &-progress {
        position: absolute;
        top: 60px;
        left: 10px;

        &-promocao-up1 {
          position: absolute;
          width: 300px;
          height: 90px;
          background-color: #dda510;
          text-align: center;
        }
      }
      .value {
        position: relative;
        height: 18px;
        //text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 15px -50px;
      }
      p {
        position: relative;
        font-size: 18px;
        color: white;
      }
    }

    &-bonus {
      width: 200px;
      position: absolute;
      color: var(--theme-secondary-color-finance);
      left: 450px;
      padding: 40px 0px;
      text-align: center;
      font-size: 18px;
    }

    &:nth-child(2n-1) {
      background-color: var(--theme-main-bg-color);
    }
  }
}

.vip-title {
  position: relative;
  color: var(--theme-text-color);
  padding: 5px 25px;
  font-size: 29px;
  margin-bottom: 18px;
  //background-color: #e2b54a;
}

.vip-tab {
  border-bottom: thin solid var(--theme-color-line);
  ::v-deep .active {
    color: var(--theme-primary-color) !important; // 你想要的颜色
  }
}
.line {
  position: relative;
  width: 95%;
  padding: 0 10px;
  height: 1px;
  left: 20px;
  background-color: var(--theme-color-line);
  margin-bottom: 35px;
}

.line2 {
  position: relative;
  width: 95%;
  padding: 0 10px;
  height: 1px;
  left: 20px;
  background-color: var(--theme-color-line);
  margin-bottom: 25px;
}

.tip-poup {
  width: 600px;
  height: 600px;
  display: flex;
  align-items: center;
  //flex-direction: column;
  position: absolute;
  justify-content: center;

  .rect {
    width: 550px;
    height: 400px;
    border-radius: 15px;
    background-color: var(--theme-main-bg-color);
    border: 2px var(--theme-text-color-lighten) solid;
  }

  .tip {
    padding-top: 30px;
    font-size: 24px;
    color: var(--theme-text-color-lighten);
    //          display: block;
    text-align: center;
  }

  .content {
    //width: 100%;
    height: 180px;
    position: relative;
    margin: 20px 40px;
    text-align: center;
    font-size: 21px;
    line-height: 30px;
    color: white;
  }

  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    top: 520px;
  }
}
</style>
