import { createApp } from 'vue'
import { setToastDefaultOptions, Lazyload, Locale ,Loading } from 'vant'
import router from '~/router/index'
import App from './App.vue'
import i18n from './lang'
import plugins from './utils/plugins'


import Vue3Lottie from 'vue3-lottie';//动画插件

 
import VueLuckyCanvas from '@lucky-canvas/vue'
import 'normalize.css'
import 'vant/lib/index.css';
import './theme/index.scss'
import './styles/transition.css' // 引入全局过渡动画样式
// 引入英文语言包
import ptBR from 'vant/es/locale/lang/pt-BR';

Locale.use('pt-BR', ptBR);

setHtmlPictureCompatilble()
setUtilsConfig({
  timezone: 'America/Sao_Paulo',
  // format: 'DD.MM HH:mm',
  format: 'YYYY-MM-DD HH:mm:ss', // 不知道到底用哪一种
})
setToastDefaultOptions({ duration: 2000, className: 'cg-toast' });

const app = createApp(App)

app.use(Lazyload).use(i18n).use(createPinia()).use(router).use(plugins).use(Loading)
app.use(VueLuckyCanvas)
app.use(Vue3Lottie, { name: 'Vue3Lottie' });
app.mount('#app')
