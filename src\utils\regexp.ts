export function useRegExpUserName() {
  const appStore = useAppStore();
  const { language } = storeToRefs(appStore);
  const reg = computed(() => {
    switch (language.value) {
      case Languages.EN_US:
        return /^[a-z]{1,9}$/;
      case Languages.PT_BR:
        return /^[0-9]{1,9}$/;
      default:
        return /./;
    }
  })
  h
  return {
    userNameReg: reg
  }
}

export function useRegExpPhone() {
  const appStore = useAppStore();
  const { language } = storeToRefs(appStore);
  const reg = computed(() => {
    switch (language.value) {
      case Languages.EN_US:
        return /^[0-9]{10,11}$/;
      case Languages.PT_BR:
        // return /^[0-9]{11,13}$/;
        //列举所有区号的方法
        // return /^(11|12|13|14|15|16|17|18|19|21|22|24|27|28|31|32|33|34|35|37|38|41|42|43|44|45|46|47|48|49|51|53|54|55|61|62|63|64|65|66|67|68|69|71|73|74|75|77|79|81|82|83|84|85|86|87|88|89|91|92|93|94|95|96|97|98|99)[0-9]{9,11}$/
        //排除部分区号的方法
        return /^(?!(0[0-9]|10|20|23|26|29|30|36|39|40|50|52|56|57|58|59|60|70|76|78|80|90))[0-9]{11,13}$/
      default:
        return /./;
    }
  })
  return {
    phoneReg: reg
  }
}

// 邮箱
export const emailReg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

// 密码(6-16位字母、数字)
export const passwordReg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/
//只需要数字和字符
export const passwordReg2 = /^[0-9A-Za-z]+$/
// 密码(6-16位字母、数字、特殊符号)
export const passwordReg3 = /^(?![0-9]+$)(?![a-zA-Z]+$)[\s\S]{6,16}$/
// 密码(6-12)
export const passwordReg4 = /^.{6,16}$/
// 支付密码(6位纯数字)
export const payPasswordReg = /^[0-9]{6}$/

// 验证码(4位数字)
export const verifyCodeReg = /^[0-9]{4}$/


// 4-16位数字或英文
export const usernameExpReg = /^[A-Za-z0-9]{4,16}$/

// 0-11位数字或英文
export const pixExpReg = /^[0-9.\-]{0,16}$/

export const cnpjExpReg = /^[0-9./\-]{18,20}$/

// 64位英文字符
// export const realnameExpReg= /^[a-zA-Z]{2,64}$/
export const realnameExpReg= /^[a-zA-Z\s]{0,100}$/


