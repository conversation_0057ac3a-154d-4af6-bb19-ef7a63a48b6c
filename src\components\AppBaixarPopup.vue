<script lang="ts" setup name="AppBaixarPopup">
const router = useRouter()
const appStore = useAppStore();
const { isShowAppBaixarPopup } = storeToRefs(appStore);

const close = () => {

    appStore.setAppBaixarPopupVisble(false)

}


</script>

<template>

    <van-popup class="App-Baixar-Popup" v-model:show="isShowAppBaixarPopup" teleport="body" round
        :style="{ width: '730px' }" :close-on-click-overlay="true">
        <div class="content">
            <div class="content-body">
                <div class="content-body-imgs">
                    <AppImage src="/img/index/appIconBig.webp" class="content-body-img"/>
                </div>
            </div>
        </div>
    </van-popup>
</template>

<style lang="scss" scoped>
.App-Baixar-Popup {
    max-width: 100%
}

.content {
    // width:710px;
    // height: 1000px;
    background-color: var(--theme-main-bg-color);
    border: 1px solid var(--theme-color-line);
    border-radius: 20px;
    width: 600px;
    height: 311px;
    margin: 0 auto;

    .content-body {
        width: 596px;
        height: 307px;
        padding: 30px 20px 20px;
    }

    .content-body-imgs {
        display: flex;
        align-items: center;
        width: 556px;
        height: 120px;

    }

    .content-body-img {
        width: 120px;
        height: 120px;
    }
}
</style>
