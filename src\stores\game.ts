import { GameNavEnum } from '~/types/common'

export const useGameStore = defineStore('game', () => {
  const gameItemPopupVisible = ref(false)
  const gameItemData = ref()
  
  // 查询游戏列表时点击场馆
  const queryGamePlat = ref<any>({
    id: '0',
    value: '0'
  })

  const gameNavData = computed<any[]>(() => {
    if (gameNavInit.value) {
      const arr = Object.keys(gameNavInit.value).map(i => +i)
      return <any[]>[
      {
        name: 'Todos',
        cateName: 'Todos',
        id: GameNavEnum.All,
        path: '/'
      }, {
        name: 'Popular',   //热门
        cateName: 'Popular',
        id: GameNavEnum.Quente,
        path: '/popular'
      },
      {
        name: 'Provedor do jogo',   //三方平台
        cateName: 'Provedor do jogo',
        id: GameNavEnum.Platform,
        path: '/popular'
      },
      // {
      //   name: 'Slots',    //老虎机分类
      //   cateName: 'Slots',
      //   id: GameNavEnum.Slots,
      //   path: '/Slots'
      // }
      {
        name: 'Slots',    //老虎
        cateName: 'Slots',
        id: GameNavEnum.Slot,
      }
    ].concat(<any[]>[
      {
        name: 'Pescaria',  //捕鱼
        cateName: 'Pescaria',
        id: GameNavEnum.Pesca
      }, {
        name: 'Ao_Vivo',  //视讯
        cateName: 'Casino Ao Vivo',
        id: GameNavEnum.Ao_Vivo
      }, {
        name: 'Bingo',   //bongo游戏
        cateName: 'Bingo',
        id: GameNavEnum.Bingo
      }, {
        name: 'Jogos de vídeo',  //接机
        cateName: 'Jogos de vídeo',
        id: GameNavEnum.Jogos_de_video
      }

      //   name: 'Dentro De Casa', //内部游戏
      //   id: GameNavEnum.Dentro_De_Casa,
      //   path: '/rec-fav-game'
      // }, {
      //   name: 'Pôquer',
      //   id: GameNavEnum.Pôquer
      // }, {
      //   name: 'Esporte',
      //   id: GameNavEnum.Esporte
      // }, {
      //   name: 'Ao Vivo',
      //   id: GameNavEnum.Ao_Vivo
      // }
      ].filter(i => arr.indexOf(i.id) !== -1))
    }
    return []
  })

  const { run: runGetGameNavInit, data: gameNavInit } = useRequest(ApiGameNav)

  const setQueryGamePlat = (item: any) => {
    queryGamePlat.value = item
  }

  // 当前选中的游戏筛选标签
  const queryGameTag = ref('0')
  const setQueryGameTag = (v: string) => {
    queryGameTag.value = v
  }


  //弹出显示进入游戏
  const setGameItemPopupVisible = (visible: boolean, data?: any) => {
    if (visible) {
      gameItemData.value = data
    } else {
      // gameItemData.value = undefined
    }
    gameItemPopupVisible.value = visible
  }

  const needResizeGameTypeTab = ref()
  const setNeedResizeGameTypeTab = (item: any) => {
    needResizeGameTypeTab.value = item
  }

  return {
    runGetGameNavInit,
    gameNavData,
    gameNavInit,
    queryGamePlat,
    queryGameTag,
    setQueryGamePlat,
    setQueryGameTag,
    gameItemPopupVisible,
    gameItemData,
    setGameItemPopupVisible,
    needResizeGameTypeTab,
    setNeedResizeGameTypeTab,
  }
})
