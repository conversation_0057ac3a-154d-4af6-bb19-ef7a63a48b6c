<script setup lang="ts" name="app-game-tab">
const labelNode = ref();
let moneyTestMin = 0;
let moneyTestMax = 13582665.0;

//事件监听
onMounted(() => {
  console.log("appGameMarquee加载");

  setTimeout(() => {
    startRun();
  }, 1000);
});

onBeforeUnmount(() => {
  console.log("appGameMarquee加载44");
});

const gotoGame = () => {
  console.log("跳转进游戏");
};

const startRun = () => {
  const intervalId = setInterval(() => {
    if (labelNode && labelNode.value) {
      if (moneyTestMin > moneyTestMax) {
        clearInterval(intervalId); //取消定时器
        return;
      }
      let temp = moneyTestMax - moneyTestMin;
      let randData = 0;
      // if(temp>1000){
      randData = temp / 10;
      // }else if(temp>300){
      //   randData = temp/10
      // }else {
      //   randData = temp/10
      // }

      moneyTestMin += randData;
      labelNode.value.innerHTML = transf(moneyTestMin);
    } else {
      clearInterval(intervalId); //取消定时器
      console.log("卸载=====setInterval");
    }
  }, 200);
};

//数字转换
const transf = (value: number, number = 2) => {
  if (typeof value !== "number") {
    return 0;
  }

  let num = parseFloat(value).toFixed(number);

  if (!/^([+\-])?(\d+)(\.\d+)?$/.test(num)) {
    alert("wrong!");
    return num;
  }

  let a = RegExp.$1;
  let b = RegExp.$2;
  let c = RegExp.$3;
  let re = new RegExp().compile("(\\d)(\\d{3})(,|$)");
  while (re.test(b)) b = b.replace(re, "$1,$2$3");

  b = b.replace(new RegExp(",", "g"), ".");
  c = c.replace(".", ",");
  return a + "" + b + "" + c;
};
</script>

<template>
  <div class="content">
    <div class="center" @click="gotoGame">
      <div class="jackpot-info">
        <div class="title">Ganhe até um milhão por mês</div>
        <div class="amount" ref="labelNode">52.370.399,91</div>
        <div class="bottom-info">
          <span class="text-1">Recebido: 0,00</span>
          <span class="text-2">Ganhe 1 milhão por mês</span>
          <span class="text-3">Total: Distância 1</span>
          <span class="text-4">Montante fixo: 20.000,00</span>
        </div>
        <div class="invite">Convidar: 0 jogador</div>
        <div class="price">10,00</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";
.content {
  width: 100%;
  height: 168px;
  margin-bottom: 3.333333vw;
  margin-top: 15px;
}

.center {
  margin: 0 auto;
  width: 720px;
  height: 180px;
  background: url("https://rcf-img-usa.img2048east007en07.com/202503/79b41d54-500a-4304-bbff-58d1677c036c.gif");
  background-size: 100% 100%;
  margin-top: 0px;
  position: relative;
}

.jackpot-info {
  position: relative;
  width: 100%;
  height: 100%;

  .title {
    color: #eb0909;
    font-size: 18px;
    text-align: center;
    padding-top: 2.8vw;
    font-weight: bold;
  }

  .amount {
    color: #ffffff;
    font-size: 3.2vw;
    text-align: center;
    font-weight: bold;
    margin-top: 1.9vw;
    font-style: italic;
    letter-spacing: 2px;
  }

  .bottom-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 53%;
    transform: translate(-50%, -50%);
    gap: 0.3vw;
    font-size: 12px;
    color: #fff;
    margin-top: 5.333333vw;

    span {
      white-space: nowrap;
    }

    .text-1 {
      color: #fe0;
      text-shadow: red 0px 0.03rem 0.06rem;
      line-height: 1.4;
      font-weight: 700;
      line-height: 0.27rem;
      font-size: 18px !important;
    }
    .text-2 {
      text-shadow: red 0px 0.03rem 0.06rem;
      line-height: 1.4;
      // margin-bottom: 20px;
      margin-top: 10px;
      font-weight: 700;
    }

    .text-3 {
      text-shadow: red 0px 0.03rem 0.06rem;
      line-height: 1.4;
      font-weight: 700;
    }

    .text-4 {
      text-shadow: red 0px 0.03rem 0.06rem;
      line-height: 1.4;
      font-weight: 700;
    }
  }

  .invite {
    // position: absolute;
    // right: 20px;
    // bottom: 20px;
    // color: #ff0000;
    // font-size: 14px;
    // font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 23%;
    line-height: 2.597107vw;
    min-height: 3.3vw;
    font-size: 1.78vw;
    color: rgb(222, 23, 23);
    word-break: break-word;
    text-align: center;
    position: absolute;
    right: 2.2vw;
    bottom: -0.771015vw;
    background-image: url("/img/img_dt_bg_hd2.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    animation: scaleAnimation 3s ease-in-out infinite;

    // animation-name: identifier-H2eLP;
    // animation-duration: 3s;
    // animation-timing-function: linear;
    // animation-iteration-count: infinite;
  }

  @keyframes scaleAnimation {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.3);
    }
    100% {
      transform: scale(1);
    }
  }

  .price {
    position: absolute;
    right: 8vw;
    top: 15.666667vw;
    color: #ffee00;
    font-size: 24px;
    font-weight: bold;
    &:after {
      content: "";
      display: block;
      width: 60px;
      height: 60px;
      background: rgba(255, 0, 0, 0.2);
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: -1;
    }
  }
}
</style>
