<script setup lang='ts' name='promotion'>
import { ProxyChildDetailInfo, ProxyChildDetailInfoParam } from "~/core/http/api"

const router = useRouter()
const isReceber = ref(false)
const isShowSelect = ref(false)

const ValidInviteCount = ref(0) //有效下级
const ChildCount= ref(0)   //直属下级人数
const ChildRunningReturn= ref(0) //流水返利
const VipLevel = ref(0) //vip
const isShowDetails = ref(false);
const isShowPersonalDetails = ref(false);
const swipeWidth = window.innerWidth;
const swipeHeight = window.innerHeight;
const showPopover = ref(false);
const selText = ref("Válido ou não")
const selectIndex = ref(0);
const inputForce = ref(false)
const question= ref()
const finished = ref(false);
const pageDatas = ref({
  pageIndex:0,
  pageCount:30,
});
const listTeamData = ref<ProxyChildDetailInfo[]>([]);
// let i=0
// for( i;i<15; i++){
//   const temp:ProxyChildDetailInfo={
//     username:'1112222',
//     totalWaged: 41144,//对父级的贡献
//     FirstDepositAmount: 4544444,//首冲金额
//     createdAt: 148851444,//绑定时间 
//   }

//   listTeamData.value.push(temp)
// }
const ValidInviteWagedReq = ref(0);
const ValidInviteMinDeposit = ref(0);
const showDetailsIndex = ref(-1);

const appStore = useAppStore();
const { userInfo } = storeToRefs(appStore)

const shareUrl = ref("")
const share=[{icon:"/icons/agent_share_Partilhar",name:"Partilhar",url:location.origin},
             {icon:"/icons/agent_share_Facebook",name:"Facebook",url:"https://m.facebook.com/"},  
             {icon:"/icons/agent_share_Instagram",name:"Instagram",url:"https://www.instagram.com/"},  
             {icon:"/icons/agent_share_Telegram",name:"Telegram",url:"https://t.me/telegram"},  
             {icon:"/icons/agent_share_WhatsApp",name:"WhatsApp",url:"https://api.whatsapp.com/"},  
             {icon:"/icons/agent_share_TikTok",name:"TikTok",url:"https://vt.tiktok.com/"},  
             {icon:"/icons/agent_share_Kwai",name:"Kwai",url:"https://www.kwai.com/"},  
             {icon:"/icons/agent_share_Line",name:"Line",url:"https://line.me/"},]

const li = computed(()=>{
  if(userInfo.value.uid && userInfo.value.uid.length < 9){
    return [ location.origin + '/register' + `?id=${userInfo.value.uid}` + '&currency=BRL&type=2' ]
  }else{
    return [""]
  }
 
})

let actions = [{ text: "Válido ou não", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Eficiente", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Inválido", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },]

const popoverStatus = (isOpen: boolean) => {
    if(!isOpen) {
        actions = [{ text: "Válido ou não", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Eficiente", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Inválido", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },]
    }   
}

const onSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selText.value = action.text;
   
    selectIndex.value = Number(action.calssName)
    pageDatas.value.pageIndex=0
    refresh();
    // pageDatas.value.pageIndex=1
}

const eventFocus=()=>{
  inputForce.value = true
}

const eventBlur=()=>{
  inputForce.value = false
}

const onConfirmID = ()=>{
  console.log("++++++++++++++++")
  // showSelectTime(false)
  pageDatas.value.pageIndex=0
  refresh();
  // pageDatas.value.pageIndex=1
}

const refresh = () => {
  pageDatas.value.pageIndex=0;
  getQueryTeam();
}

const loadMoreData = () => {
  console.log("loadMoreData");
  if(pageDatas.value?.pageIndex == 0 || pageDatas.value?.pageIndex * pageDatas.value?.pageCount < queryTeamDatas.value?.TotalCount){
    getQueryTeam();
    pageDatas.value.pageIndex++
  }
}

const getQueryTeam = () => {
  if(!question.value){
    question.value = "";
  }
  console.log("getQueryTeam",pageDatas.value.pageIndex);
  if(pageDatas.value.pageIndex == 0){
    listTeamData.value=[]
  }
  runGetProxyChildDetailInfo({ pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount ,Username: question.value, QueryType: selectIndex.value})
}

function closeSelect(){
  isShowSelect.value =false
}

function openSelect(){
  isShowSelect.value =true
}

function liClick(item:any){
  shareUrl.value = li.value[0]
}

//复制分享地址
function copyUrl(){
  showToast('Copiado com sucesso')
  copy(li.value[0])
}
//复制id
function copyID(){
  showToast('Copiado com sucesso')
  copy(userInfo.value.uid || '')
}

function openShareUrl(item:any){
  window.open(item.url, '_blank')
}

//获取可领取数据
const {run: runQueryChildRunningReturnData,data:queryDatas } = useRequest(() => ApiQueryChildRunningReturnData(), {
  manual: true,
  onSuccess(res:any) {
    ChildCount.value = res.ChildCount
    ChildRunningReturn.value = res.ChildRunningReturn
    VipLevel.value = res.VipLevel
    isReceber.value = ChildRunningReturn.value>0
  }
})

const BOX_TYPE = readonly({
  Not_Achieved: "0",
  Receive: "1",
  Received: "2"
})

function getBoxList(row){
  let data = new Array();
  if (proxyInviteBonusDatas.value[(row - 1) * 4]){
    data.push(proxyInviteBonusDatas.value[(row - 1) * 4])
  }
  if (proxyInviteBonusDatas.value[(row - 1) * 4 + 1]){
    data.push(proxyInviteBonusDatas.value[(row - 1) * 4 + 1])
  }  
  if (proxyInviteBonusDatas.value[(row - 1) * 4 + 2]){
    data.push(proxyInviteBonusDatas.value[(row - 1) * 4 + 2])
  }  
  if (proxyInviteBonusDatas.value[(row - 1) * 4 + 3]){
    data.push(proxyInviteBonusDatas.value[(row - 1) * 4 + 3])
  }
  // let data = proxyInviteBonusDatas.value.slice((row - 1) * 4, 4)
  return data;
}

// 计算行类的方法
const getRowClass = (index) => {
  // S形排列，奇数行从左到右，偶数行从右到左
  const row = Math.ceil(index / 4); // 当前行数
  const isEvenRow = row % 2 === 0; // 是否偶数行
  const colStart = isEvenRow ? 3 : 0; // 起始列，偶数行从右向左开始
  const col = colStart - (index % 4); // 当前列

  // 返回行类，可以根据col来设置样式
  return `row-${row} col-${col}`;
};

// 计算宝箱的位置
const getChestsPosition = (row, col) => {
  const totalRows = Math.ceil(49 / 4);
  let position;
  if (row % 2 === 1) { // 奇数行
    position = (row - 1) * 4 + col; // 从左到右
  } else { // 偶数行
    position = (row - 1) * 4 + (3 - col); // 从右到左
  }
  return position; // 宝箱编号从1开始
};

// 计算宝箱的样式（如果需要）
const getChestsStyle = (row, col) => {
  // 根据需要添加样式逻辑
  return {};
};

const desStr = computed(() => {
    if(proxyInviteBonusDatas.value && proxyInviteBonusDatas.value.length > 0) {
        return 'R$'+proxyInviteBonusDatas.value[0].bonus;
    } else {
      return 'R$0';
    }
})

const desStr1 = computed(() => {
    if(proxyInviteBonusDatas.value && proxyInviteBonusDatas.value.length > 1) {
        return proxyInviteBonusDatas.value[proxyInviteBonusDatas.value.length - 1].bonus;
    } else {
      return null;
    }
})

onMounted(() => {
  runGetProxyInviteBonus();
  runGetValidInviteBonusInfo();
  runQueryChildRunningReturnData()
});

const boxItem = ref()
function getHight(){
  let height = 0;
  if (boxItem && boxItem.value){
    height =  -boxItem.value[0].offsetHeight * 0.4;
  }
  return height + 'px';
  // return boxItem;
}

function getWidth(number){
  let width = 0;
  if (boxItem && boxItem.value){
    width =  number * boxItem.value[0].offsetWidth * 0.53;
  }
  return width + 'px';
}

const {run: runGetProxyInviteBonus,data:proxyInviteBonusDatas } = useRequest(() => ApiGetProxyInviteBonus(), {
  manual: true,
  onSuccess(res:any) {
    // console.log("proxyInviteBonusDatas", res)
    console.log(proxyInviteBonusDatas.value)
    runGetPlatformLinkData();
  }
})

const {run: runGetPlatformLinkData,data:platformLinkData } = useRequest(() => ApiGetPlatformLinkData(), {
  manual: true,
  onSuccess(res:any) {
    console.log(platformLinkData)
  }
})
// runGetPlatformLinkData();
const {run: runGetValidInviteBonusInfo,data:ValidInviteBonusInfo } = useRequest(() => ApiGetValidInviteBonusInfo(), {
  manual: true,
  onSuccess(res:any) {
    binaryArray.value = convertToBinaryArray(ValidInviteBonusInfo.value.BonusStatus).reverse();
    ValidInviteCount.value = ValidInviteBonusInfo.value.ValidInviteCount;
    console.log(ValidInviteBonusInfo, binaryArray.value);
  }
})

const boxId = ref(-1);
const { run: runApiInviteBonus } = useRequest(() => ApiInviteBonus(boxId.value), {
    manual: true,
    onError: (data) => {
    },
    onSuccess: (data) => {
      if(data){
          appStore.runGetUserBalance()
          // appStore.setIsShowMessage(true,"Bônus +R$ "+ data)
          appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")

          console.log(data)
          runGetValidInviteBonusInfo();
        }
    }
})

const {run: runGetProxyChildDetailInfo,data:queryTeamDatas, loading:queryTeamLoading } = useRequest((datas:ProxyChildDetailInfoParam) => ApiGetProxyChildDetailInfoItem(datas), {
  manual: true,
  onSuccess(res:any) {
    // dadosAccuInfo.value = res?.TAccuInfo!;
    ValidInviteWagedReq.value = res?.ValidInviteWagedReq;
    ValidInviteMinDeposit.value = res?.ValidInviteMinDeposit;
    if(res?.ListData){
      listTeamData.value = [...listTeamData.value,...res?.ListData!]
    }

    if(listTeamData.value.length >= res?.TotalCount){
      finished.value = true;
    } else {
      finished.value = false;
    }
   
  },
  onError(data:any){
    showErrorTip(data)
  }
  
})

//按钮点击
const isRunAni = ref(false)
function boxClick(state, index){
  console.log(state, index);
  if(state == 1){
    boxId.value = index + 1;
    runApiInviteBonus()
    isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  }
}

function getChestsState(item, index) {
  // console.log(index);
  let state = 0;
  if (item.valid_Counts <= ValidInviteBonusInfo.value.ValidInviteCount){
    state = 1;
    if (index < binaryArray.value.length){
      if (binaryArray.value[index] == 1){
        state = 2;
      }
    }
  }
  return state;
  // return Math.floor(Math.random() * 3);
}

const binaryArray = ref([]);
const convertToBinaryArray = (num) => {
        const binary = [];
        while (num > 0) {
          binary.unshift(num % 2); // 取余数并添加到数组前端
          num = Math.floor(num / 2); // 除以2并向下取整
        }
        return binary;
};

function setItemState(item, state){
  item.newState = state;
}

function showDetails(){
  isShowDetails.value = true;
  document.documentElement.style.setProperty('--overflow-value', 'hidden');
}

function closeDetails(){
  isShowDetails.value = false;
  document.documentElement.style.setProperty('--overflow-value', 'auto');
}

</script>
<template>
  <div class="linkDeConvite">
      <!-- {{ swipeWidth  }}
      {{swipeHeight}} -->
      <div class="info">
          <div class="line"></div>
          <label class="modo">Informações da promoção</label>
          <!-- 二维码 -->
          <AppImage src="/icons/agent_qrcodebg" class="QRCodebg" />
          <AppImage src="/agent_qrcode" class="QRCode" />
          <div class="agent_bottom_right" >
            <label class="men">Meu Link</label>
            <label class="alt" @click="openSelect">Alterar</label>
            <div class="httpAddress" :class={active:isShowSelect}>
              <label class="test">{{li[0]}}</label>
              <AppImage src="/icons/agent_copy" class="copy" @click="copyUrl" />
            </div>
          </div>

          <div class="conviteNum">
            <label class="subord">Subordinados diretos</label>
            <label class="subord_num">{{ ChildCount }}</label>
            <br>
            <label class="convite">Código de Convite</label>
            <label class="convite_id">{{userInfo.uid || ''}}</label>
            <AppImage src="/icons/agent_copy" class="copy2" @click="copyID"/>
          </div>
        
          <div class="share">
            <div v-for=" item in share" class="item" @click="openShareUrl(item)">
              <AppImage :src="item.icon" class="itemImg" />
              <label class="itemName">{{item.name}}</label>
            </div>

          </div>
          <div class="detail">
            <label class="one">Subordinados válidos </label>
            <label class="two"> {{ ValidInviteCount }} </label>
            <label class="one"> pessoas</label>
            <label class="two" @click="showDetails"> Detalhes</label>
            <!-- <label class="two"> Detalhes</label> -->
          </div>
      </div>
      <div class="condition">

        <span>O que é um número válido promocional?(Cumprir todos os requisitos indicados abaixo)</span>
        
        <div>
          <div class="one" v-if="platformLinkData && platformLinkData.ValidInviteMinDeposit && platformLinkData.ValidInviteMinDeposit > 0">
            <div class="text1">{{platformLinkData.valid_invite_condition == 0?"Primeiro depósito subordinado":"Depósitos acumulados do subordinado"}}</div>
            <div class="text2">&nbsp{{platformLinkData.ValidInviteMinDeposit}} ou mais</div>
          </div>
          <div class="one" v-if="platformLinkData && platformLinkData.ValidInviteWagedReq && platformLinkData.ValidInviteWagedReq > 0">
            <div class="text1">Apostas acumuladas do subordinado</div>
            <div class="text2">&nbsp{{platformLinkData.ValidInviteWagedReq}} ou mais</div>
          </div>
        </div>
      </div>

      <div class="treasure-chest-container">
        <div v-if="proxyInviteBonusDatas?.length > 0 && ValidInviteBonusInfo">
        <!-- {{ console.log("-------------") }} -->

          <div class="row" v-for="row in 13" :key="row">
            <div v-for="(item, index) in getBoxList(row)" :key="index" class="treasure-chest" ref="boxItem" :style="getChestsStyle(row, index)">
              <!-- 宝箱 {{ getChestsPosition(row, index) }} -->
              <!-- <AppImage class="box-img" :src="`/img/promotion/detail/img_bx${item.state}`"> -->
                {{ setItemState(item, getChestsState(proxyInviteBonusDatas[getChestsPosition(row, index)], getChestsPosition(row, index))) }}
              <AppImage class="box-img" :src="`/img/promotion/detail/img_bx${item.newState}`" @click="boxClick(item.newState, getChestsPosition(row, index))">
              </AppImage>
              <div class="person">
                <div class="number">{{proxyInviteBonusDatas[getChestsPosition(row, index)].valid_Counts}} pessoas</div>
              </div>
              <div class="reward" :class="{active:item.newState == 1, active1:item.newState == 2}">{{ transf(proxyInviteBonusDatas[getChestsPosition(row, index)].bonus) }}</div>

              <div v-if="(row-1) * 4 + index < proxyInviteBonusDatas.length - 1">
                <div v-if="row % 2 === 1">
                  <div v-if="(index + 1) % 4 === 0">
                    <AppImage class="arrow" src="/img/promotion/detail/arrow" :style="{'transform': 'rotate(90deg)', top: `var(--app-px-20)`}"/>
                  </div>
                  <div v-else>
                    <AppImage class="arrow" src="/img/promotion/detail/arrow" :style="{top: `var(--app-npx-82)`, left: `var(--app-px-90)`}"/>
                  </div>
                </div>
                <div v-else>
                  <div v-if="index % 4 === 0">
                    <AppImage class="arrow" src="/img/promotion/detail/arrow" :style="{'transform': 'rotate(90deg)', top: `var(--app-px-20)`}"/>
                  </div>
                  <div v-else>
                    <AppImage class="arrow" src="/img/promotion/detail/arrow" :style="{'transform': 'rotate(180deg)', top: `var(--app-npx-82)`, left: `var(--app-npx-90)`}"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

      <div class="desc">
          <span>Instruções Do Evento:</span>
          <div class="rule">1.Convide amigos para abrir o Baú de Tesouros. Conclua com diferentes quantidades de pessoas para receber baús correspondentes, com um valor máximo de 10000. Quanto mais amigos você convidar, maior será a recompensa;<br>2.Essa promoção é um bônus adicional da plataforma, e você também pode desfrutar de outras recompensas e comissões de agente, o que significa que você pode experimentar diretamente a alegria multiplicada;<br>3.A recompensa é limitada à coleta manual no final doAPP/iOS、APP/Android、PC/Windows, e o expirado será distribuído automaticamente;<br>4.O bônus (excluindo o principal) deste evento requer 0 apostas válidas para ser sacado, e as apostas não são limitadas a plataforma do jogo;<br>5.Este evento é limitado a operações normais realizadas pelo titular da conta. É proibido alugar, usar plug-ins externos, robôs, apostar em contas diferentes, brushing mútuo, arbitragem, interface, protocolo, exploração de vulnerabilidades, controle de grupo ou outros meios técnicos para participar. Caso contrário, as recompensas serão canceladas ou deduzidas, a conta será congelada ou até mesmo adicionada à lista negra;<br>6.Para evitar diferenças na compreensão do texto, a plataforma reserva-se o direito de interpretação final deste evento.</div>
      </div>

      <div class="footer-space"></div>

      <div class="selectAddress" v-if="isShowSelect" @click="closeSelect">
          <div class="ul">
              <ul>
                <li v-for="item in li" @click="liClick(item)" :class="{active:item==shareUrl}">
                  <span>{{item}}</span>
                </li>
              </ul>
          </div>
      </div>


  </div>
  <van-popup class="details-poup" v-model:show="isShowDetails" round :close-on-click-overlay="true" @click-overlay="closeDetails">
        <div class="details-content">
          <span>Link de Convite</span>
          <div class="content-body-top">
              <van-popover class="content-body-top-popover" v-model:show="showPopover" :actions="actions" 
                        @select="onSelect" @open="popoverStatus(true)" @close="popoverStatus(false)" placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-body-top-popover-select box-border"
                                :class="{ 'viewOpen': showPopover }">
                                <van-text-ellipsis :content="selText" class="top-popover-select-title"/>
                                <span class="top-popover-select-icon"
                                    :class="{ 'rotate': showPopover, 'rotate1': !showPopover }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                        
              </van-popover>
              <div class="searchParent" :class="{active:inputForce}">
                <input v-model="question"  type="string" class="search" placeholder="Conta"  @focus="eventFocus"  @blur="eventBlur"/>
                <AppImage class="select-img" src="/icons/invite_search1.webp" alt="" @click="onConfirmID" maxlength="30" />
            </div>
          </div>
          <div class="invite-list">
            <AppList class="app-List" :loading="queryTeamLoading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData"
              style="padding-top: 15px;">
              <div class="invite-list-item" v-for="(item, index) in listTeamData" :key="item.username">
                <div class="invite-list-item-body">
                  <div class="invite-list-item-body-id">  
                    <label>{{ item.username }}</label>
                  </div>
                  <div class="invite-list-item-body-time">  
                    <label>Hora de registro:</label>
                  </div>
                  <div class="invite-list-item-body-timeAt">  

                    <label>{{ dayjs(getBrazilTime(item.createdAt*1000)).format('YYYY-MM-DD') }}</label>
                  </div>
                  <div class="invite-list-item-body-apos">  
                    <label >Válido ou não</label>
                    <label :style="{color:'#04be02'}" v-if="item.totalWaged >= ValidInviteWagedReq && item.FirstDepositAmount >= ValidInviteMinDeposit"> Sim</label>
                    <label :style="{color:'#04be02'}" v-else> Não</label>
                  </div>
                  <div class="invite-list-item-body-dep">   
                    <label @click="()=>{isShowPersonalDetails = true; showDetailsIndex = index}">Detalhes</label>  <!--充值-->
                  </div>
                </div>
              </div>
            </AppList>
          </div>
          <app-empty class="onne_jl"  text="Sem Registros" v-show="listTeamData.length==0"></app-empty>

        </div>
        <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp" alt=""
          @click="closeDetails" />
  </van-popup>
  <van-popup class="detail-personal" v-model:show="isShowPersonalDetails" round :close-on-click-overlay="true">
        <div class="content">
          <span>Detalhes da expansão</span>
          <!-- <span>{{showDetailsIndex}}</span> -->
          <div>
          <div class="detail-condition" v-if="ValidInviteMinDeposit > 0">
            <div class="text" :class="{active:listTeamData[showDetailsIndex]?.FirstDepositAmount >= ValidInviteMinDeposit}">Depósito Total{{ValidInviteMinDeposit}}+</div>

          </div>
          <div class="detail-condition" v-if="ValidInviteWagedReq > 0">
            <div class="text" :class="{active:listTeamData[showDetailsIndex]?.totalWaged >= ValidInviteWagedReq}">Apostas{{ValidInviteWagedReq}}+</div>
          </div>
        </div>
        </div>
  </van-popup>
</template>

<style lang='scss' scoped>
.linkDeConvite{
  // position: absolute;
    width: 100%;
    // height: calc(100% - 90px);
}

.all-promotion {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--theme-bg-color);
}

.info{
  margin: auto;
  margin-top: 20px;
  width:710px;
  height:592px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  font-size:23px;

  .modo{
    position: absolute;
    color:var(--theme-text-color-darken);
    font-size: 24px;
    left:60px;
    top: 60px;
    width: 300px;
  }

  .difere{
    position: absolute;
    color: var(--theme-text-color-darken);
    left:340px;
    top: 108px;
    width: 380px
  }

  .receber{
    position: absolute;
    left:435px;
    top: 43px;

    width:120px;
    height:48px;
    background:var(--theme-disabled-bg-color);
    border-radius:8px;
    color:var(--theme-disabled-font-color);
    text-align: center;
    line-height: 50px;
    &.active{
      background:var(--theme-filter-active-color);
      color:var(--theme-main-bg-color);
    }
  }

  .hist{
    position: absolute;
    left:583px;
    top: 43px;

    width:120px;
    height:48px;
    background:var(--theme-primary-color);
    color:var(--theme-main-bg-color);
    border-radius:8px;
    text-align: center;
    line-height: 50px;
  
  }

  .line{
    position: absolute;
    top: 110px;
    left: 37px;
    width:676px;
    height:1px;
    background: var(--theme-color-line);
  }

  .QRCodebg{
    position: absolute;
    width: 154px;
    left:40px;
    top: 130px;
  }

  .QRCode{
    position: absolute;
    width: 140px;
    left:45px;
    top: 137px;
  }

  .agent_bottom_right{
    position: absolute;
    width: 485px;
    height: 220px;
    left:220px;
    top: 130px;
    .men{
      color:var(--theme-text-color-lighten);
    }

    .alt{
      color:var(--theme-primary-color);
      float: right;
    }

    .httpAddress{
      display: flex;
      align-items: center; /* 垂直居中 */
      margin-top:15px;
      width:494px;
      height:92px;
      background:var(--theme-main-bg-color);
      border:1px solid;
      border-color:var(--theme-color-line);
      border-radius:8px;
      &.active{
        border-color:var(--theme-filter-active-color);
      }
      .test{
        // display: inline-block;
        color:var(--theme-text-color);
        margin-left: 30px;
        margin-top: 4px;
        width: 400px;
        white-space: normal; /* 允许自动换行 */
        word-wrap: break-word; /* 允许在单词内换行 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制为3行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .copy{
        position: absolute;
        width: 26px;
        top: 80px;
        right: 20px;
      }
    }
  }

  .conviteNum{
    position: absolute;
    width: 485px;
    height: 90px;
    left:220px;
    top: 270px;
    color:var(--theme-text-color-lighten);

    .subord{
      display: inline-block;
      padding-top: 10px;
      // left:220px;
      // top: 350px;
    }

    .subord_num{
      padding-left: 10px;
    }

    .convite{
      display: inline-block;
      padding-top:15px;
      // left:220px;
      // top: 390px;
    }

    .convite_id{
      padding-left: 10px;
      color: var(--theme-text-color-darken);
    }
  }
 
  .copy2{
    margin-left: 20px;
    width: 26px;
  }

  .share{
    position: absolute;
    left:40px;
    top: 400px;
    width: 90%;


    display: flex;
    align-items: flex-start;
    // height: 120px;
    overflow: auto;
    overflow-x: scroll;
    .item{
      width: 100px;
      height: 120px;
      margin-right: 20px;
    }

    .itemImg{
      display: block;
      margin: 0 auto;
      width:72px ;
    }
    .itemName{
      width: 100px;
      margin-top: 10px;
      display: inline-block;
      text-align: center;
      color:var(--theme-text-color);
    }

  }

  .detail{
    position: absolute;
    left:40px;
    top: 560px;
    width: 90%;
    .one{
      color: var(--theme-text-color-lighten);
      font-size: 22px;
    }
    .two{
      color: var(--theme-primary-color);
      font-size: 22px;
    }
  }
  // .share::-webkit-scrollbar{
  //   background-color: rgb(122, 178, 238);
  // }
}

.condition{
  margin: auto;
  margin-top: 20px;
  width:710px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  text-align: center;
  padding-bottom: 8px;
  padding-top: 10px;
  line-height: 22px;
  span{
    width: 670px;
    margin-left: 20px;
    color:var(--theme-text-color-lighten);
    font-size: 21px;
  }

  .one{
    margin-left: 20px;
    margin-top: 10px;
    width:670px;
    height: 70px;
    background: var(--theme-bg-color);
    border-radius:6px;
    text-align: center;
    position: relative;
    display: flex;
    // grid-template-columns: repeat(2, 1fr);
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    flex-direction: row;
    .text1{
      // width: 370px;
      color:var(--theme-text-color-lighten);
      font-size: 24px;
      white-space: nowrap; /* 防止文本换行 */

    }
    .text2{
      // width: 370px;
      color:var(--theme-text-color-darken);
      font-size: 24px;
      white-space: nowrap; /* 防止文本换行 */
    }

    &:nth-child(2n){
      background:var(--theme-main-bg-color);
      // margin-top: 0px;
      padding-bottom: 10px;
      margin-bottom: 6px;
    }

  }

}

.treasure-chest-container {
  margin: auto;
  margin-top: 20px;
  padding-bottom: 30px;
  padding-top: 36px;
  width:710px;
  // height:592px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  font-size:23px;
  display: grid;
  // grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.desc {
  margin: auto;
  margin-top: 20px;
  width:710px;
  // height:592px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  // font-size:23px;
  padding: 24px;
  span{
    color: var(--theme-text-color);
    font-size:24px;
    line-height: 40px;

  }
  .rule{
    color: var(--theme-text-color);
    font-size:24px;
    line-height: 40px;
  }
}
.row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.treasure-chest {
  position: relative;

  padding: 10px;
  text-align: center;
  // margin: 5px;
  // width: 100px;
  .box-img{
    width: 132px;
  }
  .person{
    top: 92px;
    left: 37px;
    position: absolute;
    width: 96px;
    height: 35px;
    line-height: 17px;
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    .number{
      position: relative;
      display: inline;
      color: var(--theme-text-color-activities-text);
      // width: 10px; /* 固定宽度 */
      // white-space: nowrap; /* 防止文本换行 */
      line-height: 14px;
      font-size: 17px;
    }
  }

  .reward{
    position: relative;
    color: var(--theme-text-color-lighten);
    font-size: 20px;
    top:5px;
    &.active{
      color: var(--theme-secondary-color-finance);
    }
    &.active1{
      color: var(--theme-secondary-color-error);
      top:-85px;
    }
  }
  .arrow{
    position: relative;

    width: 23px;
    // transform: rotate(90deg);
  }
}

.footer-space {
  height: 23px; /* 或者任何你需要的高度 */
}

.box-info{
  margin-left: 20px;
  width: 710px;


}
.selectAddress{
  position: absolute;
  width: 100vw;
  height: 100%;
  left:0;
  top: -70px;
  .ul{
    position: absolute;
    width:494px;
    // height: 220px;
    left:220px;
    top: 340px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:8px;

    li{
      height: auto;
      color:#7cb39d;
      font-size: 23px;
      padding-left: 30px;
      // background-color: var(--theme-text-color);
      padding-top: 10px;
      padding-bottom: 10px;
      &.active{
        color:var(--theme-filter-active-color);
      }
      span{
        width: 400px;
        white-space: normal; /* 允许自动换行 */
        word-wrap: break-word; /* 允许在单词内换行 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制为3行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

 
}

.box-border {
            border-style: solid;
            // border-color: var(--theme-main-bg-color);
            border-color: var(--theme-color-line);
            border-width: 2px;
            border-radius: 10px;
        }

.details-poup {
  width: 690px;
  height: 1200px;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: fixed !important;
  justify-content: center;
  top: 45vh;
  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    bottom: 5%;
  }

  .details-content {
    width: 596px;
    height: 900px;
    background-color: var(--theme-main-bg-color);
    background-size: 596px 900px;
    text-align: center;
    position: absolute;
    display: flex;
    // justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 20px;
    border-radius: 20px;
    border: 2px solid var(--theme-color-line);
    top: 8%;
    span{
      color: var(--theme-text-color);
    }

    .content-body-top {
        width: 100%;
        height: 90px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .content-body-top-popover {
            width: 180px;
            height: 50px;
        }

        .content-body-top-popover-select {
            width: 180px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .top-popover-select-title {
            color: var(--theme-text-color);
            font-size: 18px;
            line-height: 48px;
            padding-left: 18px;
            padding-top: 4px;
            width: 101px;
            height: 48px;
        }
        .top-popover-select-icon {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 15px;
            margin-right: 10px;
            svg {
                width: 20px;
                height: 20px;
                color: var(--theme-text-color-lighten);
                position: absolute;

            }
        }
    }

    .searchParent{
    position: absolute;
    width:280px;
    height:50px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:26px;
    
    // margin-top: 52px;
    margin-left: 260px;

    &.active{
      border-color:var(--theme-filter-active-color);
    }

    .search{
      display: inline;
      margin: 0 auto;
      // padding-left: 20px;
      width:200px;
      height:50px;
      background-color: transparent;
      color: var(--theme-text-color-lighten);
      left: 24px;
      top:0px;
      position: absolute;
      font-size:20px;
     
    }

    .search::placeholder {
      color:var(--theme-text-color-lighten);
      font-size:20px;
    }

    .select-img{
      position: absolute;
      width: 27px;
      top:12px;
      right:20px;
    }

  }
  }
}

.invite{
  &-list{
    width: 596px;
    margin: 0 auto;
    // margin-top: 20px;
    height: calc(100% - 100px);
    .app-List{
      width: 90%;
      height: calc(100% - 160px);
      left: 30px;
      position: absolute;
    }
    &-item{
      width: 100%;
      height: 140px;
      margin-bottom: 10px;

      border-radius:14px;
      color: var(--theme-text-color-darken);
      font-size: 21px;
      &:nth-child(2n){
        background-color: var(--theme-bg-color);
      }
      &-body{
        width: 100%;
        height: 100%;
        position: absolute;

        // display: flex;
        // padding-top: 25px;
        // -----------------
        &-id{
          display: inline-block;
          position: absolute;
          left: 20px;
          top: 26px;
          &-copy{
            position: absolute;
            width: 23px;
            margin-left:25px;
          }
        }
        // time
        &-time{
          position: absolute;
          width: 100px;
          left: 20px;
          top: 66px;
          color:var(--theme-text-color-lighten);
          text-align: left;
        }
        &-timeAt{
          position: absolute;
          width: 100px;
          left: 150px;
          top: 66px;
          color:var(--theme-text-color-darken);
          text-align: left;
        }
        //apos
        &-apos{
          position: absolute;
          color:var(--theme-text-color-lighten);
          top: 26px;
          left: 40%;
          &-num{
            padding-left: 10px;
            color:var(--theme-text-color-darken);
          }
        }
        //dep 
        &-dep{
          position: absolute;
          color: var(--theme-primary-color);
          top: 60px;
          right: 10px;
          &-num{
            padding-left: 10px;
            color:var(--theme-text-color-darken);
          }
        }
      }
    }
  }   
}

.detail-personal{
  width: 596px;
  height: 312px;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: fixed !important;
  justify-content: center;
  top: 50vh;
  .content {
    width: 596px;
    height: 312px;
    background-color: var(--theme-main-bg-color);
    background-size: 596px 312px;
    text-align: center;
    position: absolute;
    display: flex;
    // justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 40px;
    border-radius: 16px;
    border: 2px solid var(--theme-color-line);
    // top: 8%;
    span{
      color: var(--theme-text-color);
    }
    .detail-condition{
      // margin-left: 0px;
      margin-top: 30px;
      // padding-top: 10px;
      width:540px;
      height: 52px;
      background:var(--theme-bg-color);
      // border-radius:6px;
      text-align: center;
      position: relative;
      display: flex;
      // grid-template-columns: repeat(2, 1fr);
      justify-content: left;
      /* 水平居中 */
      align-items: center;
      flex-direction: row;
      .text{
        margin-left: 26px;
        // width: 370px;
        color:white;
        font-size: 20px;
        white-space: nowrap; /* 防止文本换行 */
        &.active{
          color:#04be02;
        }
      }


      &:nth-child(2n){
        background:var(--theme-main-bg-color);

        // margin-top: 0px;
        padding-bottom: 10px;
        margin-bottom: 6px;
        // .text1{
        // color:white;
        // }
        // .text2{
        //   color:white;
        // }
      }

    }
  }
}

.onne_jl{
  width: 100%;
  height: 450px;
  position: absolute;
  top: 25%;

  // .onne_jl_img{
    // position: absolute;
    // transform: translate(-50%,-50%);
    // left:50%;
    // top:50%;
    // width: 300px;
  // }
}
</style>
