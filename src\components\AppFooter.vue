<script lang="ts" setup>
const { t } = useI18n();
const router = useRouter();

const appStore = useAppStore();
const { isLogin, appFooterVisible, secletType } = storeToRefs(appStore);

import homeIconActive from "/icons/svg/home_active.svg?raw";
import homeIcon from "/icons/svg/home.svg?raw";
// import depositIconActive from "/icons/svg/deposit_active.svg?raw";
import agentIcon from "/icons/svg/agent.svg?raw";
import giftIcon from "/icons/svg/gift.svg?raw";
import giftIconActive from "/icons/svg/gift_active.svg?raw";
import personIcon from "/icons/svg/person.svg?raw";
import personIconActive from "/icons/svg/person_active.svg?raw";
import walletIcon from "/icons/svg/wallet.svg?raw";
import registerIcon from "/icons/svg/register.svg?raw";
enum Menus {
  // HOME = 'index',
  // PROMOTION = 'promotion',
  // DEPOSIT = 'finance-deposit',
  // VIP = 'vip',
  INICIO = "inicio",
  PROMOGAO = "promogao",
  DEPOSIT = "deposit",
  CONVIDAR = "convidar",
  CONTA = "conta",

  SUPORTE = "suporte",
  SAQUE = "saque",
  MENU = "menu",
  REGISTER = "register",
}

const saqueAref = ref();
const menuAref = ref();
const depositAref = ref();
const inicioAref = ref();
const contaAref = ref();
const convidarAref = ref();
const registerAref = ref();
const oldSelref = ref();

const show = ref(true);

const menus = ref([
  {
    path: "/",
    name: Menus.INICIO,
    eleref: inicioAref,
    auth: false,
    originalImg: "/img/footer/menu_home_original.webp",
    clickImg: "/img/footer/menu_home_click.webp",
    svg: homeIcon,
    svgActive: homeIconActive,
  },
  {
    path: "/advancement",
    name: Menus.PROMOGAO,
    eleref: inicioAref,
    auth: false,
    originalImg: "/img/footer/menu_activity_original.webp",
    clickImg: "/img/footer/menu_activity_click.webp",
    svg: giftIcon,
    svgActive: giftIconActive,
  },

  {
    path: "/agent/?tab=0",
    name: Menus.CONVIDAR,
    eleref: convidarAref,
    auth: true,
    originalImg: "/img/footer/menu_agent_original.webp",
    clickImg: "/img/footer/menu_agent_click.webp",
    svg: agentIcon,
    svgActive: agentIcon,
  },
  {
    path: "/finance",
    name: Menus.DEPOSIT,
    eleref: depositAref,
    auth: true,
    originalImg: "/img/footer/menu_wallet_original.webp",
    clickImg: "/img/footer/menu_wallet_click.webp",
    svg: walletIcon,
    svgActive: walletIcon,
  },
  {
    path: "/personalCenter",
    name: Menus.CONTA,
    eleref: contaAref,
    auth: true,
    originalImg: "/img/footer/menu_personal_original.webp",
    clickImg: "/img/footer/menu_personal_click.webp",
    svg: personIcon,
    svgActive: personIconActive,
  },
]);

const { activeMenu } = storeToRefs(appStore);
const { setActiveMenu } = appStore;

const toggleAni = function (curref: any) {
  // let beat = curref.classList.contains('ani-beat')
  // if (beat) {
  //   curref.classList.remove('ani-beat')
  //   setTimeout(() => {
  //     curref.classList.add('ani-beat')
  //   }, 0);
  // } else {
  //   curref.classList.add('ani-beat')
  // }
};

const goPay = () => {
  appStore.setShowRecharge(false);
  appStore.setPayVisble(true);
};

const menuAClick = function (ty: Menus) {
  appStore.setShowIndexModule(false);
  appStore.setShowRouterView(false);

  // return;
  //充值
  if (ty == Menus.DEPOSIT) {
    if (isLogin.value) {
      appStore.setShowIndexModule(3);
      // appStore.setPayVisble(true);
      setActiveMenu(ty);
    } else {
      appStore.setLoginDialogVisible(true);
    }
    return;
  } else if (ty == Menus.CONVIDAR) {
    if (!isLogin.value) {
      appStore.setLoginDialogVisible(true);
      return;
    }
    appStore.setShowIndexModule(2);
    setActiveMenu(ty);
    return;
  } else if (ty == Menus.CONTA) {
    if (!isLogin.value) {
      appStore.setLoginDialogVisible(true);
      return;
    }
    appStore.setShowIndexModule(4);
    setActiveMenu(ty);
    return;
  } else if (ty == Menus.PROMOGAO) {
    console.log(888873);

    appStore.setShowIndexModule(1);
    setActiveMenu(ty);
    console.log(888873);
    return;
  }
  // appStore.setPayVisble(false);
  //
  let routerPush = (menu: Menus) => {
    const item = menus.value.filter((i) => i.name === menu)[0];
    if (item) {
      if (!item.auth || isLogin.value) {
        setActiveMenu(menu);
        if (item.name !== Menus.DEPOSIT) {
          toggleAni(item.eleref);
        }
      }
      if (item.name == Menus.PROMOGAO) {
        secletType.value = 0;
      }
      if (item.name == Menus.DEPOSIT) {
        goPay();
      } else if (item.name == Menus.REGISTER) {
        openRegisterDialog(true);
      } else {
        // if (item.auth && !isLogin)
        router.push(item.path);
      }
    }
  };
  console.log("调试");
  console.log(activeMenu);
  console.log(ty);

  // 关闭menu，返回主页
  if (activeMenu.value === ty && ty === "menu") {
    // router.push('/')
    if (!oldSelref.value) {
      router.push("/");
    } else {
      setActiveMenu(oldSelref.value);
      routerPush(activeMenu.value);
    }
    return;
  } else if (ty !== "menu") {
    const item = menus.value.filter((i) => i.name === ty)[0];
    if (item) {
      if (!item.auth || isLogin.value) {
        oldSelref.value = ty;
      }
    }
  }
  routerPush(ty);
};

const iconLoad = (e: any, ty: Menus) => {
  e.currentTarget.style.width =
    "var(--app-px-" + Math.ceil(e.currentTarget.naturalWidth) + ")";
};

onMounted(() => {
  setTimeout(() => {
    // show.value = true
  }, 0);
});

watch(
  router.currentRoute,
  (val, old) => {
    console.log("path = ", val.path, "  old.path  = ", old, typeof val);
    if (!val.path || (old && val.path == old.path)) {
      return;
    }
    let selMenuName = null;
    switch (val.path) {
      case "/menu":
        selMenuName = Menus.MENU;
        break;
      case "/finance":
        selMenuName = Menus.DEPOSIT;
        break;
      case "/":
        selMenuName = Menus.INICIO;
        break;
      case "/promotion-detail/invite":
        selMenuName = Menus.CONVIDAR;
        break;
      case "/personalCenter":
        selMenuName = Menus.CONTA;
        break;
      case "/advancement":
        selMenuName = Menus.PROMOGAO;
        break;
    }
    if (val.meta.layout == "home") {
      selMenuName = Menus.INICIO;
    }
    if (selMenuName) {
      setActiveMenu(selMenuName);
    }

    // if (val && val.path) {
    //   const cur = menus.value.filter(i => i.path === val.path)[0]
    //   if (cur && cur.path === '/finance') {
    //     activeMenu.value = cur.name
    //     show.value = false
    //     return
    //   }
    //   if (cur) {
    //     activeMenu.value = cur.name
    //     show.value = true
    //   } else if (val.meta.layout === 'home') {
    //     activeMenu.value = Menus.INICIO
    //     show.value = true
    //   } else {
    //     show.value = false
    //   }
    // }
  },
  { immediate: true }
);
</script>

<template>
  <div id="tab-bar-home">
    <div class="tabbar-left-bgcolor"></div>
    <img src="/img/footer/menu_bg.webp" class="tabbar-left-bg" />
    <div class="tabbar-left">
      <div
        v-for="item in menus"
        :key="item.name"
        class="tabbar-left-item"
        @click="menuAClick(item.name)"
      >
        <img v-if="activeMenu !== item.name" :src="item.originalImg" alt="" />
        <img v-else :src="item.clickImg" alt="" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.tabbar-left-bg {
  z-index: 999;
  width: 100%;
  height: 15.5vw;
  position: absolute;
  bottom: 0;
}

.tabbar-left {
  background: transparent;
  position: absolute;
  width: 100%;
  z-index: 999;
  display: flex;
  justify-content: space-around;
  align-items: center;
  bottom: 0.2vw;

  img {
    background: transparent;
    width: 17.5vw;
    height: auto;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 1;
}

.icon-img {
  width: var(--app-px-50) !important;
  position: absolute;
  top: 20px;
}

.icon-imgCenter {
  position: absolute;
  width: var(--app-px-50) !important;
  top: 20px;
}

ul {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

.app-footer {
  z-index: 2;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: var(--app-footer-height);
  // background: url() no-repeat 100%/100%;
  // @include webp('/img/app-footer-bg.png');
  // background-size: 100% 100%;
  background: var(--theme-btm-info-color);
  text-align: center;
  // color: #7d8aa2;
  font-size: 22px;
  opacity: 0;
  padding-top: 10px;
  box-shadow: 2px 6px 0px rgba(0, 0, 0, 0.1);
  ul {
    display: flex;
    width: 100%;
    height: 100%;

    li.menu-item {
      flex: 1 1 0%;
      &.deposit {
        a {
          justify-content: flex-start;
          margin-top: -8px;
        }
      }

      a {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        // &.ani-beat {
        //   animation-name: aniBeat;
        //   animation-duration: 600ms;
        //   animation-timing-function: ease-in-out;
        //   animation-iteration-count: 1;
        // }

        .menu-icon {
          // width: 46px;
          // height: 46px;
          margin: 0 auto;
          // margin-top: 24px;
        }

        .menu-title {
          // margin-top: 15px;
          position: absolute;
          line-height: 26px;
          color: var(--theme-text-color-lighten);
          // font-weight: 500;
          font-size: 23px;
          // 75px;
          top: 10vw;
          &.deposit {
            margin-top: 10px;
          }
        }

        // .icon-home {
        // background-image: url('/icons/i-home.png');
        // background-size: 46px auto;
        // background-repeat: no-repeat;
        // background-position: center center;
        // }

        .menu-icon.icon-deposit {
          width: 74px;
          height: 74px;
          margin-bottom: 4px;
          // background-image: url();
          @include webp("/img/blue-circle.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center center;

          img {
            width: 74px;
            vertical-align: top;
          }
        }
      }

      &.active {
        a {
          .menu-title {
            color: var(--app-bottom-itemSelect-color);
          }
        }
      }
    }
  }
}

@keyframes aniBeat {
  0% {
    transform: scale(1);
  }

  40% {
    transform: scale(0.6);
  }

  60% {
    transform: scale(1.1);
  }

  80% {
    transform: scale(0.85);
  }

  100% {
    transform: scale(1);
  }
}
</style>
<!-- 底部菜单 -->
