/**
 * PWA相关工具函数
 */

export interface PWAInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

/**
 * 检测设备和浏览器类型
 */
export const deviceDetection = {
  // 检测是否为Android设备
  isAndroid(): boolean {
    return /Android/i.test(navigator.userAgent)
  },

  // 检测是否为iOS设备
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  },

  // 检测是否为Chrome浏览器
  isChrome(): boolean {
    return /Chrome/i.test(navigator.userAgent) && !/Edge/i.test(navigator.userAgent)
  },

  // 检测是否为Safari浏览器
  isSafari(): boolean {
    return /Safari/i.test(navigator.userAgent) && !/Chrome/i.test(navigator.userAgent)
  },

  // 检测是否为Samsung Internet浏览器
  isSamsungInternet(): boolean {
    return /SamsungBrowser/i.test(navigator.userAgent)
  },

  // 检测是否为Edge浏览器
  isEdge(): boolean {
    return /Edge/i.test(navigator.userAgent)
  },

  // 检测是否为Firefox浏览器
  isFirefox(): boolean {
    return /Firefox/i.test(navigator.userAgent)
  },

  // 检测是否为Opera浏览器
  isOpera(): boolean {
    return /Opera|OPR/i.test(navigator.userAgent)
  },

  // 检测是否为小米浏览器
  isMiuiBrowser(): boolean {
    const userAgent = navigator.userAgent
    return /MiuiBrowser/i.test(userAgent) ||
           /XiaoMi/i.test(userAgent) ||
           /MI\s/i.test(userAgent) ||
           (this.isAndroid() && /MIUI/i.test(userAgent))
  },

  // 获取小米浏览器版本
  getMiuiBrowserVersion(): string | null {
    const userAgent = navigator.userAgent
    const match = userAgent.match(/MiuiBrowser\/(\d+\.\d+)/i)
    return match ? match[1] : null
  },

  // 检测是否为支持PWA的Android浏览器
  isSupportedAndroidBrowser(): boolean {
    if (!this.isAndroid()) return false

    const userAgent = navigator.userAgent
    return /Chrome/i.test(userAgent) ||
           /SamsungBrowser/i.test(userAgent) ||
           /Edge/i.test(userAgent) ||
           /Firefox/i.test(userAgent) ||
           /Opera|OPR/i.test(userAgent) ||
           /UCBrowser/i.test(userAgent) ||
           /MiuiBrowser/i.test(userAgent) ||
           /XiaoMi/i.test(userAgent) ||
           /HuaweiBrowser/i.test(userAgent) ||
           /VivoBrowser/i.test(userAgent) ||
           /OppoBrowser/i.test(userAgent)
  },

  // 检测是否为移动设备
  isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }
}

/**
 * PWA安装相关功能
 */
export const pwaInstaller = {
  // 检测PWA是否已安装
  isInstalled(): boolean {
    // 检测standalone模式
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return true
    }
    
    // 检测iOS Safari添加到主屏幕
    if ((window.navigator as any).standalone === true) {
      return true
    }
    
    // 检测Android Chrome WebAPK
    if (document.referrer.includes('android-app://')) {
      return true
    }
    
    return false
  },

  // 检测是否支持PWA安装
  isSupported(): boolean {
    return 'serviceWorker' in navigator
  },

  // 检测是否支持安装提示
  supportsInstallPrompt(): boolean {
    return 'BeforeInstallPromptEvent' in window || 'onbeforeinstallprompt' in window
  },

  // 获取安装状态
  getInstallStatus(): 'installed' | 'installable' | 'not_supported' {
    if (this.isInstalled()) {
      return 'installed'
    }
    
    if (this.isSupported()) {
      return 'installable'
    }
    
    return 'not_supported'
  }
}

/**
 * Service Worker相关功能
 */
export const serviceWorkerManager = {
  // 注册Service Worker
  async register(swPath: string = '/service-worker.js'): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Worker不被支持')
      return null
    }

    try {
      const registration = await navigator.serviceWorker.register(swPath)
      console.log('Service Worker注册成功:', registration)
      
      // 监听更新
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // 有新版本可用
              this.notifyUpdate()
            }
          })
        }
      })
      
      return registration
    } catch (error) {
      console.error('Service Worker注册失败:', error)
      return null
    }
  },

  // 通知用户有更新
  notifyUpdate(): void {
    // 这里可以显示更新提示
    console.log('有新版本可用')
    
    // 可以发送自定义事件
    window.dispatchEvent(new CustomEvent('sw-update-available'))
  },

  // 更新Service Worker
  async update(): Promise<void> {
    if (!navigator.serviceWorker.controller) return
    
    const registration = await navigator.serviceWorker.getRegistration()
    if (registration) {
      registration.update()
    }
  }
}

/**
 * WebAPK相关功能
 */
export const webAPK = {
  // 检测是否运行在WebAPK中
  isRunningInWebAPK(): boolean {
    return document.referrer.includes('android-app://')
  },

  // 获取WebAPK包名
  getWebAPKPackageName(): string | null {
    const referrer = document.referrer
    if (referrer.includes('android-app://')) {
      const match = referrer.match(/android-app:\/\/([^\/]+)/)
      return match ? match[1] : null
    }
    return null
  },

  // 检测WebAPK安装条件
  checkInstallCriteria(): {
    hasManifest: boolean
    hasServiceWorker: boolean
    isHTTPS: boolean
    hasIcons: boolean
    meetsEngagementHeuristics: boolean
  } {
    return {
      hasManifest: !!document.querySelector('link[rel="manifest"]'),
      hasServiceWorker: 'serviceWorker' in navigator,
      isHTTPS: location.protocol === 'https:' || location.hostname === 'localhost',
      hasIcons: true, // 简化检测，实际应该检查manifest中的图标
      meetsEngagementHeuristics: true // 简化检测，实际需要检查用户参与度
    }
  }
}

/**
 * 安装统计
 */
export const installAnalytics = {
  // 记录安装事件
  trackInstallEvent(action: 'prompt_shown' | 'prompt_accepted' | 'prompt_dismissed' | 'installed'): void {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('event', 'pwa_install', {
        event_category: 'PWA',
        event_label: action,
        value: 1
      })
    }
    
    // 自定义统计
    console.log('PWA Install Event:', action, {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      isAndroid: deviceDetection.isAndroid(),
      isChrome: deviceDetection.isChrome()
    })
  },

  // 记录使用情况
  trackUsage(): void {
    const installStatus = pwaInstaller.getInstallStatus()
    const isWebAPK = webAPK.isRunningInWebAPK()
    
    if (typeof gtag !== 'undefined') {
      gtag('event', 'pwa_usage', {
        event_category: 'PWA',
        custom_parameters: {
          install_status: installStatus,
          is_webapk: isWebAPK,
          display_mode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser'
        }
      })
    }
  }
}

/**
 * 初始化PWA功能
 */
export const initPWA = async (): Promise<void> => {
  // 注册Service Worker
  await serviceWorkerManager.register()
  
  // 记录使用情况
  installAnalytics.trackUsage()
  
  // 监听安装事件
  window.addEventListener('appinstalled', () => {
    installAnalytics.trackInstallEvent('installed')
  })
  
  // 监听beforeinstallprompt事件
  window.addEventListener('beforeinstallprompt', (e) => {
    installAnalytics.trackInstallEvent('prompt_shown')
  })
}

// 导出所有功能
export default {
  deviceDetection,
  pwaInstaller,
  serviceWorkerManager,
  webAPK,
  installAnalytics,
  initPWA
}
