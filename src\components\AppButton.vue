<script setup lang="ts" name="app-button">
interface Props {
  width: number | string; // 宽
  height: number | string; // 高
  bold?: boolean; // 文字加粗
  round?: boolean; // 半圆按钮
  radius?: number | string; // 圆角
  shadow?: boolean; // 阴影
  yellowToBottom?: boolean; // 黄色
  yellowToRight?: boolean; // 黄色
  blueToBottom?: boolean; // 蓝色
  blueToRight?: boolean; // 蓝色
  blueToRightDeep?: boolean; // 蓝色深
  purpleToRight?: boolean; // 紫色
  orange?: boolean;
  blue?: boolean;
  red?: boolean;
  red1?: boolean;
  red2?: boolean;
  blue1?: boolean;
  gray?: boolean;
  white?: boolean;
  special?: boolean; // 特殊
  inline?: boolean; // 内联
  center?: boolean; // 居中
  disabled?: boolean;
  whiteText?: boolean; // 白色文字
  fontSize?: number | string; // 字体大小
  color?: string; // 字体颜色
  loading?: boolean;
  purple?: boolean;
  yellow?: boolean;
  green?: boolean;
  gray1?: boolean;
  blue3?: boolean;
  background?: string;
  trans?: boolean;
  border?: string;
  margin?: string;
}
const props = defineProps<Props>();

const loadingWidth = computed(() => {
  if (+props.width >= 500) {
    return 35;
  } else if (+props.width >= 300) {
    return 30;
  } else {
    return 25;
  }
});
</script>
<template>
  <div class="special" v-if="special"></div>

  <button
    v-else
    :style="{
      width: width,
      height: $toPx(+height),
      lineHeight: $toPx(+height),
      borderRadius: radius ? $toPx(+radius) : '',
      fontSize: fontSize,
      color: color ? color : '',
      background: background ? background : '',
      border: border ? border : 'none',
      margin: margin ? margin : '',
    }"
    :class="{
      bold,
      round,
      shadow,
      yellowToBottom,
      yellowToRight,
      blueToBottom,
      blueToRight,
      blueToRightDeep,
      purpleToRight,
      orange,
      blue,
      inline,
      center,
      whiteText,
      loading,
      gray,
      blue1,
      red,
      red1,
      red2,
      white,
      purple,
      yellow,
      green,
      gray1,
      blue3,
      trans,
    }"
    :disabled="disabled || loading"
  >
    <AppLoading :width="loadingWidth" :borderWidth="3" v-if="loading" />
    <slot v-else />
  </button>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

button {
  font-size: 32px;
  font-weight: 400;
  border: none;
  display: block;
  color: #000;
  &:disabled {
    opacity: 0.3;
  }
}

.whiteText {
  color: var(--theme-font-on-background-color);
}

.bold {
  font-weight: 700;
}

.round {
  border-radius: 750px;
}

.shadow {
  box-shadow: 0px -3px 6px 0px #ffffff40 inset, 0px -2px 3px 0px #ffffff;
}

.yellowToBottom {
  background-image: linear-gradient(to bottom, #ffd500, #ff9901);
}

.yellowToRight {
  background-image: linear-gradient(to right, #ffd500, #ff9901);
}

.blueToBottom {
  background: linear-gradient(to bottom, #0ed1f4, #1373ef);
}

.trans {
  background: hsla(0, 100%, 0%, 0);
}

.blueToRight {
  background-image: linear-gradient(0deg, #354dff 0%, #58d2ff 100%),
    linear-gradient(#fff71a, #fff71a);
  background-blend-mode: normal, normal;
}

.blueToRightDeep {
  background: linear-gradient(
    312deg,
    rgba(19, 115, 239, 0.3) 0%,
    rgba(14, 209, 244, 0.3) 100%
  );
}

.purpleToRight {
  background-image: linear-gradient(0deg, #5804c0 0%, #9971ff 100%),
    linear-gradient(#fff71a, #fff71a);
  background-blend-mode: normal, normal;
}

.orange {
  background: #f5c31b;
}

.red {
  background: #fb2020;
}
.red1 {
  background: #5e0d87;
}
.red2 {
  background: var(--theme-primary-color);
}
.white {
  background: #fff;
}
.blue {
  background: var(--theme-primary-color);
}
.blue1 {
  background: rgb(52, 144, 255);
}
.gray {
  background: var(--theme-disabled-bg-color);
}

.purple {
  background: #96236d;
}

.yellow {
  background: #FFF0BB;
}

.green {
  background:  var(--theme-primary-color);
}

.gray1 {
  background: #484848;
}

.blue3 {
  background: #3e2a43;
}

.special {
  width: 426px;
  height: 134px;
  // background-image: url('/img/special-btn.png');
  @include webp("/img/special-btn.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 0 auto;
}

.inline {
  display: inline-block;
}

.center {
  display: block;
  margin: 0 auto;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
