<template>
    <div class="root-page">
        <AppIndexHeader />
        <!-- <AppPageTitle left-arrow title="CASHBACK EM APOSTAS" title-weight="700" /> -->
        <section class="content">
            <!-- 666 -->
            <div class="promotion-card" v-for="item in showlist" :key="item.title">
                <AppImage :src='item.img_url'></AppImage>
                <div class="title" :style="{color:item.color}" @click="$router.push(item.path)">
                    <span>{{ item.title }}</span> 
                    <div class="icon-box" :style="{backgroundColor:item.color}"><van-icon name="arrow" /></div>
                </div>
                <div class="period">
                    {{ item.period }} <br>
                    {{ item.recv_period }}
                </div>
                <div class="btn"><van-button type="primary" block :color="item.color"  text="Obter agora" :to="item.path" style="height: 100%;"></van-button></div>
                
            </div>
        </section>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

const list = [
    {
        path:"/promotion-detail/semanal",
        title:"Comissão semanal",
        img_url:"/img/promotion/list/weekly_salary",
        period:"Periodo:2024-02-26 00:00:00~2024-03-03 23:59:59",
        recv_period:"Tempo de reivindicação:2024-02-26 00:00:00~2024-03-03 23:59:59",
        color:"#96236d",
    },
    {
        path:"/activity/weekly-proxy-cashback",
        title:"Cashback de depósito",
        img_url:"/img/promotion/list/cashback_of_charge",
        period:"Periodo:2024-02-26 00:00:00~2024-03-03 23:59:59",
        recv_period:"Tempo de reivindicação:2024-02-26 00:00:00~2024-03-03 23:59:59",
        color:"#d15c37",
    },
    {
        path:"/weekly-loss-cashback",
        title:"Cashback de perdas",
        img_url:"/img/promotion/list/cashback_of_loss",
        period:"Periodo:2024-02-26 00:00:00~2024-03-03 23:59:59",
        recv_period:"Tempo de reivindicação:2024-02-26 00:00:00~2024-03-03 23:59:59",
        color:"#367ad4",
    },
    {
        path:"/activity/weekly-bet-cashback",
        title:"Cashback em apostas",
        img_url:"/img/promotion/list/cashback_of_bet",
        period:"Periodo:2024-02-26 00:00:00~2024-03-03 23:59:59",
        recv_period:"Tempo de reivindicação:2024-02-26 00:00:00~2024-03-03 23:59:59",
        color:"#0d9f56",
    }
]

export default defineComponent({
    setup () {
        const showlist = reactive(list)

        return {showlist}
    }
})
</script>

<style lang="scss" scoped>
.root-page {
  background: var(--theme-main-bg-color);
  padding-top: var(--app-navbar-height);
  padding-bottom: var(--app-footer-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Arial;
  color:#192756;
  font-size:24px;

}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}


.promotion-card{
    // background-color: aqua;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    padding: 20px 0;

    .title{
        display: flex;
        // justify-content: center;
        align-items: center;
        font-weight:700;
        text-align:center;
        vertical-align:middle;

        span{
            font-size:44px;
            line-height:normal;
        }
    }

    .period{
        font-size:23px;
        line-height:120%;
        text-align: center;
        margin: 20px 0;
    }

    .btn{
        width:476px;
        height:67px;
        font-weight:700;
        color:#ffffff;
        font-size:30px;
        text-align:center;
    }


    .icon-box{
        background-color: black;
        width: 42px;
        height: 42px;
        margin: 0 10px;
        line-height: 44px;
        border-radius: 50%;
        display: inline-block;
        color: white;
        text-align: center;
    }
    img{
        width: 50%;
    }
}


</style>

