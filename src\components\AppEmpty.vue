<script setup lang="ts" name="app-empty">
withDefaults(defineProps<{
  paddingTop?: number;
  paddingBottom?: number;
  text?: string
}>(), {
  text: 'Sem Registros'
})

</script>
<template>
  <div class="app-empty" :style="{
    // paddingTop: `var(--app-px-${paddingTop ?? 230})`,
    // paddingBottom: `var(--app-px-${paddingBottom ?? 230})`
  }">
    <AppImage class="icon" src="/icons/icon_no_data.png" alt="" />
    <slot>{{ text }}</slot>
  </div>
</template>

<style lang="scss" scoped>
.app-empty {
  width: 100%;
  height: calc(100% - 5px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 26px;
  color: var(--theme-text-color-lighten);

  .icon {
    width: 210px;
    height: 210px;
    margin-bottom: 30px;
  }
}
</style>
