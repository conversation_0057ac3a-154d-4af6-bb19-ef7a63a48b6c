<script setup lang='ts' name='invite-page'>
const { userInfo } = storeToRefs(useAppStore())
const url = computed(() => location.origin + '/register' + `?id=${userInfo.value.invite_code}` + '&currency=BRL&type=2')

const toCopy = (str: string) => {
  showToast('Copied!')
  copy(str)
}

const openWeb = (type: string) => {
  const list = {
    whatapp: 'https://www.whatsapp.com/',
    telegram: 'https://telegram.org/',
    facebook: 'https://www.facebook.com/',
    instagram: 'https://www.instagram.com/',
    twitter: 'https://twitter.com/',
    kuaishou: 'https://www.kuaishou.com/',
    tiktok: 'https://www.tiktok.com/',
    youtube: 'https://www.youtube.com/',
  }
  window.open(list[type]);
}

const swipeList = [
  { img: 0, text: '"<PERSON>, um trabalhador aposentado, lucrou mais de R$ 100.000 em apenas dois meses com a promoção de 777pgday!"' },
  { img: 1, text: '"<PERSON>, um professor do ensino médio, comprou um carro com os lucros mensais que obteve promovendo 777pgday."' },
  { img: 2, text: '"Silva é recém-formada, e agora através da promoção de 777pgday, o lucro que ela ganha todos os meses é suficiente para pagar o aluguel do meu estágio no estado do Ceará."' },
  { img: 3, text: '"Ferreira é trabalhador aposentado de uma fábrica no estado do Rio de Janeiro. Através de 2 meses de promoção contínua, ganhei mais de 20.000 reais em dois meses."' },
  { img: 4, text: '"Pereira é dona de casa e a comissão que ganha mensalmente com a promoção de 777pgday é suficiente para pagar as mensalidades escolares de seus quatro filhos."' },
  { img: 5, text: '"Oliveira, como professora do ensino médio, conseguiu realizar seu sonho de comprar um carro promovendo 777pgday e obtendo lucros mensais!"' },
  { img: 6, text: '"Costa é um advogado de renome e ganha milhões de dólares em lucros todos os meses através da promoção de 777pgday."' },
  { img: 7, text: '"Sousa, aposentado, ganhou milhares de reais em apenas dois meses com a promoção de 777pgday!"' },
  { img: 8, text: '"Rodrigues, como um entusiasta sênior de caça-níqueis, ganha facilmente centenas de milhares de reais em lucros todos os meses por meio da promoção de 777pgday."' },
  { img: 9, text: '"Martins é um empresário de TI de sucesso que ganha dezenas de milhares de reais em lucros todos os meses por meio da promoção de 777pgday."' },
]
</script>
<template>
  <div class="invite-page">
    <div class="banner">
      <h3>Divida 100 milhóes de</h3>
      <h3>reais em dinheiro</h3>
      <p>Ao jogar e ganhar prêmios na plataforma 777pgday, os usuários também podem ganhar generosas comissões por meio do programa de recompensa por convite! Promova usuários com recarga válida, com bônus máximo de 20 reais. Ao mesmo tempo, você também pode obter descontos generosos no volume de negócios do jogo!</p>
    </div>
    <div class="body">
      <div class="swipe-wrapper">
        <!-- <AppImage class="casos" src="/img/promotion/invite-casos.png" alt="" /> -->
        <h2>Casos</h2>
        <van-swipe class="my-swipe" :autoplay="3000" :show-indicators="false">
          <van-swipe-item v-for="i in swipeList" :key="i.img">
            <div class="swipe-item">
              <AppImage class="avatar" :src="`/img/promotion/invite-avatar${i.img}.png`" alt="" />
              <p>{{ i.text }}</p>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <h6>Clique no botão para copiar o link do convite</h6>
      <div class="url-box">
        <span class="url">{{ url }}</span>
        <AppButton @click="toCopy(url)" width="139" height="72" blue  white-text radius="15">Cópia
        </AppButton>
      </div>
      <h6>Partilhar ligações através de software social</h6>
      <div class="social">
        <!-- <AppImage src="/img/promotion/invite-social.png" alt="" /> -->
        <div class="logo">
          <AppImage src="/img/promotion/whatapp-logo.png" alt="" @click="openWeb('whatapp')" />
          <AppImage src="/img/promotion/telegram-logo.png" alt="" @click="openWeb('telegram')" />
          <AppImage src="/img/promotion/facebook-logo.png" alt="" @click="openWeb('facebook')" />
          <AppImage src="/img/promotion/instagram-logo.png" alt="" @click="openWeb('instagram')" />
          <AppImage src="/img/promotion/twitter-logo.png" alt="" @click="openWeb('twitter')" />
          <AppImage src="/img/promotion/kuaishou-logo.png" alt="" @click="openWeb('kuaishou')" />
          <AppImage src="/img/promotion/tiktok-logo.png" alt="" @click="openWeb('tiktok')" />
          <AppImage src="/img/promotion/youtube-logo.png" alt="" @click="openWeb('youtube')" />
        </div>
      </div>
      <!-- <AppImage class="img-group" src="/img/promotion/bg_Nivel.png" alt="" /> -->
      <div class="img-group">
        <div class="level-1">Nivel&nbsp;&nbsp;1</div>
        <div class="level-2">Nivel&nbsp;&nbsp;2</div>
        <div class="level-3">Nivel&nbsp;&nbsp;3</div>
      </div>
      <p class="text">Fornecer à 777pgday um convite efetivo ao cliente (o cliente deve preencher o registro do número
        do celular e concluir uma recarga).
      </p>
      <p class="text">Recompensamos 20.00 reais. Ao mesmo tempo, realizamos anticomissões de acordo com o valor da aposta do jogo do
        usuário do jogo na plataforma do jogo (a comissão é considerável).
      </p>
      <p class="text">A taxa de comissão varia de jogo para jogo.</p>
      <div class="example">
        <p>Por exemplo:</p>
        <p>tom se cadastrou como usuário válido de 777pgday em 10 de abril de 2023.</p>
        <p>Enquanto ganhava o grande prêmio na plataforma, ele também recomendou um colega da empresa.</p>
        <p>O colega concluiu o cadastro e recarregou 25 reais. Nesse momento, tom ganhou 20.00 reais Este colega adora
          jogos de 777pgday e aposta um total de 534.034 reais por 3 dias consecutivos.</p>
        <p>De acordo com a política de descontos do 777pgday, tom ganha mais 2.670,17 reais
          de comissão. Ao mesmo
          tempo, este colega recomenda 777pgday para seus amigos, de acordo com o 777pgday política de descontos,
          Tom também desfruta da política relevante de descontos de apostas do amigo de seu colega.</p>
      </div>
      <p class="text-yellow">Regras de liquidação da plataforma 777pgday:</p>
      <p class="text-yellow">A comissão devolvida pelo 777pgday é atualizada a cada 10 a 30 minutos, e a comissão
        devolvida será liberada toda segunda-feira, horário do Brasil.</p>
      <p class="text-yellow">Os detalhes da comissão podem ser vistos clicando em Convite diário e Detalhes do convite.
      </p>
      <div class="warn">
        Declaração especial: Para garantir a justiça da plataforma, a plataforma adota uma estratégia antitrapaça, e os
        usuários trapaceiros serão banidos permanentemente, os fundos obtidos ilegalmente serão congelados e as
        responsabilidades legais relevantes serão investigadas.
      </div>
    </div>
  </div>
</template>

<style lang='scss' scoped>
@import '../../../theme/mixin.scss';

.invite-page {
  font-size: 26px;
  font-weight: 700;
  padding-top: 100px;

  .banner {
    // background-image: url();
    @include webp('/img/promotion/bg_divida_100_milhoes_de.png');
    width: 100%;
    height: 364px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 15px 40px;
    font-size: 24px;
    margin-bottom: 29px;
    margin-top: 32px;
    h3{
      width: 500px;
      height: 44px;
      line-height: 44px;
      font-family: Arial-BoldMT;
      font-size: 44px;
      font-weight: bold;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #69ffef;
      text-align: center;
      float: right;
    }
    p {
      width: 510px;
      line-height: 28px;
      margin-top: 10px;
      height: 228px;
      overflow-y: auto;
      float: right;
      font-weight: normal;
    }
  }

  .body {
    padding: 0 25px 150px;
  }

  .swipe-wrapper {
    height: 241px;
    width: 683px;
    margin: 0 auto 30px;
    background-image: linear-gradient(0deg, 
      #6600ce 0%, 
      #a500ce 100%), 
    linear-gradient(
      #28374d, 
      #28374d);
    background-blend-mode: normal, 
      normal;
    border-radius: 20px;
    border: solid 2px #ffffff;

    .casos {
      width: 172px;
      display: block;
      margin: 0 auto;
    }
    h2{
      width: 100%;
      height: 80px;
      line-height: 80px;
      font-family: Arial-BoldMT;
      font-size: 48px;
      font-weight: bold;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #69ffef;
      text-align: center;
    }
    .my-swipe {
      height: 150px;
      width: 700px;
      overflow: hidden;

      .swipe-item {
        display: flex;
        align-items: center;
        padding-left: 25px;

        .avatar {
          width: 82px;
          margin-right: 23px;
          border-radius: 50%;
        }

        p {
          width: 540px;
          font-weight: normal;
          line-height: 30.47px;
        }
      }
    }
  }

  h6 {
    color: #679fea;
    font-size: 30px;
    margin-bottom: 20px;
  }

  .url-box {
    width: 100%;
    height: 72px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .url {
      display: block;
      width: 540px;
      height: 72px;
      line-height: 72px;
      background-color: #28374d;
      border-radius: 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 21px;
      font-weight: normal;
      overflow: hidden;
      margin-right: 13px;
      padding-left: 24px;
    }
  }

  .social {
    width: 695px;
    height: 120px;
    border-radius: 20px;
    background: #28374d;
    padding: 26px 18px;
    text-align: center;
    margin-bottom: 30px;

    img {
      width: 618px;
    }
    .logo{
      img {
        width: 65px;
        float: left;
        margin-left: 16px;
      }
    }
  }

  .img-group {
    @include webp('/img/promotion/bg_Nivel.png');
    width: 100%;
    height: 415px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-bottom: 30px;
    position: relative;
    .level-1{
      position: absolute;
      font-size: 22px;
      color: #ffffff;
      top: 105px;
      left: 311px;
    }
    .level-2{
      position: absolute;
      font-size: 22px;
      color: #ffffff;
      top: 238px;
      left: 311px;
    }
    .level-3{
      position: absolute;
      font-size: 22px;
      color: #ffffff;
      top: 369px;
      left: 311px;
    }
  }

  .text {
    line-height: 30.47px;
    font-size: 24px;
    color: #4d5d76;
  }

  .text-yellow {
    @extend .text;
    color: #FFD61C;
    font-weight: normal;
	  font-size: 24px;
  }

  .example {
    margin-top: 20px;
    width: 695px;
    background-color: #28374d;
    border-radius: 20px;
    padding: 20px 25px 20px;
    margin-bottom: 30px;
    font-weight: normal;
    font-size: 24px;

 
    p {
      line-height: 30.47px;
    }
  }

  .warn {
    margin-top: 30px;
    width: 695px;
    background-color: #28374d;
    border-radius: 20px;
    color: #fe2a2a;
    line-height: 30.47px;
    padding: 20px 20px;
    font-weight: normal;
  }
}
</style>
