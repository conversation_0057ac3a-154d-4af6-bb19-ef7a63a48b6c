<script lang="ts" setup name="weeklyDeposit">
import { SubDepositRecordItem } from '~/core/http/api';


const router = useRouter()

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)

enum TabActive {
  thisweek = 'thisweek',
  lastweek = 'lastweek',
}
const tabData = ref([
  {
    label: 'ESSA SEMANA',
    value: TabActive.thisweek
  },
  {
    label: 'SEMANA PASSADA',
    value: TabActive.lastweek
  }
])
const tabActive = ref(TabActive.thisweek)

const themeVars = reactive({
      tabsCardHeight: 'var(--app-px-67)',
      paddingMd:'var(--app-px-48)',
      buttonNormalPadding: 0,
      cellTextColor:'#ffffff',
      cellBackground:'var(--app-toggle-norbg-color)',
      cellRightIconColor:'#ffffff',
      position: 'absolute',
      left: '0px',
      right: '0px',
      top: '0px',
      left: '0px',
      // sliderBarHeight: '4px',
      // sliderButtonWidth: '20px',
      // sliderButtonHeight: '20px',
      // sliderActiveBackground: '#07c160',
      // buttonPrimaryBackground: '#07c160',
      // buttonPrimaryBorderColor: '#07c160',
    });

const getCashBack = ()=>{
  console.log('getCashBack')
}

const weekRecordViewDatas = ref<SubDepositRecordItem[]>([])

const { run: runGetLastWeekRecord, data: proxyLastWeekRecordDatas } = useRequest(() => ApiGetLastWeekRecord(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    weekRecordViewDatas.value = proxyLastWeekRecordDatas?.value || [];
  }
})

const { run: runGetthisWeekRecord, data: proxyThisWeekRecordDatas } = useRequest(() => ApiGeThisWeekRecord(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    weekRecordViewDatas.value = proxyThisWeekRecordDatas?.value || [];
  }
})

runGetthisWeekRecord();
const tabClick = (value) => {
  tabActive.value = value;
  if(value == TabActive.lastweek){
    runGetLastWeekRecord();
  } else {
    runGetthisWeekRecord();
  }
}
</script>

<template>
  <van-config-provider :theme-vars="themeVars">
      <div class="root-page">
      <AppIndexHeader />
      <AppPageTitle left-arrow title="" title-weight="700" />
      <section class="content">
        <div class="tabs">
          <div class="tab" :class="tabActive == data.value ? 'active':''" v-for="data in tabData" :key="data.value" @click="() => tabClick(data.value)">
            {{ data.label }}
          </div>
        </div>
        <div style="height: var(--app-px-33);"></div>
        <div class="list-item" v-for="(item, index) in weekRecordViewDatas" :key="index" >
          <div class="line top-bg">
            <div class="text-box">{{ "ID:" + item.oid }}</div>
            <div class="text-box">{{ "Date:" + dayjs(getBrazilTime(item.created_at * 1000)).format('YYYY-MM-DD')}}</div>
          </div>
          <div class="line bottom-bg">
            <div class="text-box">Depósitos</div>
            <div class="text-box dollar">{{ "R$" + item.amount }}</div>
          </div>
        </div>
      </section>
    </div>
  </van-config-provider>

</template>

<style lang="scss" scoped>
@import '/src/theme/mixin.scss';

.root-page {
  background: var(--theme-main-bg-color);
  padding-top: var(--app-navbar-height);
  padding-bottom: var(--app-footer-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  
  color:#192756;
  font-size:24px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left:0;
  // justify-content: center;

  // flex-wrap: wrap;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background: var(--theme-main-bg-color);
  padding-bottom: var(--app-footer-height);
  font-family: Arial;
}


.tabs{
  display: flex;
  justify-content: space-between;
  width: 716px;
  // width: ;
  .tab{
    width:350px;
    height:80px;
    background-color: var(--app-toggle-norbg-color);
    border-radius:10px;
    font-weight:700;
    color:#ffffff;
    font-size:30px;
    text-align: center;
    line-height: 80px;

    &.active {
      background-color:  var(--app-toggle-selbg-color);
    }
  }
}

.list-item{
  width:705px;
  height:100px;
  background:var(--app-box-bg-color);
  border-radius:10px;
  color:var(--app-title-color);
  font-size:24px;
  margin-bottom: 20px;
  .line{
    padding: 0 28px 0 42px;
    display: flex;
    justify-content: space-between;
    line-height: 45px;
  }
  .top-bg{
    height:55px;
    line-height: 55px;
    background:#679aec;
    color:#ffffff;
    border-radius:10px 10px 0 0;
  }
  .dollar{
    font-weight:700;
    color:#3b82f6;
    font-size:30px;
  }
}

</style>

<route lang="yaml">
  meta:
    auth: true
</route>
