<script lang="ts" setup>
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
appStore.setFooterDialogVisble(false)

const key = route.query.key;
const name = route.query.name;

const nameList = ref([
  {name:"Deposite diariamente e receba um bônuspromocional de 5%"},
  {name:""},
  {name:"Agente Nacional, Compartilhe e Ganhe Dinheiro!"},
])

function clickLeft(){
    appStore.setFooterDialogVisble(true)
}
</script>


<template>
    <AppPageTitle left-arrow :title="`${nameList[key].name}`" titleSize="17px" titleWidth="300px"  @clickLeft="clickLeft"/>
    <AppImage class="promotion-img" :src="`/img/promotion/detail/active_${key}`" alt=""/>
    <div class="footer-space"/>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.promotion-img{
    width:750px;
}

.footer-space {
  height: 200px; /* 或者任何你需要的高度 */
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>