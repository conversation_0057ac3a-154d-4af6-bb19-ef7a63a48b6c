<script setup lang="ts" name="app-index-header">
import { GameNavEnum } from "~/types/common";
const router = useRouter();
const appStore = useAppStore();
const { isLogin, custService, showLeftMenu } = storeToRefs(appStore);
import menuIcon from "/icons/svg/left-menu.svg?raw";
const { userInfo } = storeToRefs(appStore);
const gameStore = useGameStore();
const { gameNavData } = storeToRefs(gameStore);
import headerDepSelectIcon from "/icons/svg/headerDepSelect.svg?raw";
const showLeft = ref(false);
const showAddToDesktop = ref(false);
const url = ref(location.origin);
const selected = ref();
const openMenu = ref(false);
const closeMenu = ref(false);

const setShowLeft = (value: boolean) => {
  showLeft.value = value;
};
const copyUrl = () => {
  copy(url.value);
  showToast("Copied!");
};

defineProps({
  logoClick: Function,
});

const setShowAddToDesktop = (value: boolean) => {
  showAddToDesktop.value = value;
};
// 刷新金额
const loading = ref(false);
const refreshBalance = () => {
  if (loading.value) {
    return;
  }

  loading.value = true;
  appStore.runGetMemberInfo();
  setTimeout(() => {
    loading.value = false;
  }, 1500);
};
const openUrl = (url?: string) => {
  if (!url) return;
  window.open(url, "_blank");
};

const { isIos } = getBrowser();
// const { data: downloadUrl } = useRequest(() => getAppDownloadUrl(isIos ? 36 : 35))

const promos = [
  {
    path: "/promotion-detail/invite",
    icon: "btn_bonus.png",
    title: "Bônus",
  },
  {
    path: "/promotion-detail/reward-box",
    icon: "btn_pomotion.png",
    title: "Pomoção",
  },
];

const route = useRoute();
const { id } = route.params;

const clickId = ref(+id);
const clickSelect = ref(false);
// const clickMenu = ref(false)
const currentActive = computed(() => {
  return gameNavData.value.findIndex((item) => item.id === clickId.value);
});

const liClick = (item: any) => {
  clickId.value = item.id;
  // if (item.path) {
  //   router.push(item.path)
  //   return
  // }
  // router.push(`/game-list/${item.id}`)
  gameStore.setNeedResizeGameTypeTab(item);
};

const hahaClose = (func: any) => {
  if (func) func();
  setTimeout(() => {
    showLeft.value = false;
  }, 0);
};

const downloadApp = () => {
  // if (isIos) {
  //   showAddToDesktop.value = true;
  // } else {
  //   openUrl(downloadUrl.value?.url)
  // }
};

watch(
  route,
  (val, old) => {
    // if (val.path === '/menu') {
    //   clickMenu.value =true
    // }else{
    //   clickMenu.value =false
    // }

    if (val.path === "/") {
      clickId.value = GameNavEnum.Quente;
      return;
    }
    if (val.path === "/rec-fav-game") {
      clickId.value = GameNavEnum.Dentro_De_Casa;
      return;
    }
    if (val.path.indexOf("/game-list/") === -1) {
      clickId.value = -1;
    } else {
      clickId.value = +route.params.id;
    }
  },
  { immediate: true }
);

let deltime = true;
function gotoMenu() {
  if (deltime) {
    if (showLeftMenu.value) {
      appStore.setLeftMenuState(false);
    } else {
      appStore.setLeftMenuState(true);
    }
    deltime = false;
    setTimeout(() => {
      deltime = true;
    }, 500);
  }
}

//弹出选项框
const clickSelectEvent = () => {
  clickSelect.value = !clickSelect.value;
};
//选项
const clickSelectEvent_item = (type: number) => {
  switch (type) {
    case 1:
      router.push("/withdraw");
      break;
  }
  clickSelect.value = false;
};
</script>

<template>
  <header class="app-index-header">
    <div class="left">
      <!-- <button class="popup-btn" @click="setShowLeft(true)">
        <AppImage class="img" src="/icons/icon_menu.png" alt="" srcset="" />
      </button> -->
      <slot name="logoDev">
        <div class="menu" @click="gotoMenu">
          <!-- <div
            class="icon-img"
            :class="{ rotate: showLeftMenu, rotate2: !showLeftMenu }"
            v-html="menuIcon"
          ></div> -->
          <img
            class="icon-img"
            src="/img/header/homev2_icon_topleftmenu.webp"
            alt=""
          />
        </div>

        <a class="logo" @click="logoClick ? logoClick() : $router.push('/')">
          <AppImage class="img" src="/logo1.png.webp" alt="" />
        </a>
      </slot>
    </div>
    <div class="right">
      <!-- <AppIndexHeaderUserAmount v-if="isLogin" /> -->
      <slot name="right">
        <!--个人信息  -->
        <div v-if="isLogin" class="right-balance">
          <AppImage class="right-logo" src="icons/content.webp" alt="" />
          <label class="right-text" @click="appStore.setShowIndexModule(3)">
            {{ userInfo.formatAmount }}</label
          >
          <!-- <div
            class="right-refresh"
            :class="{ rotate: loading }"
            @click="refreshBalance"
          ></div> -->
          <div
            class="right-refresh"
            :class="{ rotate: loading }"
            @click="refreshBalance"
          >
            <svg
              width="25"
              height="25"
              viewBox="0 0 14 15"
              fill="#6952eb"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_6585_93480)">
                <g clip-path="url(#clip1_6585_93480)">
                  <path
                    d="M5.24939 13.0677L7.83053 8.61198L8.90223 10.512C9.66287 10.0297 10.2867 9.37531 10.7181 8.60705C11.1495 7.83879 11.375 6.98057 11.3747 6.10888C11.3747 4.63371 10.3541 3.17073 9.33343 2.21148C11.3333 3.00996 13.125 5.02394 13.125 7.22207C13.1249 8.25425 12.8084 9.26385 12.2146 10.1264C11.6207 10.989 10.7755 11.6668 9.783 12.0763L10.7919 13.625L5.24939 13.0677ZM0.875 7.77793C0.875225 6.74578 1.19169 5.73622 1.78542 4.87362C2.37915 4.01102 3.22426 3.33307 4.21656 2.92324L3.20815 1.375L8.74862 1.93135L6.16642 6.38754L5.09733 4.48756C4.33637 4.96966 3.71225 5.62408 3.28066 6.39246C2.84907 7.16083 2.62339 8.01926 2.62383 8.89113C2.62383 10.3663 3.64445 11.8293 4.66508 12.7885C2.66772 11.9915 0.875561 9.97753 0.875561 7.7794L0.875 7.77793Z"
                    fill="#6952eb"
                  ></path>
                </g>
              </g>
              <defs>
                <clipPath id="clip0_6585_93480">
                  <rect
                    width="14"
                    height="14"
                    fill="#6952eb"
                    transform="translate(0 0.5)"
                  ></rect>
                </clipPath>
                <clipPath id="clip1_6585_93480">
                  <rect
                    width="13.363"
                    height="14"
                    fill="#6952eb"
                    transform="translate(0.318359 0.5)"
                  ></rect>
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>

        <!-- 登录 -->
        <div v-else class="login-reg">
          <div class="login q-bounce" @click="() => openLoginDialog(true)">
            Entrar
          </div>
          <div
            class="register q-bounce"
            @click="() => openRegisterDialog(true)"
          >
            <span>Registro</span>
          </div>
          <!-- <div class="service-bg">
            <img src="/img/header/home_top_service.webp" alt="" />
          </div> -->
        </div>

        <div
          class="service-bg"
          @click="
            () => {
              router.push('/serviceMessages');
              appStore.setShowRouterView(true);
              showLeftMenu = false;
            }
          "
        >
          <img src="/img/header/home_top_service.webp" alt="" />
        </div>
      </slot>
      <!-- <button class="message" @click="router.push('/notice-list')">
        <AppImage class="img" src="/icons/i-message.png" alt="" srcset="" />
      </button> -->
    </div>
  </header>
  <van-popup
    v-model:show="showLeft"
    class="van-popup-dark"
    teleport="body"
    position="left"
    :style="{ height: '100%' }"
  >
    <div class="nav-pop">
      <div class="navimg">
        <AppImage
          src="/icons/icon_menu.png"
          alt=""
          srcset=""
          @click="setShowLeft(false)"
        />
      </div>
      <div v-if="isLogin">
        <AppIndexLeftUserInfo />
      </div>
      <div v-else class="login-reg">
        <AppButton
          @click="hahaClose(() => openLoginDialog(true))"
          width="195"
          height="60"
          font-size="26"
          gray
          white-text
          :radius="100"
        >
          Conecte-se
        </AppButton>
        <AppButton
          @click="hahaClose(() => openRegisterDialog(true))"
          width="152"
          height="60"
          font-size="26"
          gray1
          :radius="100"
          white-text
        >
          registro
        </AppButton>
      </div>
      <div class="promos-box">
        <ul>
          <li v-for="p in promos" :key="p.path">
            <AppImage
              :src="'/img/' + p.icon"
              @click="hahaClose(() => $router.push(p.path))"
            />
            <div class="text">{{ p.title }}</div>
          </li>
        </ul>
      </div>
      <div class="nav-box">
        <ul>
          <li
            v-for="(nav, idx) in gameNavData"
            @click="hahaClose(() => liClick(nav))"
            :key="nav.id"
            :class="{
              active: currentActive === idx,
            }"
          >
            <AppImage
              :src="`/icons/left_nav_${nav.id}${
                currentActive === idx ? '-active' : ''
              }.png`"
            />
            <span class="title">{{ nav.name }}</span>
          </li>
        </ul>
      </div>
      <div class="line"></div>
      <div class="nav-box">
        <ul>
          <li
            @click="hahaClose(() => $router.push('/promotion-detail/telegram'))"
          >
            <AppImage src="/icons/icon_telegram_channel.png" />
            <span class="title">Canal De Telegram</span>
          </li>
          <li @click="hahaClose(() => $router.push('/vip'))">
            <AppImage src="/icons/icon_introduction_to_the_VIP_level.png" />
            <span class="title">Introdução ao nível VIP</span>
          </li>
          <li @click="hahaClose(() => downloadApp())">
            <AppImage src="/icons/icon_download_app.png" />
            <span class="title">Baixar aplicativo</span>
          </li>
          <li @click="hahaClose(() => openUrl(custService))">
            <AppImage src="/icons/icon_online_service.png" />
            <span class="title">Serviço on-line</span>
          </li>
        </ul>
      </div>
      <div class="copy-container">
        <input v-model="url" type="text" class="input" disabled />
        <div class="copy-btn" @click="copyUrl()"></div>
        <!-- <AppButton @click="copyUrl()" class="btn" width="150" height="70" bold font-size="28" blue-to-bottom :radius="8"
          white-text>
          Cópia
        </AppButton> -->
      </div>
      <div class="warn-txt">
        Copie o link e cole-o no navegador do seu computador para abri-lo em seu
        computador
      </div>
    </div>
  </van-popup>
  <van-popup v-model:show="showAddToDesktop">
    <div class="add-desktop">
      <AppImage class="img" src="/img/add-to-desktop.png" />
      <div class="close" @click="setShowAddToDesktop(false)">
        <AppImage src="/icons/dialog-close.png" />
      </div>
    </div>
  </van-popup>

  <div class="selectParent" v-if="clickSelect" @click="clickSelectEvent">
    <!-- <AppImage class="select-img" :src="`icons/header_dep_${clickSelect? 'select_active':'select'}.webp`" alt=""  @click="clickSelectEvent"/> -->
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";
.service-bg {
  background: #6952eb;
  border-radius: 2.5vw;
  height: 8vw;
  width: 8vw;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    height: 6vw;
    width: 6vw;
    display: block;
  }
}
.add-desktop {
  .img {
    width: 100%;
  }
  .close {
    margin: 0 auto;
    padding: 30px 0;
    text-align: center;
    img {
      width: 40px;
    }
  }
}
.copy-container {
  border-radius: 56px;
  background: #131b26;
  width: 478px;
  height: 60px;
  line-height: 60;
  display: flex;
  padding-left: 24px;
  overflow: hidden;
  margin-top: 40px;
  margin-left: 20px;
  margin-bottom: 20px;
  input {
    flex: 1;
    background: transparent;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #fff;
    font-size: 26px;
    font-weight: 400;
    border: none;
  }

  button {
    border-radius: 0 60px 60px 0 !important;
  }
  .copy-btn {
    width: 81px;
    height: 60px;
    @include webp("/icons/btn_link.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.nav-pop {
  background: #1c2533;
  // box-shadow: 10px 0px 26px 0px rgba(0, 0, 0, 0.40);
  backdrop-filter: blur(20px);
  width: 511px;
  min-height: 100%;
  padding-bottom: 100px;

  .line {
    width: 476px;
    height: 2px;
    border-radius: 10px;
    background: #5775a6;
    margin-left: 32px;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .navimg {
    padding: 26px 61px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    img {
      width: 53px;
    }
  }

  .promos-box {
    margin-top: 30px;

    ul {
      width: 426px;
      margin: 0;
      padding: 0;
      margin-left: 16px;
      display: flex;
      gap: 18px;
      align-items: center;
      justify-content: center;

      li {
        width: 208px;
        height: 91px;
        img {
          width: 208px;
        }
        .text {
          color: #fff;
          font-size: 28px;
          // text-align: center;
          margin-top: -70px;
          margin-left: 10px;
        }
      }
    }
  }

  .nav-box {
    padding-left: 20px;

    ul {
      li {
        height: 60px;
        width: 478px;
        color: #5774a5;
        font-size: 30px;
        font-weight: 400;
        line-height: 24px;
        border-radius: 10px;
        display: flex;
        gap: 20px;
        padding-left: 28px;
        align-items: center;
        justify-content: flex-start;
        margin-top: 20px;
        margin-bottom: 20px;

        img {
          width: 40px;
        }

        &.active {
          background: #374a69;
          border-radius: 30px;
          color: #fff;
          font-size: 30px;
          // font-weight: 700;
        }
      }
    }
  }

  .warn-txt {
    color: #5775a6;
    font-size: 24px;
    font-weight: 400;
    line-height: 30px;
    text-align: left;
    width: 478px;
    margin-left: 20px;
    margin-top: 16px;
    margin-bottom: 16px;
  }
}

.login-reg {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 15px;

  .login,
  .register {
    min-width: 16.333333vw;
    height: 8vw;
    border-radius: 2.5vw;
    font-size: 3.4vw;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: box-shadow 0.2s;
    box-shadow: 0 0.266667vw 1.066667vw rgba(80, 60, 180, 0.08);
    user-select: none;
  }

  .login {
    background: #6c5cff;
    color: #fff;
    border: none;
  }

  .register {
    background: linear-gradient(90deg, #6c5cff 0%, #c0ff61 100%);
    color: #fff;
    border: none;
  }
}

.q-bounce {
  transition: transform 0.18s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.q-bounce:active {
  transform: scale(0.92);
}

.select {
  // position: absolute;
  margin-right: 10px;
  @include webp("/img/index/header_dep");
  background-repeat: no-repeat;
  background-size: 170px 52px;
  width: 170px;
  height: 52px;
  // background:#f9fd4e;
  // border-radius:7px ;
}

.select-Depos {
  height: 52px;
  line-height: 52px;
  text-align: center;
  color: var(--theme-bg-color);
  font-size: 22px;
  padding-left: 24px;
}

.select-img {
  // position: absolute;
  // margin-right: 92px;
  display: inline-block;
  width: 20px;
  height: 10px;
  margin-left: 30px;
}

.select2-down {
  position: absolute;
  top: 77px;
  right: 55px;
  width: 145px;
  // height:190px;
  background: var(--theme-bg-color);
  // border:1px solid;
  border: 1px solid;

  border-color: var(--theme-color-line);
  border-radius: 10px;

  li {
    display: inline-block;
    font-size: 22px;
    // background-color: #1d001a;
    width: 165px;
    height: 60px;
    color: #ffffff;
    margin-top: 20px;
    margin-bottom: 20px;
    padding-left: 20px;
    // text-align: center;
    line-height: 60px;
  }
}

.searchImg {
  float: right;
  margin-right: 0px;
  @include webp("/img/index/header_select");
  background-repeat: no-repeat;
  background-size: 27px 27px;
  width: 27px;
  height: 27px;
}

// .select-line{
//   position: absolute;
//   left:92px;
//   top: 4px;
//   height: 36px;
//   width: 1px;
//   background-color:#164633 ;
// }

.input-container {
  height: 72px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(0deg, #000a1d 0%, #000a1d 100%),
    linear-gradient(0deg, #011a51 0%, #011a51 100%), #2a2e3e;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .input {
    background-color: transparent;
    border: none;
    color: #fff;
    height: 100%;
    flex: 1;
    padding: 0 20px;
    font-size: 28px;
  }
}

.left-content {
  width: 600px;
  height: 100%;
  background: linear-gradient(0deg, #011a51 0%, #011a51 100%), #d9d9d9;

  .b-content {
    padding: 25px 20px;

    .btn-margin {
      margin-bottom: 12px;
    }

    .telegram-img {
      width: 50px;
      height: 50px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        vertical-align: middle;
      }

      .tip {
        position: absolute;
        top: 0;
        right: 0;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        line-height: 15px;
        text-align: center;
        background-color: #fe7e8d;
        color: #fff;
      }
    }

    .tips {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      color: #ffd500;
      text-align: center;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: 30px;
    }
  }

  .top {
    display: flex;
    justify-content: flex-end;
    // padding: 20px 20px 0 0;
    height: 106px;

    .top-btn {
      padding: 0 40px;

      .img {
        width: 40px;
        height: 40px;
        vertical-align: middle;
      }
    }
  }
}

button {
  border: none;
  background: none;
  outline: none;
}

.left {
  display: flex;
  align-items: center;

  .menu {
    // background-color: #000A1D;
    width: 70px;
    height: var(--app-navbar-height);
    display: flex;
    align-items: center;
  }

  .popup-btn {
    padding: 12px 8px 12px 20px;

    .img {
      width: 53px;
      height: 42px;
      vertical-align: middle;
    }
  }

  .logo {
    // width: 95px;
    display: inline-block;
    margin-left: 3vw;
    .img {
      // width: 100%;
      height: 7vw;
      vertical-align: middle;
    }
  }
}

.right {
  display: flex;
  justify-content: center;
  align-items: center;
  .right-balance {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 13vw;
    min-width: 100px;
    max-width: 300px;
    height: 8vw;
    // line-height: 72px;
    font-size: 30px;

    background: var(--theme-top-nav-bg);
    border: 1px solid rgba(105, 82, 235, 1);
    // border-color: var(--theme-color-line);
    border-radius: 3vw;

    // div{
    //   white-space: nowrap;
    //   width: 100%;
    //   overflow: hidden;
    // }
    .right-logo {
      // position: absolute;
      // left: 2px;
      // top: 5px;
      // display: inline-block;

      display: inline-block; /* 将元素设置为行内块元素 */
      vertical-align: middle; /* 垂直居中对齐 */

      width: 33px;
      height: 33px;
      margin-left: 5px;
      // margin-top: 5px;
      // margin-left: 5px
      // margin:  0 auto ;
      // margin-left: 5px;
      // margin-top: -10px;
      // @include webp('/icons/content');
      // background-repeat: no-repeat;
      // background-size: 33px 33px;
    }

    .right-text {
      display: inline-block; /* 将元素设置为行内块元素 */
      vertical-align: middle; /* 垂直居中对齐 */
      // vertical-align: middle; /* 垂直居中对齐 */
      // width: 100px;
      font-size: 30px;
      // text-align: right;
      // line-height: 44px;
      // word-wrap: break-word; /* 旧版浏览器支持 */
      // overflow-wrap: break-word; /* 标准属性 */
      // padding-top: 6px;
      padding-left: 10px;
      padding-right: 10px;
      color: rgb(255, 148, 38); //255 148 38
      text-decoration: underline;
      // padding-right: 5px;
    }

    .right-refresh {
      display: flex; /* 将元素设置为行内块元素 */
      justify-content: center;
      align-items: center;

      // vertical-align: middle; /* 垂直居中对齐 */
      // margin-top: 5px;
      // margin-right: 10px;
      // float: right;
      // width: 29px;
      // height: 31px;
      // @include webp("/icons/refresh");
      // background-repeat: no-repeat;
      // background-size: 29px 31px;

      &.rotate {
        // animation: spin 1s linear infinite;
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);

        -webkit-transition: -webkit-transform 1s linear;
        transition: transform 1s linear;
      }
    }

    .flex-sb {
      // display: flex;
    }

    .right-dev {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 74px;
      width: 355px;
      margin-left: 70px;
      // background-color: #D9D9D9;
    }

    .right-add {
      width: 60px;
      margin-right: 75px;
    }
  }
  .message {
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background: linear-gradient(180deg, rgba(46, 50, 69, 0) 0%, #011a51 100%);
    box-shadow: 0px 1px 4px 0px rgba(255, 255, 255, 0.3) inset,
      0px 1px 0px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 20px 0 10px;

    .img {
      width: 46px;
    }
  }
}

.app-index-header {
  height: var(--app-navbar-height);
  // background: linear-gradient(180deg, #044B9A 0%, #011A51 100%), linear-gradient(180deg, #1D1F2C 0%, #252734 100%);
  // background-color: #035ACA;
  background: var(--theme-top-nav-bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 0px; /* 距离顶部0px时固定定位 */
  z-index: 3;
  width: 750px;
  left: 0;
  // border-bottom: 0.01rem solid var(--theme-color-line);

  .right {
    margin-right: 20px;
  }
}

.activity {
  height: 70px;
  display: flex;
  align-items: center;
  color: #fff;

  .img {
    width: 40px;
    vertical-align: middle;
    margin-right: 22px;
    margin-left: 10px;
  }
}

.style4 {
  margin-bottom: 20px;
}

.icon-img {
  margin-left: 2.5vw;
  // margin-top: 35px;

  width: 8.8vw;
  transform-origin: 18px 12px;
  &.rotate {
    // animation: spin 1s linear infinite;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);

    // -webkit-transition:-webkit-transform 100ms linear;
    transition: transform 300ms linear;
  }

  &.rotate2 {
    // animation: spin 1s linear infinite;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);

    // -webkit-transition:-webkit-transform 100ms linear;
    transition: transform 300ms linear;
  }
}

.selectParent {
  position: absolute;
  // background-color: #000A1D;
  width: 100vw;
  height: 100%;
  z-index: 11;
}
</style>
<!-- 首页顶部 -->
