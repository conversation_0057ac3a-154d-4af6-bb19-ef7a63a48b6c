import { createRouter, createWebHistory } from "vue-router";
import routes from "virtual:generated-pages";
import { setupLayouts } from "virtual:generated-layouts";
import { log } from "console";
let transitionName = ref();
// 路由
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: setupLayouts(routes),
  scrollBehavior(to, from, savedPosition) {
    return { top: 0, behavior: "instant" };
  },
});
router.beforeEach((to, from, next) => {
  // 根据路由标记判断触发哪个动画
  if (to.meta.index > from.meta.index) {
    console.log("to.meta.index > from.meta.index");
    // 从右往左动画
    transitionName.value = "slide-right";
  } else if (to.meta.index < from.meta.index) {
    console.log("to.meta.index < from.meta.index");
    // 从左往右动画
    transitionName.value = "slide-left";
  } else {
    console.log("to.meta.index == from.meta.index");
    transitionName.value = "";
  }

  if (to.path == "/serviceMessages") {
    next();
    return;
  }
  const appStore = useAppStore();
  appStore.isApp = to.query["is-app"] === "1";

  if (to.query.t) {
    appStore.setToken(to.query.t.toString());
  }

  //只要有参数就保存 用于注册绑定
  let query = undefined;
  if (to.query.id || to.query.business_id || to.query.channel_id) {
    // console.log("query---id="+to.query.id)
    // console.log("query---business=id-"+to.query.business_id)
    // console.log("query---channel=id-"+to.query.channel_id)
    sessionStorage.setItem("LinkId", JSON.stringify(to.query));
    query = to.fullPath.split("?")[1];
    if (query) {
      sessionStorage.setItem("query", query);
    }
  }

  //重新取出来
  query = sessionStorage.getItem("query");

  // console.log("query ===query="+query)

  // 注册链接打开弹窗
  if (to.path === "/register") {
    // sessionStorage.setItem('LinkId', JSON.stringify(to.query))
    if (!appStore.isLogin) {
      openRegisterDialog();
      next(query ? "/?" + query : "/");
      return;
    }
  }

  // 未登录，路由需要权限，跳转到登录页
  if (to.meta.auth && !appStore.isLogin) {
    openLoginDialog();
    next(query ? "/?" + query : "/");
    return;
  } else {
    if (to.matched.length > 0 && to.matched[0].path == "/:all(.*)*") {
      //无效路径
      next(query ? "/?" + query : "/");
    } else {
      if (JSON.stringify(to.query) == "{}" && query) {
        console.log("JSON.stringify(to.query)" + JSON.stringify(to.query));
        next(to.fullPath + "?" + query);
      } else {
        next();
      }
    }
  }
});
router.afterEach(() => {
  setTimeout(() => {
    window.scrollTo({
      behavior: "smooth",
      top: 0,
    });
  }, 0);
});

export default router;
