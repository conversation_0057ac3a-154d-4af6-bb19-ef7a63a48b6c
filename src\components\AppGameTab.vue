<script setup lang="ts" name="app-game-tab">
import { GameNavEnum, GameHallTopEnum } from "~/types/common";

defineProps({
  text: String,
});

const router = useRouter();
const route = useRoute();
const { id } = route.params;

const gameStore = useGameStore();
const { gameNavData, needResizeGameTypeTab } = storeToRefs(gameStore);

//是否登录
const appStore = useAppStore();
const { isLogin } = storeToRefs(appStore);

const clickId = ref(+id);

// const navBoxRef = ref()
const gameTabRef = ref();

const menus = [
  { path: "/", id: GameHallTopEnum.Popular, cateName: "Popular" },
  { path: "/slots", id: GameHallTopEnum.Solts, cateName: "Solts" },
  {
    path: "/subgame/recente",
    id: GameHallTopEnum.Recente,
    cateName: "Recente",
  },
  {
    path: "/subgame/favoritos",
    id: GameHallTopEnum.Favoritos,
    cateName: "Favoritos",
  },
];

const liClick = (item: any) => {
  console.log(JSON.stringify(item));
  clickId.value = item.id;
  if (item.path === "/") {
    window.location.href = "#app-index-game-container-popular";
  } else if (item.path === "/slots") {
    window.location.href = "#app-index-game-container-slots";
  }
  const scrollDown = window.scrollY;
  console.log(scrollDown);
  let scrollLeft = window.scrollX || window.pageXOffset;
  let scrollTop = window.scrollY || window.pageYOffset;
  console.log(
    "Horizontal scroll position:",
    scrollLeft,
    "Vertical scroll position:",
    scrollTop
  );
  if (item.path) {
    router.push(item.path);
    return;
  }
  router.push(`/game-list/${item.id}`);
};

watch(
  route,
  (val, old) => {
    if (val.path === "/") {
      clickId.value = GameHallTopEnum.Popular;
      return;
    }
    if (val.path === "/slots/recente") {
      clickId.value = GameHallTopEnum.Recente;
      return;
    }
    if (val.path === "/slots/favoritos") {
      clickId.value = GameHallTopEnum.Favoritos;
      return;
    }

    // if (val.path === '/Slots'){
    //   clickId.value = GameNavEnum.Slots
    //   return
    // }
    // if (val.path.indexOf('/game-list/') === -1) {
    //   clickId.value = -1
    // } else {
    //   clickId.value = +route.params.id
    // }
  },
  { immediate: true }
);

watch(needResizeGameTypeTab, (val) => {
  if (val) {
    // liClick(val)
    // gameTabRef.value?.resize()
    tabsRefs.value[val.id]?.click();
  }
});

const tabsRefs = ref<any>({});

const setTabRef = (ele: any, id: any) => {
  tabsRefs.value[id] = ele;
};

// const hh = ref(clickId.value)
</script>

<template>
  <!-- <ul class="app-game-tab">
    <li class="item" v-for="(item, i) in gameNavData" @click="liClick(item)" :key="item.id" :class="{
      active: currentActive === i
    }">
      <AppImage class="icon" :src="`/icons/game-tab${item.id}.png`" alt="" />
      <span v-html="item.name"></span>
    </li>
  </ul> -->
  <div class="app-game-tab-sec">
    <div class="left">
      <!-- <ul ref="navBoxRef" class="nav-box">
        <li class="n-item" v-for="(item, i) in gameNavData" @click="liClick(item)" :key="item.id" :class="{
          active: currentActive === i
        }">
        <AppImage class="icon" :src="`/icons/gty_${item.id}.png`" alt="" />
        </li>
      </ul> -->

      <!-- <div class="left-center" v-if="!isLogin">
        <label class="left-center-text">Pronto para jogar? Cadastre-se agora！</label>
        <div class="left-center-reg" @click="openRegisterDialog(true)">
          <label class="left-center-reg-text">Cadastre-se</label>
        </div>
      </div>

      <div class="left-center" v-if="isLogin">
        <label class="left-center-text">Deposite agora e receba bônus enormes！</label>
        <div class="left-center-reg left-center-dep" @click="$router.push('/finance')">
          <label class="left-center-reg-text">Depósito</label>
        </div>
      </div> -->

      <!-- <van-tabs ref="gameTabRef" :swipe-threshold="4" line-height="0" background="transparent" line-width="0" class="game-type-tabs" v-model:active="clickId">
        <van-tab v-for="item in gameNavData" :key=item.id   :name=item.id  >
          <template #title v-if="item.id!=GameNavEnum.Platform">
            <div :ref="(ele) => setTabRef(ele, item.id)" class="n-tab-item" @click="liClick(item)" :class="{active: clickId === item.id}">
              <AppImage class="icon" :class="`icon_${item.id}`" :src="`/icons/nav_${item.id}${clickId === item.id? '-active' : ''}.png`" alt="" />
              <div class="text" :class="{text2:item.id==GameNavEnum.Slot,active: clickId === item.id}">{{ item.cateName }}</div>
              <div class="text_line" v-if="clickId === item.id" ></div>
            </div>
          </template>
        </van-tab>
      </van-tabs> -->

      <van-tabs
        ref="gameTabRef"
        :swipe-threshold="4"
        line-height="0"
        background="transparent"
        line-width="0"
        class="game-type-tabs"
        v-model:active="clickId"
      >
        <van-tab v-for="item in menus" :key="item.id" :name="item.id">
          <template #title>
            <div
              :ref="(ele) => setTabRef(ele, item.id)"
              class="n-tab-item"
              @click="liClick(item)"
              :class="{ active: clickId === item.id }"
            >
              <AppImage
                class="icon"
                :class="`icon_${10000}`"
                :src="`/icons/nav_${item.id}${
                  clickId === item.id ? '-active' : ''
                }.png`"
                alt=""
              />
              <div
                class="text"
                :class="{
                  text2: item.id == GameNavEnum.Slot,
                  active: clickId === item.id,
                }"
              >
                {{ item.cateName }}
              </div>
              <div class="text_line" v-if="clickId === item.id"></div>
            </div>
          </template>
        </van-tab>
      </van-tabs>

      <div class="line"></div>

      <!-- <div ref="gameTabRef" :swipe-threshold="4" line-height="0" background="transparent" line-width="0"  class="game-type-tabs2" >
        <div v-for="item in gameNavData" :key=item.id   :name=item.id  >
          <template  v-if="item.id!=GameNavEnum.Platform">
            <div :ref="(ele) => setTabRef(ele, item.id)" class="n-tab-item" @click="liClick(item)" :class="{active: clickId === item.id}">
              <AppImage class="icon" :class="`icon_${item.id}`" :src="`/icons/nav_${item.id}${clickId === item.id? '-active' : ''}.png`" alt="" />
              <div class="text" :class="{text2:item.id==GameNavEnum.Slot}">{{ item.cateName }}</div>
            </div>
          </template>
        </div>
      </div> -->
    </div>
    <!-- <div class="search-box">
      <AppGameSearch />
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";
.app-game-tab-sec {
  display: flex;
  // gap: 20px;
  width: 750px;
  align-items: center;
  justify-content: center;
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 88px; /* 距离顶部0px时固定定位 */
  z-index: 98;
  height: 103px;
  // overflow-x: hidden;
  // margin-left: -20px;
  .search-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 10px;
    top: 2px img {
      display: block;
    }
  }

  .left {
    flex: 1;
    // overflow: hidden;
    width: 100%;
    height: 103px;
    line-height: 52px;
    // padding-top: 310px;
    // padding-left: 15px;
    background: var(--theme-home-bg);
    //  border-radius: 15px;
    // @include webp('/icons/appGameTab_bg.png');
    // background-size: 750px 483px;

    .left-center {
      position: absolute;
      // background-color: aqua;
      width: 345px;
      height: 300px;
      top: 10px;
      left: 20px;
      color: #ffffff;
      .left-center-text {
        width: 345px;
        height: 150px;
        line-height: 35px;
        font-size: 35px;
        text-shadow: 1px 2px 2px rgba(3, 0, 0, 0.73);
        font-family: Arial;
        font-weight: 800;
      }

      .left-center-reg {
        position: absolute;
        top: 215px;
        width: 237px;
        height: 75px;
        // background:radial-gradient(ellipse 72.22% 72.22% at 50% 50% ,#f30808 0%,#f26b04 100%);
        background-color: var(--app-red-color);
        border-radius: 14px;
        .left-center-reg-text {
          display: block;
          text-shadow: 3px 3px 3px rgba(0, 0, 0, 0.36);
          font-family: Arial;
          font-weight: 900;
          font-size: 26px;
          text-align: center;
          line-height: 75px;
        }
      }
      .left-center-dep {
        // background:radial-gradient(ellipse 72.22% 72.22% at 50% 50% ,#f30808 0%,#f26b04 100%);
        background-color: var(--app-red-color);
      }
    }

    .n-tab-item {
      // flex: 1;
      // flex-basis: auto;
      width: 160px;
      height: 103px;
      // border-radius: 50%;
      line-height: 37px;
      flex-shrink: 0;
      // padding: 0 30px;
      // margin-top: 0px;
      position: relative;
      font-weight: normal;
      // margin-top: -5px;

      // @include webp('/icons/nav_bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      // color: #7d8aa2;
      // background: #fff;
      &.active {
        // border-radius: 20px;
        // background-color: var(--app-red-color);
        // @include webp('/icons/nav_bg-active.png');
        // color: #fff;
      }

      // img {
      //   width: 40px;
      //   position: absolute;
      //   left: calc(50% - 20px);
      //   top: calc(50% - 20px);
      //   margin: 0 auto;;
      // }

      .icon_10000 {
        width: 60px;
        position: absolute;
        transform: translate(-50%, -50%);
        left: 50%;
        top: 35%;
        margin: 0 auto;
      }

      .icon_0 {
        width: 50px;
        height: 49px;
        position: absolute;
        left: calc(50% - 25px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_100 {
        width: 44px;
        position: absolute;
        left: calc(50% - 22px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_6 {
        width: 50px;
        position: absolute;
        left: calc(50% - 22px);
        top: calc(50% - 35px);
        margin: 0 auto;
      }
      .icon_600 {
        width: 50px;
        position: absolute;
        left: calc(50% - 25px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_3 {
        width: 50px;
        position: absolute;
        left: calc(50% - 25px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_2 {
        width: 54px;
        position: absolute;
        left: calc(50% - 27px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_1 {
        width: 47px;
        position: absolute;
        left: calc(50% - 22px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_7 {
        width: 50px;
        position: absolute;
        left: calc(50% - 25px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
      .icon_8 {
        width: 53px;
        position: absolute;
        left: calc(50% - 26px);
        top: calc(50% - 40px);
        margin: 0 auto;
      }
    }
    .text {
      // float: left;
      // margin-left: -25px;
      position: absolute;
      width: 180px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      // font-weight:700;
      font-size: 23px;
      color: var(--theme-color-line);
      left: -10px;
      top: 57px;

      &.active {
        color: var(--theme-primary-color);
      }
    }
    .text2 {
      left: -28px;
    }

    .text_line {
      position: absolute;
      width: 90%;
      border: 2px solid var(--theme-line-color);
      margin-top: 98px;
      margin-left: 5%;
    }
  }
  .nav-box {
    display: flex;
    gap: 20px;
    flex-wrap: nowrap;
    height: 103px;
    overflow-x: scroll;

    .n-item {
      flex: 1;
      flex-basis: auto;
      width: 84px;
      height: 84px;
      flex-shrink: 0;
      border-radius: 10px;
      background: #0b1c3d;

      &.active {
        background: #f5c31b;
      }

      img {
        width: 78px;
        height: 78px;
        display: block;
        margin-top: 5px;
        margin-left: 5px;
      }
    }
  }
}

.line {
  // width: 100%;
  height: 1px;
  background-color: var(--theme-color-line);
  position: absolute;
  bottom: -1px;
  right: 18px;
  left: 18px;
}

.app-game-tab {
  height: 106px;
  flex-shrink: 0;
  border-radius: 20px;
  background: #033377;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  overflow: hidden;

  .item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #fff;
    line-height: 24px;
    font-size: 26px;
    padding-left: 14px;

    &.active {
      // background: linear-gradient(358deg, #1373EF 0%, #0ED1F4 100%), linear-gradient(180deg, #044B9A 0%, #011A51 100%);
    }

    .icon {
      width: 50px;
      margin-right: 6px;
    }
  }
}

//使用dev的tab
.game-type-tabs2 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;
}

.game-type-tabs2::-webkit-scrollbar {
  width: 10px; /* 垂直滚动条的宽度 */
  height: 10px; /* 水平滚动条的高度 */
}

/* 定义滚动条轨道样式 */
.game-type-tabs2::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道的背景颜色 */
}

/* 定义滚动条滑块样式 */
.game-type-tabs2::-webkit-scrollbar-thumb {
  background: #888; /* 滑块的背景颜色 */
}

/* 定义当滑块悬停或活动时的样式 */
.game-type-tabs2::-webkit-scrollbar-thumb:hover {
  background: #555; /* 滑块在悬停状态下的背景颜色 */
}
</style>
