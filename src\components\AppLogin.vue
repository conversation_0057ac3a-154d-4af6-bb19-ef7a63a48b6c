<script setup lang="ts" name="app-login">
const appStore = useAppStore();
const router = useRouter();
const { loginDialogVisible, loginDialogType } = storeToRefs(appStore);
import userIcon from "/icons/svg/user.svg?raw";
import lockIcon from "/icons/svg/lock.svg?raw";
import cardIcon from "/icons/svg/card.svg?raw";
// const { userInfo } = storeToRefs(appStore);
const fromData = reactive({
  username: "",
  password: "",
});

const isRemember = ref(appStore.storedAccount != "");

if (appStore.storedAccount) {
  const { username, password } = JSON.parse(appStore.storedAccount);
  (fromData.username = username), (fromData.password = password);
}

watch(loginDialogVisible,()=>{
  if (appStore.storedAccount) {
    const { username, password } = JSON.parse(appStore.storedAccount);
    (fromData.username = username), (fromData.password = password);
  }
})
const { phoneReg } = useRegExpPhone();

// 同时验证 phoneReg 和  emailReg，只要有一个通过就行
const usernameReg = new RegExp("(?:" + phoneReg.value.source + ")\\b");

const loginRef = ref();
const passwordRef = ref();

// const changeLan = () => {
//   appStore.setLanguage(Languages.EN_US)
// }

const openFindPwd = () => {
  appStore.setLoginDialogVisible(false);
  appStore.setFindPasswordDialogVisible(true);
};

const { run: runLogin, loading } = useRequest(
  () =>
    ApiLogin({
      ...fromData,
      // device_no: 'asdfasdf234',
    }),
  {
    manual: true,
    loadingDelay: 500,
    onSuccess(data) {
      if (isRemember.value) {
        //存储账号信息
        appStore.setStoredAccount(JSON.stringify(fromData));
      } else {
        appStore.clearStoredAccount();
        fromData.username = "";
        fromData.password = "";
      }

      openLoginDialog(false);
    },
    onError: (err: any) => {
      if (err && err.data) {
        DealLoginError(err.data);
      }
    },
  }
);

const login = () => {
  loginRef.value.validation();
  passwordRef.value.validation();
  // 是否验证通过
  const isPass = [loginRef.value.isValid, passwordRef.value.isValid].every(
    (item) => item
  );

  if (isPass) {
    runLogin();
  }
};

const goKf = () => {
  router.push("/serviceMessages");
  appStore.setLoginDialogVisible(false);
  appStore.setActiveDialogVisble(false);
  appStore.setFindPasswordDialogVisible(false);
};

const switchTabActiveType = () => {
  appStore.setLoginDialogVisible(false);
  appStore.setRegisterDialogVisible(true);
};

const switchFindPasswordDialogVisible = () => {
  appStore.setLoginDialogVisible(false);
  appStore.setFindPasswordDialogVisible(true);
};

//关闭界面
const closePage = () => {
  appStore.setLoginDialogVisible(false);
};
</script>

<template>
  <div class="app-login"  v-if="loginDialogType == 'login' && loginDialogVisible">
    <!-- <p class="name"></p> -->
    <div class="tab">
      <div class="tab-item">
        <div class="svg-icon"></div>
        <span>Login</span>
      </div>
      <div class="bottom-line"></div>
    </div>
    <div class="username">
      <AppInput
        icon-with="25"
        v-model="fromData.username"
        ref="loginRef"
        :pattern="usernameExpReg"
        :required="true"
        :is-png="true"
        :svg-icon-left="userIcon"
        type="text"
        placeholder="Nome de Usuário"
        msg="4-16 caráter bit, suporte em inglês/números"
        :err-height="42"
        clearable
        width="100%"
        height="70"
        maxlength="16"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      >
        <!--        <template #left>-->
        <!--          <div :style="{ color: 'var(&#45;&#45;app-title-color)', paddingRight: 'var(&#45;&#45;app-px-14)' }">+55</div>-->
        <!--        </template>-->
      </AppInput>
    </div>
    <div class="password">
      <AppInput
        icon-with="25"
        width="100%"
        height="70"
        v-model="fromData.password"
        ref="passwordRef"
        :required="true"
        :is-png="true"
        double-action="true"
        :pattern="passwordReg4"
        :err-height="42"
        placeholder="Senha"
        msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos"
        clearable
        :svg-icon-left="lockIcon"
        type="password"
        :style-obj="{
          background: 'var(--theme-main-bg-color)',
          color: '#000',
          borderRadius: 'var(--app-px-14)',
        }"
      />
    </div>

    <div class="remember-items">
      <div class="remember" @click="isRemember = !isRemember">
        <div class="toggle">
          <AppImage
            style="width:var(--app-px-26);"
            :src="`/img/login/${
              !isRemember ? 'remember-not-select' : 'remember-select'
            }.webp`"
            alt=""
          />
        </div>
        <div class="text">Lembrar Senha</div>
      </div>

      <button @click="switchFindPasswordDialogVisible">
        Esqueceu a Senha？
      </button>
    </div>

    <div class="submit">
      <AppButton
        @click="login"
        fontSize="var(--app-px-24)"
        :loading="loading"
        width="100%"
        height="70"
        green
        whiteText
        :radius="14"
      >
        Login
      </AppButton>
    </div>
    <div class="ant-row-flex-middle">
      <button type="button" @click="goKf()">Suporte</button>
      <!-- <button type="button">Jogar Grátis</button> -->
      <button type="button" @click="switchTabActiveType">Cadastre-se agora</button>
    </div>

    <!-- <AppImage class="close"  src="/icons/close_black.png.webp" @click="closePage" /> -->
  </div>
</template>

<style lang="scss" scoped>
.svg-icon {
  display: inline-block;
  width: var(--app-px-26);
  height: var(--app-px-26);
  background-color: var(--theme-primary-font-color);
  mask-image: url('/img/login/icon_login.svg');
  -webkit-mask-image: url('/img/login/icon_login.svg');
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
}

.ant-row-flex-middle {
  padding: 0 30px 20px 30px;
}
.remember-items,
.ant-row-flex-middle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > button {
    background: transparent;
    color: var(--theme-primary-font-color);
    font-size: 22px;
    line-height: 50px;
  }
}
.tab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px 15px 12px;
    > span {
      padding-left: 10px;
      color: var(--theme-primary-font-color);
      font-size: 26px;
    }
  }
  .bottom-line {
    margin: 5px 0 20px 0;
    width: 154px;
    height: 6px;
    border-radius: 3px;
    background-color: var(--theme-primary-font-color);
  }
}

.username {
  // padding-top: 10px;
  padding-bottom: 40px;
}

.password {
  // padding-top: 10px;
  padding-bottom: 40px;
}

.submit {
  padding-top: 10px;
  padding-bottom: 20px;
  button {
    margin: 0 auto;
  }
}

.app-login {
  padding: 30px 27px 0 27px;
  margin-top: 180px;
}

.name {
  color: #fff;
  text-align: center;
  font-weight: normal;
  font-size: 30px;
  height: 40px;
  line-height: 40px;
  margin-top: 10px;
  margin-bottom: 56px;
}

.retrieve {
  width: 190px;
  // height: 55px;
  color: var(--app-title-color);
  border-bottom: 1px solid var(--app-title-color);
  margin: 0 auto;
  margin-top: 20px;
  text-align: center;
  font-size: 24px;
  line-height: 50px;
}

.remember {
  display: flex;
  align-items: center;
  .text {
    margin-top: -5px;
    color: var(--theme-primary-font-color);
    margin-left: 15px;
    font-size: 24px;
  }
}

.close {
  display: block;
  margin: 0 auto;
  margin-top: 200px;
}
</style>
