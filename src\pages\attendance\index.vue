<template>
  <div class="attendance-page">
    <!-- 顶部头衔与进度 -->
    <AppPageTitle
      left-arrow
      title="Recompensa de entrada"
      title-weight="700"
      @clickLeft="clickLeft"
      style="position: sticky; top: 0; z-index: 10"
    />
    <div class="vip-banner" v-if="false">
      <img class="badge-img" src="/img/vipImg/V0.png" alt="Aprendiz" />
      <div class="badge-title">Aprendiz</div>
    </div>
    <div class="badge-section" v-if="false">
      <div class="badge-list">
        <div
          v-for="(badge, idx) in badges"
          :key="badge.name"
          class="badge-item"
          :class="{ active: idx === 0 }"
        >
          <img :src="badge.icon" :alt="badge.name" />
          <div class="badge-name">{{ badge.name }}</div>
        </div>
      </div>
      <van-progress
        :percentage="progress"
        color="var(--theme-primary-color)"
        stroke-width="8px"
        pivot-text=""
        track-color="var(--theme-main-bg-color)"
        class="badge-progress"
      />
      <div class="progress-labels">
        <span
          v-for="(badge, idx) in badges"
          :key="badge.name"
          :style="{ left: badge.progress + '%' }"
          class="progress-label"
          >{{ badge.progressLabel }}</span
        >
      </div>
    </div>

    <!-- 当前奖励金额 -->
    <div class="current-reward">
      <div class="reward-label">R$&nbsp;</div>
      <div class="reward-amount">{{ rewardAmount }}</div>
      <div class="reward-desc">Últimos 7 dias de apostas efetivas</div>
    </div>

    <!-- 签到奖励表格 -->
    <div class="reward-table">
      <div class="reward-table-header">
        <img src="/img/vipImg/crown.png" class="reward-table-icon" />
        <span class="reward-table-title">Informações sobre recompensas</span>
      </div>
      <div class="reward-table-row" v-for="(reward, idx) in rewards" :key="idx">
        <div class="reward-table-cell reward-day">
          <img src="/img/vipImg/coin.png" class="coin-icon" />
          Dia {{ reward.day }}
        </div>
        <div class="reward-table-cell reward-amount">
          R$ <span>{{ reward.reward_amount }},00</span>
        </div>
        <div class="reward-table-cell reward-action">
          <span v-if="reward.is_receive === '1'">Recebido</span>
          <span v-else>Sem recompensas</span>
        </div>
      </div>
    </div>

    <!-- 活动规则 -->
    <div class="rules-section">
      <div class="rules-title">Regras da atividade</div>
      <ul class="rules-list">
        <li v-for="(rule, idx) in rules" :key="idx">{{ rule }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
// import { useRequest } from "vue-request"; // 假设你用的是 vue-request
// import { ApiUserSigninConfig } from "@/api"; // 假设你的接口在这里
import { NavBar, Progress, Cell, CellGroup } from "vant";
const router = useRouter();
const appStore = useAppStore();
const { isApp, showIndexModule } = storeToRefs(appStore);

const badges = [
  {
    name: "Aprendiz",
    icon: "/img/vipImg/V0.png",
    progress: 0,
    progressLabel: "0,00",
  },
  {
    name: "Jogador",
    icon: "/img/vipImg/V1.png",
    progress: 20,
    progressLabel: "1.000,00",
  },
  {
    name: "Guerreiro",
    icon: "/img/vipImg/V2.png",
    progress: 40,
    progressLabel: "10.000,00",
  },
  {
    name: "Campeão",
    icon: "/img/vipImg/V3.png",
    progress: 60,
    progressLabel: "50.000,00",
  },
  {
    name: "Mestre",
    icon: "/img/vipImg/V4.png",
    progress: 80,
    progressLabel: "100.000,00",
  },
  {
    name: "Rei",
    icon: "/img/vipImg/V5.png",
    progress: 100,
    progressLabel: "1.000.000,00",
  },
];

const progress = ref(0);
const rewardAmount = ref("0,00");

const rewards = ref([
  { day: 1, reward_amount: 25, is_receive: '0' },
  { day: 2, reward_amount: 25, is_receive: '0' },
  { day: 3, reward_amount: 30, is_receive: '0' },
  { day: 4, reward_amount: 30, is_receive: '0' },
  { day: 5, reward_amount: 40, is_receive: '0' },
  { day: 6, reward_amount: 40, is_receive: '0' },
  { day: 7, reward_amount: 1000, is_receive: '0' },
]);

function clickLeft() {
  router.push("/");
  appStore.setShowIndexModule(false);

  //   appStore.setFooterDialogVisble(true);
}

const { run: getDaliyRecord } = useRequest(() => ApiGetThisDayRunningRecord(), {
  onSuccess: (res) => {
    console.log(887342342342342343);
    if (res) {
      console.log(res[0].slots_running);
      rewardAmount.value = res[0].slots_running;
    }
  },
});

const { run: getConfig } = useRequest(() => ApiUserSigninConfig(), {
  onSuccess: (res) => {
    if (res) {
      rewards.value = res;
      console.log(res);

      console.log(rewards);
    }
  },
});

const rules = [
  "Este evento é um evento de entrada contínua de 7 dias. Os membros que apostarem até um certo nível podem receber o bônus correspondente;",
  "Se a entrada for interrompida, o bônus será recebido novamente a partir do primeiro dia;",
  "O bônus (excluindo o principal) requer 1 vezes de apostas válidas para sacar;",
  "Somente o proprietário da conta pode realizar operações manuais normais, caso contrário, o bônus será cancelado ou deduzido, congelado ou até mesmo colocado na lista negra;",
  "Para evitar diferenças na compreensão do texto, a plataforma se reserva o direito final de interpretação desta atividade.",
];

function onClaim(reward) {
  // 这里写领取逻辑
  console.log("领取奖励", reward);
}

onMounted(() => {
  getConfig();
  getDaliyRecord();
});
</script>

<style lang="scss" scoped>
.vip-banner {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: column;

  background-image: url("/img/vipImg/vip-banner.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 16px;
  height: 50vw;
  img {
    width: 50%;
  }
  .badge-title {
    font-size: 42px;
    font-weight: bold;
    margin-bottom: 8px;
  }
}
.attendance-page {
  background: #2d2342;
  min-height: 100vh;
  color: #fff;
  padding-bottom: 24px;
  .badge-section {
    // background: #3a2e5a;
    border-radius: 12px;
    margin: 16px;
    padding: 16px 12px 24px 12px;
    text-align: center;
    .badge-img {
      width: 80px;
      margin-bottom: 8px;
    }
    .badge-list {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      width: 100%;
      column-gap: 6vw;
      margin-left: auto;
      margin-right: auto;
    }
    .van-progress.badge-progress {
      width: 95%;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 10px;
      display: block;
      height: 8px;
      border-radius: 15px;
    }
    .progress-labels {
      width: 92%;
      margin-left: auto;
      margin-right: 35px;
      position: relative;
      height: 16px;
      margin-top: 4px;
      .progress-label {
        position: absolute;
        top: 0;
        font-size: 16px;
        color: var(--theme-main-bg-color);
        font-weight: bold;
        transform: translateX(-50%);
      }
      // 让每个label正好在每个徽章下方（6个徽章）
      .progress-label:nth-child(1) {
        left: 0%;
      }
      .progress-label:nth-child(2) {
        left: 20%;
      }
      .progress-label:nth-child(3) {
        left: 40%;
      }
      .progress-label:nth-child(4) {
        left: 60%;
      }
      .progress-label:nth-child(5) {
        left: 80%;
      }
      .progress-label:nth-child(6) {
        left: 100%;
      }
    }
    .badge-item {
      flex: 1;
      text-align: center;
      opacity: 0.5;
      &.active {
        opacity: 1;
      }
      img {
        width: 80px;
        // margin: 0 10px;
      }
      .badge-name {
        margin-top: 10px;
        font-size: 18px;
      }
    }
  }
  .current-reward {
    background: #3a2e5a;
    border-radius: 12px;
    margin: 16px 16px 16px 16px;
    padding: 16px 0;
    text-align: center;
    .reward-label {
      font-size: 36px;
      color: #ffd700;
      display: inline-block;
      margin-right: 2px;
    }
    .reward-amount {
      font-size: 36px;
      font-weight: bold;
      color: #ffd700;
      display: inline-block;
    }
    .reward-desc {
      font-size: 22px;
      color: #aaa;
      margin-top: 14px;
    }
  }
  .reward-table {
    background: #3a2e5a;
    border-radius: 12px;
    margin: 0 16px 16px 16px;
    overflow: hidden;
    margin-top: 30px;
    .reward-table-header {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      font-weight: bold;
      color: #8883b3;
      padding: 10px;
      .reward-table-icon {
        width: 8vw;
        margin-left: 20px;
        margin-right: 12px;
        padding-bottom: 15px;
      }
    }
    .reward-table-row {
      display: flex;
      align-items: center;
      padding: 14px 16px;
      border-bottom: 1px solid #4a3e6a;
      background: #3a2e5a;
      &:nth-child(even) {
        background: #342a4d;
      }
      &:last-child {
        border-bottom: none;
      }
      .reward-table-cell {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 18px;
        color: #fff;
        &.reward-day {
          font-weight: bold;
          .coin-icon {
            width: 24px;
            margin-right: 8px;
          }
        }
        &.reward-amount {
          color: #ffd700;
          font-weight: bold;
          justify-content: flex-start;
          padding-left: 32px;
          span {
            font-size: 20px;
          }
        }
        &.reward-action {
          justify-content: center;
          span {
            color: #bdbdbd;
          }
        }
      }
    }
  }
  .rules-section {
    background: #3a2e5a;
    border-radius: 12px;
    margin: 16px 16px;
    padding: 16px;
    .rules-title {
      font-size: 3.133333vw;
      font-weight: bold;
      margin-bottom: 8px;
    }
    .rules-list {
      font-size: 3.4vw;
      color: #ccc;
      padding-left: 24px;
      list-style-type: disc;
      li {
        margin-bottom: 12px;
        line-height: 1.7;
      }
    }
  }
}
</style>
