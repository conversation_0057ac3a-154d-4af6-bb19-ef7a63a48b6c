<script setup lang="ts" name="promotion">
const router = useRouter();
const route = useRoute();
const { isApp } = storeToRefs(useAppStore());
const appStore = useAppStore();
const { secletType, showIndexModule } = storeToRefs(appStore);
import Missao from "../pages/promotion-detail/Missao.vue";
import Rebate from "../pages/promotion-detail/Rebate.vue";
import Pendente from "../pages/promotion-detail/Pendente.vue";
import Historico from "../pages/promotion-detail/Historico.vue";
// import AppVip from "./AppVip.vue";
import Poupanca from "../pages/promotion-detail/Poupanca.vue";
appStore.setFooterDialogVisble(true);

const currentType = ref("0");
const levelList = [
  { label: "Tudo", value: "0" }, // 全部
  { label: "Depósito", value: "1" }, // 存款活动
  { label: "Baixar APP", value: "2" }, // 未知
  { label: "Desconto", value: "3" }, // 未知
  { label: "Classificação", value: "4" }, // 未知
  { label: "Outros", value: "5" }, // 箱子、签到
];

const SECLET_TYPE = readonly({
  Eventos: 0,
  Missao: 1,
  Vip: 2,
  Rebate: 3,
  Pendente: 4,
  Historico: 5,
  Poupanca: 6,
});

const enum JumpViewType {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
  POUPANCA,
}

// const secletType = computed(() => {
//     if(route.query.key){
//         if(route.query.key == JumpViewType.VIP){
//             // console.log("dd=="+window.location.host)
//             // console.log("ff=="+window.location.pathname)
//             // let info = window.location.host + window.location.pathname
//             // console.log("地址-"+info)
//             // window.location.href = info
//             // console.log("地址111-"+info)
//             return SECLET_TYPE.Vip
//         }
//     }
//     return secletType?secletType:SECLET_TYPE.Eventos
// })

const key = route.query.key;

// const secletType = ref(SECLET_TYPE.Eventos)

const tabData = ref([
  {
    label: "Eventos",
    value: SECLET_TYPE.Eventos,
  },
  {
    label: "Missão",
    value: SECLET_TYPE.Missao,
  },
  {
    label: "VIP",
    value: SECLET_TYPE.Vip,
  },
  {
    label: "Rebate",
    value: SECLET_TYPE.Rebate,
  },
  {
    label: "Pendente",
    value: SECLET_TYPE.Pendente,
  },
  {
    label: "Poupança",
    value: SECLET_TYPE.Poupanca,
  },
  {
    label: "Histórico",
    value: SECLET_TYPE.Historico,
  },
]);

function onTabChange() {}

function onTabChange2(idx) {
  secletType.value = idx;
}

watch(
  route,
  (val, old) => {
    if (router.currentRoute.value.query.key == JumpViewType.HISTORY) {
      secletType.value = SECLET_TYPE.Historico;
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (route.query.key) {
    console.log(route.query.key);
    if (route.query.key == JumpViewType.REBATE) {
      secletType.value = SECLET_TYPE.Rebate;
    } else if (route.query.key == JumpViewType.PENDENTE) {
      secletType.value = SECLET_TYPE.Pendente;
    } else if (route.query.key == JumpViewType.HISTORY) {
      secletType.value = SECLET_TYPE.Historico;
    } else if (route.query.key == JumpViewType.JUROS) {
      secletType.value = SECLET_TYPE.Poupanca;
    } else if (route.query.key == JumpViewType.VIP) {
      secletType.value = SECLET_TYPE.Vip;
    } else if (route.query.key == JumpViewType.POUPANCA) {
      secletType.value = SECLET_TYPE.Poupanca;
    }
  }
});

const showModuleVisible = computed({
  get: () => showIndexModule.value === 1,
  set: (val: boolean) => {
    console.log(87373);
    appStore.setShowIndexModule(val ? 1 : false);
  },
});
</script>
<template>
  <van-popup
    v-model:show="showModuleVisible"
    position="bottom"
    teleport="body"
    :close-on-click-overlay="true"
    destroy-on-close
    z-index="998"
  >
    <div class="promotion" style="background-color: aqua;">
      <!-- <div class="tab">
        <AppTab
          :list-data="tabData"
          v-model="secletType"
          @click="onTabChange"
          :height="88"
          :class="{ border: true }"
        ></AppTab>
      </div> -->
      
      <div class="promotion-content">
        <div class="content">
          <div v-if="secletType == SECLET_TYPE.Eventos" class="content-eventos">
            <AppAllPromotion />
          </div>
          <div
            v-else-if="secletType == SECLET_TYPE.Missao"
            class="content-missao"
          >
            <Missao />
          </div>
          <div v-else-if="secletType == SECLET_TYPE.Vip" class="content-vip">
            <AppVip @onclickVip="onTabChange2" />
          </div>
          <div
            v-else-if="secletType == SECLET_TYPE.Rebate"
            class="content-rebate"
          >
            <Rebate />
          </div>
          <div
            v-else-if="secletType == SECLET_TYPE.Pendente"
            class="content-pendente"
          >
            <Pendente />
          </div>
          <div
            v-else-if="secletType == SECLET_TYPE.Historico"
            class="content-historico"
          >
            <Historico />
          </div>
          <div
            v-else-if="secletType == SECLET_TYPE.Poupanca"
            class="content-poupanca"
          >
            <Poupanca />
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.promotion {
  // background-color: black;
  height: 100vh;
  .promotion-content {
    position: absolute;
    // height: calc(100% - 164px);
    height: 100%;
    width: 100%;
    overflow: auto;
    .content {
      position: relative;
      width: 100%;
      height: 100%;
      background-color: var(--theme-bg-color);
    }
  }
}

.content-poupanca {
  height: auto;
  background-color: var(--theme-bg-color);
}
</style>
