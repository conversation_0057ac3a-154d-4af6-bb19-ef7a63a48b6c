<script setup lang="ts" name="app-find-pwd">
// const router = useRouter()
const appStore = useAppStore();
const {isShowMessage,showMessage} = storeToRefs(appStore);


//关闭界面
const closePage=()=>{
  appStore.setIsShowMessage(false,"")
}


</script>

<template>
  <van-popup class="app-login-register" v-model:show="isShowMessage" teleport="body" round :overlay-style="{background:'rgba(255,255,255,0)'}" :close-on-click-overlay="false">
    <div class="content">
      <div class="textbg">
        <AppImage src="/icons/SaldoLivre.webp" class="icon"/>
        <label class="text">{{ showMessage }}</label>
      </div>
      <div class="submit">
        <AppButton @click="closePage"  class="" width="300" height="80" blue :radius="15" black-text font-size="36" >
          &nbsp&nbsp&nbsp&nbsp&nbspOK&nbsp&nbsp&nbsp&nbsp&nbsp
        </AppButton>
      </div>
     
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';


.content {
  width:680px;
  height:350px;
  border-radius:16px;
  background-color:var(--theme-main-bg-color);
  border:2px solid;
  border-color:#2a815f;
}

.textbg{
  width:612px;
  height:100px;
  background:var(--app-message-input-color);
  border-radius:15px;
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%-17);
}

.icon{
  width: 57px;
  margin-left: 35px;
  margin-top: 30px;
}

.text{
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%+30);
  text-align: center;
  width: 500px;
  height: 100px;
  // line-height: 100px;
 
  font-weight:700;
  color:#ffffff;
  font-size:36px;

  // margin-left: 60px;
  // margin-top: 25px;
}


.submit{
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%+26);
}

</style>
