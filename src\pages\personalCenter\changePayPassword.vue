<script setup lang='ts' name='changePayPassword'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const passwordStrength = ref(0);
const passwordRef = ref();
const passwordRef2 = ref();
//隐藏底下菜单
appStore.setFooterDialogVisble(false)
const fromData = reactive({
    update_type:2,   // 1 新增密码 2 修改旧密码 3 忘记密码，通过验证码重置
    ty: 1,           // 1 手机号 2 邮箱的验证码
    sid: '',
    ts: '',
    code: '',
    confirm_password: '',
    password: '',
    old: '',
    phone:'',
})

const sidData = reactive({
    sid: '',
    ts: ''
})

const authPassword = () => {
    let strength = 0;
    let pd = fromData.password;

    // 检查大写字母
    if (/[A-Z]/.test(pd)) strength++;

    // 检查小写字母
    if (/[a-z]/.test(pd)) strength++;

    // 检查数字
    if (/\d/.test(pd)) strength++;

    // 检查特殊字符
    if (/[\W_]/.test(pd)) strength++;
    passwordStrength.value = strength;
};

function onClickBtn(){
    if( fromData.password!=fromData.confirm_password){
        showToast('As senhas inseridas não coincidem');
        return 
    }
    runResetPwd()
}

//设置支付密码
const { run: runResetPwd, loading: resetLoading } = useRequest(() => ApiUpdatePayPwd({update_type:2,ty:1,sid:fromData.sid,ts:fromData.ts,code:fromData.code,phone:fromData.phone,
                                                                           confirm_password:fromData.confirm_password,password:fromData.password ,old:"" }), {
  manual: true,
  onSuccess(data: string) {
    appStore.setPay_password(fromData.password)
    appStore.setFindPayPasswordDialogVisible(false)
    showToast("Estabelecer com sucesso");
    router.go(-1)
  },
  onError(resData){
    showErrorTip(resData)
  }
})

</script>

<template>
    <AppPageTitle left-arrow title="Senha de Saque" title-weight="700" />
    <div class="content">
        <label>É o seu primeiro saque. Você precisa configurar uma senha de saque</label>
        <!-- Defina sua senha de saque -->
        <div class = "title"></div>
        <AppPasswordInput class="passwordInput"
          v-model="fromData.password" 
          info="Nova senha de saque" 
          color="var(--theme-text-color)"
          style=" height: 80px;
          margin-top: 10px;
        "></AppPasswordInput>
        <br>
        <br>
        <AppPasswordInput class="passwordInput"
          v-model="fromData.confirm_password" 
          info="Confirme a nova senha de saque" 
          color="var(--theme-text-color)"
          style=" height: 80px;
          margin-top: 10px;
        "></AppPasswordInput>
        <div class = "bottom">Atenção: A senha de saque protege seus fundos e ê extremamente importante.Mantenha-a em segredo para evitar qualquer perta financeira</div>
        <div class="content-bottom">
            <AppButton @click="onClickBtn" :loading="resetLoading" width="100%" height="70" :radius="15" fontSize="12px"
                background="var(--theme-primary-color)"
                color="var(--theme-font-on-background-color)">
                Confirme
            </AppButton>
        </div>
        
    </div>

</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.content {
    height: calc(100vh - 100px);
    font-size: 23px;
    font-weight: 200;
    background-color: var(--theme-bg-color);
    
    .bottom{
        font-size: 25px;
        color: var(--theme-secondary-color-error);
        padding: 110px 0 0 30px;
        font-weight: bold;
    }

    .passwordInput{
        position: relative;
        padding: 30px 55px 20px 25px;
    }

    label {
        position: absolute;
        padding: 50px 0 10px 20px;
        color: var(--theme-text-color);
        white-space:normal;
        font-size: 28px;
    }

    .title{
        position: relative;
        padding: 110px 0 0 20px;
        color: var(--theme-text-color);
        font-size: 26px;
    }

    &-bottom {
        position: absolute;
        bottom: 0;
        height: 120px;
        width: 100%;
        padding: 25px 25px;
        background-color: var(--theme-main-bg-color);
    }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>