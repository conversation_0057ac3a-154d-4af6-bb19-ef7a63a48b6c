<script setup lang='ts' name='invite'>

import { ProxyTeamData, SendQueryTeamData } from "~/core/http/api"

const router = useRouter()
const { userInfo } = storeToRefs(useAppStore())
const url = computed(() => location.origin + '/register' + `?id=${userInfo.value.uid}` + '&currency=BRL&type=2')
const isLeft = ref(true)
const btnType = ref('HOJE')
const showDatePicker = ref(false)
const currentAccuInfo = ref({
  TotalCommi:0,
  team_num:0,
  DepositNum:0,
  ValidNum:0,
  Deposit:0, 
  Running:0, //一级流水
  //新加
  OtherDeposit:0 ,//其他充值金额（2+3级玩家的下级充值总额）
  OtherDepositNum :0,//其他充值人数（2+3级玩家的充值人数）
  OtherRunning:0 //三级总流水
})
const dadosAccuInfo = ref({
  TotalCommi:0,
  team_num:0,
  DepositNum:0,
  ValidNum:0,
  Deposit:0,
  Running:0,
   //新加
  OtherDeposit:0 ,//其他充值金额（2+3级玩家的下级充值总额）
  OtherDepositNum :0,//其他充值人数（2+3级玩家的充值人数）
  OtherRunning:0 //三级总流水
})
function isMobile() {
  let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
  return flag
}
const curUrl = "www.cagadoslot.com"
const shareUrls = {
  Telegram: "https://t.me/share/url?url=https%3A%2F%2F" + curUrl,
  Facebook: "https://m.facebook.com/login.php?next=https%3A%2F%2Fm.facebook.com%2Fsharer%2Fsharer.php%3Fu%3Dhttps%3A%2F%2F"+ curUrl +"&refsrc=deprecated&_rdr",
  Twitter: "https://twitter.com/intent/post?text+=&url=https%3A%2F%2F" + curUrl
}
if (!isMobile()) {
  shareUrls.Facebook = "https://www.facebook.com/login.php?skip_api_login=1&api_key=966242223397117&signed_next=1&next=https%3A%2F%2Fwww.facebook.com%2Fsharer%2Fsharer.php%3Fu%3Dhttps%3A%2F%2F"+ curUrl +"&cancel_url=https%3A%2F%2Fwww.facebook.com%2Fdialog%2Fclose_window%2F%3Fapp_id%3D966242223397117%26connect%3D0%23_%3D_&display=popup&locale=zh_CN"
}
const currentDate = ref({
  start: dayjs().subtract(0, 'day').format('YYYY/MM/DD'),
  end: dayjs().format('YYYY/MM/DD')
})



// const currentAccuInfo = ref(ProxyAccuInfo)

const dateStr = computed(() => dayjs(currentDate.value.start).format('YYYY-MM-DD') + ' - ' + dayjs(currentDate.value.end).format('YYYY-MM-DD'))

const dataList = ref([
  {
    id: '552222207770',
    date: '2024-01-02',
    comissao: '233',
    deposito: '233',
    convite: '233',
  }
])

const goToPage = (index: any) => {
  if (index === 0) {
    isLeft.value = true
    // currentAccuInfo.value = queryAccuInfoDatas.value!;
    runGetQueryAccuInfo();
  } else if (index === 1) {
    isLeft.value = false
    if(queryTeamDatas.value){
      // currentAccuInfo.value = queryTeamDatas.value?.TAccuInfo!;
    }
    pageDatas.value.pageIndex = 0
    finished.value = false
    listTeamData.value.length = 0
  }
}
const toCopy = (str: string) => {
  showToast('Copied!')
  copy(str)
}

const jumpWeek = () => {
  router.push("/activity/weekly-proxy-cashback")
}
const onConfirm = (data: any) => {
  currentDate.value = data;
  refresh();
}


const {run: runGetQueryAccuInfo,data:queryAccuInfoDatas } = useRequest(() => ApiGetQueryAccuInfo(), {
  manual: true,
  onSuccess(res:any) {
    console.log(res)
    currentAccuInfo.value = queryAccuInfoDatas.value!;
  }
})

const {run: runGetProxyInviteBonus,data:proxyInviteBonusDatas } = useRequest(() => ApiGetProxyInviteBonus(), {
  manual: true,
  onSuccess(res:any) {
    console.log(res)
    // console.log(proxyInviteBonusDatas)
  }
})

const {run: runGetQueryTeam,data:queryTeamDatas, loading:queryTeamLoading } = useRequest((datas:SendQueryTeamData) => ApiGetQueryTeam(datas), {
  manual: true,
  onSuccess(res:any) {
    // console.log("runGetQueryTeam  queryTeamDatas = ", res)
    // console.log(proxyInviteBonusDatas)
    dadosAccuInfo.value = res?.TAccuInfo!;
    if(res?.ListTeamData){
      listTeamData.value = [...listTeamData.value, ...res?.ListTeamData!]
    
    // listTeamData.value = [...listTeamData.value,...[{uid:100000,running:100000,first_deposit_mount:10000,DonateTime:12156165165,created_at:************,DonateToParent:586468468468468}]]
      // for(let i = 0; i < 10; i++) {
      //   listTeamData.value = [...listTeamData.value, ...queryTeamDatas.value?.ListTeamData!]
      // }
    } 
    if(listTeamData.value.length >= res?.TotalCount){
      finished.value = true;
    } else {
      finished.value = false;
    }
    // finished.value = true;
  },
  onError(data:any){
    showErrorTip(data)
  }
  
})


runGetQueryAccuInfo();
runGetProxyInviteBonus();

const desStr = computed(() => {
    if(proxyInviteBonusDatas.value && proxyInviteBonusDatas.value.length > 0) {
        return 'R$'+proxyInviteBonusDatas.value[0].bonus;
    } else {
      return 'R$0';
    }
})

const desStr1 = computed(() => {
    if(proxyInviteBonusDatas.value && proxyInviteBonusDatas.value.length > 1) {
        return proxyInviteBonusDatas.value[proxyInviteBonusDatas.value.length - 1].bonus;
    } else {
      return null;
    }
})

const formatYMD = (data:Date) => {
  return data.getFullYear().toString() + "-" + (data.getMonth() + 1 < 10 ? "0" : "") + (data.getMonth() + 1) + "-" + (data.getDate() < 10 ? "0" : "") + data.getDate()
}

const resetTime = (type:string) => {
  // let year = new Date().getFullYear();
  // let month = new Date().getMonth();
  // let nowTime = new Date().getTime();
  // let weekDay = new Date().getDay();
  let oneDayTime = 24 * 60 * 60 * 1000;
  switch (type) {
    case 'HOJE':
      currentDate.value = {
        start: formatYMD(new Date()),
        end: formatYMD(new Date())
      }
      break
    case 'ONTEM':
      currentDate.value = {
        start: formatYMD(new Date(new Date().getTime()  - oneDayTime)),
        end: formatYMD(new Date(new Date().getTime()  - oneDayTime))
      }
      break
    case 'SEMANA':
      // 获取当前日期
      let today = new Date();
      // 获取当前是星期几
      let day = today.getDay();
      let startDate = new Date();
      let endDate = new Date();
      if (day == 0) {
          // 计算本周第一天的日期
          startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day - 6);
          // 计算本周最后一天的日期
          endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day);
      } else {
          startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 1);
          endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - day + 7);
      }
      
      currentDate.value = {
        start: formatYMD(startDate),
        end: formatYMD(endDate),
      }
      
      break
  }
}

const listTeamData = ref<ProxyTeamData[]>([]);
const finished = ref(false);
const pageDatas = ref({
  pageIndex:0,
  pageCount:10,
});




const getQueryTeam = () => {
  let tempStartTime = currentDate.value.start + " 00:00:00"
  let tempEndTime = currentDate.value.end + " 00:00:00"
  console.log(1715223600 + " --- getBrazilTime  ---" + (getBrazilTime(1715223600 * 1000)) +"dayjs --"+ dayjs(getBrazilTime(1715223600*1000)))
  let starTime1 = dayjs(tempStartTime).utc(true).unix();
  let endTime1 = dayjs(tempEndTime).utc(true).unix();
  console.log("starTime1  " + starTime1 + "starTime1  " + endTime1)
  runGetQueryTeam({ beginTime: starTime1 + 3600 * 3, endTime: endTime1 + 3600 * 3 + 3600 * 24 -1, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount })
}

const queryBtn = (type: string) => {
  btnType.value = type
  resetTime(type);
  refresh();
}
const refresh = () => {
  pageDatas.value.pageIndex = 0
  finished.value = false
  listTeamData.value.length = 0
  getQueryTeam();
}
const loadMoreData = () => {
  if(pageDatas.value?.pageIndex == 0 || pageDatas.value?.pageIndex * pageDatas.value?.pageCount < queryTeamDatas.value?.TotalCount){
    getQueryTeam();
    pageDatas.value.pageIndex++
  }
  
}
const jumpUrl = (url: string) => {
  window.open(url, '_blank')
}
</script>
<template>
  <div class="bg">
    <div class="invite">
    <AppIndexHeader />
    <!-- 顶部按钮 -->
    <div class="invite-top-btn">
      <AppButton fontSize="32" radius="10" whiteText :blue="isLeft" :gray1="!isLeft" width="350" height="80"
        @click="goToPage(0)">PRINCIPAL</AppButton>
      <div class="invite-top-btn-space"></div>
      <AppButton fontSize="32" radius="10" whiteText :blue="!isLeft" :gray1="isLeft" width="350" height="80"
        @click="goToPage(1)">DADOS</AppButton>
    </div>
    
    <!-- 邀请链接 PRINCIPAL -->
    <div class="invite-link" v-if="isLeft">
      <h6>Link de convite</h6>
      <div class="invite-link-box">
        <span class="url">{{ url }}</span>
        <AppButton @click="toCopy(url)" width="163" height="50" fontSize="24" background='var(--app-btn-normal-blue-color)' white-text radius="10"><span class="btnCopy">Copiar</span></AppButton>
      </div>
      <div class="invite-link-bottom">
        <AppImage src="/icons/invite_fly.webp" @click="jumpUrl(shareUrls.Telegram)"/>
        <AppImage src="/icons/invite_fb.webp" @click="jumpUrl(shareUrls.Facebook)"/>
        <AppImage src="/icons/invite_tw.webp" @click="jumpUrl(shareUrls.Twitter)" />
        <AppImage v-show="false" src="/icons/invite_photo.webp" @click="jumpUrl('')" />
      </div>
    </div>
    <!-- 查询条件 DADOS -->
    <div class="invite-query" v-else>
      <!-- <div class="invite-query-btn">
        <div class="invite-query-btn-item" :class="{'invite-query-btn-item-active': btnType === 'HOJE'}"
          @click="queryBtn('HOJE')">HOJE</div>
        <div class="invite-query-btn-item" :class="{'invite-query-btn-item-active': btnType === 'ONTEM'}"
          @click="queryBtn('ONTEM')">ONTEM</div>
        <div class="invite-query-btn-item" :class="{'invite-query-btn-item-active': btnType === 'SEMANA'}"
          @click="queryBtn('SEMANA')">SEMANA</div>
      </div> -->
      <div class="invite-query-input">
        <div class="date" @click="showDatePicker = true">{{ dateStr }}</div>
        <AppImage src="/icons/invite_search.webp" />
      </div>
    </div>
    <AppDatePicker v-model="showDatePicker" :startDate="currentDate.start" :endDate="currentDate.end" group
      @confirm="onConfirm" />
    <!-- Comissāo PRINCIPAL DADOS -->
    <div class="invite-coin" v-if="isLeft">
      <div class="invite-coin-sum">
        <div class="invite-coin-sum-left">
          <div class="title">Comissāo</div>
      
          <div class="money">R${{ currentAccuInfo?.TotalCommi }}</div>
        </div>
        <AppImage src="/icons/invite_user.webp" class="user" />
      </div>
      <div class="invite-coin-list">

        <!-- <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_clover.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Novos convidados</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.team_num }}</div>
          </div>
        </div> -->

        <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_piggy-bank.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Recarga direta</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.DepositNum }}</div>
          </div>
        </div>

        <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_money.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Deposito direta</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.Deposit }}</div>
          </div>
        </div>

        <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_dollar.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Primeria recarga direta</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.ValidNum }}</div>
          </div>
        </div>


        <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_deposito.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Qutras deposito</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.OtherDeposit }}</div>
          </div>
        </div>


        <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_recargas.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Qutras recargas</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.OtherDepositNum }}</div>
          </div>
        </div>

        <div class="invite-coin-list-item">
          <div class="invite-coin-list-item-left">
            <AppImage src="/icons/invite_like.webp" />
          </div>
          <div class="invite-coin-list-item-right">
            <div class="invite-coin-list-item-right-label">Apostas totais</div>
            <div class="invite-coin-list-item-right-num">{{ currentAccuInfo?.OtherRunning }}</div>
          </div>
        </div>
        
      </div>
    </div>
    <div class="invite-jumpWeek" v-if="isLeft">
      <div class="invite-jumpWeek-top">
        <div class="invite-jumpWeek-top-content">
          Convide os jogadores a depositar e receber
          bônus elevados todas as semanas
        </div>
        
        <AppButton class="invite-jumpWeek-top-btn" @click="jumpWeek()" width="239" height="55" fontSize="24" background='var(--app-btn-jumpweek-blue-color)' white-text radius="10">Obter agora</AppButton>  
      </div>
    </div>
    <!-- DESCRICAO DAS REGRAS   PRINCIPAL     -->
    <div class="invite-rules" v-if="isLeft">
      <div class="invite-rules-top">
        <!-- <div class="invite-rules-top-title">DESCRICAO DAS REGRAS</div> -->
        <div class="invite-rules-top-content">
          Cada membros que voce convida,
          completar o primeiro
        </div>
        <div class="invite-rules-top-content1">
          deposito≥<span class="redSpan">R$25</span>,voce pode obter <span class="redSpan">{{desStr}}</span><span v-show="desStr1"> a </span><span v-show="desStr1" class="redSpan">{{ desStr1 }}</span>.
        </div>
        <!-- <div class="invite-rules-top-bottom">Convite valido</div>
        <div class="invite-rules-top-bottom">Bonus por pessoa</div> -->

        <div class="invite-rules-top-list">
        <div class="invite-rules-top-list-item" v-for="(item, index) in  proxyInviteBonusDatas" :key="index">
          <div class="invite-rules-top-list-item-left">{{"Convite valido   >=" + item.valid_Counts}}</div>
          <div class="invite-rules-top-list-item-right">{{"Bonus por pessoa   R$" + item.bonus}}</div>
        </div>
      </div>
      </div>
      
    </div>
    
    <!-- 查询列表 DADOS -->
    <div class="invite-list" v-else>
      <AppList class="app-List" :loading="queryTeamLoading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData"
        v-if="dataList.length !== 0" style="padding-top: 15px;">
        <div class="invite-list-item" v-for="item in listTeamData" :key="item.uid">
          <div class="invite-list-item-top">
            <div class="invite-list-item-top-left">ID:{{ item.uid }}</div>
            <div class="invite-list-item-top-right">Data do convite:{{ dayjs(getBrazilTime(item.created_at*1000)).format('YYYY-MM-DD') }}
            </div>
          </div>
          <div class="invite-list-item-body">
            <div class="invite-list-item-body-item">  
              <div class="invite-list-item-body-item-title">Apostas totais</div>
              <div class="invite-list-item-body-item-num">{{ item.running }}</div>
            </div>
            <div class="invite-list-item-body-item">
              <div class="invite-list-item-body-item-title">Primeiro depósito</div>
              <div class="invite-list-item-body-item-num">{{ item.first_deposit_mount }}</div>
            </div>
            <div class="invite-list-item-body-item">
              <div class="invite-list-item-body-item-title">Bónus de convite</div>
              <div class="invite-list-item-body-item-num">{{ item.DonateToParent }}</div>
            </div>
          </div>
        </div>
      </AppList>
    
    <!-- <div class="invite-list" v-else>
      <div v-if="dataList.length !== 0">
        <div class="invite-list-item" v-for="item in dataList" :key="item.id">
          <div class="invite-list-item-top">
            <div class="invite-list-item-top-left">ID:{{ item.id }}</div>
            <div class="invite-list-item-top-right">Data do convite:{{ item.date }}</div>
          </div>
          <div class="invite-list-item-body">
            <div class="invite-list-item-body-item">
              <div class="invite-list-item-body-item-title">Apostas totais</div>
              <div class="invite-list-item-body-item-num">{{ item.comissao }}</div>
            </div>
            <div class="invite-list-item-body-item">
              <div class="invite-list-item-body-item-title">Primeiro depósito</div>
              <div class="invite-list-item-body-item-num">{{ item.deposito }}</div>
            </div>
            <div class="invite-list-item-body-item">
              <div class="invite-list-item-body-item-title">Bónus de convite</div>
              <div class="invite-list-item-body-item-num">{{ item.convite }}</div>
            </div>
          </div>
        </div>
      </div> -->
    <div class="invite-list-empty" v-else>
      <AppImage src="/icons/invite_empty-box.webp" />
      <p>Sem dados</p>
    </div>
    </div>

    <div class="invite-coin2" v-if="!isLeft">
      <div class="invite-coin2-sum">
        <div class="invite-coin2-sum-left">
          <div class="title">Comissāo</div>
      
          <div class="money">R${{ dadosAccuInfo?.TotalCommi }}</div>
        </div>
        <AppImage src="/icons/invite_user.webp" class="user" />
      </div>

      <div class="invite-coin2-list">

        <!-- <div class="invite-coin2-list-item">
         <div class="invite-coin2-list-item-left">
            <AppImage src="/img/invite/invite_clover2.webp" :width="46"/>
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Novos convidados</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.team_num }}</div>
          </div> 
        </div> -->

        <div class="invite-coin2-list-item">
          <div class="invite-coin2-list-item-left">
            <AppImage src="/img/invite/invite_piggy-bank2.webp" :width="46" />
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Recarga direta</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.DepositNum }}</div>
          </div>
        </div>

        <div class="invite-coin2-list-item">
          <div class="invite-coin2-list-item-left">
            <AppImage src="/img/invite/invite_money2.webp" :width="46"/>
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Deposito direta</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.Deposit }}</div>
          </div>
        </div>

        <div class="invite-coin2-list-item">
          <div class="invite-coin2-list-item-left">
            <AppImage src="/img/invite/invite_dollar2.webp" :width="46"/>
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Primeria recarga direta</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.ValidNum }}</div>
          </div>
        </div>

       

        <!--  其他充值金额（2+3级玩家的下级充值总额）-->
        <div class="invite-coin2-list-item">
          <div class="invite-coin2-list-item-left">
            <AppImage src="/img/invite/invite_deposito.webp" :width="46"/>
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Qutras deposito</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.OtherDeposit }}</div>
          </div>
        </div>
        <!--  其他充值人数（2+3级玩家的充值人数）-->
        <div class="invite-coin2-list-item">
           <div class="invite-coin2-list-item-left">
              <AppImage src="/img/invite/invite_recargas.webp" :width="46"/>
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Qutras recargas</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.OtherDepositNum }}</div>
          </div>
        </div>

        <div class="invite-coin2-list-item">
          <div class="invite-coin2-list-item-left">
            <AppImage src="/img/invite/invite_like2.webp" :width="46"/>
          </div>
          <div class="invite-coin2-list-item-right">
            <div class="invite-coin2-list-item-right-label">Apostas totais</div>
            <div class="invite-coin2-list-item-right-num">{{ dadosAccuInfo?.OtherRunning }}</div>
          </div>
        </div>

        
      </div>
    </div>
  </div>
  </div>
  
</template>

<style lang='scss' scoped>
@import '../../theme/mixin.scss';
.bg {
  background: var(--theme-main-bg-color);
  padding-bottom: var(--app-footer-height);
  
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left:0;
 
  .invite {
  overflow-x: hidden;
  display: block;
  height: 100%;
  background: var(--theme-main-bg-color);
  // padding-bottom: var(--app-footer-height);
  
  .invite-top-btn{
    background: var(--theme-main-bg-color);
  }
  &-top-btn {
    padding-top: 20px;
    width: 717px;
    height: 100px;
    margin: 0 auto;
    margin-top: var(--app-navbar-height);
    display: flex;
    button{
      display: inline-block;
      // margin-left: 17px;
    }
    &-space {
      width: 17px;
    }
  }

  &-link {
    width: 715px;
    height: 288px;
    background: var(--app-box-bg-color);
    @include webp('/img/invite/bg1');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 10px;
    margin: 0 auto;
    margin-top: 20px;
    padding: 20px;
    h6{
      margin-top: 17px;
      width: 100%;
      height: 33px;
      font-weight: normal;
      font-size: 30px;
      color: var(--app-title-color);
    }
    &-box {
      width: 100%;
      height: 64px;
      line-height: 64px;
      border-radius: 10px;
      margin-top: 33px;
      padding: 0 8px;
      background-color:var(--theme-main-bg-color);
      .url {
        float: left;
        width: 480px;
        white-space: nowrap;
        overflow: hidden;
        margin-right: 16px;
        color:var(--app-btn-normal-blue-color);
        font-size:24px;
      }
      button{
        float: left;
        margin-top: 7px;
      }
      .btnCopy{
        font-family:Arial;
        font-weight: 700;
        font-size: 30px;
      }
    }
    &-bottom{
      img{
        width: 74px;
        margin-top: 28px;
        margin-right: 30px;
      }
    }
  }

  &-coin{
    width: 705px;
    height: 406px;
    margin: 0 auto;
    margin-top: 22px;
    // background-color: var(--app-box-bg-color);
    border-radius:0px 0px 10px 10px;
    position: relative;
    @include webp('/img/invite/bg2');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &-sum{
      width: 705px;
      height: 187px;
      position: relative;
      &-left{
        color: #fff;
        .title{
          position: absolute;
          margin: 19px 0 0 20px;
          font-family:Arial;
          font-size:24px;
          font-weight: 400;
        }
        .money{
          position: absolute;
          margin: 51px 0 0 20px;
          // font-family:Arial;
          font-weight:700;
          font-size:40px;
          color: #FFE91C;
        }
      }
      .user{
        width: 76px;
        position: absolute;
        right: 32px;
        top: 10px;
      }
    }
    &-list{
      width: 705px;
      height: 310x;
      margin-top: -70px;
      padding: 15px 35px;
      position: absolute;
      &-item{
        width: 342px;
        height: 76px;
        display: inline-block;
        position: relative;
        img{
          width: 76px;
        }
        &-left{
          float: left;
        }
        &-right{
          position: absolute;
          top: 10px;
          left: 90px;
          width: 500px;
          &-label{
            font-family:Arial;
            font-size: 20px;
            color: var(--app-title-color);
          }
          &-num{
            font-family:Arial;
            font-weight:700;
            color:#3b82f6;
            font-size:30px;
          }
        }
        &:nth-child(2n){
          width: 220px;
          margin-left: 20px;
        }
      }
    }
  }
  &-jumpWeek{
    width: 705px;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
    height: 173px;
    &-top{
      width: 705px;
      height: 173px;
      @include webp('/img/invite/bg3');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;

      &-content{
        width:530px;
        height:76px;
        line-height: 45px;
        font-family:Arial;
        color:#ffffff;
        font-size:22px;
        font-weight:900;
        position: absolute;
        top: 16px;
        left: 15px;
      }

      &-btn{
        position: absolute;
        top: 107px;
        left: 223px;
      }
    }

  }
  &-rules{
    width: 705px;
    height: 711px;
    margin: 0 auto;
    margin-top: -50px;
    margin-bottom: 40px;
    &-top{
      width: 745px;
      height: 711px;
      @include webp('/img/invite/bg4');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      &-title{
        position: absolute;
        margin-top: 53px;
        margin-left: 15px;
        font-family:Arial;
        font-weight:700;
        color:#ffffff;
        font-size:30px;
      }
      &-content{
        width:464px;
        height:67px;
        font-family:Arial;
        color:#ffffff;
        font-size:30px;
        position: absolute;
        top: 159px;
        left: 15px;
      }
      &-content1{
        width:400px;
        height:67px;
        font-family:Arial;
        color:#ffffff;
        font-size:30px;
        position: absolute;
        top: 241px;
        left: 15px;
        .redSpan{
          color: #fcca24;
        }
      }
      &-bottom{
        font-family:Arial;
        color:#ffffff;
        font-size:24px;
        position: absolute;
        top: 280px;
        left: 122px;
        &:last-child{
          font-size:24px;
          top: 280px;
          left: 404px;
        }
      }

      &-list{
      width: 705px;
      position: absolute;
      display: block;
      margin: 0 auto;
      top: 362px;
      left: 0px;
      
      &-item{
        width: 100%;
        height: 64px;
        line-height: 64px;
        color:#ffffff;
        font-size:24px;
        font-family:Arial;
        font-weight:700;
        // border: 2px solid var(--theme-main-bg-color);
        &:last-child{
          border: none;
        }
        &-left{
          // width: 50%;
          // float: left;
          // text-align: center;
          position: absolute;
          left: 15px;
          white-space:pre;
        }
        &-right{
          // width: 50%;
          // float: left;
          // text-align: center;
          position: absolute;
          left: 352px;
          white-space:pre
        }
      }
    }
    }
    
  }
  
  &-query{
    width: 723px;
    margin: 0 auto;
    margin-top: 22px;
    &-btn{
      width: 100%;
      height: 46px;
      display: flex;
      &-item{
        width:239px;
        height:46px;
        line-height: 46px;
        background:#e2e8f0;
        border-radius:29px;
        text-align: center;
        color: #474b51;
        margin-right: 3px;
        font-family:Arial;
        font-size:24px;
        &:last-child{
          margin: 0;
        }
        &-active{
          background:#3b82f6;
          color: #fff;
        }
      }
    }
    &-input{
      width: 717px;
      height: 64px;
      line-height: 64px;
      background:#e2e8f0;
      border-radius:10px;
      margin-top: 20px;
      position: relative;
      .date{
        margin-left: 22px;
        font-family:Arial;
        color:#474b51;
        font-size:24px;
      }
      img{
        position: absolute;
        width: 52px;
        right: 13px;
        top: 6px;
      }
    }
  }
  &-list{
    width: 705px;
    margin: 0 auto;
    margin-top: 15px;
    height: calc(100% - 725px);
    .app-List{
      width: 100%;
      height: 100%;
      // padding-top: 0px;
    }
    &-item{
      width: 100%;
      height: 143px;
      margin-bottom: 20px;
      &-top{
        width: 100%;
        height: 55px;
        background-color: var(--app-dados-item-title-bg-color);
        color: #fff;
        font-family:Arial;
        font-size:24px;
        position: relative;
        border-radius:10px 10px 0px 0px ;
        &-left{
          position: absolute;
          top: 18px;
          left: 42px;
        }
        &-right{
          position: absolute;
          top: 18px;
          right: 28px;
        }
      }
      &-body{
        width: 100%;
        height: 88px;
        background-color: var(--app-box-bg-color);
        display: flex;
        padding-top: 10px;
        border-radius:0px 0px 10px 10px;
        &-item{
          width: 33.3%;
          text-align: center;
          &-title{
            font-family:Arial;
            color: var(--app-title-color);
            font-size:24px;
          }
          &-num{
            font-family:Arial;
            font-weight:700;
            color:#3b82f6;
            font-size:30px;
            padding-top: 8px;
          }
        }
      }
    }
  }
  &-list-empty{
    width: 702px;
    height: 702px;
    margin: 0 auto;
    margin-top: 74px;
    position: relative;
    img{
      width: 702px;
    }
    p{
      position: absolute;
      top: 511px;
      left: 275px;
      font-family:Arial;
      color:#9aa0b5;
      font-size:30px;
    }
  }
  &-coin2{
    width: 750px;
    height: 392px;
    margin: 0 auto;
    margin-top: 22px;
    // background-color: var(--app-box-bg-color);
    // border-radius:0px 0px 10px 10px;
    position: relative;
    @include webp('/img/invite/bg5');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &-sum{
      width: 750px;
      height: 187px;
      position: relative;

      &-left{
        color: #fff;
        .title{
          position: absolute;
          margin: 19px 0 0 30px;
          font-family:Arial;
          font-size:24px;
          font-weight: 400;
        }
        .money{
          position: absolute;
          margin: 51px 0 0 30px;
          // font-family:Arial;
          font-weight:700;
          font-size:40px;
          color: #FFE91C;
        }
      }
      .user{
        width: 76px;
        position: absolute;
        right: 30px;
        top: 10px;
      }
    }
      &-list {
        width: 705px;
        height: 310x;
        margin-top: -85px;
        padding: 15px 30px;
        position: absolute;
  
        &-item {
          width: 342px;
          height: 76px;
          display: inline-block;
          position: relative;
  
          img {
            width: 76px;
          }
  
          &-left {
            float: left;
            padding-top: 14px;
          }
  
          &-right {
            float: left;
            padding-top: 10px;
            padding-left: 22px;
  
            &-label {
              font-family: Arial;
              font-size: 20px;
              color: var(--app-title-color);
            }
  
            &-num {
              font-family: Arial;
              font-weight: 700;
              color: #3b82f6;
              font-size: 30px;
            }
          }
  
          &:nth-child(2n) {
            width: 220px;
            margin-left: 50px;
          }
        }
      }
    }
  }

}


// .invite-queryTeam-list{
//   width: 622px;
//   background: #fff;
//   margin: 0 auto;
//   border-radius: 10px;
//   height:78vh; 
// }
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
