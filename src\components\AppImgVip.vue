<script setup lang="ts" name="app-img-vip">
withDefaults(defineProps<{
  vipLevel: string | number;
  width?: string | number;
  height?: string | number;
}>(), {
  vipLevel: 0,
})
</script>

<template>
  <div class="app-img-vip" :style="{
    width: width ? $toPx(+width) : '',
    height: height ? $toPx(+height) : '',
  }">
    <!-- <AppImage :src="`/img/vipbadge/n-vip${vipLevel}.png`" alt="" /> -->
    <AppImage :src="`/img/vipbadge/icon_medals_${vipLevel}.png`" alt="" />
    <div class="vip-level">{{vipLevel}}</div>
  </div>
</template>

<style lang="scss" scoped>
.app-img-vip {
  width: 142px;
  height: 187px;
  position: relative;
  img {
    width: 100%;
  }
  .vip-level {
    width: 100%;
    height: 49px;
    font-family: ArialMT;
    font-size: 60px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #fefefe;
    position: absolute;
    top: 25%;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
  }
}
</style>
