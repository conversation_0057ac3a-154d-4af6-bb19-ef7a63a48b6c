<script setup lang='ts' name='vip'>
const swipeWidth = window.innerWidth * (750 / 750)
const suffixWebp = Array.from(document.documentElement.classList).includes('webp');
const cardImg = `url('/img/vip/bg_vip_card.png${suffixWebp ? '.webp' : ''}')`

const router = useRouter()
const { userInfo } = storeToRefs(useAppStore())
const userLevel = computed(() => userInfo.value.vip ?? 0) // 用户等级
const nowValidFlow = computed(() => userInfo.value.now_valid_amount ? userInfo.value.now_valid_amount : 0) // 当前流水
const nowValidDeposit = computed(() => userInfo.value.now_deposit ? userInfo.value.now_deposit : 0) // 当前充值
const upgradeProgress = computed(() => { // 升级进度
  const flowPrecent = +nowValidFlow.value / +nextLevelData.value.flow
  const depositPrecent = +nowValidDeposit.value / +nextLevelData.value.deposit_amount
  const total = (flowPrecent >= 1 ? 1 : flowPrecent) * 50 + (depositPrecent >= 1 ? 1 : depositPrecent) * 50
  return Number.isNaN(total) ? 0 : toDecimal(total, 0)
})

const showLevel = ref(userLevel.value)
const { data: vipList } = useRequest(ApiGetMemberVip)
const showData = computed(() => vipList.value ? vipList.value[showLevel.value] : { free_withdraw_num: 0, withdraw_limit: 0, amount: 0, deposit_amount: 0, flow: 0 })
const nextLevelData = computed(() => {
  if (vipList.value && showLevel.value < vipList.value.length - 1) {
    return vipList.value[showLevel.value + 1]
  }
  else {
    return { free_withdraw_num: 0, withdraw_limit: 0, amount: 0, deposit_amount: 0, flow: 0 }
  }
})

const onChange = (index: number) => {
  showLevel.value = index
}
</script>
<template>
  <div class="vip">
    <AppHeader title="Privilégio VIP" leftArrow :fixed="false" />
    <!-- <AppImage class="bg" src="/img/vip/banner.png" alt="" /> -->
    <div class="body">

      <!-- 等级轮播 -->
      <van-swipe @change="onChange" :initial-swipe="userLevel" :loop="false" class="my-swipe" :width="swipeWidth"
        :show-indicators="false">
        <van-swipe-item v-for="item, i in vipList" :key="i">
          <div class="swipe-item">
            <div class="top">
              <AppImgVip :vipLevel="item.vip"  class="img-vip"/>
              <div class="top-vip-level">VIP{{ item.vip }}</div>
              <div v-show="vipList && i !== vipList?.length - 1" class="progress-box">
                <AppProgress class="progress" :width="284" max="100" :value="upgradeProgress" />
                <span>{{ upgradeProgress }}/100</span>
              </div>
            </div>
            <div class="bottom">
              <div class="data">
                <label>R${{ toDecimal(item.deposit_amount, 0) }}</label>
                <span>Depósito<br />
                  cumulativo</span>
              </div>
              <div class="data">
                <label>R${{ toDecimal(item.flow, 0) }}</label>
                <span>Requisitos<br />
                  de fluxo</span>
              </div>
              <div class="data">
                <label>R${{ toDecimal(userInfo.now_valid_amount ?? 0, 0) }}</label>
                <span>Valor da<br />
                  experiência</span>
              </div>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>

      <!-- 钱包 -->
      <div class="wallet">
        <div class="wallet-item">
          <!-- <AppImage src="/icons/i-withdarw.png" alt="" /> -->
          <AppImage src="/img/vip/icon_01.png" alt="" />
          <label>{{ showData.free_withdraw_num }}</label>
          <span>Número de saques<br />
            por dia</span>
        </div>
        <div class="wallet-item">
          <!-- <AppImage src="/icons/i-bet-record.png" alt="" /> -->
          <AppImage src="/img/vip/icon_02.png" alt="" />
          <label>R${{ toDecimal(showData.withdraw_limit, 0) }}</label>
          <span>Limite diário<br />
            de retirada</span>
        </div>
        <div class="wallet-item">
          <!-- <AppImage src="/icons/i-vip2.png" alt="" /> -->
          <AppImage src="/img/vip/icon_03.png" alt="" />
          <label>R${{ toDecimal(showData.amount, 0) }}</label>
          <span>Bônus de<br />
            atualização</span>
        </div>
      </div>

      <div class="wrapper">

        <!-- 进度 -->
        <div class="rest-part" v-show="vipList && showLevel < vipList?.length - 1">
          <h6>Distância próximo nível:</h6>
          <div class="part-line"></div>
          <div class="box">
            <p>Quantidade total de recarga: <span class="box-money-text">{{ toDecimal(nowValidDeposit) }}/{{
              nextLevelData.deposit_amount
            }}</span></p>
            <div class="progress">
              <AppProgress :width="432" :max="nextLevelData.deposit_amount" :value="nowValidDeposit" bgcolor="#68fffa" />
              <AppButton width="168" height="58" font-size="25" :radius="28" blue color="#fefefe" @click="router.push('/finance?tab=deposit')">IR</AppButton>
            </div>
          </div>
          <div class="box">
            <p>Número total de apostas: <span class="box-money-text">{{ toDecimal(nowValidFlow) }}/{{ nextLevelData.flow
            }}</span>
            </p>
            <div class="progress">
              <AppProgress :width="432" :max="nextLevelData.flow" :value="nowValidFlow" bgcolor="#68fffa" />
              <AppButton width="168" height="58" font-size="25" :radius="28" blue color="#fefefe" @click="router.push('/')">besides</AppButton>
            </div>
          </div>
        </div>

        <!-- table -->
        <div class="table-bg">
          <div class="table-wrapper">
            <table>
              <colgroup>
                <col style="width:var(--app-px-158);">
                <col style="width:var(--app-px-154);">
                <col style="width:var(--app-px-195);">
                <col style="width:var(--app-px-151);">
              </colgroup>
              <thead>
                <tr>
                  <th>Nivel</th>
                  <th>Prêmios</th>
                  <th>Recompensa</th>
                  <th>Aderecos</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for=" item, i   in   vipList  " :key="i">
                  <td>vip{{ item.vip }}</td>
                  <td>R${{ toDecimal(item.amount) }}</td>
                  <td>{{ toDecimal(item.rebate_rate) }}%</td>
                  <td>x1</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<style lang='scss' scoped>
@import '../../theme/mixin.scss';

.vip {
  padding-bottom: 150px;
  color: #fff;
  font-size: 28px;
  position: relative;

  .bg {
    width: 100%;
  }
}

.body {
  position: absolute;
  top: 90px;
  padding-top: 30px;
  background: #12192b;
}

.my-swipe {
  height: 280px;
  width: 750px;
  padding-left: 40px;
  margin-bottom: 20px;
  .swipe-item {
    width: 668px;
    height: 281px;
    // background-image: url();
    background-image: v-bind(cardImg);
    @include webp('/img/vip/bg_vip_card.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .top {
      width: 100%;
      height: 146px;
      display: flex;
      align-items: center;
      padding-top: 31px;
      padding-left: 48px;
      // margin-bottom: 8px;
      box-sizing: content-box;
      .img-vip{
        position: absolute;
        right: 125px;
        top: 0;
      }
      &-vip-level{
        width: 194px;
        height: 52px;
        font-family: Arial-BoldMT;
        font-size: 68px;
        font-weight: 700;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #3d81fe;
        text-shadow:-3px 0 white,0 3px white,3px 0 white,0 -3px white;
        position: absolute;
        top: 10px;
        left: 147px;
      }
      .progress-box {
        // display: flex;
        // align-items: center;
        width: 470px;
        position: absolute;
        left: 40px;
        margin-top: 25px;
        .progress{
          float: left;
        }
        span{
          float: left;
          font-size: 36px;
          line-height: 25px;
          margin-left: 20px;
        }
      }
    }

    .bottom {
      display: flex;

      .data {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        label {
          font-size: 32px;
          // font-weight: 700;
          margin-bottom: 0px;
        }

        span {
          font-size: 24px;
          color: #fff;
          text-align: center;
        }
      }
    }
  }
}

.wallet {
  margin: 0 auto;
  width: 668px;
  height: 182px;
  border-radius: 20px;
  background: #28374d;
  display: flex;
  padding-top: 20px;

  .wallet-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      margin-bottom: 10px;
      height: 44px;
    }

    label {
      font-size: 30px;
      // font-weight: 700;
      height: 36px;
    }

    span {
      color: #5186e3;
      font-size: 24px;
      text-align: center;
    }
  }
}

.wrapper {
  padding: 30px 20px 150px;
  background-color: #12192b;

  h6 {
    font-weight: 400;
    margin-bottom: 10px;
    margin-left: 5px;
    font-size: 24px;
  }
  .part-line{
    width: 707px;
    height: 2px;
    margin-bottom: 20px;
    background-color: #28374d;
  }
  .box {
    width: 710px;
    // background-image: linear-gradient(to bottom, #044B9A, #011A51);
    background: #283950;
    border-radius: 20px;
    margin: 0 auto;
    height: 116px;
    padding: 20px 21px;
    box-sizing: border-box;
    margin-bottom: 30px;

    p {
      width: 432px;
      margin-bottom: 3px;
      font-size: 24px;
      color: #b3c2d8;
      position: relative;
    }
    .box-money-text{
      min-width: 106px;
      height: 19px;
      font-family: ArialMT;
      font-size: 24px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #679fea;
      position: absolute;
      right: 0;
      top: 15px;
    }
    .progress {
      width: 100%;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      button{
        position: absolute;
        top: -22px;
        right: 20px;
      }
    }
  }

  .table-bg {
    width: 710px;
    border-radius: 20px;
    background: hsl(214, 33%, 24%);
    // padding: 25px;

    .table-wrapper {
      border-radius: 8px;

      table {
        width: 710px;
        border-collapse: collapse;
        padding: 0;
        thead{
          th{
            color: #679fea;
            font-size: 24px;
            font-weight: normal;
            line-height: 75px;
            border-bottom: 1px solid #152237;
          }
        }
        tbody {
          tr {
            color: #b3c2d8;
            font-size: 28px;
            font-weight: normal;
            line-height: 75px;
            text-align: center;
            border-bottom: 1px solid #152237;
          }
        }
      }
    }
  }


}
</style>