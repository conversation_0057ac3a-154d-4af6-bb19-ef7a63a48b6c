<script setup lang="ts" name="app-find-pwd">
// const router = useRouter()
const appStore = useAppStore();
const { showRecharge } = storeToRefs(appStore);

//关闭界面
const closePage = () => {
  appStore.setShowRecharge(false);
};
const goPay = () => {
  appStore.setShowRecharge(false);
  appStore.setPayVisble(true);
};
</script>

<template>
  <!-- @click-overlay="onClickOverlay" -->
  <div>
    <van-popup
      class="app-login-register"
      v-model:show="showRecharge"
      teleport="body"
      
      :close-on-click-overlay="false"
    >
      <div class="content">
        <div class="title">Registrado com sucesso</div>
        <div class="message">Registrado com sucesso! Vamos jogar agora~</div>
        <div class="submit">
          <AppButton
            @click="goPay()"
            class=""
            width="var(--app-px-516)"
            height="70"
            blue
            :radius="15"
            black-text
            font-size="var(--app-px-24)"
            color="var(--theme-font-on-background-color)"
          >
            Recarregue Agora
          </AppButton>
        </div>
      </div>
      <img
        class="app-image img close-icon"
        src="/icons/close_black.png.webp"
        @click="closePage()"
        alt=""
       
      />
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.content {
  width: 600px;
  height: 314px;
  border-radius: 16px;
  background-color: var(--theme-main-bg-color);
  border: thin solid;
  border-color: var(--theme-color-line);
  margin-top: 120px;
  position: relative;

  .title {
    text-align: center;
    margin-top: 20px;
    font-size: 28px;
    color:  var(--theme-primary-font-color);
    font-weight: 400;
    line-height: 42px;
  }
  .message {
    text-align: center;
    margin-top: 30px;
    color: var(--theme-text-color);
    font-size: 24px;
    line-height: 36px;
  }
}

.submit {
  // position: absolute;
  margin-top: 65px;
  width:600px;
  // left: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  // transform: translateX(50%);
  // top: 55%;
}

.close-icon {
  width: 60px;
  border: 5px solid white;
  border-radius: 100%;
  padding: 5px;
  margin: 40px auto;
  display: block;
}
</style>
