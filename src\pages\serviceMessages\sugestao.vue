<script setup lang='ts' name='sugestao'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const secletbtn = ref(0)
const textHeight = ref(0)
const isFocused = ref(false)
//隐藏底下菜单
appStore.setFooterDialogVisble(false)

const contnetRef = ref()
const fromData = reactive({
    content: ''
});
const clickBtn = (idx) => {
    secletbtn.value = idx
}

const onConfirme = () => {

}


const text = ref('');



</script>

<template>
    <div class="rect">
        <div class="btn" :style="{
            'background-color': secletbtn == 0 ? 'var(--theme-primary-color)' : 'var(--theme-main-bg-color)',
            'color': secletbtn == 0 ? 'var(--theme-filter-active-color)' : 'var(--theme-text-color)',
        }" @click="clickBtn(0)">Criar<br>feedback</div>
        <div class="btn" :style="{
            'background-color': secletbtn == 1 ? 'var(--theme-primary-color)' : 'var(--theme-main-bg-color)',
            'color': secletbtn == 1 ? 'var(--theme-primary-font-color)' : 'var(--theme-text-color)',
        }" @click="clickBtn(1)">Meu<br>Feedback</div>
    </div>
    <div v-if="secletbtn == 0" class="criar">
        <div class="criar-bg">
            <div class="criar-bg-a">
                <span>Conteúdo do Feedback</span>
                <span>(Faça sugestóes para melhorias)</span>
            </div>
            <div class="criar-bg-b">
                <textarea v-model="text" v-focus="isFocused"
                    placeholder="Suas opiniões são valiosas para nós. Qualquer sugestão valiosa será considerada, e uma vez adotada, o nível de importância determinará a recompensa em dinheiro. Sinta-se à vontade para fazer sugestões!"
                    maxlength="200" class="ant-input" rows="5" spellcheck="false"></textarea>
            </div>
            <div class="criar-bg-c">{{ text.length }}/200</div>
            <div class="criar-bg-a">
                <span>As imagens não mentem</span>
                <span>(Mais fácil de ser aceito)</span>
            </div>
            <div class="criar-bg-d">
                <input type="file" accept="image/png,image/jpeg,image/jpg,image/gif,video/mp4,video/mp4"
                    class="criar-bg-d-input">
            </div>
            <div class="criar-bg-e">
                <span>O envio de imagens e vídeos é suportado (com até 50MB)</span>
            </div>
            <div class="criar-bg-g">
                <span>Regras para recompensas</span>
            </div>
            <div class="criar-bg-f">
                <span>Estabelecemos bônus substanciais especificamente para coletar feedback, a fim de otimizar o
                    sistema e recursos para proporcionar uma melhor experiência a você! Uma vez aceitas, as recompensas
                    serão concedidas com base na utilidade (exceto aquelas não aceitas).</span>
            </div>
        </div>

    </div>
    <div v-else-if="secletbtn == 1" class="meu">
        <div class="meu-right">
            <div class="meu-right-up" style="max-width: 1.3rem;">
                <p class="meu-right-up-a">Pendente </p>
                <p class="meu-right-up-b">
                    <span class="meu-right-up-b-b"
                        style="width: 100%; margin-bottom: -2.01711px; padding-bottom: 2.01711px;">
                        <span
                            class="meu-right-up-b-a" style="font-size: 17.1454px;">
                            <span
                                class="meu-right-up-b-a-a" style="-webkit-line-clamp: 2;">R$0,00
                            </span>
                        </span>
                    </span>
                </p>
            </div>
            <button disabled="disabled" type="button"
                class="ant-btn">
                <span>Resgatar Tudo</span>
            </button>
            
        </div>
        <app-empty class="empty"  text="Sem feedback"></app-empty>
    </div>
    <div v-if="secletbtn == 0" class="content-bottom">
        <AppButton @click="onConfirme" :loading="loading" width="100%" height="70" red2 :radius="15" fontSize="13px"
            color="var(--theme-main-bg-color)">
            Enviar feedback
        </AppButton>
    </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.rect {
    position: relative;
    width: 400px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: -5px 0 0 -10px;
    .btn {
        position: relative;
        height: 50px;
        width: 145px;
        margin-right: 20px;
        //padding-top: 4px;
        background-color: var(--theme-main-bg-color);
        border-color: var(--theme-text-color);
        border: 1px var(--theme-text-color) solid;
        border-radius: 45px;
        text-align: center;
        color: #bae2d2;
        font-size: 21px;
    }

    //background-color: black;
}

.criar {
    position: relative;
    width: 100%;
    height: 900px;
    font-size: 24px;
    .criar-bg {
        width: 700px;
        height: calc(100%);
        margin-left: 25px;
        border-radius: 10px;
        background-color: var(--theme-main-bg-color);

        .criar-bg-a {
            display: flex;
            padding: 15px 0 20px 15px;
            
            span {
                &:nth-child(1) {
                    color: var(--theme-text-color);
                    font-weight:bold;
                }

                &:nth-child(2) {
                    padding-left: 15px;
                    color: var(--theme-text-color-lighten)
                }
            }

        }

        .criar-bg-e {
            display: flex;
            padding: 25px 15px;
            padding-left: 15px;
            color: var(--theme-text-color-lighten);
            height: 30px;
        }

        .criar-bg-g{
            padding: 35px 0 0 15px;
            color: var(--theme-text-color);
            font-weight:bold;
        }

        .criar-bg-f {
            padding: 5px 15px;
            padding-left: 15px;
            color: var(--theme-text-color-lighten);
            height: 140px;
        }

        .criar-bg-b {
            margin-left: 20px;
            width: 660px;
            height: 180px;

            //background-color: black;
            .ant-input {
                width: 660px;
                resize: vertical;
                padding: 20px 10px;
                background-color: var(--theme-main-bg-color);
                border-radius: 15px;
                color: white;
                border: 1px #C5E2D2 solid;
                //border: 1px var(--theme-primary-color) solid;

            }

        }

        .criar-bg-c {
            position: relative;
            width: 100%;
            height: 50px;
            right: 20px;
            top: 20px;
            text-align: right;
            color: var(--theme-text-color-lighten);
            font-size: 18px;
        }

        .criar-bg-d {
            height: 124px;

            .criar-bg-d-input {
                width: 124px;
                margin-left: 15px;
                // background-image: url('/icons/icons-jia.webp');
                background-image: url('/img/user/icon-jia.webp');
                background-repeat: no-repeat;
                background-size: 124px 124px;
            }
        }
    }
}


.meu{
    position: absolute;
    width: 100%;
    height: 900px;
    top: 100px;


    .meu-right-up-a{
        color: var(--theme-text-color-lighten);
        padding-top: 10px;
        
    }
    .meu-right-up{
        //background-color: var(--theme-disabled-bg-color);
        position: relative;
        right: 40px;
    }

    .meu-right-up-b-a-a{
        font-size: 24px;
        padding-left: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        color :var(--theme-secondary-color-finance);
    }

    .meu-right{
        position: absolute;
        display: flex;
        margin: -85px 0 0 70px;
        right: -60px;
        top: 0px;
        width: 300px;
        height: 100px;

    }

    .ant-btn{
        position: relative;
        left: 70px;
        top: 10px;
        width: 115px;
        height: 45px;
        border-radius: 15px;
        background-color: var(--theme-disabled-bg-color);
        color: white;
        font-size: 18px;
    }

    .empty{
       
        position: absolute;
        margin-top: 100px;
        width: 100%;
        height: 600px;
        text-align: center;

    }
    

}

.content-bottom {
    position: absolute;
    bottom: 0;
    height: 120px;
    width: 100%;
    padding: 25px 25px;
    background-color: var(--theme-main-bg-color);
}

.content {
    height: 400px;
}

.ant-input::-webkit-input-placeholder {
    color: var(--theme-text-color-lighten);
}

input[type="file"]::-webkit-file-upload-button {
    height: 124px;
    width: 124px;
    visibility: hidden;
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>