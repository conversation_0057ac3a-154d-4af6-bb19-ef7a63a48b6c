{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "es2016", "lib": ["DOM", "ESNext"], "strict": true, "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "noUnusedLocals": true, "strictNullChecks": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "types": ["vite/client", "vite-plugin-pages/client", "vite-plugin-pages-layouts/client", "unplugin-vue-macros/macros-global"], "paths": {"~/*": ["src/*"]}}, "exclude": ["dist", "node_modules"]}