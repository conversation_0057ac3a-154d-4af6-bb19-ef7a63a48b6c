<script setup lang="ts" name="app-game-list">

const gameStore = useGameStore()
const { gameNavInit } = storeToRefs(gameStore)

const route = useRoute()

const id = computed(() => +route.params.id)

const filterType = ref('')

const setFilterGameParam = (v: string) => {
  filterType.value = v
}

</script>

<template>
  <div v-if="gameNavInit && gameNavInit[id]" class="app-game-list-container">

    <!--分类显示 各个游戏平台的捕鱼或者接机，视讯-->
    <!-- <template v-for="plat in gameNavInit[id]" :key="plat.id">
      <AppPlatGameBox :id="id" :platform="plat" :filter-type="filterType" />
    </template> -->

    <!--不区分游戏平台 直接显示该类型游戏-->
    <AppPlatGameBox :id="id" :filter-type="filterType" />

  </div>
</template>

<style lang="scss" scoped>
.app-game-list-container {
  // padding-top: 12px;
  position: relative;
  padding-bottom: 132px;
  .g-filter {
    position: absolute;
    right: 0;
    top: 28px;
  }
}
</style>

<route lang="yaml">
meta:
  layout: home
</route>
