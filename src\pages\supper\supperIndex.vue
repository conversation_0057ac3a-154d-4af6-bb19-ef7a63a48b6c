<script lang="ts" setup>
import { reactive ,ref} from 'vue';
const appStore = useAppStore()
const { custService, isApp } = storeToRefs(appStore)
const showMain = ref(true)
const showTitle = ref("")
const showMsg  = ref("")
const showSrcUrl = ref(false)
const srcUrl = ref("")
const vivo=[{tile:"Comum",data:[{msg:"Saque Falhou & CPF Errado",id:"btn_1_1"},{msg:"Depósito Não Recebido",id:"btn_1_2"},{msg:"Esqueceu A Senha?",id:"btn_1_3"},{msg:"O que é aposta válida?",id:"btn_1_4"}]},
              {tile:"Cadastro e Login",data:[{msg:"O Usuário Não Existe?",id:"btn_2_1"},{msg:"<PERSON><PERSON><PERSON><PERSON><PERSON>ada<PERSON>",id:"btn_2_2"},{msg:"Cadastro Falhou Por Celular",id:"btn_2_3"}]},
              {tile:"Depósito",data:[{msg:"Depósito Não Recebido",id:"btn_3_1"},{msg:"Incentivos De Depósito",id:"btn_3_2"},{msg:"Bônus Convertido Em Dinheiro Real",id:"btn_3_3"}]},
              {tile:"Saque",data:[{msg:"Saque Falhou & CPF Errado",id:"btn_4_1"},{msg:"Saque Não Recebida",id:"btn_4_2"},{msg:"Obter Saldo Rextratável",id:"btn_4_3"}]},
              {tile:"Outros",data:[{msg:"Esqueceu A Senha?",id:"btn_5_1"},{msg:"O que é aposta válida?",id:"btn_5_2"},{msg:"Celular não pode receber código de verificação",id:"btn_5_3"},{msg:"Nenhuma comissão recebida",id:"btn_5_4"}]},
]

const vivoDetail={ btn_1_1:"Retirar o CPF  errado.De acordo com estatísticas oficiais, a grande maioria das falhas de retirada são devidas a entradas incorretas de CPF.Por favor, certifique-se de ter colocado o CPF correto.Se você precisa modificar o CPF, contate o suporte oficial.",
                   btn_1_2:"A probabilidade de isso acontecer é muito baixa.Se você completar o Depósito e o tempo exceder 5 minutos, o Depósito não é recebido.Por favor, contate o suporte oficial. Envie seu comprovante de pagamento e identificação da conta,o suporte verificará o processamento.",
                   btn_1_3:"Você esqueceu sua senha?Você pode recuperar selecionando.[Esqueceu a senha?] na página de login.Para obter sua senha, você precisa verificar seu número de telefone ou endereço de email.Certifique-se de usar o mesmo número de telefone ou email de quando você se inscreveu.",
                   btn_1_4:"Uma aposta válida é uma aposta que resultou na vitória ou derrota.Todas as apostas canceladas não são consideradas apostas.",
                 
                   btn_2_1:"Por favor, certifique-se de ter registrado a primeira conta relevante.Se já possui uma conta, confirme seu método de login, e e-mail.Você pode ter escolhido o método de login errado.",
                   btn_2_2:"Estamos trabalhando em mais benefícios.Atualmente, cada usuário registrado tem um benefício de Depósito.",
                   btn_2_3:"Só uma conta de jogo pode ser registrada para um número de celular.",
                
                   btn_3_1:"A probabilidade de isso acontecer é muito baixa.Se você completar o Depósito e o tempo exceder 5 minutos, o Depósito não é recebido.Por favor, contate o suporte oficial. Envie seu comprovante de pagamento e identificação da conta,o suporte verificará o processamento.",
                   btn_3_2:"Primeiro Benefício de Depósito: Você pode obter um bônus adicional de 20% para o primeiro depósito. Só há uma chance.Você não tem que se preocupar com restrições em quantidades normais de presentes.Os dois são calculados separadamente.",
                   btn_3_3:"Dinheiro Bônus precisa ser convertido em dinheiro real antes de ser retirado.A conversão por dinheiro real requer a conclusão da tarefa de prêmio de dinheiro em apostas válidas.Cada vez que você completa uma indicação, você pode converter um dinheiro de bônus parcial em dinheiro real.Se você permanecer com dinheiro bônus após o evento, uma nova tarefa de dinheiro bônus será criada com o dinheiro bônus restante.Se você tem dinheiro bônus mas não tem uma tarefa de dinheiro bônus, complete um depósito de qualquer importância e automaticamente será criado uma nova tarefa de dinheiro bônus.",
                   
                   btn_4_1:"De acordo com estatísticas oficiais, a grande maioria das falhas de retirada são devidas a entradas incorretas de CPF.Por favor, certifique-se de ter colocado o CPF correto.Se você precisar mudar o CPF, contate o suporte oficial.",
                   btn_4_2:"Por favor, confirme que o estado de retirada é mostrado como bem sucedido.Se essa pequena probabilidade ocorrer, contate o suporte oficial.Por favor, forneça o número de ordem de retirada e ID da conta.O suporte irá comunicar e verificar o PIX, certifique-se de que você recebeu a busca.",
                   btn_4_3:"Cada depósito, precisa passar por um posto antes que possa ser convertido em um saldo de retirada.Só apostas que geram ganhos e perdas podem obter o Saldo de Retirada.Todas as apostas canceladas, rejeitadas e retiradas não podem obter o saldo de retirada.",
                  
                   btn_5_1:"Você esqueceu sua senha?Você pode recuperar selecionando [esquecer a senha?] na página de login.Para obter sua senha, você precisa verificar seu número de telefone ou endereço de email.Assim, certifique-se de usar seu número de telefone ou endereço de email de quando se inscreveu.",
                   btn_5_2:"Uma aposta válida é uma aposta que resultou na vitória ou derrota.Todas as apostas canceladas, não são consideradas apostas.",
                   btn_5_3:"Verifique se o número de celular entrou corretamente.Então certifique-se de que a rede é normal.Se tudo é normal, recomenda-se que você tente algumas vezes.",
                   btn_5_4:"Confirme que há um registro de convites para esse usuário.Confirme que o usuário concluiu o primeiro depósito.Se tudo isso é verdade, contate com o suporte oficial.A medida que o número de membros que completam o primeiro depósito aumenta, você pode obter Comissões Extras.",
}






//跳转
function gotoDetial(id:any,title:any){
  // console.log("gotoDetial="+id)
  showMain.value = !showMain.value
  if(title){
    showTitle.value = title
    showMsg.value = vivoDetail[id]
  }
}



//跳转到浏览器界面
function gotoSupport(data?:any){
  if (!data) return
  if(data.open_method == "2"){ //1 内部打开 2 外部打开
    window.open(data.link, '_blank')
  } else {
    srcUrl.value = data.link;
    showSrcUrl.value = true;
  }
}


</script>

<template>
  <div class="topDiv" v-show="!showSrcUrl">
    <AppIndexHeader />
    <AppPageTitle v-show="showMain" left-arrow title="Suporte Ao Vivo" background="#324b6e" title-weight="700" />
    <div  v-show="showMain" class="content" > <!--中间显示-->
        <div  v-for="item in vivo"  :key="item.tile">
          <label class="labelTitle">{{ item.tile }}</label>
          <div  v-for="data in item.data"  :key="data.id" @click="gotoDetial(data.id,data.msg)">
            <label class="labelMsg" >{{ data.msg }}</label>
            <AppImage  src="/icons/back.png" class="imgGo" />
          </div>
        </div>
        <div class="gotoSupport" @click="() => gotoSupport(custService)">
          <AppImage src="/icons/kefu" class="icon"  />
          <span class="clientes">Contate o suporte</span>
        </div>
    </div>

    <div  v-show="!showMain" class="content2" > <!--中间显示-->
      <label class="labelTitle2">{{ showTitle}}</label>
      <AppImage class="close"  src="/icons/close_black.png.webp" @click="gotoDetial" />
      <label class="labelMsg2" >{{ showMsg }}</label>

      <div class="gotoSupport" @click="() => gotoSupport(custService)">
          <AppImage src="/icons/kefu" class="icon"  />
          <span class="clientes">Contate o suporte</span>
        </div>

    </div>
    

  </div>
  <div class="serverDiv" v-show="srcUrl">
    <!-- <div
      v-if="showLoading"
      class="launch_loading"
      :style="{ backgroundImage: `url(${localSrc}/venueLoading/l_${gameId}.png)` }"
    /> -->
    <AppIndexHeader />
    <AppPageTitle left-arrow :backFunc = '() => { showSrcUrl = false }' title="" background="#324b6e" title-weight="700" />
    <div class="divIframe"></div>
    <iframe id="iframeLaunchGame"  :src="srcUrl" ></iframe>

    <!-- <AppImage class="btns" :src="`/img/launchClose.webp`" alt=""  @click="$router.go(-1)" /> -->

  </div>    
      

</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.pt-30 {
  padding-top: 30px;
}



.topDiv {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 132px;
  background: var(--theme-main-bg-color);
  position: relative;
}


.content{
  width:719px;
  background:var(--app-box-bg-color);
  border-radius:15px;
  margin: 0 auto;
  // margin-bottom: 20px;
  // min-height: 85vh;
  //
  display: flex;
  flex-direction: column;
  // align-items: center;
}


.labelTitle{
  display: flex;
  margin-top: 25px;
  height: 40px;
  line-height: 40px;
  font-family:Arial;
  font-weight:700;
  color:#FFFFFF;
  font-size:20px;
  margin-left: 45px;
  // background-color: #9a9fb0;
}

.labelMsg{
  line-height: 60px;
  font-family:Arial;
  color:#FFFFFF;
  font-size:18px;
  margin-left: 45px;

  //background-color: #9a9fb0;
}


.imgGo{
  position: absolute;
  width: 25px;
  right:55px;
  margin-top: 15px;
  transform: rotate(180deg)
}

.gotoSupport{
  margin: auto;
  width:638px;
  height:86px;
  background:var(--app-shop-pagar-color);
  border-radius:15px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.icon{
  // display: block;
  width: 51px;
  height: 50px;
  margin-top: 20px;
  margin-left: 25px;
  // padding-left: 20px;
}

.clientes{
  position: absolute;
  font-family:Arial;
  font-weight:700;
  color:#ffffff;
  font-size:24px;
  left: 280px;
  margin-top: 30px;
}

//------------------------
.content2{
  display: block;
  width:719px;
  background:var(--app-box-bg-color);
  border-radius:15px;
  margin: 0 auto;
  min-height: 85vh;
  margin-top: 20px;
}

.labelTitle2{
  height: 160px;
  line-height: 160px;
  font-family:Arial;
  font-weight:700;
  color:#FFFFFF;
  font-size:20px;
  margin-left: 45px;
  // background-color: #ffffff;
}


.labelMsg2{
  display: block;
  width:630px;
  line-height: 60px;
  font-family:Arial;
  color:#FFFFFF;
  font-size:18px;
  word-wrap: break-word;
  margin-left: 45px;

  //background-color: #9a9fb0;
}



.close{
  float: right;
  width: 40px;
  margin-right: 40px;
  margin-top: 40px;
}

.serverDiv {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 113px;
  background: var(--theme-main-bg-color);
  position: relative;
  overflow: hidden;
  
  iframe {
    width: 100%;
    height: 80vh
  }

  .btns{
    position: absolute;
    width: 69px;
    left:30px;
    top: 5px;
  }
  // .launch_loading {
  //   width: 100%;
  //   height: 100%;
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   background-size: contain;
  // }
}
</style>


