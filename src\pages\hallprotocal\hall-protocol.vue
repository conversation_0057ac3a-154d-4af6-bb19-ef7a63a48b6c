<script lang="ts" setup>
  import {ProtocolConfig }  from './ProtocolConfig';
  const route = useRoute()
  const id = Number(route.query.id?.toString());
  const text = ref("")
  const title = ref("")
  text.value = ProtocolConfig[id].message
  title.value = ProtocolConfig[id].title
</script>

<template>
  <div class="topDiv">
    <AppIndexHeader />
    <AppPageTitle left-arrow :title="title" background="#324b6e" title-weight="700" />
    <section class="content">
      <AppRule :title="title" :content="text"></AppRule>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.pt-30 {
  padding-top: 30px;
}

.content{
  // margin: 0 auto
  display: flex;
  flex-direction: column;
  align-items: center;
  
}

.topDiv {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 132px;
  background: var(--theme-main-bg-color);
  position: relative;

}


</style>

<route lang="yaml">
  meta:
    auth: true
</route>
