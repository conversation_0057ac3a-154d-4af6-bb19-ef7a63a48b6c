<script setup lang='ts' name='todosOsDados'>
import { ListTeamData, SendQueryTeamDataTodos } from "~/core/http/api"
const question= ref()
const searchId = ref(0)
const showDatePicker = ref(false)
const showViewType = ref(false)
const inputForce = ref(false)
const currentDate = ref({
  start: dayjs().subtract(0, 'day').format('YYYY-MM-DD'),
  end: dayjs().format('YYYY-MM-DD')
})



const dateStr = computed(() => dayjs(currentDate.value.start).format('YYYY-MM-DD') + ' - ' + dayjs(currentDate.value.end).format('YYYY-MM-DD'))

//查找
const onConfirm = (data: any) => {
  showSelectTime(false)
  currentDate.value = data;
  pageDatas.value.pageIndex=0
  refresh();
  pageDatas.value.pageIndex=1
}

const onConfirmID = ()=>{
  showSelectTime(false)
  pageDatas.value.pageIndex=0
  refresh();
  pageDatas.value.pageIndex=1
}


const onCancel=()=>{
  showSelectTime(false)
}

const refresh = () => {
  pageDatas.value.pageIndex=0;
  finished.value = false
  listTeamData.value.length = 0
  getQueryTeam();
}

const showSelectTime=(isFlag:boolean)=>{
  showViewType.value = isFlag
}

//
const eventFocus=()=>{
  inputForce.value = true
}

const eventBlur=()=>{
  inputForce.value = false
}


//
const getQueryTeam = () => {
  let tempStartTime = currentDate.value.start + " 00:00:00"
  let tempEndTime = currentDate.value.end + " 23:59:59"
  // console.log(1715223600 + " --- getBrazilTime  ---" + (getBrazilTime(1715223600 * 1000)) +"dayjs --"+ dayjs(getBrazilTime(1715223600*1000)))
  // let starTime1 = dayjs(tempStartTime).utc(true).unix();
  // let endTime1 = dayjs(tempEndTime).utc(true).unix();
  // console.log("starTime1  " + starTime1 + "starTime1  " + endTime1)

  if(question.value){
    searchId.value = Number(question.value) 
    question.value = searchId.value
  }else{
    searchId.value = 0
  }

  // runGetQueryTeam({ beginTime: starTime1 + 3600 * 3, endTime: endTime1 + 3600 * 3 + 3600 * 24 -1, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount ,uid: searchId.value})
  if(pageDatas.value.pageIndex == 0){
    listTeamData.value=[]
  }
  runGetQueryTeam({ beginTime: tempStartTime, endTime: tempEndTime, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount ,uid: searchId.value})
}

const dadosAccuInfo = ref({
  ChildDeposit: 0,   //直属下级充值金额
  OtherDeposit: 0,   //二三级充值金额
  ChildFirstDepositCount: 0,    //直属下级充值人数
  OtherFirstDepositCount: 0,   //二三级流水充值人数
})

const listTeamData = ref<ListTeamData[]>([]);
// let i=0
// for( i;i<15; i++){
//   const temp:ProxyTeamData={
//     uid:1112222,
//     DonateToParent: 41144,//对父级的贡献
//     first_deposit_mount: 4544444,//首冲金额
//     running: 455444,//总流水
//     created_at: 148851444,//绑定时间 
//     DonateTime: 1111111,//
//     deposit:10
//   }

//   listTeamData.value.push(temp)
// }

const finished = ref(false);
const pageDatas = ref({
  pageIndex:0,
  pageCount:30,
});

const loadMoreData = () => {
  if(pageDatas.value?.pageIndex == 0 || pageDatas.value?.pageIndex * pageDatas.value?.pageCount < queryTeamDatas.value?.TotalCount){
    getQueryTeam();
    pageDatas.value.pageIndex++
  }
  
}

//
const {run: runGetQueryTeam,data:queryTeamDatas, loading:queryTeamLoading } = useRequest((datas:SendQueryTeamDataTodos) => ApiGetQueryTeamTodos(datas), {
  manual: true,
  onSuccess(res:any) {
    dadosAccuInfo.value = res?.TAccuInfo!;
    if(res?.ListTeamData){
      listTeamData.value = [...listTeamData.value,...res?.ListTeamData!]
    } 

    if(listTeamData.value.length >= res?.TotalCount){
      finished.value = true;
    } else {
      finished.value = false;
    }
   
  },
  onError(data:any){
    showErrorTip(data)
  }
  
})


//按钮动画
let isCanMove = true
const boxPrompt = ref()
let currentTopPosition = 0
let orightH = 0 //移动范围
let nodeHeight = 294
let imgHeight = 34
let offect = 5
function showMoreDataNode(){
  if(isCanMove){
    isCanMove = false
    let isUp = currentTopPosition>0?true:false

    const intervalId = setInterval(() => {

      if(boxPrompt && boxPrompt.value && boxPrompt.value.offsetWidth){
        if(isUp){
          //移动上去
          currentTopPosition -=offect
          if(currentTopPosition>=0){
            boxPrompt.value.style = 'transform: translateY('+ currentTopPosition +'px)'
          }else{
            clearInterval(intervalId );//取消定时器
            isCanMove = true
          }

        }else{
          //移动下来
          currentTopPosition +=offect
          if(currentTopPosition<=orightH){
            boxPrompt.value.style = 'transform: translateY('+ currentTopPosition +'px)'
          }else{
            clearInterval(intervalId );//取消定时器
            isCanMove = true
          }
        }
      }else{
        clearInterval(intervalId );//取消定时器
      }
    },16)
  }
}



//初始化
onMounted(() => {
  initMoreDataNode()
})


//
function initMoreDataNode(){
  orightH = boxPrompt.value.offsetHeight - boxPrompt.value.offsetHeight/nodeHeight*imgHeight
  currentTopPosition = orightH
  boxPrompt.value.style = 'transform: translateY('+ orightH +'px)'

}


function copyID(uid:any){
  showToast('Copiado com sucesso')
  copy(uid+"")
}
const agent_waged_times = ref(1)

const {run: runGetWithdrawConf } = useRequest(ApiGetWithdrawConf,{
    onSuccess(data) {
      // console.log(data)
      if(data){
        agent_waged_times.value = data.agent_waged_times || 1;
      }
      
    },
    onError(resData){
      
    }
})
runGetWithdrawConf();

</script>
<template>
  <div class="todpsOsDayos">
    <div class="content">
      <!-- 查下框 -->
      <div class="invite-query">
        <div class="invite-query-input">
            <div class="date" @click="showSelectTime(true)">{{ dateStr }}</div>
            <div class="searchParent" :class="{active:inputForce}">
                <input v-model="question"  type="number" class="search" placeholder="ID de Membro"    @focus="eventFocus"  @blur="eventBlur"/>
                <AppImage class="select-img" src="/icons/invite_search1.webp" alt="" @click="onConfirmID" maxlength="30" />
            </div>
        </div>
      </div>
      <!--时间选择框  -->
      <AppDatePickerNew class="selectTime" v-show="showViewType" v-model="showDatePicker" :startDate="currentDate.start" :endDate="currentDate.end" group @confirm="onConfirm" @cancel="onCancel" />
      <!-- 查询列表 DADOS -->
      <div class="invite-list">
        <AppList class="app-List" :loading="queryTeamLoading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData"
          style="padding-top: 15px;">
          <div class="invite-list-item" v-for="item in listTeamData" :key="item.uid">
            <div class="invite-list-item-body">
              <div class="invite-list-item-body-vip">  
                  <label class="invite-list-item-body-vipText">V{{item.VipLevel}}</label>
              </div>
              <div class="invite-list-item-body-id">  
                <label>{{ item.uid }}</label>
                <AppImage src="/icons/agent_copy" class="invite-list-item-body-id-copy" @click="copyID(item.uid)"/>
              </div>

              <!-- dayjs(getBrazilTime(item.created_at*1000)).format('YYYY-MM-DD HH:mm:ss') -->
              <div class="invite-list-item-body-time">  
                <label>{{ item.created_at}}</label>
              </div>
              <div class="invite-list-item-body-apos">  
                <label >Apostas Válidas</label>  <!--水流-->
                <label class="invite-list-item-body-apos-num">{{ transf(item.running * agent_waged_times) }}</label>
              </div>

              <div class="invite-list-item-body-dep">   
                <label >Depositar</label>  <!--充值-->
                <label class="invite-list-item-body-apos-num">{{ transf(item.deposit)}}</label>
              </div>
            </div>
          </div>
        </AppList>
      </div>

      <!--背景图标  -->
      <!-- <div class="onne_jl" v-show="listTeamData.length==0" >
        <AppImage class="onne_jl_img" src="/icons/agent_none_jl.webp" alt=""  />
      </div> -->

      <app-empty class="onne_jl"  text="Sem feedback" v-show="listTeamData.length==0"></app-empty>
    </div>

     <!--总结 -->
    <div class="moreData" ref="boxPrompt">
       <AppImage  class="img" src="/icons/agent_todos_more.webp" alt=""  @click="showMoreDataNode"/>
       <div class="moreData_div">
          <div class="moreData_divItem">
            <label class="moreData_divItem_text">Recarga direta</label>  <!--直属首充金额-->
            <label class="moreData_divItem_text color">{{transf(dadosAccuInfo.ChildDeposit)}}</label>
          </div>
          <div class="moreData_divItem">
            <label class="moreData_divItem_text">Primeira recarga direta</label>   <!--首次充值人数-->
            <label class="moreData_divItem_text color">{{dadosAccuInfo.ChildFirstDepositCount}}</label>
          </div>
          <div class="moreData_divItem">
            <label class="moreData_divItem_text">Outro depósito</label>  <!--二三级充值金额-->
            <label class="moreData_divItem_text color">{{transf(dadosAccuInfo.OtherDeposit)}}</label>
          </div>
          <div class="moreData_divItem">
            <label class="moreData_divItem_text">Outro recargas iniciais</label>  <!--二三首充人数-->
            <label class="moreData_divItem_text color">{{dadosAccuInfo.OtherFirstDepositCount}}</label>
          </div>
          <div class="moreData_divItem moreData_divItem2">
            <label class="moreData_divItem_text">Depósito total</label>      <!--总充值金额-->
            <label class="moreData_divItem_text color">{{transf(dadosAccuInfo.ChildDeposit + dadosAccuInfo.OtherDeposit)}}</label>
          </div>
          <div class="moreData_divItem moreData_divItem2">
            <label class="moreData_divItem_text">Total de jogadores fzaendo primeiros depósitos</label>  <!--首充总数人数-->
            <label class="moreData_divItem_text color">{{dadosAccuInfo.ChildFirstDepositCount + dadosAccuInfo.OtherFirstDepositCount}}</label>
          </div>
       </div>
    </div>


  </div>





</template>

<style lang='scss' scoped>


.todpsOsDayos{
   width: 100%;
   height:100%;
   font-size:23px;
}

.content {
  position: absolute;
  width: 100% ;
  height:100% ;
  // background-color: #FFF;

}


.invite-query{
  margin-top: 20px;
  // background-color: aliceblue;
  .invite-query-input{
    margin-left: 20px;
    width:356px;
    height:52px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:26px;
    .date{
      display: inline-block;
      color:var(--theme-text-color-lighten);
      width: 100%;
      height: 100%;
      text-align: center;
      padding-top: 12px;
      // padding-left: 20px;
    }
  }
  .searchParent{
    position: absolute;
    width:210px;
    height:52px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:26px;
    
    margin-top: -52px;
    margin-left: 380px;

    &.active{
      border-color:#FFF0BB;
    }

    .search{
      display: inline;
      margin: 0 auto;
      padding-left: 20px;
      width:140px;
      height:52px;
      background-color: transparent;
      color: #FFFFFF;
     
    }

    .search::placeholder {
      color:var(--theme-text-color-lighten);
      font-size:22px;
    }

    .select-img{
      position: absolute;
      width: 27px;
      top:13px;
      right:20px;
      
    }

  }

}

.selectTime{
  position: absolute;
  margin-top: 0px; 
  // height: 100px;
  z-index: 1
}

.onne_jl{
  width: 100%;
  height: 450px;
  position: absolute;
  top: 25%;

  // .onne_jl_img{
    // position: absolute;
    // transform: translate(-50%,-50%);
    // left:50%;
    // top:50%;
    // width: 300px;
  // }
}


//列表
.invite{
  &-list{
    width: 705px;
    margin: 0 auto;
    // margin-top: 20px;
    height: calc(100% - 100px);
    // background-color: #FFF;
    .app-List{
      width: 100%;
      height: 100%;
    }
    &-item{
      width: 100%;
      height: 108px;
      margin-bottom: 10px;
      background-color:var(--theme-main-bg-color);

      border-radius:14px;
      color: var(--theme-text-color-darken);
      font-size: 21px;
      &:nth-child(2n){
        background-color: var(--theme-bg-color);
      }
      &-body{
        width: 100%;
        height: 100%;
        // display: flex;
        // padding-top: 25px;
        &-vip{
          width: 36px;
          height:26px;
          background:#fe5049;
          border-radius:14px 0px 4px 0px;
        }
        &-vipText{  
            display: inline-block;
            font-size: 16px;
            width: 100%;
            height: 100%;
            text-align: center;
            color:#f8fb52;
        }
        // -----------------
        &-id{
          display: inline-block;
          margin-left: 20px;
          margin-top: 3px;
          &-copy{
            position: absolute;
            width: 23px;
            margin-left:25px;
          }
        }
        // time
        &-time{
          position: absolute;
          margin-left: 20px;
          margin-top: 3px;
          color:var(--theme-text-color-lighten);
        }
        //apos
        &-apos{
          position: absolute;
          margin-left: 400px;
          margin-top: -22px;
          color:var(--theme-text-color-lighten);
          &-num{
            padding-left: 10px;
            color:var(--theme-text-color-darken);
          }
        }
        //dep 
        &-dep{
          position: absolute;
          margin-left: 400px;
          margin-top: 5px;
          color:var(--theme-text-color-lighten);
          &-num{
            padding-left: 10px;
            color:#FFF;
          }
        }
      }
    }
  }
  &-list-empty{
    width: 702px;
    height: 702px;
    margin: 0 auto;
    margin-top: 74px;
    position: relative;
    img{
      width: 702px;
    }
    p{
      position: absolute;
      top: 511px;
      left: 275px;
      font-family:Arial;
      color:#9aa0b5;
      font-size:30px;
    }
  }
   
}

.moreData{
  position: fixed;
  width: 100%;
  height: 294px;
  bottom:0px;
  .img{
    display: block;
    margin: auto;
    width: 76px;
    height: 34px;
  }
  .moreData_div{
    width: 100%;
    height: 260px;
    background-color: var(--theme-main-bg-color);
    display: flex;
    flex-wrap:wrap;
    .moreData_divItem{
      width: 50%;
      height: 25%;
      // background-color: #f9a3a3;
      display: flex;
      align-items:center;
      // display: inline-block;
      // padding-left: 30px;
      // justify-content:center
      color:var(--theme-text-color-lighten);
      line-height: 20px;
      font-size: 21px;
      .moreData_divItem_text{
        width: 180px;
        padding-left: 20px
      }
      .color{
        color:#ffffff;
      }
    }

    .moreData_divItem2{
      height: 50%;

    }


  }

}


</style>
<style lang='scss'>
// :root {
  // --van-picker-background: var(--theme-main-bg-color);
  // --van-picker-mask-color: #324b6e;
  // --van-picker-option-text-color: #fff;
  
  // --van-picker-confirm-action-color: #679fea;
  // --van-picker-cancel-action-color: #FFFFFFB2;

  // --van-popup-background: #324b6e;
  // --van-picker-group-background: var(--theme-main-bg-color);
  
  // [class*=van-hairline]:after {
  //   position: absolute;
  //   box-sizing: border-box;
  //   content: " ";
  //   pointer-events: none;
  //   top: -50%;
  //   right: -50%;
  //   bottom: -50%;
  //   left: -50%;
  //   border: 1px solid #FFFFFF40;
  //   border-left: none;
  //   border-right: none;
  //   transform: scale(.5);
  // }
// }
</style>
