<script lang="ts" setup>
import Notificacao from './notificacao.vue';
import Noticia from './noticia.vue';
import Sugestao from './sugestao.vue';
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const showInfo = ref(false)
const showMail = ref(false)

const AmorpgServer = ref([])
const CustomServer = ref([])
//隐藏底下菜单
appStore.setFooterDialogVisble(false)


const enum JumpViewType {
    Suporte = 0,
    Noticia,
    Notificacao,
    PainelRolante,
    BonusDeSugestao,
}

const SECLET_TYPE = readonly({
    Suporte: 0,
    Noticia: 1,
    Notificacao: 2,
    PainelRolante: 3,
    BonusDeSugestao: 4,
})

const marqueeData = ref<any>([]) 

const noticeData = ref()

const mailData = ref()

const secletType = ref(SECLET_TYPE.Suporte);
if(router.currentRoute.value.query.tab !=null ){
    secletType.value= Number(router.currentRoute.value.query.tab)
}


const key = route.query.key;

const tabData = ref([
    {
        label: 'Suporte',
        value: SECLET_TYPE.Suporte
    },
    {
        label: 'Notícia',
        value: SECLET_TYPE.Noticia
    },
    {
        label: 'Notificação',
        value: SECLET_TYPE.Notificacao
    },
    {
        label: 'Painel Rolante',
        value: SECLET_TYPE.PainelRolante
    },
    {
        label: 'Bônus de Sugestão',
        value: SECLET_TYPE.BonusDeSugestao
    }
])


const isHaveMailData = ref(false)
const SuporteType = ref(0);
const tabSuporte = ref([
    {
        label: 'Telegram Suporte',
        value: 0
    },
])


const dataSuporte  = ref([
    {
        iconPath: 'telegram',
        content: 'Telegram atendimento ao cliente',
        name:'trigopg88',
        value: 0
    },
    {
        iconPath: 'telegram',
        content: 'canal de telegram',
        name:'TQWINCOM',
        value: 1
    },

])


const { run: runGetBroadCast } = useRequest(ApiGetBroadCast, {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        marqueeData.value=[...data]
        }else{
        
      } 
    }
  })

  const getAmorpgServerIcon = () => {
      return AmorpgServer.value[0]?AmorpgServer.value[0].avatar:"/icons/personal_head";
  };

  const { run:runGetServiceData, data: custService } = useRequest(ApiGetFBTG,{
    manual: true,
    onSuccess: (data) => {
        for(let i = 0;i<data.length;i++){
            if(data[i].customer_service_type=="1"){
                AmorpgServer.value.push(data[i]);
            }
            else{
                CustomServer.value.push(data[i]);
            }
        }
    }
  })

  runGetServiceData()


const onTabChange = () => {
    showInfo.value = false;
    showMail.value = false;
    if(secletType.value == SECLET_TYPE.PainelRolante){
        runGetBroadCast()
    }else if(secletType.value == SECLET_TYPE.Noticia){
        //注释无用消息
        // runApiGetMailListData()
    }
}

const onTabSuporteChange = () => {
    showToast('SuporteChange')
}


const openUrl = (url?: string) => {
  if (!url) return
  window.open(url, '_blank')
}

const btnAmorpg = () => {
    openUrl(AmorpgServer.value[0].customer_service_link)
}

const btnAgora = (item) =>{
    openUrl(item.customer_service_link)
}

const onClickMarquee = (item) =>{
    showInfo.value = true;
    noticeData.value = item;
}

const onClickMail = (item) =>{
    showMail.value = true;
    mailData.value = item;
}

onMounted(() => {
    if(route.query.key){
        console.log(route.query.key);
        if(route.query.key == JumpViewType.Suporte){
            secletType.value = SECLET_TYPE.Suporte;
        }else if(route.query.key == JumpViewType.Noticia){
            secletType.value = SECLET_TYPE.Noticia;
        }else if(route.query.key == JumpViewType.Notificacao){
            secletType.value = SECLET_TYPE.Notificacao;
        }else if(route.query.key == JumpViewType.PainelRolante){
             secletType.value = SECLET_TYPE.PainelRolante;
        }else if(route.query.key == JumpViewType.BonusDeSugestao){
            secletType.value = SECLET_TYPE.BonusDeSugestao;
        }
    }
})


//邮箱
const { run: runApiGetMailListData, data:emails, loading } = useRequest(() =>ApiGetMailListData(), {
  onError: () => {
  },
  onSuccess: (data) => {
    console.log(data);
    isHaveMailData.value = false;
    if(data.length > 0){
      isHaveMailData.value = true;
      // emails.value = data.concat(data,data);
      // emails.value[0].content = "We reserve the right to request proof of age documents from you at any time. Age verification checks are conducted on all customers using payment mechanisms available for persons under 18 years. Additionally, random age verification checks are performed on customers using other forms of funding mechanisms. In case we suspect or receive any notification that you are currently or were under the age of 18, or the age stipulated in the law of the jurisdiction applicable to you when you placed any bets through the services of Fullwinbet.com, your account will be suspended to prevent you from placing further bets or making withdrawals. We will then investigate the matter, and if we find that you:• a) are currently under 18, or below the age stipulated in the law of the jurisdiction applicable to you,• b) were under 18, or below the age stipulated in the law of the jurisdiction applicable to you at the relevant time,• c) have been acting as an agent for or at the behest of a person under 18, or below the age stipulated in the law of the applicable jurisdiction,then Fullwinbet.com will apply the following mitigation measures:"
    }
  }
})

//返回
function clickLeft(){
    appStore.setFooterDialogVisble(true)

}

</script>   


<template>
    <AppPageTitle left-arrow title="Centro de mensagens" title-weight="700" @click="clickLeft" />
    <div class="tab">
        <AppTab :list-data="tabData" v-model="secletType" @change="onTabChange"></AppTab>
    </div>
    <div class = "mensagens-content">
        <div class="content">
            <div v-if="secletType == SECLET_TYPE.Suporte" class="content-suporte">
                <div class="content-suporte-server">
                    <AppImage class="content-suporte-server-head" :src="getAmorpgServerIcon()"/>
                    <span class="content-suporte-server-Apoio">Apoio online 24/7</span>
                    <div class="content-suporte-server-Info">
                        Converse com o serviço profissional de apoio ao cliente online para resolver os seus problemas
                    </div>
                    <div class="content-suporte-server-Amorpg" @click="btnAmorpg">
                        serviço on-line
                    </div>
                </div>
                <div class="content-suporte-suporte">
                    <div class="content-suporte-suporte-line"></div>
                    <AppImage class="content-suporte-suporte-telegram" src="/icons/telegram.webp"/>
                    <span class="content-suporte-suporte-label">Telegram Suporte</span>
                    <div class="content-suporte-suporte-line2"></div>
                    <div class="content-suporte-suporte-agora">
                        <div class="item" v-for="(data, index) in CustomServer" :key="index">
                            <AppImage class="item-img" :src="data.avatar" width="85"/>
                            <span class="item-content">{{data.title}}</span>
                            <span class="item-name">{{data.customer_service_name}}</span>
                            <div class="item-buttton" @click="btnAgora(data)" >Contactor agora</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else-if="secletType == SECLET_TYPE.PainelRolante" class="content-rolante">
                <div v-if="!showInfo" class="rolante-item" v-for="(data, index) in marqueeData" :key="index" @click="onClickMarquee(data)">
                    <AppImage class="rolante-item-img" :src="`/icons/marquee.webp`" width="35"/>
                    <div class="rolante-item-content">{{ data.content }}</div>
                    <AppImage class="rolante-item-arrow" src='/icons/right.png' />
                </div>
                <div v-else>
                    <div class = "notice-content">
                        {{ noticeData.content }}
                    </div>
                </div>
            </div>
            <div v-else-if="secletType == SECLET_TYPE.Noticia" class="content-mail">
                <Noticia/>
                <!--
                <div v-if="!showMail" class="mail-item" v-for="(data, index) in emails" :key="index" @click="onClickMail(data)">
                    <AppImage class="mail-item-img" :src="`/icons/marquee.webp`" width="35"/>
                    <div class="mail-item-title">{{ data.title }}</div>
                    <div class="mail-item-time">{{ data.time }}</div>
                    <div class="mail-item-lidos">Lidos</div>
                    <AppImage class="mail-item-arrow" src='/icons/right.png' />
                </div>
                <div v-else>
                    <div class = "mail-content">
                        {{ mailData.content }}
                    </div>
                </div>
                -->
            </div>
            <div v-else-if="secletType == SECLET_TYPE.Notificacao" class ="content-notificacao">
                <Notificacao/>
            </div>
            <div v-else-if="secletType == SECLET_TYPE.BonusDeSugestao" class = "content-sugestao">
                <Sugestao/>
            </div>
        </div> 
    </div>

</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';
.mensagens-content{
    position: absolute;
    height: calc(100% - 164px);
    width: 100%;
    overflow: auto;
    margin-top: 160px;
    .content {
        position: relative;
        width: 100%;
        height: 100%;
        background-color: var(--theme-bg-color);
    //-${paddingTop ?? 230}
        
        &-rolante{
            position: relative;
            padding: 15px 10px 95px 10px;
            background-color: var(--theme-bg-color);
            .rolante-item{
                margin: 10px 25px 15px 25px;
                padding-bottom: 5px;
                border-radius: 15px;
                background-color: var(--theme-main-bg-color);
                height: 120px;
                position: relative;
                font-size: 25px;
                &-img{
                    position: relative;
                    padding:auto;
                    top: 45px;
                    left: 30px;
                }

                &-content{
                    position: relative;
                    top: 10px;
                    overflow: hidden;
                    left:80px;
                    width: 540px;
                    white-space: nowrap;
                    color: var(--theme-primary-font-color);
                    
                }

                &-arrow{
                    width: 40px;
                    position: absolute;
                    top: 59px;
                    right: 15px;
                    transform: translateY(-50%);
                }
            }   
            .notice-content{
                width: 100%;
                height: 100%;
                margin: 40px 20px;
                color:var(--theme-primary-font-color) ;
            }
        }
        &-suporte {
            position: relative;
            top: 18px;
            font-size: 28px;
            font-weight: 400;
            &-server {
                height: 200px;
                margin: 0px 25px;
                
                border-radius: 10px;
                background-color: var(--theme-main-bg-color);
                
                &-head{
                    position: absolute;
                    left: 45px;
                    top: 22px;
                    width: 75px;
                }

                &-Apoio{
                    position: relative;
                    left:120px;
                    //padding-top: 30px;
                    top: 22px;
                    color: var(--theme-text-color);
                    font-size: 28px;
                    font-weight: bold;
                }

                &-Info{
                    position: relative;
                    padding-right: 100px;
                    //width: 540px;
                    left:  120px;
                    top: 30px;
                    color: var(--theme-text-color-lighten);
                    font-size: 22px;
                }

                &-Amorpg{
                    position: relative;
                    border:1px solid var(--theme-primary-color);
                    border-radius: 15px;
                    width: 173px;
                    height: 50px;
                    left: 120px;
                    top: 48px;
                    color: var(--theme-primary-color);
                    text-align: center;
                    padding: 8px;
                    font-size: 22px;
                    
                }

            }




            &-suporte{
                height: 300px;
                margin: 20px 25px 0 25px;
                border-radius: 10px;
                background-color: var(--theme-main-bg-color);

                &-line{
                    position: relative;
                    width: 100%;
                    height: 1px;
                    top:75px;
                    background-color: var(--theme-bg-color);
                }

                &-telegram{
                    position: relative;
                    width: 35px;
                    left:30px;
                    top:20px;
                }

                &-label{
                    position: relative;
                    color: var(--theme-primary-color);
                    left: 40px;
                    top: 10px;
                    font-size: 22px;
                }

                &-line2{
                    position: relative;
                    width: 240px;
                    height: 1px;
                    left:30px;
                    top:30px;
                    background-color:var(--theme-primary-color);
                }

                &-agora{
                    position: relative;
                    margin: 50px 20px 0 20px;
                    height: 200px;
                    .item{
                        width: 100%;
                        height: 100px;
                        //margin: 10px 20px 10px 0;
                        border-radius: 10px;
                        position: relative;
                        .item-img{
                            position: absolute;
                            padding: 15px 0 15px 18px;
                        }

                        .item-content{
                            position: absolute;
                            margin:auto;
                            top: 30px;
                            left: 120px;
                            font-size: 17px;
                            color: var(--theme-text-color-lighten);
                        }

                        .item-name{
                            position: absolute;
                            top: 47px;
                            left: 120px;
                            font-size: 17px;
                            color: var(--theme-text-color);
                        }

                        .item-buttton{
                            position: absolute;
                            background-color: var(--theme-primary-color);
                            border-radius: 10px;
                            width: 120px;
                            height: 50px;
                            right: 20px;
                            top: 25px;
                            color: var(--theme-bg-color);
                            text-align: center;
                            padding: 5px;
                            font-size: 18px;
                        }


                        &:nth-child(1) {   
                            background-color: var(--theme-bg-color)
                        }
                    }

                    
                }

            }
        }
        &-mail{
            .mail-item{
                margin: 10px 25px 15px 25px;
                padding-bottom: 5px;
                border-radius: 15px;
                background-color: var(--theme-main-bg-color);
                height: 120px;
                position: relative;
                font-size: 30px;
                &-img{
                    position: relative;
                    padding:auto;
                    top: 45px;
                    left: 30px;
                }

                &-title{
                    position: relative;
                    top: -10px;
                    left: 90px;
                    color: white;
                }

                &-time{
                    position: relative;
                    top: -8px;
                    left: 90px;
                    color: var(--theme-text-color-lighten);
                }

                &-lidos{
                    position: absolute;
                    top: 42px;
                    right: 50px;
                    color: var(--theme-text-color-lighten);
                }

                
                &-arrow{
                    width: 40px;
                    position: absolute;
                    top: 59px;
                    right: 15px;
                    transform: translateY(-50%);
                }
                
            }
            .mail-content{
                width: 100%;
                height: 100%;
                margin: 40px 20px;
                color:white;
            }
        }
    }
}

</style>
<route lang="yaml">
  meta:
    auth: true
</route>