<script setup lang="ts" name="app-index-game-container">
import { GameTypeEnum, GameNavEnum, GameHallTopEnum } from "~/types/common";
interface Props {
  id: GameNavEnum;
  filterType?: string;
  favType?: string;
}
const props = defineProps<Props>();
const scrollView = ref();
const router = useRouter();

//目前
// 首页 获取推荐的 热门 游戏 100
// 首页 获取推荐的 收藏 游戏 101
const { run: runGetSpecialRec, data: specialRecGames } = useRequest(
  () =>
    +props.id === GameNavEnum.Quente
      ? ApiGameHotList({
          ty: 0,
          platform_id: "0",
          page_size: 69,
          page: 1,
          is_fav: props.filterType === "love" ? 1 : undefined,
        })
      : ApiRecFavGames({
          ty: "0",
          platform_id: "0",
          page_size: 69,
          page: 1,
          is_fav: props.filterType === "love" ? 1 : undefined,
        }),
  {
    manual: true,
  }
);

const { run: runGetNormalRec, data: recGames } = useRequest(
  () =>
    ApiGameRecList({
      ty: +props.id,
      platform_id: "0",
      page_size: 69,
      page: 1,
      is_fav: props.filterType === "love" ? 1 : undefined,
    }),
  {
    manual: true,
  }
);

//目前这个界面只是大厅显示 需要获取数据（数据从下面的 list 获取）
// if (+props.id !== GameNavEnum.Quente && +props.id !== GameNavEnum.Dentro_De_Casa) {
//   runGetNormalRec()
// } else {
//   runGetSpecialRec()
// }

const list = ref([
  { img: "/img/pg1.webp", platform_id: GameTypeEnum.platform_pg, key: 0 },
  { img: "/img/pp1.webp", platform_id: GameTypeEnum.platform_pp, key: 1 },
  // { img: '/img/haba.webp',platform_id: GameTypeEnum.platform_haba, key: 3},
  { img: "/img/jdb1.webp", platform_id: GameTypeEnum.platform_jdb, key: 8 },

  { img: "/img/fc1.webp", platform_id: GameTypeEnum.platform_fc, key: 5 },
  // { img: '/img/evo.webp',platform_id: GameTypeEnum.platform_evo, key: 6},
  // { img: '/img/hacksaw.webp',platform_id: GameTypeEnum.platform_hacksaw , key: 7},
  { img: "/img/jili.webp", platform_id: GameTypeEnum.platform_jili, key: 2 },
  // { img: '/img/tada1.webp',platform_id: GameTypeEnum.platform_tada, key: 2},
  { img: "/img/yb1.webp", platform_id: GameTypeEnum.platform_yesbingo, key: 4 },
]);

function gotoLeft() {
  scrollView.value?.scrollTo(
    scrollView.value.scrollLeft - scrollView.value.clientWidth,
    100
  ); //当某个元素满足scrollWidth - scrollLeft == clientWidth 时，说明水平滚动条滚动到底了。
}

function gotoRight() {
  scrollView.value?.scrollTo(
    scrollView.value.scrollLeft + scrollView.value.clientWidth,
    100
  ); //当某个元素满足scrollWidth - scrollLeft == clientWidth 时，说明水平滚动条滚动到底了。
  // animate(scrollView,400)
}
</script>

<template>
  <div id="app-index-game-container-slots" class="app-index-game-container">
    <AppIndexTitle
      :id="id"
      @gotoLeft-by="gotoLeft"
      :isCallback="false"
      @gotoRight-by="gotoRight"
    />
    <div class="app-maps game-container">
      <div ref="scrollView" class="content">
        <AppSlotsItem class="slot-item" :data="item" v-for="item in list" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#app-index-game-container-slots {
  scroll-margin-top: 27.333333vw; /* 设置向上偏移的距离 */
}
.app-maps.game-container {
  margin-left: -12.5px;
  margin-right: -12.5px;
  border-radius: 0;
}
.app-maps {
  border-radius: 0px 30px 30px 30px;
  // background: linear-gradient(180deg, #044B9A 0%, #011A51 100%), #1B2E1B;
  // padding-top: 17px;
  padding: 0 25px;
  padding-bottom: 8px;

  .content {
    // height: 590px;
    display: grid;

    // grid-auto-flow: column;
    // grid-auto-flow: row;
    grid-column-gap: 20px;
    grid-row-gap: 0px;
    scroll-snap-type: x mandatory;
    overflow: auto hidden;
    align-items: start;
    justify-items: start;
    // grid-template-columns: repeat(auto-fill, 200px);
    grid-template-columns: auto auto auto;
    grid-template-rows: auto auto auto;

    &.one-row {
      grid-template-rows: auto;
      justify-content: flex-start;
      // height: 295px;
    }
    .left {
      width: 12px;
      scroll-snap-align: start;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .more {
    width: 160px;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.6);

    .img {
      width: 12px;
      display: inline-block;
      margin-right: 8px;
    }

    .txt {
      color: rgba(255, 255, 255, 0.6);
      font-size: 24px;
    }
  }
}
</style>
