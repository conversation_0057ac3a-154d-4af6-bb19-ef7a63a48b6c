<script lang="ts" setup>
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const { depositBonusConf } = storeToRefs(appStore)
appStore.setFooterDialogVisble(false)

const datalist = computed(() => {
  return depositBonusConf.value?.sort((a, b) => a.min_amount - b.min_amount);
})

function clickLeft() {
  appStore.setFooterDialogVisble(true)
}

function clickBack() {
  // router.push('/advancement')
  router.go(-1)
  appStore.setFooterDialogVisble(true)
}

const rewardAmount = ref(0);
const id = ref("");
const { run :runGetUserDepositDiscount} = useRequest(()=>ApiUserDepositDiscount(), {
  onError: (data) => {
    // console.log(data)
    // showErrorTip(data)
  },
  onSuccess: (data) => {
    if (data) {
      rewardAmount.value = data.discount;
      id.value = data.id;
      console.log("runGetUserDepositDiscount", data)
    }
  }
})

const { run :runGetRechargeActivityMoney} = useRequest( ()=>ApiGetUserDepositDiscount({id: id.value, discount: rewardAmount.value}), {
  manual: true,
  onError: (data) => {
    console.log(data)
    showErrorTip(data)
  },
  onSuccess: (data) => {
    if (data) {
        //领取成功刷新界面
        runGetUserDepositDiscount()
        //刷新金币
        appStore.runGetUserBalance();
        //提示
        // showToast({type: "success",message: "Bônus +R$ "+data});
        // appStore.setIsShowMessage(true,"Bônus +R$ "+data)
        appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
        console.log(data)
    }
  },

})

//按钮点击
const isRunAni = ref(false)
function gotoGetMoney() {
  if (isRunAni.value) return;
  if (rewardAmount.value > 0) {
    runGetRechargeActivityMoney();
  }

  isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  console.log("gotoGetMoney click")
}

</script>

<template>
  <AppPageTitle left-arrow title="Bônus Extra De Depósito Inicial " @clickLeft="clickLeft" />
  <div class="weekly-loss-content">
    <div class="content">
      <div class="topDiv">
        <div class="reward">
          <div class="noData">
            <div v-if="rewardAmount > 0">
              <span>{{ "Disponível para coleção R$" + transf(rewardAmount) }}</span>
            </div>
            <div v-else>
              <span>Não existem prémios a receber neste momento</span>
            </div>
          </div>
        </div>
      </div>
      <div class="belowDiv"> <!--下面显示-->
        <!-- <label class="below_title">CASHBACK E REGRAS</label> -->
        <div class="below_itemTitle">
          <!-- <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp" /> -->
          <label class="below_itemTitle_aposta">Primeiro depósito</label>
          <label class="below_itemTitle_aposta below_itemTitle_cashback">Recompensa</label>
        </div>

        <div v-for="(data, index) in datalist" :key="index" class="below_itemDevPa">
          <label class="itemContent">{{ "≥" + data.min_amount }}</label>
          <!-- <label class="itemContent copy">{{ new Intl.NumberFormat('pt-BR', { style: 'percent' }).format(data.bonus)
            }}</label> -->
          <label class="itemContent copy">{{ transf(data.bonus) + "%" }}</label>
        </div>

      </div>
      <div class="rule">
        <AppRule title="Instruções Do Evento:"
          content="1.Exclusivo para a primeira recarga da conta, com apenas uma chance. Quanto mais você recarregar, maior será a recompensa;
2.Depósito sem limite. Recarregue e receba recompensas instantâneas;
3.O bônus (principal+bônus) dado por esta atividade e a apostas é uma plataforma de jogo ilimitada;
4.Este evento é limitado a operações normais realizadas pelo titular da conta. É proibido alugar, usar plug-ins externos, robôs, apostar em contas diferentes, brushing mútuo, arbitragem, interface, protocolo, exploração de vulnerabilidades, controle de grupo ou outros meios técnicos para participar. Caso contrário, as recompensas serão canceladas ou deduzidas, a conta será congelada ou até mesmo adicionada à lista negra;
Para evitar diferenças na compreensão do texto, a plataforma reserva-se o direito de interpretação final deste evento."></AppRule>
      </div>
      <div class="footer-space" />
      <div class="bottomButton">
        <div class="back" @click="clickBack">
          <span>Retornar</span>
        </div>
        <div class="receive" @click="gotoGetMoney()" :class="{active: rewardAmount > 0}">
          <span>Resgatar Tudo</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.weekly-loss-content {
  position: relative;
  height: calc(100vh - 211px);
  overflow: auto;

  .content {
    position: relative;
    width: 100%;
    // height: 100%;
    background-color: var(--theme-bg-color);
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    flex-direction: column;

  }
}

.topDiv {
  width: 710px;
  display: grid;
  padding-top: 20px;
  // padding-left: 20px;
  grid-template-columns: repeat(2, 1fr);

  .bonus {
    text-align: right;
  }

  .reward {
    .noData {
      background: var(--theme-main-bg-color);
      border-radius: 15px;
      width: 708px;
      height: 70px;
      line-height: 70px;
      text-align: center;

      span {
        color: var(--theme-text-color-lighten);
        font-size: 24px;
      }
    }
  }


}

.belowDiv {
  margin-top: 20px;
  width: 708px;
  position: relative;
  background: var(--theme-main-bg-color);
  border-radius: 15px;
  padding-bottom: 20px;
  
  .below_title {
    display: block;
    margin-top: 25px;
    margin-left: 25px;

    font-family: Arial;
    font-weight: 700;
    color: var(--app-ratio-title-color);
    font-size: 24px;
  }

  .below_itemTitle {
    display: flex;
    margin: 0 auto;
    margin-top: 20px;
    width: 668px;
    height: 80px;
    background: var(--theme-bg-color);
    border-radius: 8px;
    border: 2px solid;
    border-color: var(--theme-color-line);
  }

  .below_itemImg {
    width: 41px;
    height: 41px;
    margin-left: 20px;
    margin-top: 15px;
  }

  .below_itemTitle_aposta {
    // margin-left: 105px;
    margin-top: 28px;

    font-family: Arial;
    font-weight: 700;
    color: var(--theme-text-color-darken);
    font-size: 24px;
    width: 354px;
    text-align: center;
  }

  .below_itemDevPa {
    display: flex;
    // flex-direction: column;
    align-items: center;
    width: 668px;
    height: 70px;
    line-height: 70px;
    margin: 0 auto;
    // margin-top: 20px;
    // margin-bottom: 20px;
    // position: relative;

    &:nth-child(2n + 1) {
      background: var(--theme-bg-color);
      border-radius: 15px;
      width: 668px;
      height: 70px;
      line-height: 70px;
    }


    .itemIcon {
      position: absolute;
      width: 28px;
      height: 32px;
      margin-left: -550px;
      margin-top: 20px;
    }

    .itemIconLev {
      position: absolute;
      margin-left: -550px;
      margin-top: 2px;

      font-family: Arial;
      font-weight: 700;
      font-style: italic;
      color: #f43f5e;
      font-size: 20px;
    }

    .itemContent {
      font-family: Arial;
      color: var(--app-title-color);
      font-size: 20px;
      text-align: center;
      transform: translateY(2px);
      color: var(--theme-text-color-lighten);
      width: 354px;
      text-align: center;
    }

    .copy {
      color: var(--theme-secondary-color-finance);
    }

  }

}

.rule {
  width: 710px;
  background: var(--theme-main-bg-color);
  border-radius: 16px;
  margin-top: 20px;
}

.footer-space {
  height: 20px;
  /* 或者任何你需要的高度 */
}

.bottomButton {
  position: fixed;
  width: 100%;
  height: 116px;
  bottom: 0px;
  background: var(--theme-main-bg-color);
  box-shadow: 0px -1px 3px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  flex-direction: column;
  padding-left: 20px;

  .back {
    width: 344px;
    height: 72px;
    background: var(--theme-main-bg-color);
    border: 1px solid;
    border-color: var(--theme-ant-primary-color-0);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      color: var(--theme-ant-primary-color-0);
      font-size: 26px;
    }
  }

  .receive {
    width: 344px;
    height: 72px;
    background: var(--theme-disabled-bg-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      color: var(--theme-disabled-font-color);
      font-size: 26px;
    }

    &.active {
      background: var(--theme-primary-color);
      background: var(--theme-ant-primary-color-0);

      span {
        color: var(--theme-primary-font-color);
      }
    }
  }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>