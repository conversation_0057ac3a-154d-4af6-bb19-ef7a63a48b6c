<template>
  <div class="scroll-tab-wrapper">
    <div class="scroll-tab-list">
      <div
        class="scroll-tab-item"
        v-for="item in items"
        :key="item.label"
        @click="goPage(item.path)"
      >
        <div class="icon-square">
          <img v-if="item.img" :src="item.img" :alt="item.label" />
          <span v-else>{{ item.emoji }}</span>
        </div>
        <div class="label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
const router = useRouter();
const appStore = useAppStore();
const goPage = (path) => {
  router.push(path);
  appStore.setShowRouterView(true);
  
};
const items = [
  {
    img: "/img/gameTypeIcon/quente.png",
    label: "Quente",
    path: "/subgame",
  },
  {
    img: "/img/gameTypeIcon/pg.png",
    label: "PG",
    path: "/subgame?platform_id=101",
  },
  {
    img: "/img/gameTypeIcon/pp.png",
    label: "PP",
    path: "/subgame?platform_id=201,301",
  },
  {
    img: "/img/gameTypeIcon/jdb.png",
    label: "JDB",
    path: "/subgame?platform_id=501,503",
  },
  {
    img: "/img/gameTypeIcon/fc.png",
    label: "Fc",
    path: "/subgame?platform_id=703",
  },
  {
    img: "/img/gameTypeIcon/jili.png",
    label: "Jili",
    path: "/subgame?platform_id=603",
  },
  {
    img: "/img/gameTypeIcon/yb.png",
    label: "YesBingo",
    path: "/subgame?platform_id=801,803",
  },

  // 可继续添加更多项
];
</script>

<style scoped>
.scroll-tab-wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: transparent;
  padding: 8px 0;
}
.scroll-tab-list {
  display: flex;
  flex-direction: row;
  gap: 25px;
  padding: 0 12px;
}

.scroll-tab-item {
}

.icon-square {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  background-color: #e7e4ff;
  border-radius: 20px;
  img {
    width: 50px;
    height: 50px;
  }
}
.label {
  margin-top: 5px;
  display: flex;
  justify-content: center;
  color: #000000a6;
  text-align: center;
}
</style>
