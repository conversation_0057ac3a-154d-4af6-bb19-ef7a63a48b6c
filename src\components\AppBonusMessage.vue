<script setup lang="ts" name="app-find-pwd">
// const router = useRouter()
const appStore = useAppStore();
const {isShowBonusMessage,showMessage} = storeToRefs(appStore);


//关闭界面
const closePage=()=>{
  appStore.setIsShowBonusMessage(false,"")
}


</script>

<template>
  <van-popup class="app-login-register" v-model:show="isShowBonusMessage" teleport="body" round :overlay-style="{background:'rgba(255,255,255,0)'}" :close-on-click-overlay="false">
    <div class="content">
        <AppImage src="/img/gou.webp" class="icon"/>
        <label class="text">{{ showMessage }}</label>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';


.content {
  width:700px;
  height:160px;
  border-radius:16px;
  background-color:white;
  border:2px solid;
  border-color:#2a815f;
  display: flex;
  align-items: center;
  /* 垂直居中 */
  flex-direction: row;

}

.textbg{
  width:612px;
  height:100px;
  background:var(--app-message-input-color);
  border-radius:15px;
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%-17);
}

.icon{
  position: absolute;
  left: 7%;
  width: 50px;
}

.text{
  position: absolute;
  // transform: translate(-50%,-50%);
  // left:50%;
  // top:(50%);
  text-align: left;
  width: 500px;
  // height: 100px;
  // line-height: 100px;
 
  // font-weight:700;
  color:#000000;
  font-size:30px;
  line-height: 46px;
  left: 18%;
  // margin-left: 60px;
  // margin-top: 25px;
}


.submit{
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%+26);
}

</style>
