<script lang="ts" setup name="AppPayRecordsPopup">
const router = useRouter()
const appStore = useAppStore();
const { isLogin, isShowPayRecords } = storeToRefs(appStore);
const selectIndex = ref(2);
let actions = [{ text: "Hoje", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Ontem", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 7 Dias", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 15 Dias", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 30 Dias", calssName: "5", color: selectIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Tudo", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]


const showPopover = ref(false);
const selText = ref("Hoje")

const close = () => {
    appStore.setPayRecordsVisble(false);
}


const clickImg = (item: any) => {
    if (item.jump_type == 1) { //跳转内部
        router.push(item.redirect_url)
    } else {
        window.open(item.redirect_url, '_blank') // //跳转外部
    }
    appStore.setPayVisble(false)

}

const onClickOverlay = () => {

};

const onSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selText.value = action.text;
   
    selectIndex.value = Number(action.calssName)
    runUserDepositList();
}

const popoverStatus = (isOpen: boolean) => {
    if(!isOpen) {
        actions = [{ text: "Hoje", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Ontem", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 7 Dias", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 15 Dias", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 30 Dias", calssName: "5", color: selectIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Tudo", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]
        // console.log("popoverStatus: " + JSON.stringify(actions))
        // console.log("popoverStatus is " + isOpen + "showPopover is " + showPopover.value);

    }
    
}

const listDeposit = ref<any[]>([])
const finished = ref(false)
//充值记录
const {loading: loading1, run : runUserDepositList } = useRequest(() => UserDepositList({ flag: selectIndex.value + ""}), {
    ready: isLogin,
  onSuccess: (res:any) => {
    listDeposit.value=[]
    if(res){
        listDeposit.value = [...res]
    }
    
    // listDeposit.value=[{id:111111111111111111,created_at:555555555,amount:250000,discount:50,tot_amount:250050},{id:111111111111111111,created_at:555555555,amount:250000,discount:50,tot_amount:250050}]
   
  },
  onError(res){

  }
})



const refresh = () => {
  // formQuery.page = 1
  // finished.value = false
  // list.value.length = 0
  // run()
}
const loadMoreData = () => {
  // formQuery.page++
  // run()
}
onBeforeUpdate(()=>{
    selectIndex.value = 2;
    runUserDepositList();
    console.log("runUserDepositList  " + selectIndex.value);
    selText.value = "Hoje";
    
})
const copyID = (id:string) => {
    copy(id || '')
    showToast('Copied!')
}
const total = computed(() => {
    if(listDeposit.value && listDeposit.value.length > 0){
        let allDeposit = 0;
        for(let item of listDeposit.value){
            allDeposit += item.amount;

        }
        return "R$ " + UsAmountFormat(allDeposit);

    } else {

        return "R$ 0,00"
    }
})
</script>

<template>


    <van-popup class="app-pay-popup" v-model:show="isShowPayRecords" position="bottom" teleport="body"
        :style="{ width: 'var(--app-px-730)' }" :close-on-click-overlay="true" @click-overlay="onClickOverlay">
        <div class="content">
            <div class="content-header">
                <AppImage src="/img/finance/finance-back.png" class="content-header-back" @click="close" />
                <div class="content-header-title">Registro de Depósito</div>
            </div>
            <div class="content-body">
                <div class="content-body-top">
                    <van-popover class="content-body-top-popover" v-model:show="showPopover" :actions="actions"
                        @select="onSelect" @open="popoverStatus(true)" @close="popoverStatus(false)" placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-body-top-popover-select box-border"
                                :class="{ 'viewOpen': showPopover }">
                                <van-text-ellipsis :content="selText" class="top-popover-select-title" />
                                <span class="top-popover-select-icon"
                                    :class="{ 'rotate': showPopover, 'rotate1': !showPopover }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                        
                    </van-popover>
                    <div class="content-body-top-date">
                        Depósito Total <span>{{ total }}</span>
                    </div>
                </div>
                <div class="content-body-middle">
                    <AppList v-if="listDeposit?.length > 0" :loading="loading1" :finished="finished"  @refresh="refresh" @loadMoreData="loadMoreData"  >
                    <div class="list-item"  v-for="(item, index) in  listDeposit" :key="index">
                        <div class="list-item-conetent">
                            <div class="list-item-conetent-top">
                                <div class="list-item-conetent-top-left">
                                    <AppImage src="/img/finance/finance-icon.png" class="list-item-conetent-top-left-img" />
                                    <p class="list-item-conetent-top-left-title">PIX</p>
                                </div>
                                <div class="list-item-conetent-top-right">
                                    <p class="list-item-conetent-top-right-title">{{ "R$ " + UsAmountFormat(item.amount) }}</p>
                                </div>
                            </div>
                            <div class="list-item-conetent-bottom">
                                <div class="list-item-conetent-bottom-left">
                                    <p class="list-item-conetent-top-bottom-title">{{ format_date2(item.created_at*1000) }}<span class="list-item-conetent-top-bottom-title-id">{{ item.id }}</span>
                                        
                                        <AppImage src="/img/finance/copy.webp" class="list-item-conetent-top-bottom-title-img" @click="copyID(item.id)" />
                                        
                                    </p>
                                </div>
                                <div class="list-item-conetent-bottom-right">
                                    <p class="list-item-conetent-bottom-right-title">{{ stateFilter2(item.state) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    </AppList>
                    <app-empty v-else />

                </div>
            </div>
            <div class="globalLine"></div>


        </div>
    </van-popup>
</template>

<style lang="scss" scoped>
dd {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    color: var(--theme-primary-color);
    display: -webkit-box;
    -ms-flex: 1;
    flex: 1;
    font-size: 22px;
    justify-content: center;
    margin: 0;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    vertical-align: middle;
    word-break: break-all
}

.box-border {
    border-style: solid;
    // border-color: #194C38;
    border-color: var(--theme-color-line);
    border-width: 2px;
    border-radius: 10px;
}



.app-pay-popup {
    max-width: 100%;
    height: 100%;
    border-radius: 0px;
    top: 90px;
}

.content {
    background: var(--theme-main-bg-color);
    top: 90px;
    left: 0px;
    position: fixed;
    width: 100%;
    bottom: 0px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;

    
    border-width: 2px;
    
    border: thin solid;
}

.globalLine {
    width: 100%;
    height: 1px;
}

.content-header {
    display: block;
    width: 100%;
    height: 70px;
    position: relative;
    padding: 20px 30px 0px 20px;

    .content-header-back {
        // width: 24px;
        
        height: 24px;
    }

    .content-header-title {
        display: inline;
        font-size: 30px;
        color: white;
        line-height: 50px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

    }

    .content-header-registro {
        display: inline;
        position: absolute;
        color: var(--theme-primary-color);
        font-size: 24px;
        line-height: 36px;
        top: 15px;
        right: 30px;
    }

}

.content-body {
    width: 750px;
    height: calc(100% - 100px);

    .content-body-top {
        width: 100%;
        height: 90px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .content-body-top-popover {
            width: 160px;
            height: 50px;
        }

        .content-body-top-popover-select {
            width: 160px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .top-popover-select-title {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 101px;
            height: 48px;
        }

        @-webkit-keyframes spin {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(180deg);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(180deg);
            }
        }

        @-webkit-keyframes spin1 {
            from {
                -webkit-transform: rotate(180deg);
            }

            to {
                -webkit-transform: rotate(0deg);
            }
        }

        @keyframes spin1 {
            from {
                transform: rotate(180deg);
            }

            to {
                transform: rotate(0deg);
            }
        }

        .rotate {
            animation: spin 0.3s linear 0s 1;
            transform: rotate(180deg);
        }

        .rotate1 {
            transform: rotate(180deg);
            animation: spin1 0.3s linear 0s 1;
            transform: rotate(0deg);
        }

        .top-popover-select-icon {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 15px;

            svg {
                width: 20px;
                height: 20px;
                color: var(--theme-text-color-lighten);
                position: absolute;

            }


        }

        .content-body-top-date {
            font-size: 26px;
            color: var(--theme-text-color-lighten);

            span {
                color: #fff;
            }
        }
    }

    .content-body-middle {
        width: 100%;
        height: calc(100% - 90px);
        padding: 0 20px;

        .list-item {
            width: 700px;
            height: 131px;
            background-color: var(--theme-bg-color);
            border-radius: 10px;
            &:nth-child(2n) {
                background-color: var(--theme-main-bg-color);
            }

            .list-item-conetent {
                width: 700px;
                height: 131px;
                padding: 15px 20px; 
            }

            .list-item-conetent-top {
                width: 660px;
                height: 58px;
                padding: 5px 0;
                display: flex;
                justify-content: space-between;
            }

            .list-item-conetent-top-left {
                height: 48px;
                display: flex;
                align-items: center;

            }

            .list-item-conetent-top-left-img {
                width: 48px;
                height: 48px;
                margin-right: 15px;
            }

            .list-item-conetent-top-left-title {
                font-size: 24px;
                line-height: 36px;
                color: #fff;

            }

            .list-item-conetent-top-right {
                display: block;
                height: 39px;

            }
            .list-item-conetent-top-right-title {
                font-size: 26px;
                line-height: 39px;
                color: #fff;
                font-weight: 700;
            }

            .list-item-conetent-bottom {
                width: 660px;
                height: 43px;
                padding: 5px 0;
                display: flex;
                justify-content: space-between;
            }

            .list-item-conetent-top-bottom-title{
                font-size: 20px;
                color: var(--theme-text-color-lighten);
                line-height: 30px;
            }
            .list-item-conetent-top-bottom-title-id {
                
                margin: 0 10px;
            }
            .list-item-conetent-top-bottom-title-img{
                display: inline;
                height: 20px;
                width: 20px;
            }
            .list-item-conetent-bottom-right {
                display: block;
                height: 39px;
            }

            .list-item-conetent-bottom-right-title {
                height: 33px;
                display: block;
                color: var(--theme-secondary-color-success);
                font-size: 22px;

            }
        }
    }
    
}
</style>
<style lang="scss">
:root:root {
    --van-popover-action-width: 210px;
    --van-popover-action-height: 80px;
    --van-popover-action-font-size: 24px;
    --van-padding-md: 20px;
    --van-popover-action-line-height: 0px;

    /* .van-popover__arrow { */
    /* background: var(--theme-color-line)  !important; */
    /* border-color: var(--theme-color-line)  !important; */
    /* } */
    .content-body-top-popover .van-popover__content {
        background: var(--theme-main-bg-color) !important;
        border-color: var(--theme-color-line);
        border-width: 2px;
        border-radius: 14px;
        border-style: solid;
    }

    .content-body-top-popover .van-popover__action {
        color: var(--theme-text-color-lighten);
        font-size: 24px;
        border-style: none;
        align-items: left;
    }

    .content-body-top-popover .van-popover__action-text {
        justify-content: left
    }

    .content-body-top-popover .van-popover__action:active {
        background-color: var(--theme-text-color-lighten);
    }

    .content-body-top-popover .van-hairline--bottom:after {
        border-bottom-width: 0px;
    }

    .content {
        border-color: var(--theme-color-line);
    }
}
</style>