<script lang="ts" setup name="ResetPwd">
// const countdowm = ref(0);

const oldPwdRef = ref()
const newPwdRef = ref()
const renewPwdRef = ref()

const formdata = reactive({
  old_password: '',
  password: '',
  confirm_password: '',
})

const renewPwdChange = (v: any) => {
  newPwdRef.value?.validation()
}

const { run: runUpdatePwd, loading } = useRequest(() => ApiUpdateLoginPwd(formdata), {
  manual: true,
  onSuccess: () => {
    // localStorage.clear()
    location.replace(location.origin)
  }
})

const confirmFn = () => {
  if (oldPwdRef.value.validation() && newPwdRef.value.validation() && renewPwdRef.value.validation()) {
    runUpdatePwd()
  }
}

</script>

<template>
  <div class="reset-pwd">
    <AppHeader left-arrow title="Alterar senha de entrar" placeholder />
    <section class="content">
      <div class="top"></div>
      <AppInput
        ref="oldPwdRef"
        v-model="formdata.old_password"
        :pattern="passwordReg3"
        msg="Senha (6-12 letras e números)"
        placeholder="Senha (6-12 letras e números)"
        icon-left="icon_password"
        width="650"
        icon-with="32"
        type="password"
        :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"
      />
      <div class="divider"></div>
      <AppInput
        ref="newPwdRef"
        v-model="formdata.password"
        :pattern="passwordReg3"
        msg="Senha (6-12 letras e números)"
        placeholder="Por favor insira uma nova senha"
        icon-left="icon_password_1"
        width="650"
        icon-with="32"
        type="password"
        :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"
      />
      <div class="divider"></div>
      <AppInput
        ref="renewPwdRef"
        v-model="formdata.confirm_password"
        placeholder="Confirme a nova senha"
        icon-left="icon_password_2"
        width="650"
        icon-with="32"
        type="password"
        :rules="[
          { pattern: passwordReg3, msg: 'Senha (8-20 letras e números)' },
          { validator: (val: any) => val === formdata.password, msg: 'A nova senha e a senha de confirmação devem ser iguais' }
        ]"
        :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"
        @change="renewPwdChange"
      />
      <!-- <div class="divider"></div>
      <AppInput icon-with="32" 
        ref="codeRef"
        :pattern="verifyCodeReg"
        placeholder="Código de verificação"
        msg="Erro no código de verificação"
        icon-left="reg-code"
        clearable
        width="710"
        :style-obj="{
          borderRadius: 'var(--app-px-8)',
          background:
            'linear-gradient(0deg, #011A51 0%, #011A51 100%), #2A2E3E',
          border: 'var(--app-px-1) solid rgba(255, 255, 255, 0.10)',
        }"
      >
        <template #right>
          <div :style="{ paddingRight: 'var(--app-px-18)' }">
            <AppButton
              @click=""
              fontSize="26"
              radius="46"
              whiteText
              yellowToRight
              bold
              width="130"
              height="46"
              center
              >{{ countdowm > 0 ? countdowm + "s" : "Mandar" }}
            </AppButton>
          </div>
        </template>
      </AppInput> -->
      <div class="desc">
        * Insira 8 - 20 caracteres alfanuméricos. não diferencia maiúsculas de
        minúsculas. (caracteres chineses não permitidos)
      </div>
      <div class="btn-box">
        <AppButton
          :loading="loading"
          fontSize="36"
          radius="15"
          whiteText
          blue
          width="240"
          height="80"
          center
          @click="confirmFn"
          >To send</AppButton
        >
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.top{
  width: 710px;
	height: 81px;
	background-color: #324b6e;
	border-radius: 50px 50px 0px 0px;
  margin-bottom: 25px;
}
.desc {
  color: #fff;
  font-size: 30px;
  font-weight: 400;
  line-height: 40px;
  padding: 40px 24px;
}
.btn-box {
  padding-top: 20px;
}
.divider {
  height: 40px;
}
.reset-pwd {
  .content {
    margin: 0 auto;
    width: 710px;
    height: 715px;
    background-color: #28374d;
    border-radius: 50px;
    margin-top: 40px;
    h3 {
      color: #fff;
      font-size: 28px;
      font-weight: 400;
      padding: 30px 0 28px;
      margin: 0;
    }
  }
}
</style>
