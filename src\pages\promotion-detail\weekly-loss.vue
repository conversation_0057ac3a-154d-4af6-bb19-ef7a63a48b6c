<script lang="ts" setup>
const route = useRoute()
const router = useRouter()

const records = ref([
  { icon: 'convidar', text: 'Convidar', path: '/promotion-detail/invite', iconWidth: 0 }, // icon 图标从 figma 导出 x3 倍图

])
const appStore = useAppStore()
const { userInfo, loadingBalance, loadingUserinfo } = storeToRefs(appStore)
appStore.setFooterDialogVisble(false)


const copyName = () => {
  copy(userInfo.value.uid || '')
  showToast('Copied!')
}

// onMounted(() => {
//   runGetProSignConf()
// })

// const { run: runGetProSignConf, data: signProData } = useRequest(ApiGetPromotionSignConfig, {
//   manual: true
// })

// {{ timestamp2Date(r.created_at * 1000, 'YYYY-MM-DD') }}<br />
// {{ timestamp2Date(r.created_at * 1000, 'hh:mm:ss') }}


const thisWeekStr = ref(getWeekDate());
const lastWeekStr = ref(getWeekDate(true));

const posts = ref([
  { id: 1, title: "0+BRL", cashback: "12%" },
  { id: 2, title: "5000+BRL", cashback: "14%" },
  { id: 3, title: "20000+BRL", cashback: "16%" },
  { id: 1, title: "80000+BRL", cashback: "18%" },

])

const progressData = ref(0)
const myprogress = computed(() => {
  return progressData.value + "vw"
})

const lostbackData = ref({
  minLostCashbackRatio: 0,
  maxLostCashbackRatio: 0
})


const { run: runGetWeekLostReturn, data: lostReturnItemDatas } = useRequest(() => ApiGetWeekLostReturn(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    if (res && res.length > 0) {
      lostbackData.value.minLostCashbackRatio = res[0].return_rate / 10;
      lostbackData.value.maxLostCashbackRatio = res[res.length - 1].return_rate / 10;
    }

  }
})
runGetWeekLostReturn();

const { run: runGetQueryWeekLostReturn, data: weekLostReturnData } = useRequest(() => ApiGetQueryWeekLostReturn(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    // weekLostReturnData.value.Running = 100;
    // weekLostReturnData.value.WeekLostReturn = 100;
  }
})
runGetQueryWeekLostReturn();

const { run: runAchieveWeekLostReturn, data: achieveProxyWeekDepositData } = useRequest(() => ApiAchieveWeekLostReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true, "Bônus +R$ " + res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")

    runGetQueryWeekLostReturn();
    appStore.runGetUserBalance();
  }
})
// runGetAchieveWeekDeposit();

//按钮点击
const isRunAni = ref(false)
function getMoney() {
  if (isRunAni.value) return;
  if (weekLostReturnData.value?.WeekLostReturn > 0) {
    runAchieveWeekLostReturn();
  } else {
    isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  }
  console.log("getMoney click")
}

function clickLeft() {
  appStore.setFooterDialogVisble(true)
}

function clickBack(){
  // router.push('/advancement')
  router.go(-1)
  appStore.setFooterDialogVisble(true)
}
</script>

<template>
  <AppPageTitle left-arrow title="Bônus de suporte semanal de perdas" 
    @clickLeft="clickLeft" />
  <div class="weekly-loss-content">
    <div class="content">
      <div class="topDiv">
        <!-- <div>
          <span class="first">Perdas da última semana</span>
          <span class="second"> {{ "&nbsp" + (transf(weekLostReturnData?.Running) || "0,00") }} </span>
        </div> -->
        <div class="bonus">
          <span class="first">Subsídios desta semana</span>
          <span class="third"> {{ "&nbsp" + (transf(weekLostReturnData?.WeekLostReturn) || "0,00") }}</span>
        </div>
      </div>
      <div class="belowDiv"> <!--下面显示-->
        <!-- <label class="below_title">CASHBACK E REGRAS</label> -->
        <div class="below_itemTitle">
          <!-- <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp" /> -->
          <label class="below_itemTitle_aposta">Montante da perda</label>
          <label class="below_itemTitle_aposta below_itemTitle_cashback">Prêmios Extra</label>
        </div>

        <div v-for="(data, index) in lostReturnItemDatas" :key="index" class="below_itemDevPa">
          <label class="itemContent">{{ data.Running }}</label>
          <label class="itemContent copy">{{ transf(data.return_rate / 10) + "%" }}</label>
        </div>

      </div>
      <div class="rule">
        <AppRule title="Instruções Do Evento:" content="1.O cashback semanal é dado como recompensa todas as semanas
2.O período sobre o qual é calculado o cashback semanal vai de segunda-feira às 00:00 a domingo às 23:59
3.Horário de solicitação do cashback:De segunda-feira da próxima semana 06:00 a domingo 23:59, expirará se não for resgatado.
4.O número de Perdas de dinheiro real multiplicado pela % dereembolso é o reembolso/cashback da semana.
5.Caso você não tenha apostado durante o período em que o cashback estava ativo ou se seus ganhos da última semana ou ganhos totais saomaiores que suas perdas, você nao receberà cashback.
6. Limite máximo de recompensa de cashback por pessoa por semana:R$ 10000
7.O valor do cashback pode ser sacar diretamente ou utilizado para continuar jogando"></AppRule>
      </div>
      <div class="footer-space"/>
      <div class="bottomButton">
        <div class="back" @click="clickBack">
          <span>Retornar</span>
        </div>
        <div class="receive" @click="getMoney" :class="{active: weekLostReturnData?.WeekLostReturn > 0}">
          <span>Receber</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.weekly-loss-content {
  position: relative;
  height: calc(100vh - 211px);
  overflow: auto;

  .content {
    position: relative;
    width: 100%;
    // height: 100%;
    background-color: var(--theme-bg-color);
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    flex-direction: column;

  }
}

.topDiv {
  width: 710px;
  display: grid;
  padding-top: 20px;
  // padding-left: 20px;
  grid-template-columns: repeat(1, 1fr);

  .first {
    color: var(--theme-text-color-lighten);
    font-size: 24px;
  }

  .second {
    color: var(--theme-text-color-lighten);
    font-size: 24px;

  }

  .third {
    color: var(--theme-secondary-color-finance);
    font-size: 24px;

  }

  .bonus {
    text-align: center;
  }
}

.belowDiv {
  margin-top: 10px;
  width: 708px;
  position: relative;
  // background: var(--theme-main-bg-color);
  border-radius: 15px;

  .below_title {
    display: block;
    margin-top: 25px;
    margin-left: 25px;

    font-family: Arial;
    font-weight: 700;
    color: var(--app-ratio-title-color);
    font-size: 24px;
  }

  .below_itemTitle {
    display: flex;
    margin: 0 auto;
    margin-top: 20px;
    width: 708px;
    height: 80px;
    background: var(--theme-main-bg-color);
    border-radius: 8px;
    border: 2px solid;
    border-color: var(--theme-color-line);
  }

  .below_itemImg {
    width: 41px;
    height: 41px;
    margin-left: 20px;
    margin-top: 15px;
  }

  .below_itemTitle_aposta {
    // margin-left: 105px;
    margin-top: 28px;

    font-family: Arial;
    color: var(--theme-text-color);
    font-weight: 700;
    font-size: 24px;
    width: 354px;
    text-align: center;
  }

  .below_itemDevPa {
    display: flex;
    // flex-direction: column;
    align-items: center;
    width: 708px;
    height: 70px;
    line-height: 70px;
    margin: 0 auto;
    // margin-top: 20px;
    // margin-bottom: 20px;
    // position: relative;

    &:nth-child(2n + 1) {
      background: var(--theme-main-bg-color);
      border-radius: 15px;
      width: 708px;
      height: 70px;
      line-height: 70px;
    }


    .itemContent {
      font-family: Arial;
      color: var(--app-title-color);
      font-size: 20px;
      text-align: center;
      transform: translateY(2px);
      color: var(--theme-text-color-lighten);
      width: 354px;
      text-align: center;
    }
    .copy {
      color: var(--theme-secondary-color-finance);
    }

  }

}

.rule{
  width:710px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  margin-top: 20px;
}

.footer-space {
  height: 20px;
  /* 或者任何你需要的高度 */
}

.bottomButton{
  position: fixed;
  width: 100%;
  height: 116px;
  bottom:0px;
  background:var(--theme-main-bg-color);
  box-shadow:0px -1px 3px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  flex-direction: column;
  padding-left: 20px;
  .back{
    width: 344px;
    height: 72px;
    background: var(--theme-main-bg-color);
    border: 1px solid;
    border-color: var(--theme-primary-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: var(--theme-primary-color);
      font-size: 26px;
    }
  }
  .receive{
    width: 344px;
    height: 72px;
    background:var(--theme-primary-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: var(--theme-font-on-background-color);
      font-size: 26px;
    }
    &.active{
      background:var(--theme-primary-color);
      span{
        color: var(--theme-primary-font-color);
      }
    }
  }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>