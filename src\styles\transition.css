/* 淡入淡出 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 滑动 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.6s ease-out;
  position: absolute;
  width: 100%;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* 缩放 */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* 渐变 */
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition: transform 0.3s, opacity 0.3s ease;
}

.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.97);
}

.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.03);
}

/* 上下滑动 */
.slide-y-enter-active,
.slide-y-leave-active {
  transition: all 0.3s ease;
}

.slide-y-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-y-leave-to {
  opacity: 0;
  transform: translateY(-30px);
} 