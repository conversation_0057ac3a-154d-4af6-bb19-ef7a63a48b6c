

<script setup lang="ts" name="AppGameItem">
import { GameNavEnum, GameSoltTypeEnum } from "~/types/common"


interface Props {
  data: any
}
const router = useRouter()
const route = useRoute()
const props = withDefaults(defineProps<Props>(), {})

const gameListDialogVisible = ref(false)
const gameList = ref<any[]>([])
const platformId = ref()
const activeIndex = ref(0)
const isFinish = ref(true) //是否已经加载完成
const curPage = ref(1);  //当前页面
const tabList = ref<any[]>([
  { src: '/img/pg1.png',platform_id: GameSoltTypeEnum.Slot_pg },
  { src: '/img/pp1.png',platform_id: GameSoltTypeEnum.Slot_pp },
  { src: '/img/TADA1.png',platform_id: GameSoltTypeEnum.Slot_tada },
  { src: '/img/Yesbingo_1.png',platform_id: GameSoltTypeEnum.Slot_yesbingo },
  { src: '/img/habanero_1.png',platform_id: GameSoltTypeEnum.Slot_haba },
  { src: '/img/JDB1.png',platform_id: GameSoltTypeEnum.Slot_jdb },
  { src: '/img/FC1.png',platform_id: GameSoltTypeEnum.Slot_fc },
])

const navlist = ref();

const closePage = () => {
  gameList.value = []
  gameListDialogVisible.value = false
}
// 获取游戏列表
const { run: getGameList } = useRequest(() => ApiGameList({ page: curPage.value,
  page_size: 48, game_type: GameNavEnum.Slot, platform_id: platformId.value, tag_id: '0',is_new:0 }), {
  manual: true,
  onSuccess: (data) => {
    if (data.d && data.d.length) {
      gameList.value = gameList.value.concat(data.d)
      isFinish.value = gameList.value.length>=data.t
    }
  },
  onError: () => {
    gameList.value = []
  }
})

// 打开页签-游戏列表
const openPage = () => {
  // console.log(props.data.key, 'props.data....')
  // activeIndex.value = props.data.key
  // platformId.value = props.data.platform_id
  // gameList.value = []
  // curPage.value = 1
  // isFinish.value = true
  // getGameList()
  // gameListDialogVisible.value = true
  router.push('/subgame?platform_id='+ props.data.platform_id)
  // router.push("/game-list/" + props.data.platform_id);
}

// 点击tab切换列表
const clickTab = (item: any, index: number) => {
  activeIndex.value = index
  gameList.value = []
  platformId.value = item.platform_id
  curPage.value = 1
  isFinish.value = true
  getGameList()
  
}

//下一页
function goPage(){
  curPage.value +=1
  getGameList()
}


onUpdated(() =>{
  if(props.data.key > 2){
    navlist.value?.scrollTo(800,0);
  }

})
</script>

<template>
  <div class="app-game-item">
    <div class="item_content"> <!--6个老虎机图标 -->
      <div class="game-img" v-lazy:background-image="data.img" @click="openPage"></div>
    </div>
    <!-- 游戏列表弹窗 -->
    <van-popup class="app-login-register" teleport="body" v-model:show="gameListDialogVisible" >
      <div class="content-box">
        <div class="top">
          <AppImage class="icon" src="/icons/nav_600-active.png.webp" alt="" />
          <span>Slots</span>
          <AppImage class="close"  src="/icons/close_black.png.webp" @click="closePage" />
        </div>
        <div class="bottom">
          <div ref="navlist" class="nav-list">
            <div class="nav-list-item" :class="{'nav-list-item-active': activeIndex === index}" v-for="(item, index) in tabList" :key="index" @click="clickTab(item, index)">
              <AppImage class="nav-list-item-img" :src="item.src" />
            </div>
          </div>
          <div class="app-maps game-container">
            <div class="content" > <!--:class="{'one-row': gameList.length <= 7}"-->
              <!-- <div class="left"></div>
              <div class="left"></div> -->
              <AppGameItem class="game-item" v-for="(item, idx) in gameList" :key="item.id + idx" :data="item" />
            </div>
            
            <div @click="goPage" class="more" v-if="!isFinish">
              <label class="more-text">Mostrar mais</label>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<style>
:root {
  --app-slots-item-height: 280px;
  --app-slots-item-width: 210px;
}
</style>
<style lang="scss" scoped>
.app-game-item {
  width: var(--app-slots-item-width);
  height: var(--app-slots-item-height);
  scroll-snap-align: start;
  // background: rgba(32, 53, 91, 0.5);
  // background: rgba(14, 48, 117, 0.5);
  // background: #122039;
  border-radius: 10px;
  position: relative;
  margin: 15px 0;

  .game-img {
    width: var(--app-slots-item-width);
    height: var(--app-slots-item-height);
    position: absolute;
    top: 0;
    left: 0px;
    transition: opacity .5s;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    opacity: 0;
    object-fit: cover;
    border-radius: 10px;
    overflow: hidden;
  }

  .game-img[lazy=loading] {
    opacity: 0;
  }

  .game-img[lazy=error] {}

  .game-img[lazy=loaded] {
    opacity: 1;
  }

  img.game-img[lazy=loading] {
    opacity: 0;
  }

  img.game-img[lazy=error] {}

  img.game-img[lazy=loaded] {
    opacity: 1;
  }

  .item_content {
    width: var(--app-slots-item-width);
    height: var(--app-slots-item-height);
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    .item_content_focus {
      width: 35px;
      height: 35px;
      // background: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 0 10px 0 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .focus_img {
        width: 18px;
      }
    }
  }

}

.van-popup--center{
  max-width: 100%;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.content-box{
  width: 100%;
  height: 100%;
  margin: 0;
  background: var(--theme-main-bg-color);;
  .top {
    background-color:var(--theme-top-nav-bg) ;
    color: var(--app-title-color);
    text-align: center;
    width: 100%;
    height: 140px;
    line-height: 140px;
    font-size: 45px;
    font-weight: 700;
    position: relative;
    span {
      margin-left: 25px;
    }
    .icon{
      width: 50px;
    }
    .close{
      position: absolute;
      right: 30px;
      top: 30px;
      width: 80px;
    }
  }
  .bottom{
    width: 100%;
    margin: 0;
    background: var(--theme-main-bg-color);
    padding: 0 20px;
  }
  .nav-list{
    width: 100%;
    height: 113px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    margin-bottom: 10px;
    padding-top: 20px;
    //
    // -ms-overflow-style: none;
    // scrollbar-width: none;
    
    &-item{
      width: 240px;
      height: 84px;
      border-radius: 10px;
      background: #0f3882;
      padding-top: 15px;
      margin-bottom: 15px;
      position: relative;
      margin-right: 10px;
      // float: left;
      display: inline-block;
      &-active{
        background: var(--app-red-color);
      }
      &-img{
        display: block;
        margin: 0 auto;
        width: 210px;
      }
      &:nth-child(2){
        img{
          width: 190px;
          margin-top: -25px;
        }
      }
      &:nth-child(3){
        img{
          width: 180px;
        }
      }
      &:nth-child(4){
        padding-top: 10px;
        img{
          width: 160px;
        }
      }
      &:nth-child(5){
        img{
          width: 190px;
        }
      }
      &:nth-child(6){
        img{
          width: 100px;
        }
      }
    }
  }
  .app-maps.game-container {
    // margin-left: 5px;
    // margin-right: 5px;
    border-radius: 0;
    height: calc(100% - 273px);
    padding-bottom: 40px;
    // overflow: auto;
  }
  .app-maps {
    border-radius: 0px 30px 30px 30px;
    // background: linear-gradient(180deg, #044B9A 0%, #011A51 100%), #1B2E1B;
    // padding-top: 17px;
    // padding: 0 18px;
    padding-bottom: 8px;

    .content {
      // height: 590px;
      display: grid;
      grid-template-columns:auto auto auto;
      grid-template-rows: auto auto;
      // grid-auto-flow: row;
      grid-column-gap: 21px;
      grid-row-gap: 17px;
      scroll-snap-type: x mandatory;
      // overflow: auto hidden;
      align-items: start;
      justify-items: start;
      // grid-template-columns: repeat(auto-fill, 200px);
      &.one-row {
        grid-template-rows: auto;
        justify-content: flex-start;
        // height: 295px;
      }
      .left {
        width: 12px;
        scroll-snap-align: start;
      }

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .more {
      margin: 0 auto;
      margin-top: 50px;
      width: 190px;
      height: 60px;
      // display: flex;
      // align-items: center;
      text-align: center;
      // justify-content: center;
      background-color: #0E3D8F;
      border-radius: 10px;
      &-text{
        color: white;
        line-height: 60px;
        font-size: 24px;
      }
    }

    .img2{
      // transform: rotate(180deg);
      width: 16px;
      height: 27px;
      margin-top: 5px;
      right: 20px;
    }
  }
}
</style>
