<script setup lang='ts' name='details'>
import { beforeEach } from 'node:test';
const appStore = useAppStore()
const router = useRouter()
const dataList = ref<any>([])
const activeIndex = ref<any>([])  //ref(-1) //已经领取到第几位

const curHaveGetMoney = ref(-1)//当前可领取第几位
const sendGetMoneyIndex = ref(-1) //发送领取的位置
const depositAmount = ref(0)

const { run :runGetRechargeActivity} = useRequest(RechargeActivity, {
  onError: () => {
  },
  onSuccess: (data) => {
    if (data) {
      activeIndex.value=[]

      dataList.value=[...data.item]
      depositAmount.value = data.deposit_amount
      for(let i=0;i<data.item.length;i++){
        if(data.deposit_amount>=data.item[i].accu_deposit_amount){
          curHaveGetMoney.value = i
          if(data.state & (1 << i)){
            activeIndex.value.push(1) //已经领取
          }else{
            activeIndex.value.push(2) //未领取
          }
        }else{
          activeIndex.value.push(0) //不可领取
        }
      }
    }
  }
})

const { run :runGetRechargeActivityMoney} = useRequest( ()=>getRechargeActivity({state:sendGetMoneyIndex.value}), {
  manual: true,
  onError: (data) => {
    console.log(data)
    showErrorTip(data)
  },
  onSuccess: (data) => {
    if (data) {
        //领取成功刷新界面
        runGetRechargeActivity()
        //刷新金币
        appStore.runGetUserBalance();
        //提示
        // showToast({type: "success",message: "Bônus +R$ "+data});
        // appStore.setIsShowMessage(true,"Bônus +R$ "+data)
        appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    }
  },

})

function gotoGetMoney(index:number){
  console.log("getgmoney===="+index)
  if(index>curHaveGetMoney.value){
    //跳转 充值
    console.log("跳转 充值")
    if(router.options.history.state.back =="/finance"){
      router.go(-1)
    }else{
      router.push('/finance')
    }
  
  }else if( index<=curHaveGetMoney.value   &&   activeIndex.value[index]==2  ){
    console.log("领取")
    sendGetMoneyIndex.value = index+1
    runGetRechargeActivityMoney()
  }else{
    console.log("不管")
  }

}

</script>
<template>
  <div class="details">
    <AppIndexHeader />
    <div class="details-box">
      <div class="details-box-border"  v-for="(item, index) in dataList" :key="index">

        <div class="details-box-item" :class="{'details-box-item-end': activeIndex[index]==1 } " v-if="item.bonus>0">
          
          <!-- <div class="details-box-item-left">
            <div class="details-box-item-left-img">
              <AppImage src="/icons/invite_dollars.webp" />
            </div>
            <div class="details-box-item-left-num">R${{ item.bonus }}</div>
          </div> -->

          <div class="details-box-item-right">
            <div class="details-box-item-right-top">O depósito totalchega a {{item.accu_deposit_amount}}, ganhe {{item.bonus}} bónus</div>
            <div class="details-box-item-right-bottom">
              <div class="details-box-item-right-bottom-progress">
                <AppProgress :value=depositAmount/item.accu_deposit_amount*100 :max="100" :width="441" :height="13" :type="1" />
                 <!--AppProgress :value=50 :max="100" :width="441" :height="13" bgcolor :type="1" /--> 
              </div>
              <div class="details-box-item-right-bottom-num">
                {{depositAmount}}/{{item.accu_deposit_amount}}
              </div>
            </div>
            <div class="details-box-item-right-btn"   :class="{'details-box-item-right-btn-noMoney': activeIndex[index]==0}"  @click="gotoGetMoney(index)">
              <AppImage class="details-box-item-right-btn-img" src="/icons/money_bag1.webp"></AppImage>
              <label class="details-box-item-right-btn-label" >R$  {{item.bonus}} </label>
              <label class="details-box-item-right-btn-label2" >depósito</label>
            </div>
          </div>

          <div class="details-box-item-check" v-if="activeIndex[index]==1">
            <AppImage src="/icons/invite_check.webp" />
          </div>
        </div>
      </div>
    </div>

    <AppRule title="BONUS DE GRADIENTE DE CONVITE" 
          content="1.A medida que o numero de membros que completamseu primeiro deposito aumenta e atinge o gradientenumérico corre spondente,voce pode receber bonusadicionais">
    </AppRule>
  </div>
</template>

<style lang='scss' scoped>
@import '../../theme/mixin.scss';
.details {
  padding-bottom: 120px;
  &-box{
    width: 710px;
    margin: 0 auto;
    margin-top: 137px;
    &-border{
      position: relative;
      margin-bottom: 20px;
    }
    &-item{
      width: 100%;
      height: 109px;
      background:var(--theme-top-nav-bg);
      border-radius:10px;
      position: relative;
      &:last-child{
        margin-bottom: 0;
      }
      &-end{
        opacity: 60%;
        z-index: 1;
      }
      &-check{
        position: absolute;
        top: 28px;
        left: 328px;
        z-index: 100;
        img{
          width: 54px;
        }
      }
      &-left{
        &-img{
          position: absolute;
          left: 32px;
          top: 9px;
          img {
            width: 74px;
          }
        }
        &-num{
          position: absolute;
          left: 33px;
          top: 77px;
          font-family:Arial;
          font-weight:700;
          color:#ff2147;
          font-size:24px;
        }
      }
      &-right{
        &-top{
          position: absolute;
          top: 75px;
          left: 30px;
          font-family:Arial;
          color:#ffffff;
          font-size:20px;
        }
        &-bottom{
          &-progress{
            position: absolute;
            top: 50px;
            left: 30px;
          }
          &-num{
            position: absolute;
            top: 15px;
            left:150px;
            font-family:Arial;
            color:#ffffff;
            font-size:23px;
            width: 200px;
            text-align: center
          }
        }
        &-btn{
          position: absolute;
          top: 18px;
          left: 510px;
          background-color:var(--app-shop-title-color);
          border-radius: 15px;
          width:180px;
          height: 75px;
          &-noMoney{
            opacity: 60%;
          }

          &-img{
            width: 28px;
            position: absolute;
            top: 10px;
            left: 20px;
          }
          &-label{
            font-size: 26px;
            position: absolute;
            color: #fff;
            top: 13px;
            left: 50px;
            width: 120px;
            text-align: center;
          }
          &-label2{
            font-size: 22px;
            position: absolute;
            color: #fff;
            top: 43px;
            width: 180px;
            text-align: center;
          }

        }
      }
    }
  }
  &-text{
    width: 655px;
    margin: 0 auto;
    margin-top: 67px;
    &-title{
      font-family:Arial;
      font-weight:700;
      color:#474b51;
      font-size:30px;
    }
    &-body{
      font-family:Arial;
      color:#474b51;
      font-size:24px;
      margin-top: 17px;
      margin-top: 17px;
    }
  }
}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
