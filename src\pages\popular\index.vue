<script setup lang='ts' name='promotion'>
const router = useRouter()
const appStore = useAppStore();
const {isSmallGameIcon} = storeToRefs(appStore);
//重连数据
const isGetDataFinish  =ref(false)
// 分页的列表数据
const gameList = ref<any[]>([])
const pageItemNum = ref(20)
const curPage = ref(1)
const maxPage = ref(1)
const total = ref(0)
const finished = ref(false)
const isShowVertical  = ref(true)
const question = ref()
const selectItem = ref(1)
const left_arrow = ref(false)
const right_arrow = ref(false)
// 首页 获取推荐的 热门 游戏 100
// 首页 获取推荐的 收藏 游戏 101
const { run: runGetSpecialRec, loading:listLoading } = useRequest(() => ApiGameHotList({ ty: 0, platform_id: '0', page_size: pageItemNum.value, page: curPage.value, is_fav:undefined }), {
  manual: true,
  onSuccess: (data) => {
    isGetDataFinish.value =true
    onSuccessPage(data)
  },
})


const { run: runGetNormalRec } = useRequest(() => ApiGameRecList({ ty: +3, platform_id:'0', page_size: pageItemNum.value, page: curPage.value, is_fav:  undefined }), {
  manual: true,
  onSuccess: (data) => {
    isGetDataFinish.value =true
    onSuccessPage(data)
  },
  onAfter:()=>{
   
  }
})



//
runGetSpecialRec()
// runGetNormalRec()

const onSuccessPage = (data: any) => {
  if (data) {
    if (curPage.value === 1) {
      total.value = data.t
      maxPage.value = Math.ceil(data.t/pageItemNum.value) 
      gameList.value = []
    }
    if (data.d && data.d.length) {
      gameList.value = gameList.value.concat(data.d)
    }

    if(curPage.value==1){
      left_arrow.value =false
    }else{
      left_arrow.value =true
    }

    if(curPage.value>=maxPage.value){
      right_arrow.value =false
    }else{
      right_arrow.value =true
    }


    // if ((curPage.value === 1 && gameList.value.length < pageItemNum.value) || (gameList.value.length && total.value > 0 && gameList.value.length >= total.value)) {
    //   finished.value = true;
    // } else {
    //   finished.value = false
    // }

  }
}

appStore.setFooterDialogVisble(false)

//下一页
const goPage = (type:number) => {
    if((curPage.value + type)<=maxPage.value && (curPage.value + type)>=1  ){
      curPage.value = curPage.value + type
      // runGetNormalRec()
      runGetSpecialRec()
    }
}
   

//查找
const search=()=>{
    console.log("search")
}

//时间查找
const searchTime=(type:number)=>{
    console.log("searchTime")
    selectItem.value = type
}

//返回按钮
const callback=()=>{
    console.log("callback")
    appStore.setFooterDialogVisble(true)
    router.go(-1)
}

//
const gameItemInnerStyle: any = {
  margin:isSmallGameIcon.value ? `var(--app-npx-22) var(--app-npx-20)`:`var(--app-npx-15) 0`,
};
</script>
<template>
  <div class="app-index-game-container" >
    <AppPageTitle  :backFunc = 'callback' left-arrow title="Popular" title-weight="700" />
    <div class="content">
        <div class="procurar">
            <div class="searchParent">
                <input v-model="question"  class="search" placeholder="Procurar jogos" />
                <AppImage class="select-img" src="/img/index/header_select" alt="" @click="search" maxlength="30" />
            </div>
            <div class="button_div">
                <div class="button_item" :class="{active: selectItem ==1}" @click="searchTime(1)"> Tudo </div>
                <div class="button_item" :class="{active: selectItem ==2}" @click="searchTime(2)"> Recente </div>
                <div class="button_item" :class="{active: selectItem ==3}" @click="searchTime(3)"> Favoritos </div>
            </div>
        </div>

        <div class="app-maps game-container">
            <div ref="scrollView" class="content2"  :class="{'versionContent':isShowVertical ,'is-empty-loading': !gameList?.length}" :style="gameItemInnerStyle">
        
                <template  v-if="gameList && gameList.length > 0">
                    <AppGameItem v-if="isSmallGameIcon" class="game-item" v-for="(item, idx) in gameList" :key="item.id + idx" :data="{...item}" />
                    <AppGameItemBig v-else class="game-item" v-for="(item, idx) in gameList"  :key="item.id + idx" :data="{...item}" />
                </template>
                <div v-else>
                    <div v-if="listLoading" class="last-loading">
                        <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
                    </div>
                </div>
            </div>
        </div>

        <!--  -->
        <div class="more"  v-if="!finished">
            <AppImage class="popular_arr" :src="`/icons/popular_left${left_arrow ? '_select':''}`" alt="" @click="goPage(-1)" maxlength="30" />
            <label class="popular_text"> {{curPage}}/{{ maxPage }}</label>
            <AppImage class="popular_arr" :src="`/icons/popular_right${right_arrow ? '_select':''}`" alt="" @click="goPage(1)" maxlength="30" />
        </div> 
        
    </div>

  </div>
</template>

<style lang='scss' scoped>
.app-index-game-container {
  height:100vh;

}



.content {
    position: absolute;
    width: 100% ;
    min-height:calc(100% - 90px);
    // height: calc(100% - 100px);
    background-color: var(--theme-bg-color);
    overflow-x: hidden;
}

.procurar{
    width: 100%;
    padding-bottom: 45px;
    .searchParent{
        margin: 0 auto;
        margin-top: 25px;    
        width:700px;
        height:52px;
        border:1px solid;
        background: var(--theme-bg-color);
        border-color:var(--theme-color-line);
        border-radius:26px;
    }
    .search{
        display: inline;
        margin: 0 auto;
        padding-left: 20px;
        width:600px;
        height:52px;
        background-color: transparent;
        color: #FFFFFF;
        font-size:24px;
    }

    .select-img{
        position: absolute;
        width: 27px;
        top:40px;
        right:60px;
    }
    .search::placeholder {
        color: var(--theme-text-color-lighten); /* 灰色 */
    }

}


.button_div{
    // background-color: #FFFFFF;
    margin: auto;
    width: 500px;
    margin-top: 25px;
    .button_item{
        margin-left: 20px;
        display: inline-block;
        width:140px;
        height:56px;
        background:var(--theme-main-bg-color);
        border:2px solid;
        border-color:var(--theme-color-line);
        border-radius:12px;
        color:var(--theme-text-color-darken);
        font-size:24px;
        text-align: center;
        line-height: 56px;
        &.active {
        background:var(--theme-primary-color);
        color:var(--theme-primary-font-color);
        // border-radius:6px;
      }
    }


}



.app-maps.game-container {
  margin-left: 20px;
  margin-right: 10px;
 
  
  // margin-: 10px;
//   border-radius: 0;
//   padding-left: 20px;
//   padding-right: 20px;
}

.content2 {
    max-height: calc(100vh - 380px);
   
    display: flex;
    flex-wrap: wrap;
    // grid-column-gap: 5px;
    // grid-row-gap: 5px;
    // scroll-snap-type: x;
    overflow: auto ;

    // min-height: 305px;
    // padding-top: 10px;
    // padding-left: 10px;
  
    &::-webkit-scrollbar {
      display: none;
    }

    &.one-row {
      // grid-template-rows: auto;
      // justify-content: flex-start;
      // height: 295px;
    }
    
    // .left {
    //   width: 12px;
    //   scroll-snap-align: start;
    // }
    &.is-empty-loading {
      align-items: center;
      justify-items: center;
      justify-content: center;
    }

    &.overflow-hidden {
      overflow: hidden;
    }
    &.flow-row {
      // grid-auto-flow: row;
    }
}

.more{
    // position: absolute;
    position: fixed;
    // background-color: #cdeddf;
    width: 250px;
    height: 48px;
    display: flex;
    transform: translate(-50%,-50%);
    left:50%;
    top:97%;
  
    .popular_arr{
        width: 48px;
    }

    .popular_text{
        display: inline-block;
        width: 150px;
        // height: 40px;
        line-height: 48px;
        text-align: center;
        color: var(--theme-text-color-darken);
        // padding-top: 10px;
        // background-color: #2a815f;
       
    }
}

.last-loading {
    grid-row-start: 1;
    grid-row-end: 3;
    width: 750px;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-item{
  // width: 230px;
  // height: 170px
    // padding-left: 15px;
    // padding-right: 15px;
    // padding-bottom: 20px;
    // background-color: #FFFFFF;
}
  //热门竖版显示
.versionContent{
    // grid-template-columns:auto auto auto auto;
    // grid-template-rows: auto auto;
  // grid-template-columns: repeat(4, 1fr); /* 创建三个等宽的列 */
  // grid-template-rows: repeat(3, 230px); /* 创建两行固定高度为100px的网格 */
  }

</style>
