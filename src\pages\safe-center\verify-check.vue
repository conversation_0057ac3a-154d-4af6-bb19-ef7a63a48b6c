<script lang="ts" setup name="VerifyCheck">
const countdowm = ref(0);
const startCountdown = () => {
  countdowm.value = 60
  const timer = setInterval(() => {
    countdowm.value--
    sessionStorage.setItem('countdowm', countdowm.value.toString())
    if (countdowm.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const router = useRouter()

const { phoneReg } = useRegExpPhone();

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)

const emailRef = ref()
const phoneRef = ref()
const codeRef = ref()

const hasPhone = computed(() => userInfo.value && userInfo.value.phone_verify && +userInfo.value.phone_verify === 1)

const formdata = reactive({
  phone: userInfo.value.phone || '',
  email: userInfo.value.email || '',
  code: '',
})

const sidData = reactive({
  sid: '',
  ts: ''
})

const dealSid = (data: string) => {
  const [sid, ts] = data.split(':')
  sidData.sid = sid
  sidData.ts = ts
  startCountdown()
  showToast('Código de verificação enviado')
}

// 绑定邮箱
const { run: runBindEmail, loading: bindEmailLoading } = useRequest(ApiBindEmail, {
  manual: true,
  onSuccess: () => {
    router.go(-1)
  }
})
// 邮箱验证码
const { run: runGetEmailCode, loading: emailCodeLoading } = useRequest(() => ApiOnlineEmailCode({ ty: 2, mail: formdata.email }), {
  manual: true,
  onSuccess: (data) => {
    if (data) {
      dealSid(data)
    }
  }
})

// 绑定手机号
const { run: runBindPhone, loading: bindPhoneLoading } = useRequest(ApiBindPhone, {
  manual: true,
  onSuccess: () => {
    router.go(-1)
  }
})
// 手机验证码
const { run: runGetPhoneCode, loading: codeLoading } = useRequest(() => ApiOnlinePhoneCode({ ty: 5, tel: formdata.phone }), {
  manual: true,
  onSuccess: (data) => {
    if (data) {
      dealSid(data)
    }
  }
})

const handleSendCode = () => {
  if (hasPhone.value) {
    // 手机已经绑定过，这里绑邮箱，是给用户填的邮箱发验证码
    if (emailRef.value.validation()) {
      runGetEmailCode()
    }
  } else {
    if (phoneRef.value.validation()) {
      runGetPhoneCode()
    }
  }
}

const confirmFn = () => {
  const { sid, ts } = sidData
  const { phone, email, code } = formdata
  if (hasPhone.value) {
    if (emailRef.value.validation() && codeRef.value.validation()) {
      runBindEmail({ sid, ts, email, code })
    }
  } else {
    if (phoneRef.value.validation() && codeRef.value.validation()) {
      runBindPhone({ sid, ts, phone, code })
    }
  }
}

watch(userInfo, (val) => {
  if (val) {
    formdata.phone = val.phone || ''
    formdata.email = val.email || ''
  }
})

</script>

<template>
  <div class="verify-check">
    <AppHeader left-arrow :title="userInfo.phone_verify && +userInfo.phone_verify === 1 ? 'Verificação de email' : 'Verificação de telefone'" placeholder />
    <section class="content">
      <div class="top"></div>
      <template v-if="hasPhone">
        <AppInput
          ref="emailRef"
          v-model="formdata.email"
          :pattern="emailReg"
          msg="Erro de e-mail"
          placeholder="Por favor introduza o seu e-mail"
          icon-left="icon_email_1"
          icon-with="38"
          width="650"
          height="75"
          :err-height="34"
          :style-obj="{
            margin: '0 auto',
            background:'#192841',
            color: '#fff'
          }"
        />
        <div class="divider"></div>
      </template>
      <template v-else>
        <AppInput
          ref="phoneRef"
          v-model="formdata.phone"
          :pattern="phoneReg"
          msg="Número de telefone errado"
          placeholder="Digite sua senha atual."
          icon-left="icon_phone_verification"
          width="650"
          height="75"
          icon-with="29"
          type="number"
          err-height="34"
          :style-obj="{
            margin: '0 auto',
            background:'#192841',
            color: '#fff'
          }"
        />
        <div class="divider"></div>
      </template>
      <AppInput
        ref="codeRef"
        v-model="formdata.code"
        :pattern="verifyCodeReg"
        placeholder="Código de verificação"
        msg="Erro no código de verificação"
        icon-left="icon_verification_code"
        clearable
        width="650"
        height="75"
        icon-with="38"
        err-height="34"
        :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"
      >
        <template #right>
          <div :style="{ paddingRight: 'var(--app-px-18)' }">
            <AppButton
              :loading="codeLoading || emailCodeLoading"
              @click="handleSendCode"
              fontSize="24"
              radius="23"
              whiteText
              blue
              width="125"
              height="46"
              center
              >{{ countdowm > 0 ? countdowm + "s" : "Mandar" }}
            </AppButton>
          </div>
        </template>
      </AppInput>
      <div class="btn-box">
        <AppButton
          :loading="bindEmailLoading || bindPhoneLoading"
          fontSize="36"
          radius="15"
          whiteText
          blue
          width="240"
          height="80"
          center
          @click="confirmFn"
          >Enviar</AppButton
        >
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.btn-box {
  padding-top: 86px;
}
.divider {
  height: 34px;
}
.verify-check {
  .content {
    margin: 0 auto;
    width: 710px;
    height: 524px;
    background-color: #28374d;
    border-radius: 50px;
    margin-top: 40px;
    .top{
      width: 710px;
      height: 81px;
      background-color: #324b6e;
      border-radius: 50px 50px 0px 0px;
      margin-bottom: 25px;
    }
    h3 {
      color: #fff;
      font-size: 28px;
      font-weight: 400;
      padding: 30px 0 28px;
      margin: 0;
    }
  }
}
</style>
