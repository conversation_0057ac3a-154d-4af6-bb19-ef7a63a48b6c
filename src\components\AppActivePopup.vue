<script lang="ts" setup name="AppActivePopup">
const router = useRouter();
const appStore = useAppStore();
const { activeDialogVisble } = storeToRefs(appStore);
const curObj = ref({});

const close = () => {
  // if (itemAll.value.length > curIndex.value + 1) {
  //   curIndex.value += 1;
  //   curItem.value[0] = itemAll.value[curIndex.value];
  // } else {
  appStore.setActiveDialogVisble(false);
  // }
};

const clickImg = (item: any) => {
  if (item.jump_type == 1) {
    //跳转内部
    router.push(item.redirect_url);
  } else {
    window.open(item.redirect_url, "_blank"); // //跳转外部1
  }
  close();
};

const curItem = ref<any>([]);
const itemAll = ref<any>([]);
const curIndex = ref(0);

const { data: list } = useRequest(ApiGetActivepopup, {
  onSuccess(data) {
    if (data && data.length > 0) {
      // data.sort((a, b) => Number(a.sort) - Number(b.sort));
      // console.log(data,'1111111111111111111111111111111111111111');

      // itemAll.value = data.map((i) => {
      //   i.redirect_url = decodeURIComponent(decodeURIComponent(i.redirect_url));

      //   if (isFullPath(i.images)) {
      //     i.images = `url(${i.images})`;
      //     console.log("appAcivePopup—完整的Ui地址=" + i.images);
      //   } else {
      //     let images = i.images.split("&");
      //     let h5str = images.filter((m: any) => m.indexOf("h5=") != -1)[0];
      //     if (h5str) {
      //       i.images = h5str.split("=")[1];
      //       i.images = `url(${brazilImg + i.images})`;
      //     }
      //     console.log("appAcivePopup—不完整的Ui地址=" + i.images);
      //   }

      //   return i;
      // });
      // curItem.value.push(itemAll.value[curIndex.value]);
      curItem.value = data;
      curObj.value = data[0];
      // curObj.value.func_type = 1;
      // curObj.value.annou_title = "(SIGAP) registado com sucesso (SIGAP) registado com sucesso";
      // curObj.value.content ="🔴Temos o prazer de anunciar que o Sistema de Gestão de Apostas (SIGAP) foi registado com sucesso! Nosso Grupo 1999 já está totalmente legalizado e regulamentado no Brasil.\nIsto marca um passo importante na nossa missão de fornecer um sistema de gestão de apostas seguro e protegido. Com o SIGAP, cumprimos todos os requisitos legais e padrões do setor para garantir uma experiência segura, compatível e confiável para todos! Você pode participar de uma variedade de atividades de apostas divertidas e transparentes com tranquilidade.\nEstamos empenhados em melhorar continuamente a sua experiência e ajudar todos a desfrutar de momentos incríveis de entretenimento. Vamos explorar o mundo das apostas que preparamos para você!\nAgradecemos o apoio de todos!"
    
    }
  },
});

const changeCur = (val) => {
  curObj.value = val;
};

// const banners = computed(() => {
//   if (list.value && list.value.length) {
//     itemAll.value= list.value.map((i) => {
//       i.redirect_url = decodeURIComponent(decodeURIComponent(i.redirect_url))

//       if(isFullPath(i.images)){
//         i.images = `url(${i.images})`
//         console.log("appAcivePopup—完整的Ui地址="+i.images)
//       }else{
//         let images = i.images.split('&')
//         let h5str = images.filter((m: any) => m.indexOf('h5=') != -1)[0]
//         if (h5str) {
//           i.images = h5str.split('=')[1]
//           i.images = `url(${brazilImg + i.images})`
//         }
//         console.log("appAcivePopup—不完整的Ui地址="+i.images)
//         }

//       return i
//     })

//     return [itemAll.value[0]]
//   }
//   return []
// })
const isRemember = ref(false);
</script>

<template>
  <!-- <van-popup class="app-login-register" v-model:show="activeDialogVisble" teleport="body" round :style="{ width: '730px' }"  :close-on-click-overlay="true" >
    aaa
    <div class="content">

      <div class="contentbg1">
          <div class="contentbg2">
            <AppBannerActivePopup class="app-banner-border" :list-data="[]" />
          </div>
          <div v-for="(item, index) in curItem" :key="index">
            <div class="div_btns"  :style="{marginLeft:`var(--app-px-${item.width-80})`}" >
              <AppImage class="btns" :src="`/img/activePopuClose.webp`" alt=""  @click="close" />
            </div>
            <RouterLink :to="item.redirect_url" class="link" @click="clickImg">  
              <div class="img2" :style="{ backgroundImage: item.images,width:`var(--app-px-${item.width})`,height:`var(--app-px-${item.length})` }" @click="clickImg(item)"></div>   
            </RouterLink>
         </div>

      </div>
          
    </div>
  </van-popup> -->
  <van-popup
    class="app-login-register"
    v-model:show="activeDialogVisble"
    teleport="body"
    round
    :close-on-click-overlay="false"
  >
    <div class="content">
      <div class="left-box">
        <div
          class="left-box-item"
          v-for="(item, index) in curItem"
          :class="item.id == curObj.id ? 'active' : ''"
          :key="index"
          @click="changeCur(item)"
        >
          <AppImage v-if="item.id != curObj.id"
            src="/img/index/popemail.webp"
            class="left-box-item-img"
            @click="close"
          />
          <AppImage v-else
            src="/img/index/popemail1.webp"
            class="left-box-item-img"
            @click="close"
          />
          <div class="title">
            {{ item.annou_title }}
          </div>
        </div>
      </div>
      <div class="img-box">
        <!-- <div
          :style="{ backgroundImage: curObj.images }"
          @click="clickImg(curObj)"
        ></div> -->
        <img
          :src="curObj.images"
          alt=""
          @click="clickImg(curObj)"
          v-if="curObj.func_type - 0 == 2"
        />
        <div
          class="text-box"
          v-if="curObj.func_type - 0 == 1"
        >
          <div class="text-box-title">
            <span>{{ curObj.annou_title }}</span>
          </div>
          <div class="text-box-content">
            <span>{{ curObj.content }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="bottom-content">
        <div class="remember" @click="isRemember = !isRemember">
          <div class="toggle">
            <AppImage
              style="width:var(--app-px-26);"
              :src="`/img/index/${
                !isRemember ? 'remember-not-select' : 'remember-select'
              }.webp`"
              alt=""
            />
          </div>
          <div class="text">Não mostrar novamente hoje</div>
        </div>
      </div>
    </div>
    <img
      class="app-image img close-icon"
      src="/icons/close_black.png.webp"
      @click="close"
      alt=""
    />
  </van-popup>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.content {
  width: 680px;
  height: 450px;
  border-radius: 16px;
  background-color: var(--theme-main-bg-color);

  overflow: hidden;
  margin-top: 160px;
  .left-box,
  .img-box {
    display: inline-block;
    vertical-align: top;
  }
  .text-box {
    padding: 20px;
    font-size: 24px;
    color: #fff;
    line-height: 35px;
    max-height: 450px;
    overflow-x: hidden; 
    overflow-y: auto;
    word-wrap: break-word;
    display: block;
    scrollbar-width: thin;
    &::-webkit-scrollbar {
        display: block;
        width: 4px;
    }
  
    .text-box-title {
      display: block;
      min-width: 60%;
      max-width: 90%;
      font-size: 30px;
      color: var(--theme-text-color-darken);
      margin: 0 auto 20px;
      text-align: center;
    }

    .text-box-content {
        display: block;
        min-width: 60%;
        max-width: 95%;
        font-size: 22px;
        line-height: 36px;
        color: var(--theme-text-color);
        margin: 0 auto 20px;
        text-align: left;
        white-space: break-spaces;
    }
  }
  .left-box {
    width: 162px;
    height: 100%;
    border-right: thin solid var(--theme-color-line);
    overflow-y: auto;
    scrollbar-width: thin;
    &::-webkit-scrollbar {
        display: block;
        width: 2px;
    }
    .left-box-item {
      width: 100%;
      height: 80px;
      padding: 10px 15px;
      text-align: revert !important;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: var(--theme-text-color-lighten);
    }
    .active {
      background: #fafd4e1a;
      color: var(--theme-primary-color);
    }

    .left-box-item-img {
      width: 28px;
      margin-right: 8px;
      display: inline-block;
    }
    .title {
      line-height: 28px;
      font-size: 22px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      white-space: break-spaces !important;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .img-box {
    width: 518px;
    height: 449px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.bottom {
  width: 100%;
  display: flex;
  align-items: center;
  position: absolute !important;
  justify-content: center;
  margin-top: 20px;

  .bottom-content {
    font-size: 22px;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    padding: 10px 15px 10px 10px;
    border-radius: 10px;
  }
  .remember {
  display: flex;
  align-items: center;
  height: 42px;
  margin-top: 3px;
    .text {
      margin-top: -0px;
      color: #fff;
      margin-left: 15px;
      font-size: 24px;
    }
  }
  
}

.submit {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 65%;
}

.close-icon {
  width: 60px;
  border: 5px solid white;
  border-radius: 100%;
  padding: 5px;
  margin: 90px auto;
  display: block;
}
</style>
