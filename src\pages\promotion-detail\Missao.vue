<script setup lang='ts' name='<PERSON><PERSON>'>

const router = useRouter()
const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const emailRef = ref()
//隐藏底下菜单
// appStore.setFooterDialogVisble(false)



</script>

<template>
    <div class = "rect"></div>
    <div class = "content">
        <app-empty text="Em breve"/>
    </div>
    
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.rect{
    height: 300px;
}

.content{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45vh; /* 使用视口高度来使容器占满屏幕垂直空间 */
}

</style>
<route lang="yaml">
  meta:
    auth: true
</route>