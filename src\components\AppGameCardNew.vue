<script setup lang="ts" name="AppGameCardNew">
import { ref, computed } from 'vue'

interface GameCardData {
  id: string
  name: string
  image: string
  bgColor: string
  isFavorite?: boolean
  isHot?: boolean
}

interface Props {
  data: GameCardData
  width?: string
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '180px'
})

const emit = defineEmits<{
  launch: [game: GameCardData]
  toggleFavorite: [gameId: string]
}>()

const isImageLoaded = ref(false)
const isImageError = ref(false)

const cardStyle = computed(() => ({
  background: props.data.bgColor,
  width: props.width,
  minHeight: props.height
}))

const handleImageLoad = () => {
  isImageLoaded.value = true
  isImageError.value = false
}

const handleImageError = () => {
  isImageError.value = true
  isImageLoaded.value = false
}

const handleLaunch = () => {
  emit('launch', props.data)
}

const handleToggleFavorite = (event: Event) => {
  event.stopPropagation()
  emit('toggleFavorite', props.data.id)
}
</script>

<template>
  <div 
    class="app-game-card-new"
    :style="cardStyle"
    @click="handleLaunch"
  >
    <!-- 收藏按钮 -->
    <button 
      class="favorite-btn"
      :class="{ active: data.isFavorite }"
      @click="handleToggleFavorite"
    >
      <svg width="18" height="16" viewBox="0 0 18 16" fill="none">
        <path 
          d="M9 15.5L7.95 14.6C3.6 10.74 0.5 8.02 0.5 4.75C0.5 2.42 2.42 0.5 4.75 0.5C5.86 0.5 6.99 1.01 7.8 1.85C8.61 1.01 9.74 0.5 10.85 0.5C13.18 0.5 15.1 2.42 15.1 4.75C15.1 8.02 12 10.74 7.65 14.6L6.6 15.5H9Z"
          :fill="data.isFavorite ? '#FF4757' : 'rgba(255,255,255,0.8)'"
        />
      </svg>
    </button>

    <!-- 热门标识 -->
    <div v-if="data.isHot" class="hot-badge">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path d="M8 0L10.472 5.528L16 8L10.472 10.472L8 16L5.528 10.472L0 8L5.528 5.528L8 0Z" fill="#FFD700"/>
      </svg>
    </div>

    <!-- 游戏图片容器 -->
    <div class="game-image-container">
      <!-- 默认占位图 -->
      <div v-if="!isImageLoaded && !isImageError" class="image-placeholder">
        <div class="loading-spinner"></div>
      </div>
      
      <!-- 游戏图片 -->
      <img 
        v-show="isImageLoaded && !isImageError"
        :src="data.image" 
        :alt="data.name"
        class="game-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />
      
      <!-- 错误占位图 -->
      <div v-if="isImageError" class="image-error">
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
          <path d="M20 0C8.954 0 0 8.954 0 20s8.954 20 20 20 20-8.954 20-20S31.046 0 20 0zm0 36c-8.837 0-16-7.163-16-16S11.163 4 20 4s16 7.163 16 16-7.163 16-16 16z" fill="rgba(255,255,255,0.5)"/>
          <path d="M20 10c-1.105 0-2 .895-2 2v8c0 1.105.895 2 2 2s2-.895 2-2v-8c0-1.105-.895-2-2-2zM20 26c-1.105 0-2 .895-2 2s.895 2 2 2 2-.895 2-2-.895-2-2-2z" fill="rgba(255,255,255,0.5)"/>
        </svg>
      </div>
    </div>

    <!-- 游戏名称 -->
    <div class="game-name">
      {{ data.name }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-game-card-new {
  position: relative;
  border-radius: 20px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(-2px);
  }

  .favorite-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 2;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
      transform: scale(1.1);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      
      svg path {
        fill: #FF4757;
      }
    }
  }

  .hot-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 215, 0, 0.2);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    z-index: 2;
  }

  .game-image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    width: 100%;
    min-height: 80px;

    .image-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;

      .loading-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    .game-image {
      max-width: 80px;
      max-height: 80px;
      object-fit: contain;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
      transition: transform 0.3s ease;
    }

    .image-error {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      opacity: 0.5;
    }
  }

  &:hover .game-image {
    transform: scale(1.05);
  }

  .game-name {
    color: white;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
    margin-top: 10px;
    max-width: 100%;
    word-wrap: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 480px) {
  .app-game-card-new {
    padding: 15px;
    
    .favorite-btn {
      width: 32px;
      height: 32px;
      top: 12px;
      right: 12px;
      
      svg {
        width: 16px;
        height: 14px;
      }
    }
    
    .hot-badge {
      width: 28px;
      height: 28px;
      top: 12px;
      left: 12px;
      
      svg {
        width: 14px;
        height: 14px;
      }
    }
    
    .game-image-container {
      min-height: 70px;
      
      .game-image {
        max-width: 70px;
        max-height: 70px;
      }
      
      .image-placeholder,
      .image-error {
        width: 70px;
        height: 70px;
      }
    }
    
    .game-name {
      font-size: 12px;
      margin-top: 8px;
    }
  }
}
</style>
