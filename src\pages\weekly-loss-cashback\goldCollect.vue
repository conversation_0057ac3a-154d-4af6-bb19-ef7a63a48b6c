<script lang="ts" setup>

const router = useRouter()
const enum JumpViewType {
  PROXY_CASHBACK = 0,
  DEPOSIT_CASHBACK,
  LOSS_CASHBACK,
  BET_CASHBACK,
  DAY_BET_CASHBACK

}

const appStore = useAppStore()

const thisWeekStr = ref(getWeekDate());
const lastWeekStr = ref(getWeekDate(true));


const { run: runueryAllReturn, data: weekReturnData } = useRequest(() => ApiGetQueryAllReturn(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)

  }
})
runueryAllReturn();

const { run: runAchieveProxyWeekDeposit } = useRequest(() => ApiAchieveProxyWeekDeposit(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})
const { run: runAchieveWeekDepositReturn } = useRequest(() => ApiAchieveWeekDepositReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance()
  }
})

const { run: runAchieveWeekLostReturn} = useRequest(() => ApiAchieveWeekLostReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})

//周投注返利
const { run: runAchieveWeekRunningReturn} = useRequest(() => ApiAchieveWeekRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})


//日投注返利
const { run: runAchieveDayRunningReturn} = useRequest(() => ApiAchieveDayRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})



//按钮点击
const isRunProxyAni = ref(false)
const isRunDepositAni = ref(false)
const isRunLossAni = ref(false)
const isRunBetAni = ref(false)
const isRunDayBetAni = ref(false)

function getMoney(type: any) {
  console.log("getMoney==" + type)
  switch (type) {
    case JumpViewType.PROXY_CASHBACK:
      if(isRunProxyAni.value) return;
      if(weekReturnData.value.week_proxy_commi_return > 0){
        runAchieveProxyWeekDeposit();
      } else {
        isRunProxyAni.value = true
        setTimeout(() => {
          isRunProxyAni.value = false
        }, 800);

      }
      break;
    case JumpViewType.DEPOSIT_CASHBACK:
      if(isRunDepositAni.value) return;
      if(weekReturnData.value.week_deposit_return > 0){
        runAchieveWeekDepositReturn();
      } else {
        isRunDepositAni.value = true
        setTimeout(() => {
          isRunDepositAni.value = false
        }, 800);
      }
      
      break;
    case JumpViewType.LOSS_CASHBACK:
      if(isRunLossAni.value) return;
      if(weekReturnData.value.week_lost_return > 0){
        runAchieveWeekLostReturn();
      } else {
        isRunLossAni.value = true
        setTimeout(() => {
          isRunLossAni.value = false
        }, 800);
      }
      
      break;
    case JumpViewType.BET_CASHBACK:
      if(isRunBetAni.value) return;
      if(weekReturnData.value.week_bet_return > 0){
        runAchieveWeekRunningReturn();
      } else {
        isRunBetAni.value = true
        setTimeout(() => {
          isRunBetAni.value = false
        }, 800);
      }
      break;
    case JumpViewType.DAY_BET_CASHBACK:
      if(isRunDayBetAni.value) return;
      if(weekReturnData.value.day_bet_return > 0){
        runAchieveDayRunningReturn();
      } else {
        isRunDayBetAni.value = true
        setTimeout(() => {
          isRunDayBetAni.value = false
        }, 800);
      }
      break;
  }
}

//跳转
function goToLayer(type: any) {
  console.log("goToLayer=" + type)
  let pathArr = ["/activity/weekly-proxy-cashback", "/activity/weekly-deposit-cashback", "/weekly-loss-cashback", "/activity/weekly-bet-cashback","/activity/day-bet-cashback"];
  router.push(pathArr[type])
}



</script>

<template>
  <div class="topDiv">
    <AppIndexHeader />
    <section class="content">

      <div class="devContent" style="margin-top: 30px;" >
        <AppImage  src="/img/weekly/cashback_apostas.webp"  class="apostas" />
        <div >
          <label class="semanal color4" @click="() =>goToLayer(JumpViewType.DAY_BET_CASHBACK)">Cashback em apostas</label>
          <AppImage class="btn_go" src="/img/weekly/down4.webp"  @click="() =>goToLayer(JumpViewType.DAY_BET_CASHBACK)"/>
        </div>
      
        <label class="periodo">Periodo:{{ getDayDate() }}  </label>
        <label class="Tempo" :class="{ 'scaleAni': isRunDayBetAni }" >Tempo de reivindicação:{{ getDayDate(true) }} </label>
        <AppButton class="btn" fontSize="30" radius="10" whiteText green width="476" height="67" center @click="() =>getMoney(JumpViewType.DAY_BET_CASHBACK)">Obter agora</AppButton>
      </div>

      <div class="devContent" >
        <AppImage  src="/img/weekly/semabak.webp"  class="semabak" />
        <div >
          <label class="semanal color1" @click="() =>goToLayer(JumpViewType.PROXY_CASHBACK)">Comissão semanal</label>
          <AppImage class="btn_go" src="/img/weekly/down4.webp"  @click="() =>goToLayer(JumpViewType.PROXY_CASHBACK)"/>
        </div>
      
        <label class="periodo">Periodo:{{thisWeekStr}}  </label>
        <label class="Tempo" :class="{ 'scaleAni': isRunProxyAni }" >Tempo de reivindicação:{{lastWeekStr}} </label>
        <AppButton class="btn" fontSize="30" radius="10" whiteText purple width="476" height="67" center @click="() =>getMoney(JumpViewType.PROXY_CASHBACK)">Obter agora</AppButton>
      </div>


      <!-- <div class="devContent" style="margin-top: 30px;" >
        <AppImage  src="/img/weekly/cashback_dep.webp"  class="dep" />
        <div >
          <label class="semanal color2" @click="() =>goToLayer(JumpViewType.DEPOSIT_CASHBACK)">Cashback de depósito</label>
          <AppImage class="btn_go" src="/img/weekly/down4.webp"  @click="() =>goToLayer(JumpViewType.DEPOSIT_CASHBACK)"/>
        </div>
      
        <label class="periodo">Periodo:{{ thisWeekStr }} </label>
        <label class="Tempo" :class="{ 'scaleAni': isRunDepositAni }" >Tempo de reivindicação:{{ lastWeekStr}} </label>
        <AppButton class="btn" fontSize="30" radius="10" whiteText yellow width="476" height="67" center @click="() =>getMoney(JumpViewType.DEPOSIT_CASHBACK)">Obter agora</AppButton>
      </div>
     -->

      <div class="devContent" style="margin-top: 30px" >
        <AppImage  src="/img/weekly/cashback_perdas.webp"  class="perd" />
        <div >
          <label class="semanal color3"  @click="() =>goToLayer(JumpViewType.LOSS_CASHBACK)">Cashback de perdas</label>
          <AppImage class="btn_go" src="/img/weekly/down4.webp"  @click="() =>goToLayer(JumpViewType.LOSS_CASHBACK)"/>
        </div>
      
        <label class="periodo">Periodo:{{ thisWeekStr }}</label>
        <label class="Tempo" :class="{ 'scaleAni': isRunLossAni }" >Tempo de reivindicação:{{ lastWeekStr }}</label>
        <AppButton class="btn" fontSize="30" radius="10" whiteText blue width="476" height="67" center @click="() =>getMoney(JumpViewType.LOSS_CASHBACK)">Obter agora</AppButton>
      </div>

      <!-- <div class="devContent" style="margin-top: 30px;" >
        <AppImage  src="/img/weekly/cashback_apostas.webp"  class="apostas" />
        <div >
          <label class="semanal color4" @click="() =>goToLayer(JumpViewType.BET_CASHBACK)">Cashback em apostas</label>
          <AppImage class="btn_go" src="/img/weekly/down4.webp"  @click="() =>goToLayer(JumpViewType.BET_CASHBACK)"/>
        </div>
      
        <label class="periodo">Periodo:{{ thisWeekStr }}  </label>
        <label class="Tempo" :class="{ 'scaleAni': isRunBetAni }" >Tempo de reivindicação:{{ lastWeekStr }} </label>
        <AppButton class="btn" fontSize="30" radius="10" whiteText green width="476" height="67" center @click="() =>getMoney(JumpViewType.BET_CASHBACK)">Obter agora</AppButton>
      </div> -->

    

  
    </section>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';


.content{
  margin-top:60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 112px;
  padding-bottom: 132px;
}

.devContent{
  display: flex;
  flex-direction: column;
  align-items: center;

}

.semabak{
  width: 338px;
  height: 226px;
  display: block;
}


.dep{
  width: 282.4px;
  height:273.4px;
  display: block;
}


.btn_go{
  position: absolute;
  width: 68px;
  height: 68px;
  float: right;
  margin-top: -10px;
  margin-left: 15px;
 
}


.semanal{
  font-family:Arial;
  font-weight:700;

  font-size:44px;
  text-align:center;
}

.color1{
  color:var(--app-title-color);
}

.color2{
  color:var(--app-title-color);
}

.color3{
  color:var(--app-title-color);
}

.color4{
  color:var(--app-title-color);
}

.periodo{
  display: block;
  margin-top: 10px;
  height:35px;
  font-family:Arial;
  color:var(--app-title-color);
  font-size:23px;
  padding-bottom:20px;
}

.Tempo{
  display: block;
  margin-top: 5px;
  height:35px;
  font-family:Arial;
  color:var(--app-title-color);
  font-size:23px;
  padding-bottom:20px;
  &.scaleAni {
    // animation: spin 1s linear infinite;
    // transform: scale(1.08) scale(1) scale( 1.08); /* 放大1.2倍 */
    // transition:spin 0.1s linear;
    animation:sacleAniFrames 0.8s ease;
  }
}
@keyframes sacleAniFrames {
  25% {
    transform: scale(1.08);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.08);
  }

  100% {
    transform: scale(1);
  }
}
.btn{
  margin-top: 10px;
}

.perd{
  width: 282.09px;
  height:255.62px;
  display: block;

}

.apostas{
  width: 237.76px;
  height:229.65px;
  display: block;
}


</style>

<route lang="yaml">
  meta:
    auth: true
</route>
