<script setup lang="ts">
// import { useAppStore } from '@/store-pinia';
// import { useImage } from '@/hooks/useImage';
// import { useLoading } from '@/hooks/useLoading';
// const { startLoading, closeLoading } = useLoading();

// const { localSrc } = useImage();
const appStore = useAppStore()
const route = useRoute();
const router = useRouter();
// const appStore = useAppStore();

const url = localStorage.getItem('callbackHTML');
console.log(url);
const iframeSrc = ref(route.query.url?.toString());
const showLoading = ref(true);
// const gameId = ref(route.query.id);

const info = computed(() => {
  return url?.indexOf('https') !== -1 ? url : `data:text/html;charset=utf-8,${encodeURIComponent(url)}`
})


appStore.setFooterDialogVisble(false)

// appStore.setIsShowLoading(true)
onMounted(() => {
  const iframe: any = document.getElementById('iframeLaunchGame');
  iframe.onload = function () {
    console.log('iframeLaunchGame') 
    showLoading.value = false;
  };

  iframe.onerror = function () {
    showLoading.value = false;
  };
  window.addEventListener('message', (data: any) => {
    switch (data.data.action) {
      case 'goBack':
        // router.push('/home');
        goOut()
        break;
      case 'login':
        // appStore.removeToken();
        // router.push({ path: '/entry/login' });
        break;
      default:
        break;
    }
  });
});
onBeforeUnmount(() => {
  window.removeEventListener('message', () => { });
  // closeLoading()
});

function goOut(){
  appStore.setFooterDialogVisble(true)
  router.push('/');
  appStore.runGetUserBalance();
}

// watch(
//   () => showLoading.value,
//   newVal => {
//     if (!newVal) {
//       closeLoading();
//     }
//   }
// );
// onMounted(() => {
//   startLoading();
// });
// const onloadFunction = ()=>{ console.log('iframeLaunchGame') }


</script>
<template>
  <div class="launch-game" id="launch-game">
    <!-- <div
      v-if="showLoading"
      class="launch_loading"
      :style="{ backgroundImage: `url(${localSrc}/venueLoading/l_${gameId}.png)` }"
    /> -->
    <iframe id="iframeLaunchGame" :style="{ opacity: showLoading ? 0 : 1 }" :src="info" allowfullscreen="true" ></iframe>

    <!-- <van-popup v-model:show="showLoading" teleport="body" :close-on-click-overlay="false" > -->
    <div class="content" v-if="showLoading">
      <!-- <van-loading vertical>
        <template #icon>
          <van-icon name="star-o" size="30" />
        </template>
      </van-loading> -->
      <!-- <van-loading color="#1989fa"  /> -->
      <div class="contentBg"></div>
      <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
    </div>
    <!-- </van-popup> -->

    <AppImage class="btns" :src="`/img/launchClose.webp`" alt=""  @click="goOut" />

  </div>
</template>

<style lang="scss" scoped>
.launch-game {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  iframe {
    width: 100%;
    height: 100%;
  }
  .content {
    position: absolute; 
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    
    display: flex;
    align-items: center;
    justify-content: center;
    
    .contentBg {
      position: absolute; 
      width: 100%;
      height: 100%;
      left: 0px;
      top: 0px;
      background-color: black;
      opacity: 0.7;
    }
  // margin: 0 auto;
  }
  .btns{
    position: absolute;
    width: 85px;
    left:5px;
    top: 5px;
  }
  // .launch_loading {
  //   width: 100%;
  //   height: 100%;
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   background-size: contain;
  // }
}

</style>
