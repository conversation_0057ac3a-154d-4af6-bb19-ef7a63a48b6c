<script lang="ts" setup name="AppPromoBox">
const router = useRouter()

const promos = [
  {
    path: '/promotion-detail/invite',
    // icon: 'btn_invitation.png',
    title: 'Bónus de',
    title1: 'Convite'
  },
  {
    path: '/promotion-detail/reward-box',
    // icon: 'btn_unboxing.png',
    title: 'Jackpot de',
    title1: 'desempacotar'
  },
]

const imgLoaded = (e: any) => {
  e.currentTarget.style.width = `var(--app-px-${Math.ceil(e.target.naturalWidth / 3)})`
}

const itemClick = (p: any) => {
  if (p.path) {
    router.push(p.path)
  }
}

</script>

<template>
  <div class="app-promos-box">
    <ul class="inner">
      <li v-for="(p, idx) in promos" :key="p.path" class="item" :class="'item-'+idx" @click="() => itemClick(p)">
        <h3>{{ p.title }}</h3>
        <h3>{{ p.title1 }}</h3>
        <!-- <AppImage :src="'/img/'+p.icon" @load="$event => imgLoaded($event)" /> -->
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.app-promos-box {
  overflow: visible;
  margin-bottom: 24px;
  .inner {
    display: grid;
    grid-template-rows: auto;
    grid-column-gap: 16px;
    grid-auto-flow: column;
    grid-template-columns: 1fr 1fr;
    align-items: start;
    justify-items: start;
    overflow: visible;

    &::-webkit-scrollbar {
      display: none;
    }
    .item {
      position: relative;
      overflow: visible;
      width: 340px;
      height: 120px;
      background-image: url('/img/btn_invitation.png');
      background-size: 340px 120px;
      border-radius: 10px;
      padding: 20px 146px 0 20px;
      text-align: center;
      h3 {
        margin: 0;
        padding: 0;
        color: #FFF;
        font-size: 28px;
        font-weight: normal;
        line-height: 34px;
        word-break: break-all;
      }
      img {
        position: absolute;
        width: 0;
        right: 0;
        bottom: 0;
      }
      &.item-0 {
        img {
          right: 4px;
          bottom: -7px;
        }
      }
      &.item-1 {
        width: 340px;
        height: 120px;
        background-image: url('/img/btn_unboxing.png');
        background-size: 340px 120px;
        h3 {
          word-break: normal;
        }
        img {
          right: -4px;
          bottom: 0;
        }
      }
    }
  }
}
</style>
