<template>
  <div class="app-game-container" v-if="todos !== 0">
    <!-- 顶部栏 -->
    <div class="header">
      <div class="logo">
        <img :src="gameLogo" alt="Logo" />
        <span class="brand">{{ gameName }}</span>
      </div>
      <div class="filters pagination-bar">
        <button
          class="arrow-btn"
          :disabled="currentPage === 1"
          @click="prevPage"
        >
          <span>&lt;</span>
        </button>
        <button class="todos-btn" style="color: #000000a6; font-size: 3vw">
          Todos
          <span class="count highlight">{{ todos }}</span>
        </button>
        <button
          class="arrow-btn"
          :disabled="currentPage === totalPages"
          @click="nextPage"
        >
          <span>&gt;</span>
        </button>
      </div>
      <button
        class="more-btn"
        @click="handleMoreClick"
      >
        Mais
      </button>
    </div>
    <!-- 游戏卡片网格 -->
    <div class="game-grid">
      <div
        v-for="game in filteredGames"
        :key="game.title + game.img"
        class="game-card"
      >
        <img
          class="game-img"
          :src="
            isFullPath(game.img || '') ? game.img : getRemoteImgUrl + game.img
          "
          :alt="game.title"
          @error="onImgError"
          loading="lazy"
          @click="lunchGame"
        />
        <button class="favorite-btn" @click="toggleFavorite(game)">
          <span v-if="game.favorite">❤️</span>
          <span v-else>🤍</span>
        </button>
        <!-- <div class="game-title">{{ game.title }}</div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
// import { log } from "console";
import { ref, computed, watch } from "vue";
import { defineProps } from "vue";
import router from "~/router";
const appStore = useAppStore();
const { isLogin, getRemoteImgUrl, isShowStar, isShowGood, PGSlotsImgType } =
  storeToRefs(appStore);
// 这里假设useRequest、ApiGameHotList等已引入

const props = defineProps({
  gameType: {
    type: [String, Number],
    default: null,
  },
});

const games = ref([]); // 用于存储接口返回的游戏数据
const todos = ref(0);
const gameLogo = ref("");
const gameName = ref("Game");

function onImgError(event) {
  event.target.src = "/img/index/bigDefault.webp"; // 这里写你的默认图片路径
}

const GameTypeEnum = [
  {
    platformId: "0",
    PlatformName: "Quente",
    icon: "/img/gameTypeIcon/quente.png",
  },
  { platformId: "101", PlatformName: "PG", icon: "/img/gameTypeIcon/pg.png" },
  {
    platformId: "201,301",
    PlatformName: "PP",
    icon: "/img/gameTypeIcon/pp.png",
  },
  {
    platformId: "501,503",
    PlatformName: "JDB",
    icon: "/img/gameTypeIcon/jdb.png",
  },
  { platformId: "703", PlatformName: "Fc", icon: "/img/gameTypeIcon/fc.png" },
  {
    platformId: "603",
    PlatformName: "Jili",
    icon: "/img/gameTypeIcon/jili.png",
  },
  {
    platformId: "801,803",
    PlatformName: "YesBingo",
    icon: "/img/gameTypeIcon/yb.png",
  },
];

const { run: runGetHotGames } = useRequest(
  () =>
    ApiGameHotList({
      ty: 0,
      platform_id: "0",
      page_size: 999,
      page: 1,
      is_fav: undefined,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      // 适配接口数据结构
      games.value = (data?.d || []).map((item) => ({
        title: item.en_name || item.br_alias || item.name || "",
        img: item.img,
        favorite: item.is_fav === 1,
        type: item.game_type || item.platform_id,
        raw: item, // 保留原始数据
      }));
      todos.value = data.t;
    },
  }
);

const { run: runGetRecGames } = useRequest(
  (platformId) =>
    ApiGameRecList({
      ty: 0,
      platform_id: platformId,
      page_size: 999,
      page: 1,
      is_fav: undefined,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      // 适配接口数据结构
      games.value = (data?.d || []).map((item) => ({
        title: item.en_name || item.br_alias || item.name || "",
        img: item.img,
        favorite: item.is_fav === 1,
        type: item.game_type || item.platform_id,
        raw: item, // 保留原始数据
      }));
      todos.value = data.t;
    },
  }
);

const lunchGame = () => {
  if (isLogin.value) {
    //显示loading
    appStore.setIsShowLoading(true);
    runLunch();
    // gameStore.setGameItemPopupVisible(true, props.data)
  } else {
    appStore.setLoginDialogVisible(true);
  }

  saveGame();
  // router.push({ path: '/embedded', query: { url: '' } })
};

const { run: runLunch } = useRequest(
  () =>
    ApiLunchGame({
      pid: props.data.platform_id + "",
      code: props.data.game_id,
      game_type: props.data.game_type,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        window.localStorage.removeItem("callbackHTML");
        window.localStorage.setItem("callbackHTML", data);
        router.push({ path: "/embedded", query: { url: "" } });
        // window.open(data)
      }
    },
    onError(err) {
      console.log(err);
      if (err.data.msg && err.data.msg == 5001) {
        showToast(
          "Seu saldo é menor que R$" +
            err.data.data +
            ", Por favor, faça um depósito para jogar"
        );
      }
    },
    onAfter: () => {
      appStore.setIsShowLoading(false);
      gameStore.setGameItemPopupVisible(false);
    },
  }
);

//保存数据
const saveGame = () => {
  let recenteData = window.localStorage.getItem("recenteData");
  if (recenteData == null) {
    let mapSet = [];
    mapSet.push(props.data);
    window.localStorage.setItem("recenteData", JSON.stringify(mapSet));
  } else {
    let loadedMap = JSON.parse(recenteData);
    let curPos = -1;
    for (let i = 0; i < loadedMap.length; i++) {
      if (loadedMap[i].id == props.data.id) {
        curPos = i;
        break;
      }
    }

    if (curPos >= 0) {
      const element = loadedMap.splice(curPos, 1)[0];
      loadedMap.unshift(element);
    } else {
      loadedMap.unshift(props.data);
    }

    if (loadedMap.length > 20) {
      loadedMap = loadedMap.splice(0, 20);
    }

    window.localStorage.setItem("recenteData", JSON.stringify(loadedMap));
  }

  // window.localStorage.removeItem('recenteData')
};

watch(
  () => props.gameType,
  (val) => {
    console.log(val);

    if (val == 0) {
      runGetHotGames();
      // runGetRecGames(201);
    } else {
      runGetRecGames(GameTypeEnum[val].platformId);
      console.log(8373);
      console.log(games);
    }
    gameLogo.value = GameTypeEnum[val].icon;
    gameName.value = GameTypeEnum[val].PlatformName;
  },
  { immediate: true }
);

const pageSize = 12; // 每页12个
const currentPage = ref(1);

const filteredAllGames = computed(() => {
  // 如果需要类型过滤
  return games.value;
});

const totalPages = computed(() => {
  return Math.ceil(filteredAllGames.value.length / pageSize) || 1;
});

const filteredGames = computed(() => {
  const start = (currentPage.value - 1) * pageSize;
  return filteredAllGames.value.slice(start, start + pageSize);
});

function toggleFavorite(game) {
  game.favorite = !game.favorite;
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function handleMoreClick() {
  console.log(123123);
  console.log(GameTypeEnum[props.gameType].platformId);
  router.push('/subgame?platform_id=' + GameTypeEnum[props.gameType].platformId);
  appStore.setShowRouterView(true);
}
</script>

<style scoped>
.app-game-container {
  /* min-height: 62vh; */
  padding: 24px 12px;
  font-family: "Poppins", Arial, sans-serif;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-grow: 1;
}
.logo img {
  height: 10vw;
}
.brand {
  color: #6c63ff;
  font-weight: bold;
  font-size: 1.3rem;
  letter-spacing: 1px;
}
.filters.pagination-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  margin-right: 15px;
}
.arrow-btn {
  background: #6952eb26;
  border: none;
  border-radius: 10px;
  width: 7vw;
  height: 7vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: #00000073;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.arrow-btn:disabled {
  color: #00000073;
  background: #6952eb26;
  cursor: not-allowed;
}
.todos-btn {
  background: #6952eb26;
  border: none;
  border-radius: 12px;
  padding: 0 22px;
  height: 7vw;
  font-size: 1.1rem;
  color: #6c63ff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: default;
}
.count.highlight {
  color: #6c63ff;
  font-weight: bold;
  font-size: 1.15rem;
  margin-left: 2px;
}
.more-btn {
  background: #6952eb26;
  border: none;
  border-radius: 12px;
  padding: 8px 18px;
  font-size: 1rem;
  color: #000000a6;
  font-weight: 600;
  cursor: pointer;
  height: 7vw;
}
.game-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 18px;
}
.game-card {
  position: relative;
  border-radius: 18px;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.15s;
}
.game-card:hover {
  transform: translateY(-4px) scale(1.03);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.13);
}
.game-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
}
.favorite-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 2;
  transition: background 0.2s;
}
.favorite-btn:hover {
  /* background: #fff; */
}
.game-title {
  color: #fff;
  font-weight: bold;
  font-size: 1.1rem;
  text-align: center;
  margin: 16px 0 12px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  letter-spacing: 1px;
}
</style>
