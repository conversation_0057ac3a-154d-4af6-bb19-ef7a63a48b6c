<script setup lang="ts" name="AppBaixar">
import xIcon from "/icons/svg/X.svg?raw";
const appStore = useAppStore();
const { isShowAppBaixar } = storeToRefs(appStore);

const hideChild = ref(false);

const isIos = ref(false);

const iosImg = ref(false);

const userAgent = navigator.userAgent || navigator.vendor;

const closeView = () => {
  appStore.setIsShowAppBaixar(false);
  setTimeout(() => {
    hideChild.value = true;
  }, 500);
};

if (
  userAgent.indexOf("iPad") > -1 ||
  userAgent.indexOf("iPhone") > -1 ||
  userAgent.indexOf("iPod") > -1
) {
  iosImg.value = true; //'iOS'; // 苹果浏览器
}

function gotoDownLoad() {
  closeView();
  if (
    userAgent.indexOf("iPad") > -1 ||
    userAgent.indexOf("iPhone") > -1 ||
    userAgent.indexOf("iPod") > -1
  ) {
    isIos.value = true; //'iOS'; // 苹果浏览器
  } else if (userAgent.indexOf("Android") > -1) {
    if (typeof window !== "undefined" && "installButtonclick" in window) {
      console.log("点击了");

      (window as any).installButtonclick();
    }
  } else {
    //  show.value= false // 'unknown'; // 其他浏览器或平台
    if (typeof window !== "undefined" && "installButtonclick" in window) {
      console.log("点击了");
      (window as any).installButtonclick();
    }
  }
}

//关闭界面
function cancelar() {
  // show.value = false
  isIos.value = false;
  appStore.setIosInfoShow(false);
}

watch(isShowAppBaixar, (val) => {
  if (val) {
    hideChild.value = true;
  }
});
</script>

<template>
  <div>
    <section
      :class="{ AppBaixar: isShowAppBaixar, close: !isShowAppBaixar }"
      style="transition: height 0.5s ease 0s"
    >
      <div v-if="!hideChild" class="AppBaixar-box">
        <div class="AppBaixar-content">
          <div class="AppBaixar-content-close" @click="closeView">
            <!-- <AppImage class="AppBaixar-content-img" :src="`/img/index/close.webp`" alt="" /> -->
            <div v-html="xIcon"></div>
          </div>
          <div class="AppBaixar-content-imgBox">
            <AppImage
              class="AppBaixar-content-img1"
              :src="`/baixar-img1.webp`"
              alt=""
            />
          </div>
          <div class="AppBaixar-content-button">
            <AppButton
              @click="gotoDownLoad"
              class=""
              width="var(--app-px-140)"
              height="44"
              blue
              :radius="10"
              background="#1678FF"
              font-size="var(--app-px-18)"
              color="#ffffff"
            >
              <span>Baixar Agora</span>
            </AppButton>
          </div>
        </div>
      </div>
    </section>
  </div>
  <van-popup
    class="app-login-register"
    v-model:show="isIos"
    position="bottom"
    teleport="body"
  >
    <div class="ios-add-content">
      <div class="ios-add-content-close" @click="">
        <AppImage
          class="ios-add-content-close-img"
          :src="`/img/index/close.webp`"
          alt=""
          @click="cancelar"
        />
      </div>

      <div class="ios-add-content-body">
        <h1>Adicionar à tela inicial</h1>
        <div class="ios-add-content-body-tip">
          <p>
            1. Toque no ícone "Mais" e, em seguida, toque em Adicionar ao ecrã
            principal
          </p>
          <div class="ios-add-content-body-tip-imgBox">
            <AppImage
              class="ios-add-content-body-tip-img"
              :src="`/img/index/img_ios_jc_1.webp`"
              alt=""
            />
            <span>Adicionar a memorando rápido</span>
            <span>Encontrar na página</span>
            <span>Adicionar à tela inicial</span>
            <span>Marcador</span>
          </div>
          <p class="ios-add-content-body-tip-span">
            2. Clique em Adicionar e selecione “Adicionar”
          </p>

          <div class="ios-add-content-body-tip-imgBox1">
            <AppImage
              class="ios-add-content-body-tip-img1"
              :src="`/img/index/img_ios_jc_2_zh.webp`"
              alt=""
            />
            <span>Cancelar</span>
            <span>Adicionar à tela inicial</span>
            <span>Adicionar</span>
            <!-- <span>CAGADOSLOT.COM</span> -->
            <span>CAGADOSLOT.COM</span>
            <span>https://cagadoslot.com/home/<USER>/span>
            <span
              >Será adicionado um ícone ao seu ecrã inicial para aceder
              rapidamente a este website</span
            >
            <AppImage
              class="ios-add-content-body-tip-h5icon"
              :src="`ic_launcher1.png.webp`"
              alt=""
            />
          </div>
        </div>
        <AppImage
          class="ios-add-content-body-img imgAnimation"
          :src="`/img/index/pointer.webp`"
          alt=""
        />
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.AppBaixar {
  width: 100%;
  height: 70px;
  border-bottom: 1px solid var(--theme-color-line);
  z-index: 101;
}

.close {
  height: 0px;
}

.AppBaixar-box {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
}

.AppBaixar-content {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--theme-download-color);
  align-items: center;
  justify-content: center;
}

.AppBaixar-content-close {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  height: 100%;
  width: 80px;
  padding: 0 30px;
}

.AppBaixar-content-img {
  width: 20px;
  height: 20px;
}

.AppBaixar-content-imgBox {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 357px;
}

.AppBaixar-content-img1 {
  height: 51px;
  min-width: 50px;
  will-change: auto !important;
  opacity: 1;
  vertical-align: middle;
  max-width: 100%;
}

.AppBaixar-content-button {
  width: 140px;
  height: 45px;
  flex-shrink: 0;
  margin-left: auto;
  margin-right: 20px;

  span {
    font-size: 18px;
    font-family: MicrosoftYaHeiLobby;
    text-align: center;
  }
}

.ios-add-content {
  width: 100%;
  height: 866px;
  position: relative;
  font-family: Arial;
  border-radius: 20px 20px 0 0;
  background-color: var(--theme-main-bg-color);
  border: thin solid;
  border-color: var(--theme-color-line);
  overflow: auto;

  .ios-add-content-close {
    width: 60px;
    height: 60px;
    position: absolute;
    top: 0px;
    right: 0px;
    text-rendering: auto;
    text-align: center;
    text-transform: none;
  }
  .ios-add-content-close-img {
    width: 20px;
    height: 20px;
    vertical-align: -10px;
  }
  .ios-add-content-body {
    height: 100%;
    line-height: 30px;
    font-size: 28px;
    word-wrap: break-word;
    padding: 20px 30px 50px;

    h1 {
      font-size: 30px;
      font-weight: 700;
      line-height: 45px;
      text-align: center;
      color: var(--theme-hx-color);
      margin-top: 0;
      margin-bottom: 40px;
    }

    .ios-add-content-body-tip {
      width: 480px;
      margin: 0 auto;
      p {
        display: block;
        unicode-bidi: isolate;
        margin-top: 0;
        color: var(--theme-text-color-darken);
        font-size: 22px;
        margin-left: -30px;
        line-height: 33px;
      }

      .ios-add-content-body-tip-span {
        margin-top: 30px;
      }
    }

    .ios-add-content-body-tip-imgBox {
      height: 258px;
      width: 480px;
      margin-top: 20px;
      position: relative;

      span {
        position: absolute;
        line-height: 30px;
        font-family: MicrosoftYaHeiLobby;
        font-stretch: normal;
        font-style: normal;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 350px;
        letter-spacing: 0.5px;
        font-stretch: 100%;
        left: 40px;
        font-size: 20px;
        color: #211f20;

        &:nth-child(2) {
          top: 18px;
        }
        &:nth-child(3) {
          top: 79px;
        }
        &:nth-child(4) {
          top: 140px;
        }
        &:nth-child(5) {
          top: 214px;
        }
      }
    }

    .ios-add-content-body-tip-img {
      width: 100%;
      display: inline;
    }

    .ios-add-content-body-tip-imgBox1 {
      height: 280px;
      width: 480px;
      margin-top: 20px;
      position: relative;

      .ios-add-content-body-tip-img1 {
        width: 100%;
        display: inline;
      }

      span {
        position: absolute;
        font-family: MicrosoftYaHeiLobby;
        font-stretch: normal;
        font-style: normal;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 350px;
        font-size: 20px;

        &:nth-child(2) {
          color: #1678ff;
          height: 28px;
          left: 20px;
          text-align: left;
          top: 19px;
          width: 90px;
        }
        &:nth-child(3) {
          color: #333;
          font-weight: 700;
          height: 28px;
          left: 110px;
          text-align: center;
          top: 19px;
          width: 250px;
        }
        &:nth-child(4) {
          color: #1678ff;
          height: 28px;
          right: 20px;
          text-align: right;
          top: 19px;
          width: 90px;
        }
        &:nth-child(5) {
          color: #211f20;
          left: 110px;
          top: 125px;
          width: 350px;
        }
        &:nth-child(6) {
          color: #8f8f8f;
          left: 110px;
          top: 175px;
          width: 350px;
        }
        &:nth-child(7) {
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          color: #8f8f8f;
          display: -webkit-box;
          font-size: 15px;
          left: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          top: 227px;
          vertical-align: middle;
          white-space: inherit;
          width: 440px;
          line-height: 22.5px;
        }
      }
      .ios-add-content-body-tip-h5icon {
        height: 75px;
        left: 18px;
        position: absolute;
        top: 127px;
        width: 75px;
      }
    }
  }

  @-webkit-keyframes spin {
    0% {
      bottom: 0;
    }

    50% {
      bottom: 30px;
    }

    100% {
      bottom: 0;
    }
  }

  @keyframes spin {
    0% {
      bottom: 0;
    }

    50% {
      bottom: 30px;
    }

    100% {
      bottom: 0;
    }
  }
  .ios-add-content-body-img {
    height: 110px;
    position: absolute;
    width: 86.9px;
    transform: translateX(-65%) rotate(180deg);
    left: 50%;
    bottom: 0;
  }
  .imgAnimation {
    animation: spin 0.9s ease-in-out infinite;

    bottom: 0;
  }
}
</style>
<!-- 首页顶部 -->
