<script setup lang='ts' name='relatorio'>
import Conta from './conta.vue';
import Apostas from './apostas.vue';
import Relatorio from './relatorio.vue';
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const { isApp } = storeToRefs(appStore)
//隐藏底下菜单
appStore.setFooterDialogVisble(false)
const currentType = ref('0')
const SECLET_TYPE = readonly({
    Conta: 0,
    Apostas: 1,
    Relatorio: 2,
})

const secletType = ref(SECLET_TYPE.Conta);

const tabData = ref([
    {
        label: 'Conta',
        value: SECLET_TYPE.Conta
    },
    {
        label: 'Apostas',
        value: SECLET_TYPE.Apostas
    },
    {
        label: 'Relatório',
        value: SECLET_TYPE.Relatorio
    },
])

const onTabChange = () => {
    // showToast(secletType.value)
    // secletType.value = 
}


//返回
function clickLeft(){
    appStore.setFooterDialogVisble(true)

}

onMounted(() => {
    if(route.query.key){
        console.log(route.query.key);
        if(route.query.key == SECLET_TYPE.Conta){
            secletType.value = SECLET_TYPE.Conta;
        }else if(route.query.key == SECLET_TYPE.Apostas){
            secletType.value = SECLET_TYPE.Apostas;
        }else if(route.query.key == SECLET_TYPE.Relatorio){
            secletType.value = SECLET_TYPE.Relatorio;
        }
    }
})




</script>
<template>
  <div class="relatorio">
    <AppPageTitle left-arrow title="Relatório" title-weight="700"  @clickLeft="clickLeft"/>
    <div class="tab">
        <AppTab :list-data="tabData" v-model="secletType" @change="onTabChange"></AppTab>
    </div>
    <div class = "relatorio-content" style=" overflow: auto;">
        <Conta v-if="secletType==SECLET_TYPE.Conta"/>
        <Apostas v-else-if="secletType==SECLET_TYPE.Apostas"/>
        <Relatorio v-else-if="secletType==SECLET_TYPE.Relatorio"/>
    </div>

  </div>
</template>

<style lang='scss' scoped>
.relatorio{
    .relatorio-content{
        position: relative;
        min-height:calc(100vh - 163px);
        background-color: var(--theme-bg-color);
    }
}


</style>

<route lang="yaml">
    meta:
      auth: true
  </route>
  