<script setup lang="ts" name="app-find-pwd">
// const router = useRouter()
const appStore = useAppStore();
const { privacyShow } = storeToRefs(appStore);

//关闭界面
const closePage = () => {
  appStore.setShowPrivacy(false);
};
</script>

<template>
  <van-popup
    class="app-login-register"
    v-model:show="privacyShow"
    teleport="body"
    round
    :close-on-click-overlay="false"
  >
    <div class="content">
      <div class="text">
        <div class="title">Acordo do Usuário</div>
        <p class="p1">
          1. Para evitar disputas de apostas, os membros devem ler as regras da
          empresa antes de entrar no aplicativo. Uma vez que o jogador“ Eu
          concordo” Ao entrar nesta empresa para apostar, será considerado que
          você está de acordo com o Acordo do Usuário da empresa.
        </p>
        <p>
          2. É de responsabilidade do membro garantir a confidencialidade de sua
          conta e informações de login. Quaisquer apostas online feitas com o
          seu número de conta e senha de membro serão consideradas válidas. Por
          favor, altere sua senha de tempos em tempos. A empresa não se
          responsabiliza por qualquer compensação de apostas feitas com conta e
          senha roubadas.
        </p>
        <p>
          3. A empresa reserva-se o direito de alterar este acordo ou as regras
          do jogo ou as regras de confidencialidade de tempos em tempos. Os
          termos modificados entrarão em vigor na data especificada após a
          ocorrência da alteração, e o direito de tomar decisões finais sobre
          todas as disputas é reservado à empresa.
        </p>
        <p>
          4. Os usuários devem ser maiores de idade de acordo com as leis do
          país de residência para usar cassino ou aplicativo online. As apostas
          online que não tenham sido submetidas com sucesso serão consideradas
          nulas.
        </p>
        <p>
          5. Quando um jogador é desconectado automaticamente ou forçosamente do
          jogo antes que o resultado do jogo seja divulgado, isso não afetará o
          resultado do jogo.
        </p>
      </div>
      <div class="submit">
        <AppButton
          @click="closePage"
          class=""
          width="300px"
          height="80"
          blue
          :radius="15"
          black-text
          font-size="14px"
          color="var(--theme-primary-font-color)"
        >
          Lido e Compreendido
        </AppButton>
      </div>
    </div>
    <img
      class="app-image img close-icon"
      src="/icons/close_black.png.webp"
      @click="closePage()"
      alt=""
    />
  </van-popup>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.content {
  width: 680px;
  height: 850px;
  border-radius: 16px;
  background-color: var(--theme-main-bg-color);
  border: 2px solid;
  border-color: var(--theme-color-line);
  .text {
    margin-top: 40px;
    text-align: center;
    color: #fff;
    font-size: 22px;
    padding: 20px 40px;
    height: 650px;
    overflow-y: scroll;
    p {
      text-align: left;
      line-height: 35px;
    }
    .p1 {
      margin-top: 40px;
    }
  }
}

.submit {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  bottom: 130px;
}

.close-icon {
  width: 2.1rem;
  border: 5px solid white;
  border-radius: 100%;
  padding: 5px;
  margin: 40px auto;
  display: block;
}
</style>
