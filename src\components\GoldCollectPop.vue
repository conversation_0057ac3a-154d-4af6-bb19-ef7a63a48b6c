<script lang="ts" setup name="AppActivePopup">
const router = useRouter()
const enum JumpViewType {
  PROXY_CASHBACK = 0,
  DEPOSIT_CASHBACK,
  LOSS_CASHBACK,
  BET_CASHBACK,
  DAY_BET_CASHBACK

}

const appStore = useAppStore();
const { isShowGoldCollect} = storeToRefs(appStore);

//跳转到具体界面
function gotoLayer(type: JumpViewType){
  close()
  if(type == JumpViewType.PROXY_CASHBACK){
    router.push("/activity/weekly-proxy-cashback")
  }else if(type == JumpViewType.DEPOSIT_CASHBACK){
    router.push("/activity/weekly-deposit-cashback")
  }else if(type == JumpViewType.LOSS_CASHBACK){
    router.push("/weekly-loss-cashback")
  }else if(type == JumpViewType.BET_CASHBACK){
    router.push("/activity/weekly-bet-cashback")
  }else if(type == JumpViewType.DAY_BET_CASHBACK){
    router.push("/activity/day-bet-cashback")
  }
}

//获取金币
function getMoney(type: JumpViewType){
  if(type == JumpViewType.PROXY_CASHBACK){
    runAchieveProxyWeekDeposit()
  }else if(type == JumpViewType.DEPOSIT_CASHBACK){
    runAchieveWeekDepositReturn()
  }else if(type == JumpViewType.LOSS_CASHBACK){
    runAchieveWeekLostReturn()
  }else if(type == JumpViewType.BET_CASHBACK){
    runAchieveWeekRunningReturn()
  }else if(type == JumpViewType.DAY_BET_CASHBACK){
    runAchieveDayRunningReturn()
  }
}

function close(){
  appStore.setIsShowGoldCollect(false)

}


const proxy = ref(0)
const despoit = ref(0)
const loss = ref(0)
const weekBet = ref(0)
const dayBet = ref(0)

//-------------------------------------------------------------
//数据获取
const { run: runueryAllReturn, data: weekReturnData } = useRequest(() => ApiGetQueryAllReturn(), {
  manual: true,
  onSuccess(res: any) {
    console.log(res)
    proxy.value = weekReturnData.value.week_proxy_commi_return
    despoit.value = weekReturnData.value.week_deposit_return
    loss.value = weekReturnData.value.week_lost_return
    weekBet.value = weekReturnData.value.week_bet_return
    dayBet.value = weekReturnData.value.day_bet_return
  }
})
// runueryAllReturn();

//代理周奖励
const { run: runAchieveProxyWeekDeposit } = useRequest(() => ApiAchieveProxyWeekDeposit(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})

//个人周充值奖励
const { run: runAchieveWeekDepositReturn } = useRequest(() => ApiAchieveWeekDepositReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance()
  }
})

//个人周亏损奖励
const { run: runAchieveWeekLostReturn} = useRequest(() => ApiAchieveWeekLostReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})

//个人周投注奖励
const { run: runAchieveWeekRunningReturn} = useRequest(() => ApiAchieveWeekRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})

//个人日投注奖励
const { run: runAchieveDayRunningReturn} = useRequest(() => ApiAchieveDayRunningReturn(), {
  manual: true,
  onSuccess(res: any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runueryAllReturn();
    appStore.runGetUserBalance();
  }
})


</script>

<template>


  <van-popup v-model:show="isShowGoldCollect" teleport="body" :close-on-click-overlay="false" >
    <div class="content">
      
      <div class="item item4" > 
        <div v-if="dayBet<=0" class="Detalhes" @click="gotoLayer(JumpViewType.DAY_BET_CASHBACK)"> Detalhes </div>
        <div v-else class="money" @click="getMoney(JumpViewType.DAY_BET_CASHBACK)"> R${{ dayBet }} </div>
      </div>

      <div  class="item item1"> 
        <div v-if="proxy<=0" class="Detalhes" @click="gotoLayer(JumpViewType.PROXY_CASHBACK)"> Detalhes </div>
        <div v-else class="money" @click="getMoney(JumpViewType.PROXY_CASHBACK)"> R${{ proxy }} </div>
      </div>

      <!-- <div class="item item2"> 
        <div v-if="despoit<=0"   class="Detalhes" @click="gotoLayer(JumpViewType.DEPOSIT_CASHBACK)"> Detalhes </div>
        <div v-else class="money" @click="getMoney(JumpViewType.DEPOSIT_CASHBACK)"> R${{ despoit }} </div>
      </div> -->

      <div class="item item3"> 
        <div v-if="loss<=0" class="Detalhes" @click="gotoLayer(JumpViewType.LOSS_CASHBACK)"> Detalhes </div>
        <div v-else class="money" @click="getMoney(JumpViewType.LOSS_CASHBACK)"> R${{ loss }} </div>
      </div>

    

      <AppImage class="btns" :src="`/img/activePopuClose.webp`" alt=""  @click="close" />

    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
@import '/src/theme/mixin.scss';


.content {
  //  width:150px;
  //  height:150px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap:wrap;
}

.item{
  background-size: 100% 100%;
  width: 678px;
  height: 140px;
  margin-top: 25px;
}

.item1{
  @include webp("/img/goldCollectPop/semanal");
}
.item2{
  @include webp("/img/goldCollectPop/deposito");
}
.item3{
  @include webp("/img/goldCollectPop/perdas");
}
.item4{
  @include webp("/img/goldCollectPop/apostas");
}



.Detalhes{
  width:240px;
  height:80px;
  background:#000000;
  border-radius:10px;

  font-weight:700;
  color:var(--app-red-color);
  font-size:30px;

  text-align:center;
  line-height: 80px;
  margin-top: 30px;
  margin-left: 420px;
}

.money{
  width:240px;
  height:80px;
  background:var(--app-red-color);
  border-radius:10px;

  font-weight:700;
  color:#ffffff;
  font-size:48px;

  text-align:center;
  line-height: 80px;
  margin-top: 30px;
  margin-left: 420px;
}

.btns {
  
    margin-top: 35px;
    width: 87px;
    height:87px;
  }

</style>
