<script setup lang='ts' name='desempenho'>
import { ProxyTeamData, SendQueryTeamData } from "~/core/http/api"
const question= ref()
const showDatePicker = ref(false)
const showViewType = ref(false)
const inputForce = ref(false)
const currentDate = ref({
  start: dayjs().subtract(0, 'day').format('YYYY/MM/DD'),
  end: dayjs().format('YYYY/MM/DD')
})



const dateStr = computed(() => dayjs(currentDate.value.start).format('YYYY-MM-DD') + ' - ' + dayjs(currentDate.value.end).format('YYYY-MM-DD'))

//查找
const onConfirm = (data: any) => {
  showSelectTime()
  currentDate.value = data;
  refresh();
}

const onCancel=()=>{
  showSelectTime()
}

const refresh = () => {
  pageDatas.value.pageIndex=0;
  finished.value = false
  listTeamData.value.length = 0
  getQueryTeam();
}

const showSelectTime=()=>{
  showViewType.value = !showViewType.value
}

//
const eventFocus=()=>{
  inputForce.value = true
}

const eventBlur=()=>{
  inputForce.value = false
}


//
const getQueryTeam = () => {
  let tempStartTime = currentDate.value.start + " 00:00:00"
  let tempEndTime = currentDate.value.end + " 00:00:00"
  console.log(1715223600 + " --- getBrazilTime  ---" + (getBrazilTime(1715223600 * 1000)) +"dayjs --"+ dayjs(getBrazilTime(1715223600*1000)))
  let starTime1 = dayjs(tempStartTime).utc(true).unix();
  let endTime1 = dayjs(tempEndTime).utc(true).unix();
  console.log("starTime1  " + starTime1 + "starTime1  " + endTime1)
  // runGetQueryTeam({ beginTime: starTime1 + 3600 * 3, endTime: endTime1 + 3600 * 3 + 3600 * 24 -1, pageIndex: pageDatas.value.pageIndex, pageCount:  pageDatas.value.pageCount })
}

const dadosAccuInfo = ref({
  TotalCommi:0,
  team_num:0,
  DepositNum:0,
  ValidNum:0,
  Deposit:0,
  Running:0,
   //新加
  OtherDeposit:0 ,//其他充值金额（2+3级玩家的下级充值总额）
  OtherDepositNum :0,//其他充值人数（2+3级玩家的充值人数）
  OtherRunning:0 //三级总流水
})

const listTeamData = ref<ProxyTeamData[]>([]);
// let i=0
// for( i;i<15; i++){
//   const temp:ProxyTeamData={
//     uid:1112222,
//     DonateToParent: 41144,//对父级的贡献
//     first_deposit_mount: 4544444,//首冲金额
//     running: 455444,//总流水
//     created_at: 148851444,//绑定时间 
//     DonateTime: 1111111,//
//   }

//   listTeamData.value.push(temp)
// }


const finished = ref(false);
const pageDatas = ref({
  pageIndex:0,
  pageCount:30,
});

const loadMoreData = () => {
  if(pageDatas.value?.pageIndex == 0 || pageDatas.value?.pageIndex * pageDatas.value?.pageCount < queryTeamDatas.value?.TotalCount){
    getQueryTeam();
    pageDatas.value.pageIndex++
  }
  
}

//
const {run: runGetQueryTeam,data:queryTeamDatas, loading:queryTeamLoading } = useRequest((datas:SendQueryTeamData) => ApiGetQueryTeam(datas), {
  manual: true,
  onSuccess(res:any) {
    dadosAccuInfo.value = res?.TAccuInfo!;
    if(res?.ListTeamData){
      listTeamData.value = [...listTeamData.value, ...res?.ListTeamData!]
    } 
    if(listTeamData.value.length >= res?.TotalCount){
      finished.value = true;
    } else {
      finished.value = false;
    }
   
  },
  onError(data:any){
    showErrorTip(data)
  }
  
})


</script>
<template>
  <div class="todpsOsDayos">
    <div class="content">
      <!-- 查下框 -->
      <div class="invite-query">
        <div class="invite-query-input">
            <div class="date" @click="showSelectTime">{{ dateStr }}</div>
            <div class="searchParent" :class="{active:inputForce}">
                <input v-model="question"  class="search" placeholder="ID de Membro"    @focus="eventFocus"  @blur="eventBlur"/>
                <AppImage class="select-img" src="/icons/invite_search1.webp" alt="" @click="onConfirm" maxlength="30" />
            </div>
        </div>
      </div>
      <!--时间选择框  -->
      <AppDatePickerNew class="selectTime" v-show="showViewType" v-model="showDatePicker" :startDate="currentDate.start" :endDate="currentDate.end" group @confirm="onConfirm" @cancel="onCancel" />
      <!-- 查询列表 DADOS -->
      <div class="invite-list">
        <AppList class="app-List" :loading="queryTeamLoading" :finished="finished" @refresh="refresh" @loadMoreData="loadMoreData"
          style="padding-top: 15px;">
          <div class="invite-list-item" v-for="item in listTeamData" :key="item.uid">
            <div class="invite-list-item-body">
              <div class="invite-list-item-body-vip">  
                  <label class="invite-list-item-body-vipText">V0</label>
              </div>
              <div class="invite-list-item-body-id">  
                <label>{{ item.uid }}</label>
                <AppImage src="/icons/agent_todos_copy" class="invite-list-item-body-id-copy" />
              </div>

              <div class="invite-list-item-body-time">  
                <label>{{ dayjs(getBrazilTime(item.created_at*1000)).format('YYYY-MM-DD HH:mm:ss') }}</label>
              </div>
              <div class="invite-list-item-body-apos">  
                <label >Apostas Válidas</label>  <!--水流-->
                <label class="invite-list-item-body-apos-num">{{ item.running }}</label>
              </div>

              <div class="invite-list-item-body-dep">   
                <label >Depositar</label>  <!--充值-->
                <label class="invite-list-item-body-apos-num">{{ item.running }}</label>
              </div>
            </div>
          </div>
        </AppList>
      </div>

      <!--背景图标  -->
      <!-- <div class="onne_jl" v-show="listTeamData.length==0" >
        <AppImage class="onne_jl_img" src="/icons/agent_none_jl.webp" alt=""  />
      </div> -->
      <app-empty class="onne_jl"  text="Sem feedback" v-show="listTeamData.length==0"></app-empty>
    </div>


  </div>





</template>

<style lang='scss' scoped>


.todpsOsDayos{
   width: 100%;
   height:100%;
   font-size:23px;
}

.content {
  position: absolute;
  width: 100% ;
  height:100% ;
  // background-color: #FFF;

}


.invite-query{
  margin-top: 20px;
  // background-color: aliceblue;
  .invite-query-input{
    margin-left: 20px;
    width:356px;
    height:52px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:26px;
    .date{
      display: inline-block;
      color:var(--theme-text-color-lighten);
      width: 100%;
      height: 100%;
      text-align: center;
      padding-top: 12px;
      // padding-left: 20px;
    }
  }
  .searchParent{
    position: absolute;
    width:210px;
    height:52px;
    background:var(--theme-main-bg-color);
    border:1px solid;
    border-color:var(--theme-color-line);
    border-radius:26px;
    
    margin-top: -52px;
    margin-left: 380px;

    &.active{
      border-color:#FFF0BB;
    }

    .search{
      display: inline;
      margin: 0 auto;
      padding-left: 20px;
      width:140px;
      height:52px;
      background-color: transparent;
      color: #FFFFFF;
     
    }

    .search::placeholder {
      color:var(--theme-text-color-lighten);
      font-size:22px;
    }

    .select-img{
      position: absolute;
      width: 27px;
      top:13px;
      right:20px;
    }

  }

}

.selectTime{
  position: absolute;
  margin-top: 0px; 
  // height: 100px;
  z-index: 1
}

.onne_jl{
  width: 100%;
  height: 450px;
  // background-color: #FFFFFF;
  position: absolute;
  top: 30%;

  // .onne_jl_img{
  //   position: absolute;
  //   transform: translate(-50%,-50%);
  //   left:50%;
  //   top:50%;
  //   width: 300px;
  // }
}


//列表
.invite{
  &-list{
    width: 705px;
    margin: 0 auto;
    // margin-top: 20px;
    height: calc(100% - 100px);
    // background-color: #FFF;
    .app-List{
      width: 100%;
      height: 100%;
    }
    &-item{
      width: 100%;
      height: 108px;
      margin-bottom: 10px;
      background-color:var(--theme-main-bg-color);

      border-radius:14px;
      color: #FFFFFF;
      font-size: 21px;
      &:nth-child(2n){
        background-color: var(--theme-bg-color);
      }
      &-body{
        width: 100%;
        height: 100%;
        // display: flex;
        // padding-top: 25px;
        &-vip{
          width: 36px;
          height:26px;
          background:#fe5049;
          border-radius:14px 0px 4px 0px;
        }
        &-vipText{  
            display: inline-block;
            font-size: 16px;
            width: 100%;
            height: 100%;
            text-align: center;
            color:#f8fb52;
        }
        // -----------------
        &-id{
          display: inline-block;
          margin-left: 20px;
          margin-top: 3px;
          &-copy{
            position: absolute;
            width: 23px;
            margin-left:25px;
          }
        }
        // time
        &-time{
          position: absolute;
          margin-left: 20px;
          margin-top: 3px;
          color:var(--theme-text-color-lighten);
        }
        //apos
        &-apos{
          position: absolute;
          margin-left: 400px;
          margin-top: -22px;
          color:var(--theme-text-color-lighten);
          &-num{
            padding-left: 10px;
            color:#FFF;
          }
        }
        //dep 
        &-dep{
          position: absolute;
          margin-left: 400px;
          margin-top: 5px;
          color:var(--theme-text-color-lighten);
          &-num{
            padding-left: 10px;
            color:#FFF;
          }
        }
      }
    }
  }
  &-list-empty{
    width: 702px;
    height: 702px;
    margin: 0 auto;
    margin-top: 74px;
    position: relative;
    img{
      width: 702px;
    }
    p{
      position: absolute;
      top: 511px;
      left: 275px;
      font-family:Arial;
      color:#9aa0b5;
      font-size:30px;
    }
  }
   
}



</style>
<style lang='scss'>
// :root {
  // --van-picker-background: var(--theme-main-bg-color);
  // --van-picker-mask-color: #324b6e;
  // --van-picker-option-text-color: #fff;
  
  // --van-picker-confirm-action-color: #679fea;
  // --van-picker-cancel-action-color: #FFFFFFB2;

  // --van-popup-background: #324b6e;
  // --van-picker-group-background: var(--theme-main-bg-color);
  
  // [class*=van-hairline]:after {
  //   position: absolute;
  //   box-sizing: border-box;
  //   content: " ";
  //   pointer-events: none;
  //   top: -50%;
  //   right: -50%;
  //   bottom: -50%;
  //   left: -50%;
  //   border: 1px solid #FFFFFF40;
  //   border-left: none;
  //   border-right: none;
  //   transform: scale(.5);
  // }
// }
</style>
