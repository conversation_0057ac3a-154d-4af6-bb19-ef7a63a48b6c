<script setup lang="ts" name="app-banner">
const appStore = useAppStore();

interface BannerObj {
  src: string
}

interface Props {
  listData: BannerObj[]
}

withDefaults(defineProps<Props>(), {})

const { data: list } = useRequest(ApiGetActivepopup,{
    onSuccess(data){
      if(data && data.length>0){
        
      }
        
    }
  })

const banners = computed(() => {
  if (list.value && list.value.length) {
    return list.value.map((i) => {
      let images = i.images.split('&')
      let h5str = images.filter((m: any) => m.indexOf('h5=') != -1)[0]
      i.redirect_url = decodeURIComponent(decodeURIComponent(i.redirect_url))
      if (h5str) {
        i.h5img = h5str.split('=')[1]
      }
      return i
    })
  }
  return []
})

const close = () => {
  appStore.setActiveDialogVisble(false)
}
</script>

<template>
  <div class="app-banner">
    <van-swipe :autoplay="3000" :duration="1000">
      <van-swipe-item v-for="(item, index) in banners" :key="index">
        <RouterLink :to="item.redirect_url" class="link" @click="close">
          <div class="img2"
            :style="{ backgroundImage: isFullPath(item.h5img || '') ? item.h5img : `url(${brazilImg + item.h5img})` }">
          </div>
        </RouterLink>
      </van-swipe-item>


      <template v-if="banners && banners.length > 1" #indicator="{ active }">
        <div class="custom-content">
          <span v-for="(item, i) in banners" :key="i + item.h5img" :class="{
            active: active === i
          }" class="custom-indicator">
          </span>
        </div>
      </template>
    </van-swipe>
  </div>
</template>

<style lang="scss" scoped>
.app-banner {
  width: 690px;
  height :850px;


  overflow: hidden;
  .van-swipe{
    width: 690px;
    height: 850px;
  }
  .custom-content {
    position: absolute;
    bottom:15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    z-index: 1000;

    .custom-indicator {
      width: 16px;
      height: 16px;
      background: var(--app-blue-color);
      transition: all .3s;
      border-radius: 100px;
      margin: 0 4px;

      &.active {
        width: 46px;
        background: var(--app-red-color);
      }
    }
  }


  .link {
    display: block;
    width: 100%;
    height: 280px;



    .img2 {
      width: 690px;
      height: 800px;
      background-repeat: repeat;
      background-position: center;
      background-size: cover;
      border-radius: 20px;

      // width: 100%;
      // height: 100%;
      // background-repeat: no-repeat;
      // background-position: center;
      // background-size: cover;
      // border-radius: 20px;
    }

  }
}
</style>
