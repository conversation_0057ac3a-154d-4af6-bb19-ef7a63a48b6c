<script lang="ts" setup name="BindPayPwd">
import { showFailToast } from 'vant'

// const countdowm = ref(0);

const router = useRouter()

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)

const oldPwdRef = ref()
const newPwdRef = ref()
const renewPwdRef = ref()

const hasPayPwd = computed(() => userInfo.value && userInfo.value.pay_password && +userInfo.value.pay_password === 1)

const formdata = reactive({
  oldPwd: '',
  newPwd: '',
  renewPwd: '',
})

const confirmFn = () => {
  if (hasPayPwd.value && !oldPwdRef.value.validation()) {
    showFailToast('Digite a senha de pagamento antiga')
    return
  }
  if (formdata.newPwd.trim() !== formdata.renewPwd.trim()) {
    showFailToast('A nova senha e a senha de confirmação devem ser iguais')
    return
  }
  if (newPwdRef.value.validation() && renewPwdRef.value.validation()) {
    router.push({ path: '/safe-center/verify-code', state: { payPwd: formdata.newPwd, oldPayPwd: formdata.oldPwd }, query: { type: 'paypwd' } })
  }
}

const renewPwdChange = (v: any) => {
  newPwdRef.value?.validation()
}

</script>

<template>
  <div class="bind-pay-pwd">
    <AppHeader left-arrow title="Senha de pagamento" placeholder />
    <section class="content">
      <div class="top"></div>
      <template v-if="hasPayPwd">
        <AppInput
          ref="oldPwdRef"
          v-model="formdata.oldPwd"
          :pattern="payPasswordReg"
          msg="Senha (6 letras e números)"
          placeholder="Digite sua senha atual."
          icon-left="icon_password"
          icon-with="38"
          width="650"
          height="75"
          type="payPassword"
          err-height="34"
          :max-length="6"
          clearable
          :style-obj="{
            margin: '0 auto',
            background:'#192841',
            color: '#fff'
          }"
        />
        <div class="divider"></div>
      </template>
      <AppInput
        ref="newPwdRef"
        v-model="formdata.newPwd"
        placeholder="Por favor insira uma nova senha"
        icon-left="icon_password_1"
        icon-with="35" 
        width="650"
        height="75"
        type="payPassword"
        err-height="34"
        :max-length="6"
        clearable
        :rules="[
          { pattern: payPasswordReg, msg: 'Senha (6 letras e números)' },
          // { validator: (val: any) => val === formdata.renewPwd, msg: 'A nova senha e a senha de confirmação devem ser iguais' }
        ]"
        :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"
      />
      <div class="divider"></div>
      <AppInput
        ref="renewPwdRef"
        v-model="formdata.renewPwd"
        :rules="[
          { pattern: payPasswordReg, msg: 'Senha (6 letras e números)' },
          { validator: (val: any) => val === formdata.newPwd, msg: 'A nova senha e a senha de confirmação devem ser iguais' }
        ]"
        placeholder="Confirme a nova senha"
        icon-left="icon_password_2"
        icon-with="35" 
        width="650"
        height="75"
        type="payPassword"
        err-height="34"
        :max-length="6"
        clearable
        :style-obj="{
          margin: '0 auto',
          background:'#192841',
          color: '#fff'
        }"
        @change="renewPwdChange"
      />
      <div class="divider"></div>
      <!-- <AppInput icon-with="32" 
        ref="codeRef"
        :pattern="verifyCodeReg"
        placeholder="Código de verificação"
        msg="Erro no código de verificação"
        icon-left="reg-code"
        clearable
        width="710"
        :style-obj="{
          borderRadius: 'var(--app-px-8)',
          background:
            'linear-gradient(0deg, #011A51 0%, #011A51 100%), #2A2E3E',
          border: 'var(--app-px-1) solid rgba(255, 255, 255, 0.10)',
        }"
      >
        <template #right>
          <div :style="{ paddingRight: 'var(--app-px-18)' }">
            <AppButton
              @click=""
              fontSize="26"
              radius="46"
              whiteText
              yellowToRight
              bold
              width="130"
              height="46"
              center
              >{{ countdowm > 0 ? countdowm + "s" : "Mandar" }}
            </AppButton>
          </div>
        </template>
      </AppInput> -->
      <div class="desc">
        * Insira 6 números. não diferencia maiúsculas de
        minúsculas. (caracteres chineses não permitidos)
      </div>
      <div class="btn-box">
        <AppButton
          fontSize="36"
          radius="15"
          whiteText
          blue
          width="240"
          height="80"
          center
          @click="confirmFn"
          >Enviar</AppButton
        >
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.top{
  width: 710px;
	height: 81px;
	background-color: #324b6e;
	border-radius: 50px 50px 0px 0px;
  margin-bottom: 25px;
}
.desc {
  color: #fff;
  font-size: 30px;
  font-weight: 400;
  line-height: 40px;
  padding: 20px 24px;
}
.btn-box {
  // padding-top: 28px;
}
.divider {
  height: 40px;
}
.bind-pay-pwd {
  .content {
    margin: 0 auto;
    width: 710px;
    height: 724px;
    background-color: #28374d;
    border-radius: 50px;
    margin-top: 40px;
    h3 {
      color: #fff;
      font-size: 28px;
      font-weight: 400;
      padding: 30px 0 28px;
      margin: 0;
    }
  }
}
</style>
