<script setup lang="ts" name="app-single-slots-container">
import { GameTypeEnum, GameNavEnum } from '~/types/common';
interface Props {
  id: GameNavEnum
  filterType?: string
  favType?: string
}
const props = defineProps<Props>()
const scrollView = ref()
const router = useRouter()

const list = ref([
  { img: '/img/pg1.webp',platform_id: GameTypeEnum.platform_pg, key: 0},
  { img: '/img/pp1.webp',platform_id: GameTypeEnum.platform_pp, key: 1},
  { img: '/img/tada1.webp',platform_id: GameTypeEnum.platform_tada, key: 2},
  { img: '/img/yb1.webp',platform_id: GameTypeEnum.platform_yesbingo, key: 4},
  { img: '/img/fc1.webp',platform_id: GameTypeEnum.platform_fc, key: 5},
  { img: '/img/jdb1.webp',platform_id: GameTypeEnum.platform_jdb, key: 8},
])

function gotoLeft(){
  scrollView.value?.scrollTo(scrollView.value.scrollLeft-scrollView.value.clientWidth,100);
}

function gotoRight(){
  scrollView.value?.scrollTo(scrollView.value.scrollLeft+scrollView.value.clientWidth,100);
}

</script>

<template>
  <div class="app-single-slots-container">
    <AppIndexTitle :id="id" @gotoLeft-by="gotoLeft" :isCallback="false" @gotoRight-by="gotoRight" />
    <div class="app-maps game-container">
      <div ref="scrollView" class="content">
        <AppSingleSlotItem v-for="item in list" :data="item" class="slot-item" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-maps.game-container {
  margin-left: -12.5px;
  margin-right: -12.5px;
  border-radius: 0;
}
.app-maps {
  border-radius: 0px 30px 30px 30px;
  padding: 0 25px;
  padding-bottom: 8px;

  .content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: auto;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style> 