<script setup lang='ts' name='promotion'>
import LinkDeConvite from './invitation-linkDeConvite.vue';
import MeusDados from './invitation-meusDados.vue';

const router = useRouter()
const appStore = useAppStore()
const { isApp } = storeToRefs(appStore)
//隐藏底下菜单
appStore.setFooterDialogVisble(false)
const currentType = ref('0')
const SECLET_TYPE = readonly({
    Convite: 0,
    MenuDados: 1,
    TodosOsDados: 2,
})

const secletType = ref(SECLET_TYPE.Convite);

const tabData = ref([
    {
        label: 'Link de convite',
        value: SECLET_TYPE.Convite
    },
    {
        label: 'Meus dados',
        value: SECLET_TYPE.MenuDados
    },
    {
        label: 'Todos os dados',
        value: SECLET_TYPE.TodosOsDados
    },
])

const onTabChange = () => {
    // showToast(secletType.value)
    // secletType.value = 
}


//返回
function clickLeft() {
    appStore.setFooterDialogVisble(true)

}
</script>
<template>
    <div class="agente">
        <AppPageTitle left-arrow title="Indique um amigo e ganhe R$ 10 de bônus" 
            @clickLeft="clickLeft" />
        <div class="agente-content">
            <div class="content">
                <LinkDeConvite v-if="secletType == SECLET_TYPE.Convite" />
                <!-- <MeusDados v-else-if="secletType == SECLET_TYPE.MenuDados" />
                <div v-else-if="secletType == SECLET_TYPE.TodosOsDados" class="content-todososdados"></div> -->
            </div>
        </div>

    </div>
</template>

<style lang='scss' scoped>
.agente {
    .agente-content {
        position: relative;
        height: calc(100vh - 95px);
        overflow: var(--overflow-value);

        .content {
            position: absolute;
            width: 100%;
            // height: 100%;
            background-color: var(--theme-bg-color);
        }
    }
}
</style>

<route lang="yaml">
    meta:
      auth: true
  </route>