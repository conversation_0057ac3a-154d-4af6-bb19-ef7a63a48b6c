<script setup lang="ts" name="app-login-register">
const appStore = useAppStore();

const {
  loginDialogVisible,
  loginDialogType,
} = storeToRefs(appStore);

enum TabActive {
  login = 'login',
  register = 'register'
}
const tabData = ref([
  {
    label: 'Entrar',
    value: TabActive.login
  },
  {
    label: 'Cadastre-se',
    value: TabActive.register
  }
])

const onLogoClick = ()=>{
  loginDialogVisible.value = false;
  console.log("点击logo")
}

const getWeekDate = (lastWeek:boolean=false)=>{

    const startOfWeek =  dayjs().startOf('Week').add(1,"day")
    const endOfWeek =  dayjs().endOf('Week').add(1,"day")
    console.log("getWeekDate   ---------------" ,  startOfWeek.format('YYYY-MM-DD h:mm:ss') + "~" + endOfWeek.format('YYYY-MM-DD h:mm:ss'))
    if(lastWeek){
      return dayjs().startOf('Week').add(1,"day").format('YYYY-MM-DD h:mm:ss') + "~"+ dayjs().endOf('Week').add(1,"day").format('YYYY-MM-DD h:mm:ss')
    } else {
      return dayjs().startOf('Week').subtract(6,"day").format('YYYY-MM-DD h:mm:ss') + "~" + dayjs().endOf('Week').subtract(6,"day").format('YYYY-MM-DD h:mm:ss')
    }
  }
  console.log(getWeekDate())


defineProps({
  logoClick: Function,
})


</script>

<template>
  <van-popup class="app-login-register" close-icon="cross" v-model:show="loginDialogVisible" position="bottom" teleport="body" :style="{height:'100%',width:'100%'}">
    <div class="content">
      
<!--      <div class="title">-->
<!--        &lt;!&ndash; <AppLoginTab :list-data="tabData" v-model="loginDialogType" :height="110" active-color="#F43F5E" /> &ndash;&gt;-->

<!--        <AppIndexHeader :logo-click="onLogoClick">-->
<!--          <template #logoDev>-->
<!--            <a class="logo" @click="onLogoClick">-->
<!--              &lt;!&ndash; <AppImage class="img" src="/icons/logo.png" alt="" /> &ndash;&gt;-->
<!--              <AppImage class="img" src="/icons/close_black.png.webp" alt="" />-->
<!--            </a>-->
<!--          </template>-->

<!--          <template #right>-->
<!--            &lt;!&ndash; <van-tabs v-model:active="loginDialogType" type="card" :ellipsis="false" color="#f43f5e" background="#829aab" title-inactive-color="#FFFFFF">-->
<!--              <van-tab :title="tabData[0].label" :name="TabActive.login"></van-tab>-->
<!--              <van-tab :title="tabData[1].label" :name="TabActive.register"></van-tab>-->
<!--            </van-tabs> &ndash;&gt;-->
<!--            <AppLoginTab :list-data="tabData" v-model="loginDialogType" :height="76" />-->
<!--          </template>-->
<!--        </AppIndexHeader>-->
<!--      </div>-->

<!--      <AppImage class="loginTitle" :src="`/img/login_title.webp`" alt=""  />-->

      <!-- <div class="login-img">
        <AppImage src="/icons/logo_login.png" alt="" />
      </div> -->
      <div class="login-or-register-box">
        <div v-show="loginDialogType === TabActive.login">
          <AppLogin/>
          <img class="app-image img close-icon" src="/icons/close_black.png.webp" @click="loginDialogVisible = false" alt="" >
        </div>
        <div v-show="loginDialogType === TabActive.register">
          <AppRegister/>
          <img class="app-image img close-icon" src="/icons/close_black.png.webp" @click="loginDialogVisible = false" alt="" >
        </div>

      </div>

    </div>
  </van-popup>
</template>


<!-- <style lang="scss">
[theme='blue']:root {

}
</style> -->
<style lang="scss" scoped>
@import '../theme/mixin.scss';
.login-or-register-box{
  display: flex;
  justify-content: center;
  align-content: center;
  height: 100%;
  >div{
    justify-content: center;align-content: center;
  }
  .app-login{
    background-color: var(--theme-main-bg-color);
    border: thin solid;
    border-color: var(--theme-color-line);
    border-radius: 15px;
    width: 680px;
  }
  .close-icon{width: 60px;border:5px solid white;border-radius: 100%;padding: 5px;margin: 40px auto;
    display: block;}
}
.logo {
  width: 146px;
  display: inline-block;
  margin-left: 40px;
  .img {
    width: 60px;
    vertical-align: middle;
  }
}



.content {
  width: 100%;
  height: calc(100% - var(--app-navbar-height));
  position: relative;
  padding-top: var(--app-navbar-height);
  font-family: Arial;
  // background-color: var(--app-page-bg-color);
  .bg-img {
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    // @include webp('/img/bg_sign_in.png');
    background-color: var(--app-page-bg-color);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &-active{
      transform: scaleX(-1);
    }
  }
  .title {
    // padding: 20px 0;
  }

  .login-img {
  margin-top: 30px;
  text-align: center;
  // padding: 0 0 50px 0;

  img {
    scale: 0.6;
    // width: 186px;
    // height: 88px;
    vertical-align: middle;
  }
 }

 .loginTitle{
    width: 750px;
    // height: 363px;
 }


}




</style>
