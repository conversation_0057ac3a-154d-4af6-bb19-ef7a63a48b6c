<script lang="ts" setup name="withdraw">
import { reactive ,ref} from 'vue';
import { showErrorTip } from '~/utils/utils';

import arrowIcon from "/icons/svg/arrow.svg?raw";
const router = useRouter()
const appStore = useAppStore()
const { pay_password ,userInfo, userBanks, cardLimit} = storeToRefs(appStore)
const showLoadingSpin = ref(false);
const showTarefasdeLoadingSpin = ref(false);
const showRegistroLoadingSpin = ref(false);
const TarefasdeApostas = ref();
const ContasAReceber = ref();
const showSelPix = ref(false);
//------------------------------------------------------------------------------------------------------------------------

const sortedBanks = computed(() => {
  return userBanks.value.sort((a, b) => b.state - a.state);
})

//设置提现密码
const passwordSettingFormData = reactive({
  pwd1:"",
  pwd2:"",
})

//设置提现密码
const setWithdrawPwd = ()=>{
  if(passwordSettingFormData.pwd1 !== passwordSettingFormData.pwd2){
    return showToast('As senhas inseridas não coincidem');
  }

  if( passwordSettingFormData.pwd1==""){
    showToast('A senha não pode estar vazia');
    return 
  }
  runApiUpdatePayPwd()
}

//设置提现密码
const { run: runApiUpdatePayPwd, loading: setLoading } = useRequest(() => ApiUpdatePayPwd({update_type:1,ty:1,sid:"",ts:"",code:"",
                                                                              confirm_password:passwordSettingFormData.pwd1,password:passwordSettingFormData.pwd2 ,old:"" }), {
  manual: true,
  onSuccess(data: string) {
    appStore.setPay_password(passwordSettingFormData.pwd1)
    
    showToast("Estabelecer com sucesso");
  },
  onError(resData){
    showErrorTip(resData)
  }
})
// appStore.setPay_password("1")
//-----------------------------------------------------------------------------------------------------------------------------
//提现手续费比例
const costRatio = ref(0)//手续费比例
const costRatioMoney = ref(0)//手续费 
const havaBetMoney = ref(0)//new 还需打码  未解锁金额
const withdraw_max_limit = ref(0)//最大提现
const withdraw_min_limit = ref(0)//最小提现
const tabActive = ref(0); 

const {run: runGetWithdrawConf } = useRequest(ApiGetWithdrawConf,{
    onSuccess(data) {
      // console.log(data)
      if(data){
        havaBetMoney.value = data.unfinished_tot_amount || 0;
        costRatio.value = data.withdrawal_fees/100
        withdrawFormData.cpf = data.pix_id ?  data.pix_id: ""
        withdraw_max_limit.value = data.withdraw_max_limit
        withdraw_min_limit.value = data.withdraw_min_limit
        withdrawFormData.fid = data.config.fid
      }
      
    },
    onError(resData){
      
    }
})

//提现金额
const withdrawFormData = reactive({
  cpf:"",
  amount:"",
  pwd:"",
  fid:"",
})

//提现手续费计算
watchEffect(async () => {
  // console.log(withdrawFormData.amount.replace(/[^\d]/g,''))
  //不需要小数点
  // withdrawFormData.amount = withdrawFormData.amount.replace(/[^\d]/g,'')
  withdrawFormData.amount =parseInt(withdrawFormData.amount)+""

  if(parseFloat(withdrawFormData.amount) < withdraw_min_limit.value){
    // withdrawFormData.amount= withdraw_min_limit.value.toString()
  }else if(parseFloat(withdrawFormData.amount) > withdraw_max_limit.value ){
    withdrawFormData.amount= withdraw_max_limit.value.toString()
  }

  //判断当前可以提现
  if(userInfo.value.unlock_amount){
    if(parseFloat(withdrawFormData.amount)>userInfo.value.unlock_amount){
      withdrawFormData.amount=userInfo.value.unlock_amount.toString()
    }
  }
 
  costRatioMoney.value = isNaN(parseFloat(withdrawFormData.amount)*costRatio.value) ? 0: parseFloat(withdrawFormData.amount)*costRatio.value
  costRatioMoney.value = parseFloat(costRatioMoney.value.toFixed(2))

})

//跳转到打码记录界面
function toDetails(){
  router.push('/record-list/alreadybet')
}

//打开找回支付密码
function openFindPwd(){
  appStore.setFindPayPasswordDialogVisible(true)
}

const cpf = ref("");
const isShow = ref(false);
//提现
const withdrawMoney=()=>{
  
  if(!showPixValue.value?.pix_account){
    openConta();
  } else if(showPixValue.value?.pix_account!=""  && withdrawFormData.amount!=""  && withdrawFormData.pwd!="" ){
      if(parseFloat(withdrawFormData.amount) < withdraw_min_limit.value){ //提现金额要大于最小值
        showToast("O valor da retirada deve ser superior a "+UsAmountFormat(withdraw_min_limit.value))
        return
      }

      // if(withdrawFormData.cpf.length !=11){
      //   showToast("CPF deve ser um número de 11 dígitos")
      //   return
      // }

    withdrawFormData.amount =parseInt(withdrawFormData.amount)+""
    cpf.value = showPixValue.value?.pix_account;
    appStore.setBindCPFInfoVisible(true,showPixValue.value.flag == 1? showPixValue.value?.pix_account:"");
    
   
  
    // postWithdraw()
  } else {
    
    showToast("Insira o valor correto")
  }
}

//绑定银行卡
const { run: runApiBindBank,  } = useRequest(() => ApiBindBank({pix_id:withdrawFormData.cpf,pix_account:withdrawFormData.cpf,flag:"1"}), {
  manual: true,
  onSuccess(data: string) {
    postWithdraw()
  },
  onError(data){
    showErrorTip(data)
  }
})
	
//提现
const { loading: setWitdhraw,run:postWithdraw } = useRequest(() => ApiPostWithdraw({
  amount: withdrawFormData.amount,
  bank_id: showPixValue.value.pix_account,
  fid:  withdrawFormData.fid,
  pay_password:  withdrawFormData.pwd,
  flag:showPixValue.value.flag+""
}), {
  manual: true,
  onError: (data) => {
    console.log(data)
    showErrorTip(data)
  },
  onSuccess: (data) => {
    appStore.runGetUserBalance()
    runGetWithdrawConf()
    withdrawFormData.pwd = ""
    withdrawFormData.amount =""
    showToast("Retirada bem-sucedida")
  }
})


appStore.setFooterDialogVisble(false)

onBeforeUnmount(() => {
  appStore.setFooterDialogVisble(true)
})
const lineWidth = ref(80)
const onClickTab = ({ title }) => {
  console.log(title)
  switch (title){
    case "Saque":
      lineWidth.value = 80;
      break;
    case "Registro de Saques":
      lineWidth.value = 210;
      runUserWitdrawList();
      break;
    case "Tarefas de Apostas":
      lineWidth.value = 204;
      runGetFlowRecordList();
      break;
    case "Conta de Retirada":
      lineWidth.value = 186;
      break;
  }
  
}

const tabTitleStyle = {
  height: `var(--app-px-80)`,
  lineHeight: `var(--app-px-80)`,
  marginRight: `var(--app-px-60)`,
  fontSize: `var(--app-px-24)`,
  marginLeft: `var(--app-px-0)`,
}

const setLoadingSpin = ()=>{
  runGetWithdrawConf();
  appStore.runGetMemberInfo();
  showLoadingSpin.value = true
  setTimeout(() => {
    showLoadingSpin.value = false
  }, 1500);
}

const setRegistroLoadingSpin = ()=>{

runUserWitdrawList();
showRegistroLoadingSpin.value = true
setTimeout(() => {
  showRegistroLoadingSpin.value = false
}, 1500);
}

const setTarefasdeLoadingSpin = ()=>{
  runGetFlowRecordList();
  showTarefasdeLoadingSpin.value = true
  setTimeout(() => {
    showTarefasdeLoadingSpin.value = false
  }, 1500);
}

const showContaLoadingSpin = ref(false);
const setContaLoadingSpin = ()=>{
  appStore.runGetUserBanks()
  showContaLoadingSpin.value = true
  setTimeout(() => {
    showContaLoadingSpin.value = false
  }, 1000);
}

const selectIndex = ref(2);
let actions = [{ text: "Hoje", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Ontem", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 7 Dias", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 15 Dias", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 30 Dias", calssName: "5", color: selectIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Tudo", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]

const tarefasSelectIndex = ref(1);
let tarefasActions = [{ text: "Tudo", calssName: "1", color: tarefasSelectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Não Começou", calssName: "2", color: tarefasSelectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Em andamento", calssName: "3", color: tarefasSelectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Completado", calssName: "4", color: tarefasSelectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
]


let saqueActions = [ 
]

const showPopover = ref(false);
const selText = ref("Hoje")
const onSelect = (action: any) => {
    console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selText.value = action.text;
   
    selectIndex.value = Number(action.calssName)
    runUserWitdrawList();
}

const showTarefasPopover = ref(false);
const selTarefasText = ref("Tudo")
const onTarefasSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selTarefasText.value = action.text;
   
    tarefasSelectIndex.value = Number(action.calssName)
    runGetFlowRecordList();
}

const showSaquePopover = ref(false);
const selSaqueText = ref("Hoje")
const selectSaqueIndex = ref(2);
const onSaqueSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    // showToast(action.text);
    selSaqueText.value = action.text;
   
    selectSaqueIndex.value = Number(action.calssName)

}
const {run: runGetFlowRecordList,data:promoInspectionDatas } = useRequest(() => ApiGetFlowRecordList({QueryType:tarefasSelectIndex.value}), {
  manual: true,
  onSuccess(res:any) {
    if(promoInspectionDatas.value) {
    
      for(let i = promoInspectionDatas.value.length - 1; i >= 0; i--) {
        if(promoInspectionDatas.value[i].Status == 1){
          promoInspectionDatas.value[i].ChangeMoney = 0;
        }
      }
      for(let i = promoInspectionDatas.value.length - 1; i >= 0; i--) {
        if(promoInspectionDatas.value[i].Status == 0){
          promoInspectionDatas.value[i].Status = 3;
          break;

        }
      }

    }
    
    // console.log(res,promoInspectionDatas.value)
    // promoInspectionDatas.value=[{ID:"123",ChangeMoney:112221,Status:1},{ID:"123456789123456789",ChangeMoney:112221,Status:1}] //测试
  }
})

const popoverStatus = (isOpen: boolean) => {
    if(!isOpen) {
        actions = [{ text: "Hoje", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Ontem", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 7 Dias", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 15 Dias", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 30 Dias", calssName: "5", color: selectIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Tudo", calssName: "0", color: selectIndex.value == 0 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]
        // console.log("popoverStatus: " + JSON.stringify(actions))
        // console.log("popoverStatus is " + isOpen + "showPopover is " + showPopover.value);

    }
    
}

const tarefasPopoverStatus = (isOpen: boolean) => {
    if(!isOpen) {
      tarefasActions = [{ text: "Tudo", calssName: "1", color: tarefasSelectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      { text: "Não Começou", calssName: "2", color: tarefasSelectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      { text: "Em andamento", calssName: "3", color: tarefasSelectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      { text: "Completado", calssName: "4", color: tarefasSelectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
      ]
        // console.log("popoverStatus: " + JSON.stringify(actions))
        // console.log("popoverStatus is " + isOpen + "showPopover is " + showPopover.value);

    }
    
}
const refresh = () => {
  // formQuery.page = 1
  // finished.value = false
  // list.value.length = 0
  // run()
}

const loadMoreData = () => {
  // formQuery.page++
  // run()
}

const copyID = (id:string) => {
    copy(id || '')
    showToast('Copied!')
}
const listWithdraw = ref<any[]>([])
const finished = ref(false)
//提现记录
const {loading: loading2, run : runUserWitdrawList } = useRequest(() => UserWitdrawList({ flag: selectIndex.value + ""}), {
  onSuccess: (res:any) => {
    listWithdraw.value=[]
    if(res) {
      listWithdraw.value = [...res]
    }
    
    // listWithdraw.value=[{id:111111111111111111,created_at:555555555,amount:250000,fee:50,state:1},{id:111111111111111111,created_at:555555555,amount:250000,fee:50,state:1}]
   
  },
  onError(res){

  }
})

const total = computed(() => {
    if(listWithdraw.value && listWithdraw.value.length > 0){
        let allDeposit = 0;
        for(let item of listWithdraw.value){
            allDeposit += item.amount;

        }
        return "R$ " + UsAmountFormat(allDeposit);

    } else {

        return "R$ 0,00"
    }
})

function format_tarefas_date(time:any) {
  // console.log(dayjs(time).unix())
  time = dayjs(time).unix() || new Date().getTime();
  
  let date = new Date(time*1000);
  // return date.format("yyyy-MM-dd HH:mm:ss")

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  const formattedDate = `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  return formattedDate
}

function getTraefasChange(type:number){
  switch(type){
    case 100:
      return "Recarga de Membro";
    case 102:
    case 103:
    case 104:
      return "Oferta de Depósito";
    default:
      return "Outro";
    

  }

}
function getTraefasStatus(type:number){
  switch(type){
    case 3:
      return "Em andamento";
    case 1:
      return "Completado";
    default:
      return "Não Começou";
    

  }

}
const traefasTotal = computed(() => {
    if(promoInspectionDatas.value && promoInspectionDatas.value.length > 0){
        let allChangeMoney= 0;
        for(let item of promoInspectionDatas.value){
          if(item.Status != 1)
          allChangeMoney = allChangeMoney + item.NeedWageRequire - item.CurWageRequire;//修改----计算还需打码

        }
        return "R$ " + UsAmountFormat(allChangeMoney);

    } else {

        return "R$ 0,00"
    }
})
console.log("pay_password = " + pay_password.value)
function openTarefas(){
  tabActive.value = 1;
  lineWidth.value = 204;
  runGetFlowRecordList();
}

const pixId = ref("");
const { run: runApiUpdateBankState } = useRequest(() => ApiUpdateBankState({pix_id:pixId.value, state:1}), {
    manual: true,
    onError: (data) => {
    },
    onSuccess: (data) => {
      if(data){
          console.log(data)
          showToast("Definido com êxito")
          appStore.runGetUserBanks();
        }
    }
})

const isShowDetail = ref(false);

//1 cpf 2 phone 3 email
function getBankType(flag: number){
  let type = "";
  switch(flag){
    case 1:
      type = "CPF"
      break;
    case 2:
      type = "PHONE"
      break;
    case 3:
      type = "EMAIL"
      break;
    case 4:
      type = "CNPJ"  
      break;
  }
  return `(${type})`;
}

function getAccount(account: string, flag: number){
  if (isShowDetail.value == true){
    if (flag == 1){
      if (account.length == 11 && /^\d+$/.test(account)) {
        return account.slice(0, 3) + '.' + account.slice(3, 6) + '.' + account.slice(6, 9) + '-' + account.slice(9);
      }
    }
    else if(flag == 4){
        if (account.length == 14 && /^\d+$/.test(account)) {
            return account.slice(0, 2) + '.' + account.slice(2, 5) + '.' + account.slice(5, 8) + '/' + account.slice(8, 12) + '-' +  account.slice(12);
        }
    }
    return account;

  }
  
  let masked = '*'.repeat(length < 4 ? 4 : length - 4) + account.slice(-4);
  return masked;
}

function getBankNum(){
  return `(${userBanks?.value.length}/${cardLimit.value})`;
}

// let i=0
// for( i;i<2; i++){
//   const temp={
//     created_at: 11111,
//     flag: 1,
//     id: "string",
//     pix_account: "string",
//     pix_id: "string",
//     real_name: "string",
//     state: 0,
//     uid: "string",
//     username: "string" 
//   }  
//   const temp1={
//     created_at: 22222,
//     flag: 2,
//     id: "temp1",
//     pix_account: "temp1",
//     pix_id: "temp1",
//     real_name: "temp1",
//     state: 1,
//     uid: "temp1",
//     username: "temp1" 
//   }

//   userBanks.value.push(temp)
//   userBanks.value.push(temp1)
// }
function openConta(){
  tabActive.value = 2;
  lineWidth.value = 176;
}


const showPixValue = ref();



if(userBanks.value && userBanks.value.length > 0) {
    for(let index in userBanks.value){
      if(userBanks.value[index].state == 1){
        showPixValue.value = {pix_account:userBanks.value[index].pix_account,flag:userBanks.value[index].flag,id:userBanks.value[index].id};
        break;
      }
    }
  }
function getPixTypeStr(flag:number) {
  let strArray = ["","CPF", "PHONE", "EMAIL","CNPJ"]
  return strArray[flag];

}

function pixPopverClick(item:any){
  showPixValue.value = {pix_account:item.pix_account,flag:item.flag,id:item.id};
  showSaquePopover.value = false;
}

//事件监听
onMounted(() => {
  watch(userBanks,(newValue,oldValue)=>{
    if(userBanks.value && userBanks.value.length > 0) {
      for(let index in userBanks.value){
        if(userBanks.value[index].state == 1){
          showPixValue.value = {pix_account:userBanks.value[index].pix_account,flag:userBanks.value[index].flag,id:userBanks.value[index].id};
          break;
        }
      }
      if(!showPixValue.value) {
        showPixValue.value = {pix_account:userBanks.value[0].pix_account,flag:userBanks.value[0].flag,id:userBanks.value[0].id};
      }
    }
  
  })
  showPixValue.value = null;
  appStore.runGetUserBanks()
})

const route = useRoute();
onMounted(() => {
    if(route.query.key){
        console.log(route.query.key);
        tabActive.value = Number(route.query.key);
    }
})

const isSendMessage = (event)=>{
    console.log("isSendMessage   " + event);
    appStore.setIsShowLoading(false);
    appStore.setBindCPFInfoVisible(false);
    if(event){
      postWithdraw()
    }
}

</script>

<template>
  <div class="withdraw-page">
    <!-- <AppIndexHeader /> -->

    <AppPageTitle v-if="pay_password=='0'" left-arrow title="Senha de Saque" />
    
    <div class="page" v-if="pay_password != '0'">
      <van-tabs class="page-tabs" v-model:active="tabActive" @click-tab="onClickTab" :swipe-threshold=3 :line-width="`var(--app-px-${lineWidth})`"	animated>
        <van-tab title="Saque" :title-style="tabTitleStyle" 	>
          <template #title>
            <div class="tabTitle" title="Saque">
              <span>Saque</span>
            </div>
          </template>

          <div class="page-tab-saques"> 
            <div class="page-tab-saques-top">
              <div class="page-tab-saques-top-left">
                <p class="saques-top-left-title">
                  <span class="saques-top-left-title-text">Saldo da Conta</span>
                  <div>
                      <div class="saques-top-left-title-money">
                          <section class="currency-count">
                            <div class="countTextBox">
                              <span :style="{ height: `var(--app-px-24)`}">
                                <span v-if="!showLoadingSpin" class="count-to">
                                  {{  UsAmountFormat(userInfo.brl!) }}
                                </span>
                                <span v-else class="count-loading">
                                  loading
                                </span>
                              </span>
                              <i class="countTextBox-img-box " @click="setLoadingSpin">
                                  <AppImage src="/img/withdraw/refresh-money" class="countTextBox-img" :class="{ 'animate__spin': showLoadingSpin}"  />
                              </i>
                            </div>

                          </section>
                      </div>
                  </div>
                </p>
                <p class="saques-top-left-text">
                  <span class="saques-top-left-text-span">
                    <span>Você precisa apostar  &nbsp;</span>
                    <span class="saques-top-left-text-span-money" @click="openTarefas">{{ "R$ " + UsAmountFormat(havaBetMoney) }}</span>
                    <span> &nbsp;&nbsp;sacar dinheiro</span> 
                  </span>
                </p>
              </div>
              <div v-if="false" class="page-tab-saques-top-right">
                <AppButton @click="" class="" width="var(--app-px-120)" height="50" blue :radius="14"
                color="var(--theme-primary-font-color)" font-size="var(--app-px-20)">
                Juros
                <div class="page-tab-saques-top-right-tip">
                  <AppImage src="/img/withdraw/qipao2" class="page-tab-saques-top-right-ti-img"  :width="51" />
                  <p class="tool-tips-box">
                    500%
                  </p>
                </div>
                </AppButton>
              </div>
          
            </div>
            <section class="page-tab-saques-middle">
              <section class="page-tab-saques-middle-content">
                <div class="globalTabsWrapper">
                  <ul class="common-tabs-nav">
                    <li class="common-tab-item common-tab-item-active">
                      <p class="common-tab-item-sel">
                        <AppImage src="/img/withdraw/jiao" :width="31" />
                      </p>
                      <span class="common-tab-item-title">
                        <span>Retirada normal</span>
                      </span>
                    </li>
                  </ul>
                </div>
                <div class="common-tabs-content">
                  <article class="common-tabs-content-article">
                    <div class="tabs-content-form">
                      <div class="tabs-content-form-input">
                        <div class="add-cpf" v-if="!showPixValue" @click="openConta">
                          <div class="cpf-popover-select box-border" :style="{width: 'var(--app-px-710)'}">
                            <div class="add-cpf-left">
                              <!-- <AppImage src="/img/withdraw/cpf.webp" :width="70" :style="{paddingLeft:'var(--app-px-14)',  paddingRight: 'var(--app-px-14)'}" /> -->
                              <div class="add-card-icon">
                              <svg t="1745433809352" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="42961" width="20" height="20"><path d="M220.202667 170.666667H803.84c22.485333 0 41.898667 0 57.856 1.28 16.853333 1.408 33.664 4.437333 49.792 12.672a128 128 0 0 1 55.936 55.936c8.234667 16.128 11.264 32.938667 12.629333 49.792 1.322667 16 1.322667 35.413333 1.322667 57.856V512a42.666667 42.666667 0 1 1-85.333333 0v-42.666667H128v204.8c0 24.618667 0 40.490667 1.024 52.565334 0.938667 11.605333 2.56 15.872 3.626667 18.005333a42.666667 42.666667 0 0 0 18.645333 18.645333c2.133333 1.066667 6.4 2.688 18.005333 3.626667C181.333333 768 197.248 768 221.866667 768H512a42.666667 42.666667 0 1 1 0 85.333333H220.202667c-22.485333 0-41.856 0-57.856-1.28-16.853333-1.408-33.664-4.437333-49.792-12.672a128 128 0 0 1-55.936-55.936c-8.234667-16.128-11.264-32.938667-12.629334-49.792C42.666667 717.653333 42.666667 698.24 42.666667 675.754667V348.245333c0-22.485333 0-41.856 1.28-57.856 1.408-16.853333 4.437333-33.664 12.672-49.792a128 128 0 0 1 55.936-55.936c16.128-8.234667 32.938667-11.264 49.792-12.629333C178.346667 170.666667 197.76 170.666667 220.202667 170.666667zM128 384h768v-34.133333c0-24.618667 0-40.490667-1.024-52.565334-0.938667-11.605333-2.56-15.914667-3.626667-18.005333a42.666667 42.666667 0 0 0-18.645333-18.645333c-2.133333-1.066667-6.4-2.688-18.005333-3.626667C842.666667 256 826.752 256 802.133333 256H221.866667c-24.618667 0-40.490667 0-52.565334 1.024-11.605333 0.938667-15.914667 2.56-18.005333 3.626667a42.666667 42.666667 0 0 0-18.645333 18.645333c-1.066667 2.133333-2.688 6.4-3.626667 18.005333C128 309.333333 128 325.248 128 349.866667V384z" p-id="42962" fill="--svg-icon-color"></path><path d="M810.666667 597.333333a42.666667 42.666667 0 0 1 42.666666 42.666667v85.333333h85.333334a42.666667 42.666667 0 1 1 0 85.333334h-85.333334v85.333333a42.666667 42.666667 0 1 1-85.333333 0v-85.333333h-85.333333a42.666667 42.666667 0 1 1 0-85.333334h85.333333v-85.333333a42.666667 42.666667 0 0 1 42.666667-42.666667z" p-id="42963" fill="var(--svg-icon-color)"></path></svg>
                              </div>
                              <span>Adicionar Conta</span>
                            </div>
                            <AppImage src="img/finance/finance-back.png.webp" class="add-cpf-right-icon" />
                           
                          </div>
                        </div>
                        <div class="select-cpf" v-else>
                          <!-- <div class="select-cpf-box">

                          </div> -->
                          <!-- content-header-popover   content-header-popover-select-->
                          <van-popover class="select-cpf-box" placement="bottom" v-model:show="showSaquePopover" :actions="actions" :offset="[0,2]"
                              @open="popoverStatus(true)" @close="popoverStatus(false)"  :show-arrow=false>
                              <div class="cpf-popover-content">
                                <div class="cpf-popover-item" v-for="(item, index) in userBanks" :key="index" @click="pixPopverClick(item)">
                                    <div class="cpf-popover-select-left">
                                      <AppImage src="/img/withdraw/pix" class="cpf-popover-item-img"></AppImage>
                                      <div class="cpf-select-title" :style="{marginLeft:'var(--app-px-20)',color: (item.id == showPixValue.id ? 'var(--theme-primary-color)':'')}">{{"PIX"}}</div>
                                      <div class="cpf-select-title" :style="{color: (item.id == showPixValue.id ?'var(--theme-primary-color)':'')}">{{"(****" + item.pix_account.slice(-4) + ")" }}</div>
                                      <div class="cpf-select-title" :style="{color:'var(--theme-text-color-lighten)'}">{{ getPixTypeStr(item.flag) }}</div>
                                    </div>
                                </div>
                              </div>
                              
                              <template #reference>
                                  <div class="cpf-popover-select box-border"
                                      :class="{ 'viewOpen': showSaquePopover }">
                                      <div class="cpf-popover-select-left">
                                        <AppImage src="/img/withdraw/pix" class="cpf-select-img"></AppImage>
                                        <div class="cpf-select-title" :style="{marginLeft:'var(--app-px-20)'}">{{"PIX"}}</div>
                                        <div class="cpf-select-title" >{{"(****" + showPixValue.pix_account.slice(-4) +")" }}</div>
                                        <div class="cpf-select-title" :style="{color:'var(--theme-text-color-lighten)'}">{{ getPixTypeStr(showPixValue.flag) }}</div>
                                      </div>
                                      
                                      <span class="cpf-select-icon"
                                          :class="{ 'rotate-svg': showSaquePopover, 'rotate-svg1': !showSaquePopover }">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                              viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                              aria-hidden="true" focusable="false" class="">
                                              <path id="comm_icon_fh"
                                                  d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                                  transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                          </svg>
                                      </span>
                                  </div>
                                  
                              </template>
                             
                        </van-popover>
                          <div class="cpf-popover-jump" @click="openConta">
                            <AppImage src="/img/withdraw/pix-setting.webp" :width="45" :style="{marginTop:'var(--app-px-15)'}" />
                          </div>
                        </div>
                        <!-- <div class="input-cpf">


                          <AppInput  width="710" height="74" v-model="withdrawFormData.cpf" placeholder="Adicionar Conta"
                             type="number" :style-obj="{
                              background:'var(--theme-main-bg-color)',
                              color: '#000',
                              borderRadius: 'var(--app-px-14)',
                            }" >
                              <template #left>
                                <AppImage src="/img/withdraw/cpf.webp" :width="70" :style="{paddingLeft:'var(--app-px-14)',  paddingRight: 'var(--app-px-14)'}" />
                                
                              </template>
                            </AppInput>
                        </div>  -->
                        <div class="input-money">
                          <AppInput  width="710" height="74" v-model="withdrawFormData.amount"
                            placeholder="Por favor, primeiro adicione a conta para saque" type="number" :style-obj="{
                              background:'var(--theme-main-bg-color)',
                              color: '#000',
                              borderRadius: 'var(--app-px-14)'
                            }" >
                            <template #left>
                                
                                <div :style="{ color: 'var(--theme-text-color)',fontSize:'var(--app-px-18)', paddingLeft:'var(--app-px-14)',  paddingRight: 'var(--app-px-14)' }">R$</div>
                              </template>
                            </AppInput>
                        </div>
                      </div>

                      <div class="tabs-content-form-paypassword">
                        <div class="form-paypassword-input">
                          <AppPasswordInput v-model="withdrawFormData.pwd" info="Senha de Saque"
                            color="var(--theme-text-color)">
                          </AppPasswordInput>
                        </div>
                          <AppButton @click="withdrawMoney" :loading="setWitdhraw" :style="{ border: `1px solid var(--theme-text-color-lighten)`,pointerEvents:`auto` }" width="var(--app-px-710)" height="70" blue :radius="14"
                          color="var(--theme-font-on-background-color)" font-size="var(--app-px-24)">Confirmar</AppButton>
                      </div>
                    </div>
                    
                    
                  </article>
                  
                </div>
                
              </section>
            </section>
            
          </div>
        </van-tab>
        <van-tab :ref="TarefasdeApostas" title="Tarefas de Apostas" class="page-tab" :title-style="tabTitleStyle" >
          <template #title>
            <div class="tabTitle" title="Tarefas de Apostas">
              <span>Tarefas de Apostas</span>
            </div>
          </template>

          <div class="page-tab-panel"> 
            <section class="page-tab-panel-content">
                <div class="panel-content-header">
                  <div class="panel-content-header-left">
                    <van-popover class="content-header-popover" v-model:show="showTarefasPopover" :actions="tarefasActions"
                          @select="onTarefasSelect" @open="tarefasPopoverStatus(true)" @close="tarefasPopoverStatus(false)" placement="bottom-start" :show-arrow=false>
                          <template #reference>
                              <div class="content-header-popover-select box-border"
                                  :class="{ 'viewOpen': showTarefasPopover }">
                                  <div class="content-header-select-title" >{{selTarefasText}}</div>
                                  <span class="content-header-select-icon"
                                      :class="{ 'rotate-svg': showTarefasPopover, 'rotate-svg1': !showTarefasPopover }">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                          viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                          aria-hidden="true" focusable="false" class="">
                                          <path id="comm_icon_fh"
                                              d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                              transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                      </svg>
                                  </span>
                              </div>
                          </template>
                          
                    </van-popover>
                    <AppImage src="/img/user/icon-server.webp" :style="{marginLeft:`var(--app-px-15)`}" :width="50" @click="$router.push('/serviceMessages')" />
                  </div>
                  <div class="content-header-total">
                    Total pendente de auditoria
                    <span>{{ traefasTotal }}</span>
                    <i class="content-header-total-img-box " @click="setTarefasdeLoadingSpin">
                        <AppImage src="/img/withdraw/refresh" class="content-header-total-img" :class="{ 'animate__spin': showTarefasdeLoadingSpin}"  />
                    </i>
                  </div>
                </div>
                <div class="content-body-middle" >
                    <AppList v-if="promoInspectionDatas?.length > 0" :loading="loading2" :finished="finished"   @refresh="refresh" @loadMoreData="loadMoreData" :style="{height:`100%`}"  >
                    <div class="tarefas-list-item"  v-for="(item, index) in  promoInspectionDatas" :key="index">
                        <div class="tarefas-list-item-left">
                          <p class="tarefas-list-item-left-p1">{{ getTraefasChange(item.ChangeType) }}</p>
                          <p class="tarefas-list-item-left-p2">{{ format_tarefas_date(item.AddTime) }}</p>
                        </div>
                        <div class="tarefas-list-item-right">
                          <p>
                            <p class="tarefas-list-item-right-p1">{{ "R$ " + UsAmountFormat(item.NeedWageRequire - item.CurWageRequire) }}</p>
                            <p class="tarefas-list-item-right-p2" :class="{color0:item.Status==0,color1:item.Status==1,color2:item.Status==3}">{{ getTraefasStatus(item.Status) }}</p>
                          </p>
                          <!-- <p class="tarefas-list-item-right-icon">
                            <AppImage src="/img/finance/finance-back.png" class="tarefas-list-item-right-img" @click="" />
                          </p> -->
                        </div>
                    </div>
                    </AppList>
                    <app-empty v-else />

                </div>
              </section>
          </div>
        </van-tab>
        <van-tab :ref="ContasAReceber" title="Conta de Retirada" class="page-tab" :title-style="tabTitleStyle" >
          <template #title>
            <div class="tabTitle" title="Conta de Retirada">
              <span>Conta de Retirada</span>
            </div>
          </template>

          <div class="page-tab-panel"> 
            <section class="page-tab-panel-content">
                <div class="content-body-middle" >
                  <div class="content-banklist">
                    <div class="top-banklist">
                      <div class="number-bank">
                        Conta de Retirada
                        <span> {{ getBankNum() }}</span>
                        <i class="content-header-total-img-box " @click="setContaLoadingSpin">
                            <AppImage src="/img/withdraw/refresh" class="content-header-total-img" :class="{ 'animate__spin': showContaLoadingSpin}"  />
                        </i>
                      </div>
                      <AppImage :class="!isShowDetail ? `eye-close` : `eye-open`"
                        @click="isShowDetail = !isShowDetail"
                        :src="!isShowDetail ? `/img/withdraw/eye-close.webp` : `/img/withdraw/eye-open.webp`"
                        :style="{ width: 'var(--app-px-36)' }"/>
                    </div>
                    {{console.log("sortedBanks", sortedBanks)}}
                    <AppList v-if="sortedBanks?.length > 0"  @refresh="refresh" @loadMoreData="loadMoreData" >
                      <div class="bank-list-item"  v-for="(item, index) in  sortedBanks" :key="index" :class="{last: index == sortedBanks.length - 1, active: item.state == 1}">
                          <div class="content-bank-info">
                            <AppImage src="/icons/icon_normal_pix.webp" :style="{ width: 'var(--app-px-60)' }" />
                            <div class="bank-info">
                              <div class="bank-type">
                                <span>PIX</span>{{ getBankType(item.flag) }}
                              </div>
                              <div class="bank-account">
                                {{ getAccount(item.pix_account, item.flag) }}
                                <AppImage v-if="isShowDetail" class="copy" src="/icons/personal_copy.webp" :style="{ width: 'var(--app-px-18)' }" @click="()=>{
                                      copy(item.pix_account || '')
                                      showToast('Copied!')
                                }"/>
                              </div>
                            </div>
                          </div>
                          <div v-if="item.state != 1" class="bank-define">
                            <span @click="()=>{
                              pixId = item.pix_id;
                              runApiUpdateBankState();
                            }">Definir como padrão</span>
                          </div>

                          <AppImage class="bank-define-gou" v-if="item.state == 1" src="/img/withdraw/jiao" :width="31" />
                      </div>
                    </AppList>
                  </div>
                </div>
                <div class="content-body-bottom" v-if="userBanks?.length < cardLimit">
                  <div class="bg">
                    <div class="content-pix-border">
                      <div class="content-pix" @click="openFindPwd">
                        <div class="pix-icon">
                          <AppImage src="/icons/icon_normal_pix.webp" :style="{ width: 'var(--app-px-60)' }" />
                          <span>PIX</span>
                        </div>
                        <div class="button-pix" >
                          <span>Adicionar</span>
                          <!-- <AppImage src="/icons/arrow.webp" :style="{ width: 'var(--app-px-16)' }" /> -->
                          <div class="arrow"  v-html="arrowIcon"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
          </div>
        </van-tab>
        <van-tab title="Registro de Saques" class="page-tab" :title-style="tabTitleStyle" >
          <template #title>
            <div class="tabTitle" title="Registro de Saques">
              <span>Registro de Saques</span>
            </div>
          </template>

          <div class="page-tab-panel">
              <section class="page-tab-panel-content">
                <div class="panel-content-header">
                  <van-popover class="content-header-popover" v-model:show="showPopover" :actions="actions"
                        @select="onSelect" @open="popoverStatus(true)" @close="popoverStatus(false)" placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select box-border"
                                :class="{ 'viewOpen': showPopover }">
                                <div class="content-header-select-title" >{{selText}}</div>
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover, 'rotate-svg1': !showPopover }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
  
                  </van-popover>
                  <div class="content-header-total">
                    Total de Saques
                    <span>{{ total }}</span>
                    <i class="content-header-total-img-box " @click="setRegistroLoadingSpin">
                        <AppImage src="/img/withdraw/refresh" class="content-header-total-img" :class="{ 'animate__spin': showRegistroLoadingSpin}"  />
                    </i>
                  </div>
                </div>
                <div class="content-body-middle" >
                    <AppList v-if="listWithdraw?.length > 0" :loading="loading2" :finished="finished"   @refresh="refresh" @loadMoreData="loadMoreData" :style="{height:`100%`}"  >
                    <div class="list-item"  v-for="(item, index) in  listWithdraw" :key="index">
                        <div class="list-item-conetent">
                            <div class="list-item-conetent-top">
                                <div class="list-item-conetent-top-left">
                                    <AppImage src="/img/finance/finance-icon.png" class="list-item-conetent-top-left-img" />
                                    <p class="list-item-conetent-top-left-title">PIX</p>
                                </div>
                                <div class="list-item-conetent-top-right">
                                    <p class="list-item-conetent-top-right-title">{{ "R$ " + UsAmountFormat(item.amount) }}</p>
                                </div>
                            </div>
                            <div class="list-item-conetent-bottom">
                                <div class="list-item-conetent-bottom-left">
                                    <p class="list-item-conetent-top-bottom-title">{{ format_date2(item.created_at*1000) }}<span class="list-item-conetent-top-bottom-title-id">{{ item.id }}</span>
                                        
                                        <AppImage src="/img/finance/copy.webp" class="list-item-conetent-top-bottom-title-img" @click="copyID(item.id)" />
                                        
                                    </p>
                                </div>
                                <div class="list-item-conetent-bottom-right">
                                    <p class="list-item-conetent-bottom-right-title" :class="{active: item.state == 0,}">{{ stateFilter(item.state) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    </AppList>
                    <app-empty v-else />

                </div>
              </section>
          </div>
        </van-tab>
        
      </van-tabs>
      <div class="page-back" @click="appStore.setShowRouterView(false)">
        <!-- <AppImage src="/img/finance/finance-back.png" class="page-back-img" @click="appStore.setShowRouterView(false)" /> -->
        <svg
          t="1745438032429"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="56450"
          width="30"
          height="30"
        >
          <path
            d="M670.165333 225.834667a42.666667 42.666667 0 0 1 0 60.330666L444.330667 512l225.834666 225.834667a42.666667 42.666667 0 0 1-60.330666 60.330666l-256-256a42.666667 42.666667 0 0 1 0-60.330666l256-256a42.666667 42.666667 0 0 1 60.330666 0z"
            fill="var(--svg-icon-color)"
            p-id="56451"
          ></path>
        </svg>
      </div>
    </div>

    <div class="content" >
      <div class="panel">
        <div class="panel_passwordSetting" v-if="pay_password=='0'">
          <div class="passwordSetting-content">
            <div class="passwordSetting-content-title">
              É o seu primeiro saque. Você precisa configurar uma senha de saque
            </div>
            <span class="passwordSetting-content-des">
              <!-- Defina sua senha de saque -->
              <span></span>
            </span>
            <div class="passwordSetting-content-input">
              <AppPasswordInput v-model="passwordSettingFormData.pwd1" info="Nova Senha de Saque"
                color="var(--app-top_username-color)">
              </AppPasswordInput>
            </div>
            <div class="passwordSetting-content-input">
              <AppPasswordInput v-model="passwordSettingFormData.pwd2" info="Confirme a nova senha de saque"
                color="var(--app-top_username-color)">
              </AppPasswordInput>
            </div>
            <p class="passwordSetting-content-tip">
              Atenção: A senha de saque protege seus fundos e é extremamente importante. Mantenha-a em segredo para
              evitar qualquer perda financeira.
            </p>
            <div class="passwordSetting-content-bottom">
              <div class="passwordSetting-content-bottom-button">
                <!-- 密码设置 -->
                <AppButton @click="setWithdrawPwd" class="" width="var(--app-px-690)" height="70" blue :radius="14"
                  color="var(--theme-font-on-background-color)" font-size="var(--app-px-24)">Confirmar</AppButton>
                <!-- <AppButton :loading="setLoading"  @click="setWithdrawPwd" type="primary" block color="var(--app-red-color)" style="font-weight: 400;" text="Confirmar" /> -->

              </div>
            </div>
          </div>

        </div>

      </div>

      <!-- 提现 -->
      <!-- <van-button v-if="pay_password !='0'"  :loading="setWitdhraw"  @click="withdrawMoney" type="primary" block color="var(--app-red-color)" style="font-weight: bold;" text="Confirmar"></van-button> -->

      <!-- <div class="retrieve" v-if="pay_password =='100'" @click="openFindPwd">
        Esqueça a senha
      </div> -->


    </div>
    
  </div>
  <bindCPFInfoPopup :cpf="cpf" @updateMessage="isSendMessage($event)" />
</template>

<style lang="scss" scoped>
.add-card-icon{
  color: green;
  padding-left:10px;
  padding-right:10px
}
.withdraw-page{
  font-family: Arial;
  background: var(--theme-bg-color);
  // padding-top: var(--app-navbar-height);
  position: absolute;
  display: block;
  width: 100%;
  top: 0;
  bottom: 0;
  flex-direction: column;
  align-items: center;
  overflow-y: hidden;
  // font-family: Arial;
}

@-webkit-keyframes spin0 {
      from {
          -webkit-transform: rotate(0deg);
      }

      to {
          -webkit-transform: rotate(180deg);
      }
  }

  @keyframes spin0 {
      from {
          transform: rotate(0deg);
      }

      to {
          transform: rotate(180deg);
      }
  }

  @-webkit-keyframes spin1 {
      from {
          -webkit-transform: rotate(180deg);
      }

      to {
          -webkit-transform: rotate(0deg);
      }
  }

  @keyframes spin1 {
      from {
          transform: rotate(180deg);
      }

      to {
          transform: rotate(0deg);
      }
  }

  .rotate-svg {
      animation: spin0 0.3s linear 0s 1;
      transform: rotate(180deg);
  }

  .rotate-svg1 {
      transform: rotate(180deg);
      animation: spin1 0.3s linear 0s 1;
      transform: rotate(0deg);
  }

.content{
  display: block;
  position: absolute;
  width: 100%;
  height:calc(100% - 93px) ;
  // padding: 0 36px;
  
}

.panel{
  width: 100%;
  // height: 692px;
  border-radius: 10px;
  position: absolute;
  height: 100% ;
  // padding: 45px 25px 45px 25px;
  // margin-bottom: 27px;
	font-size: 24px;
	font-weight: normal;
	line-height: 37px;
	// letter-spacing: px;
	// color:var(--app-top_username-color); 

  .dollar{
    font-weight: normal;
    font-size: 26px;
    color: #f43f5e;
  }
  .todetails{
    position: absolute;
    left: 580px;
    margin-top: -40px;
    text-decoration: underline;
    font-size: 25px;
    color:#3b82f6;
    font-weight:700;
  }
}

.title{
	font-size: 30px;
	font-weight: normal;
	color: var(--app-withdraw-title-color);
  text-align: center;
  margin-bottom: 30px;
}

.para{
  text-align: center;
  color:var(--app-top_username-color)
}

img.tipsIcon{
  width: 33px;
  transform: translate(0, 9px);
}

.panel_passwordSetting{
  width: 100%;
  height: 868px;
}

.passwordSetting-content {
  width: 750px;
  height: 760px;
  padding: 40px 20px 20px 20px;
  
  
}
.passwordSetting-content-title {
  display: block;
  text-align: left;
  line-height: 23px;
  color: var(--theme-text-color);
  margin-bottom: 10px;
  font-size: 28px;
}

.passwordSetting-content-des {
  display: flex;
  justify-content: space-between;
  height: 36px;
  line-height: 36px;
  margin-bottom: 40px;
  font-weight: 700;
  color: var(--theme-text-color-darken);

}

.passwordSetting-content-input {
  height: 147px;
  margin-bottom: 50px;
}

.passwordSetting-content-tip {
  color: var(--theme-secondary-color-error);
  height: 111px;
  font-size: 25px;
  line-height: 38px;
  font-weight: bold;
}

.passwordSetting-content-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.passwordSetting-content-bottom-button{
  position: relative;
  background-color: var(--theme-main-bg-color);
  padding-top: 20px;
  display: flex;
  justify-content: center;
  position: fixed;
  width: 100%;
  height: 110px;
  bottom: 0;
}
.retrieve {
  width: 190px;
  height: 46px;
  color:var(--app-text-color);
  border-bottom: 1px solid var(--app-top_username-color);
  margin: 0 auto;
  margin-top: 20px;
  text-align: center;
  font-size: 24px;
  line-height: 46px;
}

.tips{
  color: var(--app-title-color);
}

.tips_span{
  // color: #f43f5e;
  font-size: 26px;
}


.unlock_amount{
    height: 92px;
    width: 625px;
    line-height: 75px;
    background: url('/icons/unlock_amount.webp') no-repeat;
    background-size: 100% 100%;
    // margin: 0 auto;
    color: #fff;
    // text-align: center;

    .unlock_amount_test{
      display: block;
      padding-top: 15px;
      padding-left: 120px;
      line-height:normal;
      font-family:Arial;
      font-weight:700;
      color:#ffffff;
      font-size:20px;
    }

    .unlock_amount_test2{
      display: block;
      padding-top: 5px;
      padding-left: 120px;
      line-height:normal;
      font-family:Arial;
      font-weight:700;
      color:#ffe91c;
      font-size:30px;

    }

}
.page {
  height: 100%;
}

.page-back {
  width: 95px;
  height: 78px;
  position: absolute;
  left: 0px;
  top: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-back-img {
  height: 24px;
  position: absolute;
  left: 20px;
}
.page-tabs {
  // background-color: var(--theme-main-bg-color);
  height: 100%;
}

.page-tabs ::-webkit-scrollbar {
  display: none;  
}

.page-tab-saques {
  height: 100%;
}
.page-tab-saques-top {
  display: flex;
  align-items: center;
  background-color: var(--theme-main-bg-color);
  height: auto;
  justify-content: space-between;
  padding: 20px;
  width: 100%;
}

.page-tab-saques-top-left {
  display: block;
  font-size: 22px;
  width: 550px;
  height: 71px;
}

.saques-top-left-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: var(--theme-text-color-darken);
  justify-content: flex-start;
  margin-bottom: 15px;
}

.saques-top-left-title-text {
  color: var(--theme-text-color-lighten);
  font-size: 24px;
}

.saques-top-left-title-money {
  font-size: 18px;
  color: var(--theme-text-color-darken);
  display: flex;
  align-items: center;
  justify-content: start;
  line-height: normal;
  padding-left: 2px;
  padding-right: 8px;
  width: auto;
  height: 30px;
}

.currency-count {
  display: flex;
  align-items: center;
  font-size: 22px;
  height: 28px;
  padding-left: 2px;
  padding-right: 8.4px;
}

.countTextBox {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  font-size: 18px;
  line-height: 18px;
  height: 28px;
}

.count-to {
  color: var(--theme-secondary-color-finance) !important;
  position: relative;
  display: block;
  top: 2px;
  margin: 0 7.3px -3px 10px;
  max-width: 164px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 24px;
  line-height: 24px;
}

.count-loading {
  font-size: 18px;
  color: var(--theme-text-color-lighten);
  padding: 0 13px;
}

.countTextBox-img-box {
  height: 30px;
  width: 30px;
  position: relative;
  z-index: 10;
  display: block;

  .rotate {
    animation: spin 1s linear infinite;
  }
}
.countTextBox-img{
  height: 26px;
  width: 28px;
}

.animate__spin {
  -webkit-animation-name: spin;
  animation-name: spin;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

.saques-top-left-text {
  display: block;
  width: 550px;
  text-align: left;
  white-space: normal;
}

.saques-top-left-text-span {
  display: flex;
  align-items: center;
  overflow: auto;
  white-space: normal;
  width: auto;
  color: var(--theme-text-color-lighten);
  font-size: 22px;
}

.saques-top-left-text-span-money {
  border-bottom: thin solid var(--theme-primary-color);
  color: var(--theme-primary-color);
  white-space: normal;
  text-align: left;
  display: block;
}

.page-tab-saques-top-right {
  font-size: 14px;
  color: var(--theme-text-color-lighten);
  display: flex;
  align-items: center;
  position: relative;
}

.page-tab-saques-top-right-tip {
  position: absolute;
  top: -22px;
  width: fit-content;
  left: 46px;
  right: 17px;
  height: 32px;
}
.tool-tips-box {
  display: block;
  color: #fff;
  width: 100%;
  font-size: 15px;
  line-height: 26px;
  padding: 0 7px;
  position: absolute;
  top: 0;
}

.page-tab-saques-middle {
  background-color: var(--theme-bg-color);
  border-top: 2px solid var(--theme-color-line);
  padding-bottom: 100px;
  display: block;
  height: 937px;

  .page-tab-saques-middle-content {
    font-size: 14px;
    height: 755px;
    color: var(--theme-text-color);
    line-height: normal;
  }

  .globalTabsWrapper {
    background-color: var(--theme-main-bg-color);
    position: relative;
    z-index: 1;
    height: 130px;
  }

  .common-tabs-nav {
    margin: 0 20px;
    padding: 30px 0;
    width: 710px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    border-radius: 10px 10px 0 0;
    height: 130px;

  }

  .common-tab-item {
    width: 710px;
    height: 70px;
    padding: 0 5px;
    display: flex;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border: thin solid var(--theme-color-line);
    border-radius: 10px;
    font-size: 18px;
    justify-content: center;
    line-height: 60px;
    position: relative;
    text-align: center;
    word-break: break-word;
  }

  .common-tab-item-active {
    border: thin solid var(--theme-primary-color);
    color: var(--theme-primary-color);
    border-radius: 10px;
  }

  .common-tab-item-sel {
    width: 33px;
    height: 31px;
    position: absolute;
    right: -2px;
    bottom: 3px;
  }

  .common-tab-item-title {
    color: var(--theme-primary-color);
    font-size: 18px;
  }

  .common-tabs-content {
    height: 625px;

    .common-tabs-content-article {
    width: 750px;
    height: 623px;
    padding: 30px 20px 0 20px;
    border-top: 2px solid var(--theme-color-line);
    }

    .tabs-content-form {
      width: 100%;
      height: 100%;
    }

    .tabs-content-form-input {
      width: 100%;
      height: 296px;
      border-bottom: 2px solid var(--theme-color-line);
    }

    .input-cpf {
      width: 100%;
      height: 120px;
    }

    .add-cpf {
      width: 100%;
      height: 120px;
      position: relative;
    }
    .add-cpf-left{ 
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .add-cpf-right-icon {
      position: absolute;
      // width: 20px;
      height: 20px;
      right: 15px;
      rotate: -180deg;
    }
    .select-cpf {
      width: 100%;
      height: 120px;
      position: relative;
    }
    .tabs-content-form-paypassword {
      height: 297px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
    }
    .form-paypassword-input {
      margin-top: 30px;
      height: 177px;
      margin-bottom: 50px;
      width: 750px;
      color: black;
      position: relative;
    }
    
  } 

  
}



.select-cpf-box {
  border-width: 0px;
  border: rgba($color: #000000, $alpha: 0.0);
  padding: 2px;
}

.cpf-popover-content {
  width: 640px;

  background-color: var(--theme-main-bg-color);
  border: 2px solid;
  border-color: var(--theme-color-line);
  border-radius: 14px;
  padding: 20px 0;
  //
}
.cpf-popover-bg {
  width: 10px;

  background-color: var(--theme-main-bg-color);
  border: 2px solid;
  border-color: var(--theme-color-line);
  border-radius: 14px;
  padding: 20px 0;
  //
}
.cpf-popover-item {
  width: 640px;
  
  height: 92px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  font-size: 22px;
}
.cpf-popover-item-img {
  width: 40px;
}

.cpf-popover-select {
  width: 640px;
  
  height: 74px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  background-color: var(--theme-main-bg-color);
  font-size: 22px;
  // margin-left: 20px;
  &.viewOpen {
      border-color: var(--theme-primary-color);
      
  }
}

.cpf-popover-select-left {
  display: flex;
  align-items: center;
  height: 70px;
  line-height: 70px;
  width: 100%;
  padding-left: 20px;
  border-width: 2px;
}

.cpf-select-title {
  color: var(--theme-text-color-lighten);
  font-size: 22px;
  line-height: 48px;
  max-width: 100%;
  margin-left: 8px;
  overflow: hidden;
  white-space: nowrap;
  height: 48px;
  font-weight: 400;
}

.cpf-select-img {
  width: 48px;
}

.cpf-select-icon {
  position: absolute;
  width: 20px;
  height: 20px;
  right: 15px;

  svg {
      width: 20px;
      height: 20px;
      color: var(--theme-text-color-lighten);
      position: absolute;

  }
}

.cpf-popover-jump {
  position: absolute;
  width: 45px;
  height: 57px;

  right: 0px;
  top: 10px;
}

.content-header-popover {
  min-width: 160px;
  max-width: 315px;
  height: 50px;
}
.box-border {
    border-style: solid;
    // border-color: #194C38;
    border-color: var(--theme-color-line);
    border-width: 2px;
    border-radius: 10px;
}
.page-tab-panel {
  display: block;
  width: 100%;
  height: 100%;
  color: var(--theme-text-color);
  font-size: 14px;

  .page-tab-panel-content {
    height: 100%;
    padding: 0 20px 20px;
  }

  .panel-content-header {
    height: 90px;
    padding: 20px 0;
    color: var(--theme-text-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 22px;
  }

  .panel-content-header-left {
    width: 224px;
    height: 54px;
    display: flex;
    align-items: center;
  }
  

  .content-header-popover-select {
      width: 100%;
      min-width: 105px;
      height: 50px;
      border-radius: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      background-color: var(--theme-main-bg-color);
      // margin-left: 20px;
      margin-right: 55px;
      &.viewOpen {
          border-color: var(--theme-primary-color);
      }

      
  }
  
  .content-header-select-title {
      color: var(--theme-text-color);
      font-size: 20px;
      line-height: 48px;
      padding-left: 20px;
      max-width: 100%;
      float: left;
      overflow: hidden;
      white-space: nowrap;
      height: 48px;
  }

  

  .content-header-select-icon {
      position: absolute;
      width: 20px;
      height: 20px;
      right: 15px;

      svg {
          width: 20px;
          height: 20px;
          color: var(--theme-text-color-lighten);
          position: absolute;

      }
  }

  .content-header-total {
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-text-color-lighten);
    text-align: right;
    margin-left: 20px;
    font-size: 22px;
    line-height: normal;
    -webkit-font-smoothing:antialiased;
    span {
      font-size: 22px;
      color: var(--theme-text-color-darken);
      margin-left: 5px;
    }
  }

  .content-header-total-img-box {
    width: 30px;
    height: 30px;
    display: inline-block;
    margin-left: 10px;
    .rotate {
      animation: spin 1s linear infinite;
    }
  }
  .content-header-total-img{
  height: 30px;
  width: 30px;
}
}
.content-body-middle {
        width: 100%;
        height: calc(100% - 200px);
        padding: 0 0px;

        .list-item {
            width: 700px;
            height: 131px;
            // background-color: var(--theme-bg-color);
            border-radius: 10px;
            margin-top: 5px;
            margin-left: 5px;
            &:nth-child(2n) {
                background-color: var(--theme-main-bg-color);
            }

            .list-item-conetent {
                width: 700px;
                height: 131px;
                padding: 15px 20px; 
            }

            .list-item-conetent-top {
                width: 660px;
                height: 58px;
                padding: 5px 0;
                display: flex;
                justify-content: space-between;
            }

            .list-item-conetent-top-left {
                height: 48px;
                display: flex;
                align-items: center;

            }

            .list-item-conetent-top-left-img {
                width: 48px;
                height: 48px;
                margin-right: 15px;
            }

            .list-item-conetent-top-left-title {
                font-size: 24px;
                line-height: 36px;
                color: var(--theme-text-color);

            }

            .list-item-conetent-top-right {
                display: block;
                height: 39px;

            }
            .list-item-conetent-top-right-title {
                font-size: 26px;
                line-height: 39px;
                color: var(--theme-text-color);
                font-weight: 700;
            }

            .list-item-conetent-bottom {
                width: 660px;
                height: 43px;
                padding: 5px 0;
                display: flex;
                justify-content: space-between;
            }

            .list-item-conetent-top-bottom-title{
                font-size: 20px;
                color: var(--theme-text-color-lighten);
                line-height: 30px;
            }
            .list-item-conetent-top-bottom-title-id {
                
                margin: 0 10px;
            }
            .list-item-conetent-top-bottom-title-img{
                display: inline;
                height: 20px;
                width: 20px;
            }
            .list-item-conetent-bottom-right {
                display: block;
                height: 39px;
            }

            .list-item-conetent-bottom-right-title {
                height: 33px;
                display: block;
                color: var(--theme-secondary-color-success);
                font-size: 22px;
            }
        }

        .tarefas-list-item {
            width: 704px;
            height: 110px;
            // background-color: var(--theme-bg-color);
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            &:nth-child(2n-1) {
                background-color: var(--theme-main-bg-color);
            }
        }

        .tarefas-list-item-left {
          display: block;
          height: 46px;

          p {
              display: block;
              
              margin-inline-start: 0px;
              margin-inline-end: 0px;
              unicode-bidi: isolate;
          }

          .tarefas-list-item-left-p1 {
            color: var(--theme-text-color-darken);
            font-size: 22px;
            line-height: normal
          } 

          .tarefas-list-item-left-p2 {
            color: var(--theme-text-color-lighten);
            font-size: 20px;
          }
        }

        .tarefas-list-item-right {
          display: flex;
          align-items: center;
          text-align: right;
          height: 52px;

          .tarefas-list-item-right-p1 {
            color: var(--theme-text-color);
            font-size: 24px;
            font-weight: 700;
            text-align: right;
          } 

          .tarefas-list-item-right-p2 {
            font-size: 22px;
            
          }
          .color0 {
            color: var(--theme-text-color-lighten);
          }
          .color1 {
            color: var(--theme-secondary-color-success);
          }
          .color2 {
            color: var(--theme-secondary-color-finance);
          }
          .tarefas-list-item-right-icon {
            width: 26px;
            height: 32px;
            margin-right: 15px;
          }

          .tarefas-list-item-right-img {
            // width: 13px;
            height: 26px;
          }
        }
    }

.content-banklist{
  background-color: var(--theme-main-bg-color);
  border-radius: 14px;
  box-shadow: 0 5px 18px 0 var(--theme-bg-shadow);
  margin: 20px 20px 0 0;
  overflow: hidden;
  padding: 20px;
  width: 100%;
  .top-banklist{
    align-items: center;
    display: flex;
    justify-content: space-between;
    font-size: 22px;
    height: 60px;
  }
  .number-bank {
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-text-color);
    text-align: right;
    font-size: 22px;
    line-height: normal;
    -webkit-font-smoothing:antialiased;
    span {
      font-size: 22px;
      color: var(--theme-text-color-lighten);
      margin-left: 5px;
    }
  }
  .bank-list-item{
    // background-image: url('/icons/bank-list-item.webp');
    border-radius: 14px;
    width: 670px;
    height: 80px;
    background-size: 100% 100%;

    padding: 10px 20px 10px 10px;
    background-color: var(--theme-main-bg-color);
    border: thin solid var(--theme-color-line);
    justify-content: space-between;
    margin-bottom: 20px;
    position: relative;
    align-items: center;
    display: flex;
    &.active{
      // background-image: url('/icons/bank-list-item-active.webp');
      border: thin solid var(--theme-filter-active-color);
    }
    &.last{
      margin-bottom: 0px;
    }
    .content-bank-info{
      align-items: center;
      display: flex;
      font-size: 22px;
      .bank-info{
        position: relative;
        padding-left: 20px;
        color: var(--theme-text-color-lighten);
        span{
          color:white;
        }
      }
      .bank-account{
        align-items: center;
        display: flex;
        img{
          left:10px;
          position: relative;
        }
      }
    }
    .bank-define{
      font-size: 22px;
      line-height: 1.5;
      text-align: right;
      align-items: center;
      display: flex;
      box-sizing: border-box;
      max-width: 162px;
      span{
        color: var(--theme-primary-color);
        max-width: 162px;
        line-height: 1.1;
        word-wrap: break-word;
        margin-right: 15px;
        shape-outside: content-box;
        text-align: right;
        box-sizing: border-box

      }

    }
    .bank-define-gou{
      position: relative;
      bottom:-23px;
      left: 20px;
    }
  }
} 

.content-body-bottom {
        width: 750px;
        height: 120px;
        bottom: 0;
        left: 0;
        position: absolute;
        .bg{
          width: 100%;
          height: 100%;
          background-color: var(--theme-main-bg-color);
          border-radius: 14px 14px 0 0;
          box-shadow: 0 -6px 10px 0 var(--theme-bg-shadow);
        }
        .content-pix{
          position: relative;
          left: 20px;
          top:20px;
          width: 710px;
          height: 80px;
          background-color: var(--theme-main-bg-color);
          border: thin solid var(--theme-primary-color);
          border-radius: 14px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }
        .pix-icon{
          align-items: center;
          display: flex;
          padding-left: 10px;
          span{
            color: var(--theme-text-color-darken);
            font-size: 22px;
            padding-left: 20px;
          }
        }
        .button-pix{
          align-items: center;
          display: flex;
          padding-right: 20px;

          span{
            color: var(--theme-primary-color);
            font-size: 22px;
            padding-right: 10px;
          }
        }

}
</style>

<style lang="scss">
:root:root {
  
  .arrow {
      width: 3vw;
      position: absolute;
      top: 53%;
      right: 0.7vw;
      transform: translateY(-50%);
    }

  .page-tabs .van-tabs__wrap {
    background-color: var(--theme-main-bg-color) !important;
    border-bottom: thin solid var(--theme-color-line);
    display: flex;
    height: auto;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .page-tabs .van-tabs__nav {
  
    margin-left: 95px;
    background-color: var(--theme-main-bg-color);
    display: inline-block;
    overflow-x: auto;
    padding: 0;
    white-space: nowrap;
  }

  .page-tabs .van-tabs__nav .van-tab {
    color: var(--theme-text-color-lighten);
    display: inline-block;
    padding: 0;
    position: relative;
  }
  .page-tabs .van-tabs__nav .van-tab--active {
    color: var(--theme-primary-color);
    transition: all .3s;
  }

  .page-tabs .van-tab--active {
    font-weight: 500;
  }

  .page-tabs .van-tabs__line {
    background-color: var(--theme-primary-color);
    bottom: 0;
    height: 3px;
    position: absolute;
    z-index: 1;
    left: 0px;
    display: block;
  }

  .form-paypassword-input .van-number-keyboard{
    width: 750px;

  }

  .page-tabs .van-tabs__content {
    height: calc(100% - 82px);
  }

  .page-tabs .van-tab__panel {
    height: 100%;
  }

  --van-popover-action-width: 210px;
  --van-popover-action-height: 80px;
  --van-popover-action-font-size: 24px;
  --van-padding-md: 20px;
  --van-popover-action-line-height: 0px;

  /* .van-popover__arrow { */
  /* background: var(--theme-color-line)  !important; */
  /* border-color: var(--theme-color-line)  !important; */
  /* } */
  .content-header-popover .van-popover--light{
    background: var(--theme-main-bg-color) !important;
  }
  .content-header-popover .van-popover__content {
      background: var(--theme-main-bg-color) !important;
      border-color: var(--theme-color-line);
      border-width: 2px;
      border-radius: 14px;
      border-style: solid;
  }

  .content-header-popover .van-popover__action {
      color: var(--theme-text-color-lighten);
      font-size: 24px;
      border-style: none;
      align-items: left;
  }

  .content-header-popover .van-popover__action-text {
      justify-content: left
  }

  .content-header-popover .van-popover__action:active {
      background-color: var(--theme-text-color-lighten);
  }

  .content-header-popover .van-hairline--bottom:after {
      border-bottom-width: 0px;
  }

  .content {
      border-color: var(--theme-color-line);
  }


}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
