<script setup lang="ts" name="app-find-pwd">
import { log } from "util";

// const router = useRouter()
const appStore = useAppStore();
const { findPasswordDialogVisible } = storeToRefs(appStore);
enum TabActive {
  findPass = "login",
}
const tabData = ref([
  {
    label: "Esqueça a senha",
    value: TabActive.findPass,
  },
]);
const tabActive = ref(TabActive.findPass);
const phoneRef = ref();
const emailRef = ref();
const pwdRef = ref();
const codeRef = ref();
const usernameRef = ref();
const isPhone = ref(true);
const resetStep = ref(0);
const phoneFlag = ref(false);
const passwordStrength = ref(0);
const passwordRef = ref();
const passwordRef2 = ref();
// const { userNameReg } = useRegExpUserName();
const { phoneReg } = useRegExpPhone();
const fromData = reactive({
  email: "",
  phone: "",
  password: "",
  code: "",
  sid: "",
  ts: "",
  username: "",
  repassword: "",
});
watch(findPasswordDialogVisible,()=>{
  resetStep.value = 0;
  fromData.username = "";
  phone_encryption.value = "";
  phone_two.value = "";
  phone_sex.value = "";
  phone_three.value = "";
})
const phone_two = ref();
const phone_sex = ref();
const phone_three = ref();
const phone_encryption = ref();
const code_time = ref(120);
const { run: runResetPwd, loading: resetLoading } = useRequest(
  () =>
    ApiResetPwd({
      username: fromData.username,
      sid: fromData.sid,
      ts: fromData.ts,
      code: fromData.code,
      phone: phone_two.value + phone_sex.value + phone_three.value,
      password: fromData.password,
    }),
  {
    manual: true,
    onSuccess: () => {
      closePage();
      // localStorage.clear()
      location.replace(location.origin);
    },
  }
);

const authPassword = () => {
  let strength = 0;
  let pd = fromData.password;

  // 检查大写字母
  if (/[A-Z]/.test(pd)) strength++;

  // 检查小写字母
  if (/[a-z]/.test(pd)) strength++;

  // 检查数字
  if (/\d/.test(pd)) strength++;

  // 检查特殊字符
  if (/[\W_]/.test(pd)) strength++;
  passwordStrength.value = strength;
};

const { run: runGetPhone } = useRequest(
  () => checkAccount({ username: fromData.username }),
  {
    manual: true,
    onSuccess: (data) => {
      phone_encryption.value = data.phone;
      phone_two.value = data.phone.substr(0, 2);
      phone_three.value = data.phone.substr(
        data.phone.length - 3,
        data.phone.length
      );
      phoneFlag.value = true;
    },
  }
);

const { run: runGetCode } = useRequest(
  () =>
    checkPhoneAndSendSms({
      username: fromData.username,
      phone: phone_two.value + phone_sex.value + phone_three.value,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        resetStep.value = 1;
        phoneFlag.value = false;
        timeSet();
        fromData.ts = data.ts + "";
        fromData.sid = data.sid;
      }
    },
  }
);

const timeClick = () => {
  if (code_time.value <= 0) {
    code_time.value = 120;
    runGetCode()
  } else if (code_time.value < 120 && code_time.value > 0) {
    return;
  }
};

const timeSet = () => {
  const timer = setInterval(function () {
    code_time.value = code_time.value - 1;
    if (code_time.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

const submit = () => {
  // emailRef.value.validation();
  if (resetStep.value - 0 == 0 && !phone_encryption.value) {
    runGetPhone();
  }
  if (resetStep.value - 0 == 0 && phone_encryption.value) {
    if (phone_sex.value !== "******" && phone_sex.value.length == 6) {
      runGetCode();
    } else {
      showToast("Introduza o seu número de telefone");
    }
  }
  if (resetStep.value - 0 == 1) {
    if (fromData.password !== fromData.repassword) {
      return showToast("As senhas inseridas não coincidem");
    }
    if (!fromData.code) {
      return showToast("Introduza o código de verificação");
    }
    runResetPwd();
  }
};

const { run: runSendCode, loading: codeLoading } = useRequest(
  () => ApiSendOfflineSms({ flag: "text", tel: fromData.phone, ty: 2 }),
  {
    manual: true,
    onSuccess(data: string) {
      const [sid, ts] = data.split(":");
      fromData.sid = sid;
      fromData.ts = ts;
      startCountdown();
      showToast("Código de verificação enviado");
    },
  }
);

// 邮箱注册验证码获取
const { run: runEmailCode, loading: emailCodeLoading } = useRequest(
  () => ApiEmailCode({ ty: "2", mail: fromData.email }),
  {
    manual: true,
    onSuccess: (data) => {
      const [sid, ts] = data.split(":");
      fromData.sid = sid;
      fromData.ts = ts;
      startCountdown();
      showToast("Código de verificação enviado");
    },
  }
);

const countdowm = ref(0);
const startCountdown = () => {
  countdowm.value = 120;
  const timer = setInterval(() => {
    countdowm.value--;
    sessionStorage.setItem("countdowm", countdowm.value.toString());
    if (countdowm.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

const handleSendCode = () => {
  if (isPhone.value) {
    phoneRef.value.validation();
  } else {
    emailRef.value.validation();
  }
  const isPass = isPhone.value
    ? phoneRef.value.isValid
    : emailRef.value.isValid;
  if (isPass) {
    if (isPhone.value) {
      runSendCode();
    } else {
      runEmailCode();
    }
  }
};

const codeDisabled = computed(() => {
  if (codeLoading.value || emailCodeLoading.value) {
    return true;
  }
  if (
    isPhone.value &&
    phoneReg.value.test(fromData.phone) &&
    countdowm.value <= 0
  ) {
    return false;
  }
  if (!isPhone.value && emailReg.test(fromData.email) && countdowm.value <= 0) {
    return false;
  }
  return true;
});

const changeRegType = (bool: boolean) => {
  isPhone.value = bool;
  fromData.code = "";
  fromData.password = "";
};

// 同时验证 phoneReg 和  emailReg，只要有一个通过就行
// "|" + emailReg.source +
const usernameReg = new RegExp("(?:" + phoneReg.value.source + ")\\b");

//关闭界面
const closePage = () => {
  resetStep.value = 0;
  appStore.setFindPasswordDialogVisible(false);
};
</script>

<template>
  <van-popup
    class="app-login-register"
    v-model:show="findPasswordDialogVisible"
    teleport="body"
    round
  >
    <div class="content" v-if="findPasswordDialogVisible">
      <div class="title">
        <label>Esqueça a senha</label>
        <!-- <AppTab :list-data="tabData" v-model="tabActive" :height="84" active-color="#0ED1F4" /> -->
        <!--        <AppImage class="close"  src="/icons/close_black.png.webp" @click="closePage" />-->
      </div>
      <!-- <div class="login-img">
        <div class="reg-tab" :class="{'active': !isPhone}" @click="changeRegType(false)">
          <div class="email" :class="{'active': !isPhone}"> </div>
        </div>

        <div class="reg-tab" :class="{'active': isPhone}" @click="changeRegType(true)">
          <div class="phone" :class="{'active': isPhone}"></div>
        </div>
      </div> -->
      <div class="app-login">
        <div :class="`reset-password-step all-step-${resetStep}`">
          <div class="step">1<span> Verificação de ID</span></div>
          <div class="line"></div>
          <div class="step">2<span>Redefinição de Senha</span></div>
          <div class="line"></div>
          <div class="step">3<span>Terminar</span></div>
        </div>
        <div class="username" v-if="resetStep == 0">
          <AppInput
            icon-with="25"
            v-model="fromData.username"
            ref="usernameRef"
            :pattern="usernameExpReg"
            icon-left="input-user-icon"
            type="text"
            placeholder="Inserir nome de usuário"
            msg="4-16 caráter bit, suporte em inglês/números/símbolos"
            :err-height="42"
            clearable
            width="100%"
            height="70"
            maxlength="16"
            :style-obj="{
              background: 'transparent',
              color: '#000',
              borderRadius: 'var(--app-px-14)',
            }"
          >
            <!--        <template #left>-->
            <!--          <div :style="{ color: 'var(&#45;&#45;app-title-color)', paddingRight: 'var(&#45;&#45;app-px-14)' }">+55</div>-->
            <!--        </template>-->
          </AppInput>
        </div>
        <div class="phone-box" v-if="phoneFlag">
          <div class="top-line"></div>
          <div class="title">Verificacao Movel</div>
          <div class="message">
            Numero de Telemovel Vinculado Atula +55 {{ phone_encryption }}
          </div>
          <div class="phone-num">
            <span class="phone-type">+55</span>
            <span class="two-num"> {{ phone_two }}</span>
            <span class="input-box">
              <input
                type="text"
                maxlength="6"
                v-model="phone_sex"
                placeholder="******"
              />
            </span>
            <span class="three-num">
              {{ phone_three }}
            </span>
          </div>
        </div>
        <div class="username" v-if="resetStep == 1">
          <div class="password">
            <AppInput
              icon-with="25"
              width="100%"
              height="70"
              v-model="fromData.password"
              ref="passwordRef"
              :required="true"
              double-action="true"
              :pattern="passwordReg3"
              :err-height="42"
              placeholder="Senha"
              @change="authPassword"
              msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos"
              clearable
              icon-left="input-password-icon"
              type="password"
              :style-obj="{
                background: 'transparent',
                color: '#000',
                borderRadius: 'var(--app-px-14)',
              }"
            />
          </div>
          <div :class="`ant-space-item passwordStrength-${passwordStrength}`">
            <span>Força</span><span class="step"></span
            ><span class="step"></span><span class="step"></span
            ><span class="step"></span>
          </div>
          <div class="password">
            <AppInput
              icon-with="25"
              width="100%"
              height="70"
              v-model="fromData.repassword"
              ref="passwordRef2"
              :required="true"
              double-action="true"
              :pattern="passwordReg3"
              :err-height="42"
              placeholder="Confirme a senha novamente, o mesmo que a senha!"
              msg="6-16 caracteres, incluindo pelo menos duas letras/números/símbolos"
              clearable
              icon-left="input-password-icon"
              type="password"
              :style-obj="{
                background: 'transparent',
                color: '#000',
                borderRadius: 'var(--app-px-14)',
              }"
            />
          </div>
          <div class="code">
            <AppInput
              icon-with="25"
              width="70%"
              height="70"
              v-model="fromData.code"
              ref="codeRef"
              :required="true"
              double-action="true"
              :err-height="42"
              placeholder="Código de verificação"
              msg="Introduza o código de verificação"
              clearable
              type="code"
              :style-obj="{
                background: 'transparent',
                color: '#000',
                borderRadius: 'var(--app-px-14)',
                paddingLeft: '5px',
              }"
            />
            <span class="code-button" @click="timeClick()">{{
              code_time > 0 ? code_time + "s" : "enviar"
            }}</span>
          </div>
        </div>
        <!--        <div class="password">-->
        <!--          <AppInput icon-with="36"  v-model="fromData.password" placeholder="Senha (6-12 letras e números)" icon-left="icon_verification_code" width="650" height="100"-->
        <!--              type="password" clearable ref="pwdRef" :pattern="passwordReg" msg="6-12 letras e números"  :style-obj="{-->
        <!--              background:'var(&#45;&#45;app-psw-input-color)',-->
        <!--              color: '#000',-->
        <!--              borderRadius: '15px'-->
        <!--          }"/>-->

        <!--        </div>-->

        <!-- <div class="email" v-show="!isPhone">
          <AppInput icon-with="35"  v-model="fromData.email" ref="emailRef" :pattern="emailReg" icon-left="icon_email_1" width="677" height="107" placeholder="Email" msg="Error Email"  :style-obj="{background:'#192841',color: '#fff',borderRadius: '32px'
        }"/>
        </div> -->
        <!--        <div class="code">-->
        <!--          <AppInput icon-with="36" ref="codeRef" v-model="fromData.code" placeholder="Código de verificação" icon-left="icon_verification_code_h" width="650" height="100"  clearable :pattern="/^[0-9]{4}$/" msg="4 números" :style-obj="{-->
        <!--             background:'var(&#45;&#45;app-psw-input-color)',-->
        <!--            color: '#000',-->
        <!--            borderRadius: '15px'-->
        <!--          }">-->
        <!--          <template #right>-->
        <!--            <div :style="{ paddingRight: 'var(&#45;&#45;app-px-14)' }">-->
        <!--              <AppButton :loading="codeLoading || emailCodeLoading" :disabled="codeDisabled" fontSize="26" radius="23" whiteText blue width="200" height="90" center @click="handleSendCode">{{ countdowm > 0 ?-->
        <!--              countdowm + 's' : 'Enviar' }}-->
        <!--              </AppButton>-->
        <!--            </div>-->
        <!--          </template>-->
        <!--          </AppInput>-->
        <!--        </div>-->

        <div class="submit">
          <AppButton
            @click="submit"
            fontSize="var(--app-px-24)"
            :loading="resetLoading"
            width="100%"
            height="70"
            color="var(--theme-primary-font-color)"
            blue
            :radius="14"
            white-text
          >
            Seguintes
          </AppButton>
        </div>
      </div>
    </div>
    <img
      class="app-image img close-icon"
      src="/icons/close_black.png.webp"
      @click="closePage"
      alt=""
   
    />
  </van-popup>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.app-login {
  padding:14px;
  .reset-password-step {
    border-radius: 14px;
    border: 1px solid var(--theme-color-line);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    padding: 50px 50px 75px 50px;
    .step {
      background-color: var(--theme-main-bg-color);;
      border: 1px solid var(--theme-color-line);
      border-radius: 50%;
      font-size: 20px;
      color: var(--theme-text-color-lighten);
      height: 60px;
      line-height: 60px;
      width: 60px;
      text-align: center;
      position: relative;
      > span {
        position: absolute;
        bottom: -90%;
        font-size: 20px;
        text-align: center;
        width: 220px;
        color: var(--theme-text-color-lighten);
        left: -75px;
      }
    }
    .line {
      background-color: var(--theme-color-line);
      width: 150px;
      height: 10px;
    }
    &.all-step-0 div:nth-child(1),
    &.all-step-1 div:nth-child(1),
    &.all-step-1 div:nth-child(2),
    &.all-step-1 div:nth-child(3),
    &.all-step-2 div:nth-child(1),
    &.all-step-2 div:nth-child(2),
    &.all-step-2 div:nth-child(3),
    &.all-step-2 div:nth-child(4),
    &.all-step-2 div:nth-child(5) {
      background-color: var(--theme-primary-color);;
      border: 1px solid var(--theme-primary-color);;
      color: var(--theme-primary-font-color) !important;
    }
    &.all-step-0 div:nth-child(1) span,
    &.all-step-1 div:nth-child(1) span,
    &.all-step-1 div:nth-child(2) span,
    &.all-step-1 div:nth-child(3) span,
    &.all-step-2 div:nth-child(1) span,
    &.all-step-2 div:nth-child(2) span,
    &.all-step-2 div:nth-child(3) span,
    &.all-step-2 div:nth-child(4) span,
    &.all-step-2 div:nth-child(5) span {
      color: var(--theme-primary-color) !important;;
    }
  }
}
.app-login-register {
  width: 100%;
}
.close-icon {
  width: 60px;
  border: 5px solid white;
  border-radius: 100%;
  padding: 5px;
  margin: 40px auto;
  display: block;
}
.tab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px 15px 12px;
    > span {
      padding-left: 10px;
      color: #f9fd4e;
    }
  }
  .bottom-line {
    margin: 5px 0 20px 0;
    width: 250px;
    height: 9px;
    border-radius: 3px;
    background-color: #f9fd4e;
  }
}
.content {
  width: 100%;
  background-color: var(--theme-main-bg-color);
  border: thin solid var(--theme-color-line);
  padding-bottom: 65px;
  border-radius: var(--van-popup-round-radius);
  .title {
    width: 100%;
    padding: 20px 0 20px 0;
    color: #ffffff;
    text-align: center;
    font-size: 30px;
    border-radius: 0;
  }

  // .phone, .email {
  //   padding-top: 0;
  // }
}

.close {
  width: 40px;
  position: absolute;
  top: 25px;
  right: 20px;
}

.email,
.phone {
  margin-left: 10px;
  padding-top: 1px;
  padding-bottom: 25px;
  margin: 0 auto;
}

.password {
  padding-top: 10px;
}

.code {
  padding-top: 35px;
}

.submit {
  padding-top: 80px;
  button {
    margin: 0 auto;
  }
}

.login-img {
  text-align: center;
  padding-top: 40px;
  padding-bottom: 35px;
  // padding: 0 0 40px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .line {
    width: 1px;
    height: 72px;
    background: rgba(255, 255, 255, 0.25);
    margin: 0 36px;
  }

  img {
    width: 72px;
    height: 72px;
    vertical-align: middle;
  }

  .reg-tab {
    width: 229px;
    height: 70px;
    border-radius: 10px;
    background: #192841;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s;
    &:last-child {
      margin-left: 30px;
    }
    &.active {
      background: #679fea;
    }

    .email {
      width: 45px;
      height: 45px;
      // background: url() no-repeat center center;
      @include webp("/icons/icon_email_1.png");
      background-position: center center;
      background-size: 100% 100%;

      &.active {
        // background: url() no-repeat center center;
        @include webp("/icons/icon_email_1-active.png");
        background-position: center center;
        background-size: 100% 100%;
      }
    }

    .phone {
      width: 48px;
      height: 48px;
      // background: url() no-repeat center center;
      @include webp("/icons/icon_phone_1.png");
      background-position: center center;
      background-size: 100% 100%;

      &.active {
        // background: url() no-repeat center center;
        @include webp("/icons/icon_phone_1-active.png");
        background-size: 100% 100%;
        background-position: center center;
      }
    }
  }
}

.retrieve {
  width: 260px;
  height: 46px;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid #0ed1f4;
  color: #0ed1f4;
  text-align: center;
  font-size: 26px;
  line-height: 46px;
}

.phone-box {
  width: 90%;
  margin: 10px auto;
  margin-top: 50px;
  .top-line {
    border-bottom: 1px solid;
    border-color: var(--theme-color-line);
  }
  .title {
    color: #ffffff;
    font-size: 18px;
    text-align: left;
  }
  .message {
    font-family: Arial;
    color: var(--theme-text-color-lighten);
    font-size: 18px;
  }
  .phone-num {
    margin-top: 20px;
    text-align: center;
    .phone-type {
      font-weight: 700;
      color: var(--theme-text-color-lighten);
      font-size: 26px;
      margin-right: 20px;
      vertical-align: middle;
    }
    .two-num,
    .three-num {
      font-weight: 700;
      color: #c5e2d2;
      vertical-align: middle;
      font-size: 26px;
    }
    .input-box {
      display: inline-block;
      border: 1px solid;
      border-color: #fafd4d;
      border-radius: 4px;
      height: 60px;
      margin: 0 10px;
      vertical-align: middle;
      line-height: 60px;
      input {
        outline: none; /* 移除默认边框 */
        background: transparent;
        caret-color: #fff;
        width: 160px;
        color: #fff;
        font-size: 26px;
        text-align: center;
        letter-spacing: 7px;
      }
      input:focus {
        outline: none;
      }
    }
  }
}

.ant-space-item {
  color: white;
  font-size: 20px;
  margin: 5px 0 15px 0;
  display: flex;
  align-items: center;
  flex-direction: row;
  margin-top: 35px;
  &.passwordStrength-0 .step {
    background-color: var(--theme-color-line);
  }
  &.passwordStrength-1 span:nth-child(2) {
    background-color: var(--theme-secondary-color-error);
  }
  &.passwordStrength-2 span:nth-child(2),
  &.passwordStrength-2 span:nth-child(3) {
    background-color: var(--theme-secondary-color-finance);
  }
  &.passwordStrength-3 .step {
    background-color: var(--theme-secondary-color-success);
  }
  .step {
    border-radius: 10px;
    height: 12.5px;
    width: 75px;
    display: block;
    margin-left: 10px;
    background-color: var(--theme-color-line);
  }
}

.code {
  position: relative;
}

.code-button {
  position: absolute;
  right: 40px;
  top: 40px;
  display: inline-block;
  text-align: center;
  width: 120px;
  height: 60px;
  background: var(--theme-primary-color);
  border-radius: 7px;
  line-height: 60px;
  color: var(--theme-primary-font-color);
  font-size: 22px;
}
</style>
