<script setup lang='ts'>
import { colorFilter, stateFilter,format_date } from '~/utils/utils';
const tabList = [
  {
    label: 'De<PERSON><PERSON><PERSON>',
    value: '271'
  }, {
    label: 'Reti<PERSON>',
    value: '272'
  }
]
const formQuery = reactive({
  flag: '271',
  page: 1,
  page_size: 50
})

// const total = ref(0)
const listDeposit = ref<any[]>([])
const finished = ref(false)
const listWithdraw = ref<any[]>([])

//充值记录
const {loading: loading1, run : runUserDepositList } = useRequest(() => UserDepositList(), {
  onSuccess: (res:any) => {
    listDeposit.value=[]
    listDeposit.value = [...res]
    // listDeposit.value=[{id:111111111111111111,created_at:555555555,amount:250000,discount:50,tot_amount:250050},{id:111111111111111111,created_at:555555555,amount:250000,discount:50,tot_amount:250050}]
   
  },
  onError(res){

  }
})


//提现记录
const {loading: loading2, run : runUserWitdrawList } = useRequest(() => UserWitdrawList(), {
  onSuccess: (res:any) => {
    listWithdraw.value=[]
    listWithdraw.value = [...res]
    // listWithdraw.value=[{id:111111111111111111,created_at:555555555,amount:250000,fee:50,state:1},{id:111111111111111111,created_at:555555555,amount:250000,fee:50,state:1}]
   
  },
  onError(res){

  }
})




const refresh = () => {
  // formQuery.page = 1
  // finished.value = false
  // list.value.length = 0
  // run()
}
const loadMoreData = () => {
  // formQuery.page++
  // run()
}




const isLeft = ref(true)
const goToPage = (index: any) => {
  if(tabList[0].value==index){
    isLeft.value = true
    formQuery.flag =index
    if(listDeposit.value.length==0){
      runUserDepositList()
    }
    
    
  }else if(tabList[1].value==index){
    isLeft.value = false
    formQuery.flag =index
    if(listWithdraw.value.length==0){
      runUserWitdrawList()
    }
   
  }
 
}


</script>

<template>
  <div class="topDiv">
    <AppIndexHeader />
    <AppPageTitle left-arrow title="TRANSACOES" background="#324b6e" title-weight="700" />
    <div class="content">
      <div class="top-btn">
        <AppButton class="top-btn-item" fontSize="32" radius="10" whiteText :red1="isLeft" :red2="!isLeft" width="325" height="66" @click="goToPage(tabList[0].value)">{{tabList[0].label }}</AppButton>
        <AppButton class="top-btn-item" fontSize="32" radius="10" whiteText :red1="!isLeft" :red2="isLeft" width="325" height="66" @click="goToPage(tabList[1].value)">{{tabList[1].label }}</AppButton>
      </div>
      <div class="listbox">
      <AppList>
        <div class="listbox_ItemTitle"  v-if="formQuery.flag==tabList[0].value">
          <span class="listbox_ItemTitle_item element1" >ID Transações</span>
          <span class="listbox_ItemTitle_item element2" >Data</span>
          <span class="listbox_ItemTitle_item element3">Valor do depósito</span>
          <span class="listbox_ItemTitle_item element4">Bônus</span>
          <span class="listbox_ItemTitle_item element5">Valor Final</span>
          <span class="listbox_ItemTitle_item element6">Status</span> 
        </div>
          <div class="list-item"  v-for="(item, index) in  listDeposit" :key="index">
            <span class="element7">{{ item.id }}</span>  
            <span class="element8">{{ format_date(item.created_at*1000)  }}</span> 
            <span class="element9">{{ UsAmountFormat(item.amount) }}</span>
            <span class="element10">{{UsAmountFormat(item.discount) }}</span>
            <span class="element11">{{UsAmountFormat(item.tot_amount) }}</span>
            <span  class="element12">{{stateFilter2(item.state) }}</span>
    
          </div>
        </AppList>

        <!--提现  -->
        <div  class="listbox_ItemTitle" v-if="formQuery.flag==tabList[1].value">
            <span class="element1">ID Transações</span>
            <span class="element2">Data</span>
            <span class="element16">Valor do Saque</span>
            <span class="element13">Taxa de saque</span>
            <span class="element6">Status</span>
            <!-- <span class="element6">Detalhes</span> -->
        </div> 
        <AppList v-if="formQuery.flag==tabList[1].value" :loading="loading2" :finished="finished" height="600px" @refresh="refresh" @loadMoreData="loadMoreData" v-show="listWithdraw.length"  style="margin-top: 10px;">
          <div class="list-item"  v-for="(item, index) in  listWithdraw" :key="index">
            <span class="element7">{{ item.id }}</span>  
            <span class="element8">{{format_date(item.created_at*1000)  }}</span> 
            <span class="element17">{{ UsAmountFormat(item.amount) }}</span>
            <span class="element14">{{ UsAmountFormat(item.fee) }}</span>
           <span  class="element15" :style="{ color: colorFilter(item.state) }" >{{stateFilter(item.state) }}</span>
          </div>
        </AppList>
      <app-empty v-if="formQuery.flag==tabList[0].value  && listDeposit.length==0 " />
      <app-empty v-if="formQuery.flag==tabList[1].value  && listWithdraw.length==0 " />
    </div>

      
  </div>

  </div>
  
</template>

<style lang='scss' scoped>


.topDiv {
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 132px;
  background: var(--theme-main-bg-color);
  // background: #aa0c85;
  position: relative;

}

.content{
  // background: var(--app-box-bg-color);
  width: 700px;
  margin:0 auto;
  border-radius: 10px;
  height:78vh; 

  .top-btn{
    display: flex;
    justify-content: flex-start;
   
  }
  .top-btn-item{
    margin-top: 20px;
    margin-left: 16.6px; /* 按钮之间的间隔 */
  }
}


.top-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  height: 210px;
}

.flex-sb {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.listbox {
    margin: auto;
    margin-top: 20px;
    font-size: 20px;
    // color: var(--app-title-color);
    width: 700px;
    .listbox_ItemTitle{
      @extend .flex-sb;
      background:#3b82f6;
      border-radius:10px;
      height: 75px;
      color: #ffffff;
    }

    .element1 {
      left:40px; /* 向右移动40px */
    }

    .element2{
      left:220px; /* 向右移动40px */
    }

    .element3{
      left:290px; /* 向右移动40px */
      width: 120px;
    }
    .element4{
      left:425px; /* 向右移动40px */
      width: 80px;
    }

    .element5{
      left:530px; /* 向右移动40px */
      width: 80px;
    }

    .element6{
      left:625px; /* 向右移动40px */
      width: 80px;
    }

    .element13{
      left:460px; /* 向右移动40px */
      width: 80px;
    }

    .list-item {
      height: 45px;
      margin-top:25px;
      overflow: hidden;
      color: #FFFFFF;
      &:nth-child(2n){
        @extend .flex-sb;
        background:#0f306a;
        border-radius:10px;   
        height:80px;
      }
    }

    .list-item span , .listbox_ItemTitle span{
      word-wrap: break-word;
      text-align: center;
      position: absolute;

    }

    // 
    .element7{
      left:10px; 
      width: 125px;
    }

    .element8{
      left:150px; 
      width: 125px;
    }

    .element9{
      left: 275px; 
      width: 100px;
    }

    .element10{
      left: 400px; 
      width: 80px;
    }

    .element11{
      left: 490px; 
      width: 100px;
    }

    .element12{
      left:600px; 
      width: 80px;
    }

    .element14{
      left:435px; 
      width: 80px;
    }

    .element15{
      left:560px; 
      width: 120px;
    }

    .element16{
      left:310px; /* 向右移动40px */
      width: 120px;
    }

    .element17{
      left: 295px; 
      width: 100px;
    }
  }

</style>

<route lang="yaml">
  meta:
    auth: true
</route>

















