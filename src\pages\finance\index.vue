<script lang="ts" setup name="FinancePage">
import { showConfirmDialog } from 'vant';
const router = useRouter()

const appStore = useAppStore()
const { isRefreshShop} = storeToRefs(appStore)

// const tabs = [
//   { label: 'Depósito', value: '1' },
//   { label: 'Retirar', value: '2' },
// ]
const activeTab = ref(router.currentRoute.value.query.tab === 'withdraw' ? '2' : '1')
//渠道信息
const depositChannel = ref({
  fid: "",
  fmax: 0,
  fmin: 0,
})
//充值物品
const items=ref<any>([])

const depositAmount = ref(0)//当前充值金额
const nextBonus =ref(0) //下一级可以领取金币
const nextdeposit = ref(0) //下一级需要充值进而
const bonusProgress = ref(0);//进度
const bonusProgressTip= ref(0)
const curCanGetMoney = ref(0) //当前可以领取多少钱
//-------------------------------------------------
const curOptionIndex = ref(-1); //选择充值选项id
const bonusText = ref(0);  //送多少金币
const rechargeAmount = ref(0);
//------------------------------------------------
const isShowIosTip = ref(false)
const showMessage  = ref("")
const chargeUrl = ref("")
//获取活动数据
const {  run: runGetRechargeActivity} = useRequest(RechargeActivity, {
  onError: () => {
  },
  onSuccess: (data) => {
    if (data) {
      curCanGetMoney.value = 0
      depositAmount.value = data.deposit_amount
      nextBonus.value = 0
      nextdeposit.value = 0
      bonusProgress.value= 0
      
      for(let i=0;i<data.item.length;i++){
        if(depositAmount.value<data.item[i].accu_deposit_amount ){  //!(data.state & (1 << i))
          nextBonus.value = data.item[i].bonus
          nextdeposit.value = data.item[i].accu_deposit_amount
          bonusProgress.value = depositAmount.value/nextdeposit.value*100
          break
        }
        //
        if(depositAmount.value>=data.item[i].accu_deposit_amount){
          if( data.state & (1 << i) ){
         
          }else{
            curCanGetMoney.value+=data.item[i].bonus
          }
        }
      }

      bonusProgressTip.value =parseInt(bonusProgress.value/100 * 422 + 20) //329 // bonusProgress.value/100 * 422 + 20
      // console.log("==========="+bonusProgress.value )
      // console.log("==========="+bonusProgressTip.value)
    }
  }
})

//获取充值选项
const { run: runApiGetDepositChannel,data: channelsConf } = useRequest(ApiGetDepositChannel, {
  onError: () => {
  },
  onSuccess: (data) => {
    if (data && data.d[0]) {
      items.value=[...data.d[0].amount_array]
      depositChannel.value.fid = data.d[0].fid
      depositChannel.value.fmin =Number(data.d[0].fmin) 
      depositChannel.value.fmax = Number(data.d[0].fmax)
    }
  }
})

//充值发送  () => ApiGetMemberRecord(formQuery)

const { run: postDeposit ,loading} = useRequest(() =>ApiDoDeposit({fid:depositChannel.value.fid , amount:rechargeAmount.value+"",flag:"1"}), {
  manual: true,
  onError: () => {
  },
  onSuccess: (data: any) => {
    if (data && data.addr) {
      chargeUrl.value = data.addr
      //ios 打开浏览器提示
      const userAgent = navigator.userAgent || navigator.vendor 
      if (userAgent.indexOf("iPad") > -1 || userAgent.indexOf("iPhone") > -1 || userAgent.indexOf("iPod") > -1) {
        showMessage.value = "R$ "+rechargeAmount.value
        if(Number(bonusText.value)>0){
          showMessage.value = "R$ "+rechargeAmount.value + " Bônus"+bonusText.value
        }
        isShowIosTip.value=true
      }else{
        openUrl()
      }
    }
  },
  onAfter: () => {
    appStore.runGetMemberInfo()
    // showMessage.value = "R$ "+rechargeAmount.value
    // if(Number(bonusText.value)>0){
    //   showMessage.value = "R$ "+rechargeAmount.value + " Bônus"+bonusText.value
    // }
    // isShowIosTip.value=true
  }
})

//选中物品
function optionSelect(idx:number){
  // console.log(`选择参数${idx}`)
  if(items.value[idx]){
    curOptionIndex.value = idx;
    rechargeAmount.value = items.value[idx].amount;
    // bonusText.value = items.value[idx].discount;
   
  }
 
}

//点击充值
function goToRecharge(){
  // console.log(`充值金额：${rechargeAmount.value}`)
  if(rechargeAmount.value>0){ // && curOptionIndex.value>=0
    postDeposit()
  }

  // isShowIosTip.value = true
  // appStore.setIsShowMessage(true,"Bônus +R$ ")
}


// watch(activeTab, (val) => {
//   if (val === '2') {
//     if (userInfo.value && userInfo.value.pay_password && +userInfo.value.pay_password !== 1) {
//       payPwdTip.value = true
//     }
//   }
// })
// const payPwdTip = ref(false)


watch(rechargeAmount, (val) => {
  // console.log(val)
  if(val && val>0 && val>=depositChannel.value.fmin && val<=depositChannel.value.fmax ){
    for(let i=0;i<items.value.length;i++){
      if( Number(val)>= Number(items.value[i].amount) && (items.value[i+1] ? (Number(val)< Number(items.value[i+1].amount)):(true) ) ){
        bonusText.value = items.value[i].discount;
        break
      }
    }
  }else{
    bonusText.value = 0
  }
})


const goPayPwd = () => {
  router.push('/safe-center/bind-pay-pwd')
}

const goCashbackLoss = () =>{
  router.push('/finance/details')
}


const { run :runGetRechargeActivityMoney} = useRequest( ()=>getRechargeActivity({state:0}), {
  manual: true,
  onError: (data) => {
    console.log(data)
    showErrorTip(data)
  },
  onSuccess: (data) => {
    if (data) {
        //重新获取数据
        runGetRechargeActivity()
        //刷新金币
        appStore.runGetUserBalance();
        //提示
        appStore.setIsShowMessage(true,"Bônus +R$ "+data)
    }

    // appStore.setIsShowMessage(true,"Bônus +R$ "+1000000.01)
  },

})


//领取活动奖励
const goGetMoney=()=>{
  runGetRechargeActivityMoney()
}

//刷新充值活动
watch(isRefreshShop, (val) => {
  if(isRefreshShop.value){
    runGetRechargeActivity()
    appStore.runGetUserBalance();
  }
 
})

function closePage(){
  isShowIosTip.value =false
}

function openUrl(){
  window.open(chargeUrl.value)
  isShowIosTip.value=false
}

const cpf = ref("");
</script>

<template>
  <div class="finance-page">
    <AppIndexHeader />
    <AppPageTitle left-arrow title="Depósito" title-weight="700" />
    <section class="content">
      <!-- 充值奖励信息 -->
      <div class="bonus-info">
        <!-- <AppImage src="/img/finance/bonus-title.webp"></AppImage> -->
        <div class="bonus-progress">
          <AppProgress :value="bonusProgress" :max="100" :width="422" :height="54" :radius="10"/>
          <!-- 进度条上面的三角形标记 -->
          <div class="progress-tips" :style="`left:var(--app-px-${bonusProgressTip})`"><!--R${{depositAmount}}--> 
            <AppImage src="/img/finance/icon_progress_tips"></AppImage>
          </div>
          
          <!-- 进度条中间 当前值 -->
          <div class="curValue">
            <span class="curValue-span1">R${{depositAmount}}/</span><span class="curValue-span2">R$ {{nextdeposit}}</span> 
          </div>
          <!--进度条右边  -->
          <div class="bonus-value">
              <label class="label1" >R${{nextBonus}}</label> 
          </div>
          <!-- <div class="progress-tips1">
            <AppImage src="/img/finance/whileTop"></AppImage>
          </div>
          <label class="progress-tips1_test">R$ {{nextdeposit}}</label> -->
        </div>

          
        <div class="deposite-mais-e-ganhe-de-b-nus">
          Deposite mais <span>R$ {{ nextdeposit }}</span> e ganhe <span>R$ {{ nextBonus }}</span> de bônus
        </div>


        <div class="canGetMoneybg">
          <span> Deposit bonus : </span> <span> R$ {{curCanGetMoney}} </span>
          <!--  -->
          <div class="btn_getMoney" @click="goGetMoney">
            <label>
              Obter agora
            </label>
          </div>

          <div class="btn_detalhes" @click="goCashbackLoss">
            <label>
              Detalhes
            </label>
          </div>

        </div>

      </div>

      <!-- 充值选项列表 -->
      <div class="charge-option-list">
        <div class="option-item" v-for="(item,n) in items" :key="n" :class="n==curOptionIndex?'active':''" @click="optionSelect(n)">
          <span style="font-weight: bold;font-style: italic;font-size: var(--app-px-30);" :class="{'Disc': item.discount<=0  }" >R$ </span> <span style="font-size: var(--app-px-30);" :class="{'Disc': item.discount<=0  }">{{ item.amount }}</span> 
          <br> 
          <span style="color:#FFE91C;" v-if="item.discount>0">
            +RS {{item.discount}}
          </span>
        </div>
      </div>
      <!-- 自定义输入框 -->
      <div class="custom-input-box">
        Digite seu valor
        <AppInput v-model="rechargeAmount" type="number" :styleObj="{
          width:'100%',
          background:'var(--app-box-bg-color)',
          color: '#172554',
          // fontSize:'30px'
        }">
          <template #left ><span class="font-default">R$</span></template>
          <template #right ><span style="color: #3b82f6; padding: 0 10px;">+R$ {{ bonusText }}</span></template>
        </AppInput>
      </div>
      <!-- 充值按钮 -->
      <van-button @click="goToRecharge" type="primary" :loading="loading"  block color="var(--app-shop-pagar-color)" style="font-weight: bold;" text="PAGAR"></van-button>
    </section>
  </div>
  <!-- ios提示框 -->
  <van-popup class="app-login-register" v-model:show="isShowIosTip" teleport="body" round :overlay-style="{background:'rgba(255,255,255,0)'}" :close-on-click-overlay="false">
    <div class="contentIosTip">
      <div class="textbgIosTip">
        <AppImage src="/icons/SaldoLivre.webp" class="iconIosTip"/>
        <label class="textIosTip">{{ showMessage }}</label>
      </div>
      <div class="submitIosTip">
        <AppButton @click="closePage"  class="" width="220" height="80" green :radius="15" white-text font-size="30" bold>Cancelar</AppButton>
      </div>
      <div class="submitIosTip2">
        <AppButton @click="openUrl"  class="" width="220" height="80" blue :radius="15" white-text font-size="30" bold>Confirmar</AppButton>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';


.finance-page {
  background: var(--theme-main-bg-color);
  padding-top: var(--app-navbar-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 132px;
  // font-family: Arial;
  // justify-content: center;

  // flex-wrap: wrap;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 678px;

  .bonus-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 678px;
    height: 420px;
    @include webp('/img/finance/bonus-info');
    background-size: 100% 100%;
    border-radius: 10px;
    padding: 40px 0 0 0;

    img {
      margin-left: 19px;
      width: 650px;
      height: 65px;
    }


    .bonus-progress {
      position: relative;
      display: flex;
      align-items: center;
      padding: 20px;
      margin-top: 55px;
      margin-left:-90px;
      .progress-tips{
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        top: 30px;
        transform: translateX(-50%);
        color: #f43f5e;
        font-size: 22px;
        img {
          display: block;
          margin-left: 0;
          width: 22px;
          height: 20px;
        }
      }


      .progress-tips1{
        display: block;
        position: absolute;
        top: 100px;
        right: 160px;
        img {
          display: block;
          margin-left: 0;
          width: 16px;
          height: 8px;
        }
      }

      .progress-tips1_test{
        display: block;
        width: 250px;
        text-align: center;
        position: absolute;
        color: #fff;
        top: 120px;
        left: 280px;
        font-size: 22px;
       
      }

    
      .bonus-value {
        // position: relative;
        margin-left: -8px;
        @include webp('/img/finance/bg_bonus');
        background-size: 100% 100%;
        width: 137px;
        height: 126px;
        display: flex;
        justify-content: space-between;
        align-items: center;
     
        .label1 {
          // display: block;
          // width: 100px;
          // height: 120px;
          margin-top: 15px;
          margin-left: 12px;
          // font-size: 36px;
          // font-weight: bold;
          // font-stretch: normal;

          text-align: center;
          // background-color: #000000;
          font-weight: bold;
          font-size: 36px;
          width: 110px;
          line-height: 36px;
          word-wrap: break-word; /* 旧版浏览器支持 */
          overflow-wrap: break-word; /* 标准属性 */
          color: #ffe91c;
        }

        .label2 {
          margin-top: 73px;
          font-size: 32px;
          line-height:32px ;
        }

       
      }

      // img {
      //   margin-left: 16px;
      //   width: 104px;
      //   height: 113px;
      // }

    }
  }
}

.deposite-mais-e-ganhe-de-b-nus {
  padding-top: 0px;
  // margin-left: -60px;
  width: 628px;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 37px;
  letter-spacing: 0px;
  color: #FFF;
  text-align: left;;

  span:nth-of-type(1) {
    font-size: 25px;
    letter-spacing: 0px;
    color: #f43f5e;
  }

  span:nth-of-type(2) {
    font-size: 25px;
    letter-spacing: 0px;
    color: #ffe91c;
  }
}


.charge-option-list {
  margin-top: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: flex-start;
  

  flex-wrap: wrap;
  .option-item {
    width: 204px;
    height: 100px;
    background: #2c2031;
    border-radius: 15px;
    margin-top: 24px;
    padding-top: 20px;
    margin-left: 33px;
    position: relative;
    // display: flex;
    text-align: center;
    color: white;
	  font-size: 22px;
    line-height: 130%;

    &:nth-child(3n+1){
      margin-left: 0px;
    }

    &.active {
      background: var(--app-red-color);
    }

    .Disc{
      // background-color: #172554;
      line-height: 60px;
    }
    
  }
}

.custom-input-box{
  width: 100%;
  color: var(--app-title-color);
  font-size: 24px;
  line-height: 60px;
  padding-bottom: 18px;
  .font-default {
    padding: 12px;
    line-height: 100%;
    color: var(--app-title-color);
    font-size: 30px;
    font-weight:600;

  }

  :deep(input) {
    font-size: 30px !important;
    font-weight:600;
  }
  
}


.curValue{
  position: absolute;
  top: 69px;
  left: 85px;
  width: 300px;
  font-size:26px;
  font-weight:700;
  text-align: center;
  &-span1{
    color:#f43f5e;
  }

  &-span2{
    color:#000000;
  }
}

.canGetMoneybg{
  position: absolute;
  top:530px;
  left: 52px;
  width:456px;
  height:60px;
  background:#090a20;
  border-radius:31px;
  color:#ffffff;
  span:nth-of-type(1){
    position: absolute;

    font-weight:700;
    font-size:20px;

    top:20px;
    left: 10px;

  }
  span:nth-of-type(2){
    position: absolute;
    font-weight:700;
    font-size:25px;
    color: #ffe91c;
    top:20px;
    left: 160px;
  }

}

.btn_getMoney{
  position: absolute;
  top: 0px;
  left: 290px;

  width:168px;
  height:62px;
  background:#6ec440;
  border-radius:31px;
  text-align: center;
 
  label{
    // background-color: #000000;
    line-height: 62px;
    width:168px;
    height:62px;
    font-weight:700;
    color:#ffffff;
    font-size:20px;
  }
}

.btn_detalhes{
  position: absolute;
  top: 0px;
  left: 480px;

  width:168px;
  height:62px;
  background:var(--app-red-color);
  border-radius:31px;
  text-align: center;
 
  label{
    // background-color: #000000;
    line-height: 62px;
    width:168px;
    height:62px;
    font-weight:700;
    color:#ffffff;
    font-size:20px;
  }


}

//--------------------------------------------
.contentIosTip {
  width:680px;
  height:350px;
  border-radius:20px;
  background-color:var(--theme-main-bg-color);
}

.textbgIosTip{
  width:612px;
  height:100px;
  background:var(--app-message-input-color);
  border-radius:15px;
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%-17);
}

.iconIosTip{
  width: 57px;
  margin-left: 35px;
  margin-top: 30px;
}

.textIosTip{
  position: absolute;
  transform: translate(-50%,-50%);
  left:50%;
  top:(50%+30);
  text-align: center;
  width: 500px;
  height: 100px;
  // line-height: 100px;
 
  font-weight:700;
  color:#ffffff;
  font-size:36px;

  // margin-left: 60px;
  // margin-top: 25px;
}


.submitIosTip{
  position: absolute;
  transform: translate(-50%,-50%);
  left:30%;
  top:(50%+26);
}

.submitIosTip2{
  position: absolute;
  transform: translate(-50%,-50%);
  left:70%;
  top:(50%+26);
}

// :deep(.c-app-progress) {
//   box-sizing: border-box;
//   font-size: 24px;
//   color: var(--app-text-color);

//   .text {
//     margin-bottom: 17px;
//     height: 33px;
//     line-height: 33px;

//     >span {
//       color: var(--app-text-color-2);
//     }
//   }

//   .progress {
//     // border-radius: 100px;
//     overflow: hidden;
//     // height: 22px;
//     position: relative;
//     width: 490px;
//     height: 8px;
//     border-radius: 4px;

//     .progress-bottom {
//       background-color: #eff6ff;
//       width: 100%;
//       height: 100%;
//     }

//     .progress-top {
//       height: 100%;
//       background: #f43f5e;
//       // background: linear-gradient(90.3deg, #0BE3FF 5.43%, #FFD90C 94.35%);
//       position: absolute;
//       top: 0;
//       transition: all .5s ease-out;

//       >span {
//         position: absolute;
//         top: 50%;
//         transform: translateY(-50%);
//         left: 22px;
//       }
//     }
//   }
// }


// .tip-content {
//   width: 634px;
//   border-radius: 20px;
//   background: #374a69;
//   padding-top: 48px;

//   .text {
//     color: #FFF;
//     text-align: center;
//     font-size: 26px;
//     font-weight: 400;
//     line-height: 40px;
//     padding: 0 66px;
//   }

//   .btns {
//     padding-top: 48px;
//     padding-bottom: 30px;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     gap: 20px;

//     .cancel {
//       width: 280px;
//       height: 90px;
//       line-height: 90px;
//       color: #FFF;
//       text-align: center;
//       font-size: 28px;
//       font-weight: 400;
//       border-radius: 100px;
//       background: #28374e;
//       cursor: pointer;
//     }
//   }
// }

// .finance-page {
//   background: var(--theme-main-bg-color);
//   min-height: 100vh;
//   padding-top: 90px;
//   padding-bottom: 132px;

//   .money-info {
//     width: 710px;
//     // height: 581px;
//     margin: 30px 20px 0;
//     color: #fff;
//     border-radius: 50px;
//     background-color: #28374d;
//     position: relative;
//     padding-bottom: 20px;
//     .bar {
//       // height: 192px;
//       // border-radius: 50px;
//       // background: linear-gradient(360deg, #044B9A 0%, #1373EF 53.65%, #0ED1F4 100%), linear-gradient(144deg, #466AC5 0%, #46B5FB 100%);
//       // box-shadow: 0px -16px 34px 0px #49B5FF inset;
//       // position: relative;

//       .tag {
//         width: 710px;
//         height: 82px;
//         line-height: 82px;
//         background-color: #324b6e;
//         text-align: center;
// 	      font-size: 30px;
//         color: #679fea;
//         border-radius: 50px 50px 0px 0px;
//       }

//       ul {
//         list-style: none;
//         color: #FFF;
//         font-size: 30px;
//         // font-weight: 700;
//         display: flex;
//         text-align: center;
//         align-items: center;

//         li {
//           width: 50%;
//           // padding-top: 56px;
//           display: flex;
//           flex-direction: column;
//           align-items: center;
//           justify-content: center;
//           &:nth-child(1){
//             width: 197px;
//             height: 236px;
//             @include webp('/icons/icon_full.png');
//             background-repeat: no-repeat;
//             background-size: 100% 100%;
//             position: absolute;
//             top: 112px;
//             left: 85px;
//             .desc {
//               width: 260px;
//               color: #ff3485;
//               font-size: 30px;
//               // padding-top: 17px;
//               font-weight: normal;
//               position: absolute;
//               // border: 1px solid #fff;
//               top: 222px;
//               left: -30px;
//             }
//           }
//           &:nth-child(2){
//             width: 197px;
//             height: 236px;
//             @include webp('/icons/icon_full_withdrawable.png');
//             background-repeat: no-repeat;
//             background-size: 100% 100%;
//             position: absolute;
//             top: 112px;
//             right: 89px;
//             .desc {
//               width: 260px;
//               color: #a44cff;
//               font-size: 30px;
//               // padding-top: 17px;
//               font-weight: normal;
//               position: absolute;
//               // border: 1px solid #fff;
//               top: 222px;
//               right: -40px;
//             }
//           }
//           div{
//             margin-top: -40px;
//             font-size: 25px;
//             font-weight: normal;
//           }
//         }
//       }
//     }

//     .detail {
//       color: #FFF;
//       font-size: 28px;
//       line-height: 40px;
//       display: flex;
//       margin-top: 310px;
//       li {
//         // width: 50%;
//         padding-left: 60px;
//         position: relative;
//         >div:nth-child(2) {
//           padding-top: 16px;
//           padding-bottom: 16px;
//         }
//         img{
//           width: 43px;
//           height: 43px;
//           margin-left: 10px;
//           position: absolute;
//         }
//       }
//       .van-popover__wrapper{
//         li{
//           >div:nth-child(2) {
//             padding-left: 35px;
//           }
//         }
//       }
//       .li2{
//         >div:nth-child(1) {
//           padding-right: 20px;
//         }
//         >div:nth-child(2) {
//           padding-left: 90px;
//         }
//       }
//       .small-white-txt{
//         background: #334b6f;
//         color: #4e7aaf;
//       }
//     }
//   }

//   .fin {
//     .fin-tab {
//       display: flex;
//       flex-direction: column;
//       justify-content: flex-end;
//       height: 110px;
//       background: linear-gradient(180deg, rgba(1, 26, 81, 0.00) 35.94%, #011A51 100%);
//       box-shadow: 0px -1px 0px 0px rgba(255, 255, 255, 0.10) inset, 0px 4px 5px 0px rgba(0, 0, 0, 0.30);
//     }

//     ul.fin-inner {

//       li.content-withdraw {
//         padding: 32px 20px 88px;
//       }
//     }
//   }
// }
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
