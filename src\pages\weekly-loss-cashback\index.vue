<script lang="ts" setup>

  const records = ref([
    { icon: 'convidar', text: 'Convidar', path: '/promotion-detail/invite', iconWidth: 0 }, // icon 图标从 figma 导出 x3 倍图
  
  ])
  const appStore = useAppStore()
  const { userInfo, loadingBalance, loadingUserinfo } = storeToRefs(appStore)


  const copyName = () => {
    copy(userInfo.value.uid || '')
    showToast('Copied!')
  }

// onMounted(() => {
//   runGetProSignConf()
// })

// const { run: runGetProSignConf, data: signProData } = useRequest(ApiGetPromotionSignConfig, {
//   manual: true
// })

  // {{ timestamp2Date(r.created_at * 1000, 'YYYY-MM-DD') }}<br />
  // {{ timestamp2Date(r.created_at * 1000, 'hh:mm:ss') }}


  const thisWeekStr =ref(getWeekDate());
  const lastWeekStr =ref(getWeekDate(true));

  const posts= ref([
    {id:1,title:"0+BRL",cashback:"12%"},
    {id:2,title:"5000+BRL",cashback:"14%"},
    {id:3,title:"20000+BRL",cashback:"16%"},
    {id:1,title:"80000+BRL",cashback:"18%"},
    
  ])

  const progressData = ref(150)
  const myprogress = computed(()=>{
    return progressData.value+"px"
  })


  const lostbackData = ref({
    minLostCashbackRatio: 0,
    maxLostCashbackRatio: 0
  })

  const {run: runGetWeekLostReturn,data:lostReturnItemDatas } = useRequest(() => ApiGetWeekLostReturn(), {
  manual: true,
  onSuccess(res:any) {
    console.log(res)
    if(res && res.length > 0) {
      lostbackData.value.minLostCashbackRatio = res[0].return_rate / 10;
      lostbackData.value.maxLostCashbackRatio = res[res.length - 1].return_rate / 10;
    }
    
  }
  })
  runGetWeekLostReturn();

  const curCashbackData = ref({
    curAposta: 0,
    curLv: 0,
    nextLvAposta: 0,
    nextLv:0
  })

  const getCurLvConfig = ()=>{
    if(weekLostReturnData.value && lostReturnItemDatas.value) {
      for(let index = 0; index < lostReturnItemDatas.value.length; index++) {
        if(weekLostReturnData.value.Running > lostReturnItemDatas.value[index].Running) {
          if(lostReturnItemDatas.value.length == index + 1) {
            curCashbackData.value.curLv = index + 1;
            curCashbackData.value.nextLvAposta =  0;
            break;
          } else if(lostReturnItemDatas.value[index + 1].Running < weekLostReturnData.value.Running){
            continue;
          } else {
            curCashbackData.value.curLv = index + 1;
            curCashbackData.value.nextLvAposta = lostReturnItemDatas.value[index + 1].Running;
            break;
          }
        } else {
          curCashbackData.value.curLv = index;
          curCashbackData.value.nextLvAposta = lostReturnItemDatas.value[index].Running;
          break;
        }
      }
      if(curCashbackData.value.curLv < lostReturnItemDatas.value.length){
        curCashbackData.value.nextLv = curCashbackData.value.curLv + 1;
        let curProgressDataNum = curCashbackData.value.curAposta / curCashbackData.value.nextLvAposta
        progressData.value = 84 * (curProgressDataNum > 1 ? 1: curProgressDataNum);
      } else {
        curCashbackData.value.nextLv = 0;
        progressData.value = 84;
      }
    }
  }
  const { run: runGetQueryWeekLostReturn, data: weekLostReturnData } = useRequest(() => ApiGetQueryWeekLostReturn(), {
    manual: true,
    onSuccess(res: any) {
      console.log(res)
      curCashbackData.value.curAposta = res.Running;
      getCurLvConfig();
    }
  })
  runGetQueryWeekLostReturn();

  const {run: runAchieveWeekLostReturn,data:achieveProxyWeekDepositData } = useRequest(() => ApiAchieveWeekLostReturn(), {
  manual: true,
  onSuccess(res:any) {
    //提示
    // appStore.setIsShowMessage(true,"Bônus +R$ "+ res)
    appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    runGetQueryWeekLostReturn();
    appStore.runGetUserBalance();
  }
  })
  // runGetAchieveWeekDeposit();

    //按钮点击
  const isRunAni = ref(false)
  function getMoney(){
    if(isRunAni.value) return;
    if(weekLostReturnData.value?.WeekLostReturn > 0){
      runAchieveWeekLostReturn();
    } else {
    isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  }
    console.log("dfdsfasf")
  }
</script>

<template>
  <div class="topDiv">
    <AppIndexHeader />
    <AppPageTitle left-arrow title="PERDA DE CASHBACK" background="#324b6e" title-weight="700" />
      <div class="user-info-box-outer"> <!--顶部显示-->
        <AppImage  src="/img/weekly/weekly_top_bg.webp"  class="topImgSize" />
        <AppImage  src="/img/weekly/weekly_top_m.webp"   class="topImgMoney" />
        <label class="topProgress">{{ lostbackData.minLostCashbackRatio + "% Cashback" }}</label>
        <label class="topMax">{{"Max " + lostbackData.maxLostCashbackRatio + "%"}}</label>
        <AppImage class="topleveImg" src="/img/weekly/weekly_top_le.webp"/>
        <label class="topleveTest">{{ curCashbackData.curLv }}</label>
        <!-- <progress max="100" value="0"></progress> -->
        <div class="progressbg"></div>
        <div class="progressBar" id="progress" :style={width:myprogress} ></div>
        <label class="topNextLeve" v-if="curCashbackData.nextLv > 0">{{ (curCashbackData?.nextLvAposta || 0) + " Apostas to"}} </label>
        <AppImage class="topleveImg2" src="/img/weekly/weekly_top_le.webp" v-if="curCashbackData.nextLv > 0"/>
        <label class="topleveTest2" v-if="curCashbackData.nextLv > 0">{{ curCashbackData.nextLv }}</label>
      </div>
      <div class="centerDiv"> <!--中间显示-->
        <div class="center_titleBg">
          <label class="center_title">Seu Cashback de Semanal </label>
          <AppImage class="center_leftImg" src="/img/weekly/weekly_c_right.webp"/>
          <label class="center_detial">Você ganhará <label style="color: #f43f5e;">{{ weekLostReturnData?.WeekLostReturn || 0 }}</label> BRL em cashback jogando esta semana.</label>

          <label class="center_timeEnd">{{ "Periodo " + thisWeekStr }}</label>

          <AppButton  @click="getMoney"  width="631" height="67" red :radius="15" white-text  style="margin-top: 30px; margin-left: 7px;">
            Reivindicar Agora
          </AppButton>
          <label class="center_endTime" >Tempo de reivindicação</label>
          <label class="center_endTime" :class="{ 'scaleAni': isRunAni }" style="margin-top: 30px;" > {{ "Periodo " + lastWeekStr }}</label>
        </div>
      </div>
      <div class="belowDiv"> <!--下面显示-->
        <label class="below_title">CASHBACK E REGRAS</label>
        <div class="below_itemTitle">
          <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp"/>
          <label class="below_itemTitle_aposta">APOSTA</label>
          <label class="below_itemTitle_aposta below_itemTitle_cashback">CASHBACK</label>
        </div>
          
        <div v-for="(data, index) in lostReturnItemDatas"  :key="index"  class="below_itemDevPa">
          <AppImage class="itemIcon" src="/img/weekly/weekly_top_le.webp"/>
          <label class="itemIconLev">{{ index + 1}}</label>
          <label class="itemContent">{{ data.Running }}</label> 
          <label class="itemContent1  ">{{ (data.return_rate / 10) + "%" }}</label>   
        </div>
      
      </div>
    <section>
      <AppRule title="TERMOS E REGRAS" 
          content="1.O cashback semanal é dado como recompensa todas as semanas
2.O período sobre o qual é calculado o cashback semanal vai de segunda-feira às 00:00 a domingo às 23:59
3.Horário de solicitação do cashback:De segunda-feira da próxima semana 06:00 a domingo 23:59, expirará se não for resgatado.
4.O número de Perdas de dinheiro real multiplicado pela % dereembolso é o reembolso/cashback da semana.
5.Caso você não tenha apostado durante o período em que o cashback estava ativo ou se seus ganhos da última semana ou ganhos totais saomaiores que suas perdas, você nao receberà cashback.
6. Limite máximo de recompensa de cashback por pessoa por semana:R$ 10000
7.O valor do cashback pode ser sacar diretamente ou utilizado para continuar jogando"></AppRule>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.pt-30 {
  padding-top: 30px;
}

.content{
  // margin: 0 auto
  display: flex;
  flex-direction: column;
  align-items: center;
  
}

.topDiv {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 112px;
  padding-bottom: 132px;
  background: var(--theme-main-bg-color);
  position: relative;
  .user-info-box-outer {
    width: 700px;
    height: 160px;
    background-color: var(--theme-main-bg-color);
   
  }

  .topImgSize{
    width: 678px;
    height: 160px;
    margin: 0 auto;
    display: block;
  }

  .topImgMoney{
    position: absolute;
    margin-top: -140px;
    margin-left: 43px;
    width: 51px;
    height:47px;
   
  }

  .topProgress{
    position: absolute;
    margin-top: -130px;
    margin-left: 117px;
    text-align: left;


   
    font-family:Arial;
    font-weight:700;
    color:#ffffff;
    font-size:38px;
  }

  .topMax{
    position: absolute;
    margin-top: -113px;
    margin-left: 402px;
    text-align: left;


  
    font-family:Arial;
    color:#ffffff;
    font-size:18px;
  
  }

  .topleveImg{
    position: absolute;
    margin-top: -140px;
    margin-left: 620px;
    width: 48px;
    height:56px;
   
  }

  .topleveTest{
    position: absolute;
    margin-top: -132px;
    margin-left: 620px;

    width: 48px;
    height:56px;
    text-align: center;
    font-family:Arial;
    font-weight:700;
    font-style:italic;
    color:#f43f5e;
    font-size:30px;
   
  }


  .topNextLeve{
    position: absolute;
    margin-top: -40px;
    margin-left: 500px;
    text-align: center;
    color:#ffffff;
    font-size:18px;
  
  }


  
  .topleveImg2{
    position: absolute;
    margin-top: -45px;
    margin-left: 635px;
    width:28px;
    height:32px;
   
  }

  .topleveTest2{
    position: absolute;
    margin-top: -42px;
    margin-left: 643px;

    
    font-weight:700;
    font-family:Arial;
    font-style:italic;
    color:#f43f5e;
    font-size:20px;
  }




}


.centerDiv{
  width:678px;
  height:405px;
  background:#f8e5f6;
  border-radius:15px;
  position: relative;
  margin-top: 20px;
  .center_titleBg{
    width:482px;
    background-size: 100%;
    margin-left:10px;
    margin-top: 20px;
    @include webp('/img/weekly/weekly_c_titlebg');
  }

  .center_title{
    position:absolute;
    margin-left: 30px;
    margin-top: 7px;
   
    font-family:Arial;
    font-style:italic;
    color:#ffffff;
    font-size:34px;
    font-weight:700;
  }

  .center_leftImg{
    width:165px;
    height:164px;
    margin-left: 500px;
    margin-top: -20px;
  }

  .center_detial{
    position:absolute;
    margin-top: -75px;
    margin-left: 12.5px;
    width:441px;
    height:66px;
    
    font-family:Arial;
    color:#172554;
    font-size:26px;
  }

  .center_timeEnd{
    position:absolute;
    margin-top: 10px;
    margin-left: 12.5px;
    font-family:Arial;
    color:#f43f5e;
    font-size:26px;
  }

  .center_endTime{
    position:absolute;
    margin-top: 20px;
    margin-left: 12.5px;
    font-family:Arial;
    color:#172554;
    font-size:26px;
    padding-bottom:5px;
    &.scaleAni {
      // animation: spin 1s linear infinite;
      animation:sacleAniFrames 0.8s ease;
    }
  }
  

@keyframes sacleAniFrames {
  25% {
    transform: scale(1.08);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.08);
  }

  100% {
    transform: scale(1);
  }
}
.belowDiv{
  margin-top: 20px;
  width:678px;
  position: relative;
  background:var(--app-box-bg-color);
  border-radius:15px;
    .below_title{
      display: block;
      margin-top: 25px;
      margin-left: 25px;
      
      font-family:Arial;
      font-weight:700;
      color:var(--app-ratio-title-color);
      font-size:24px;
    }
    .below_itemTitle{
      display: flex;
      margin: 0 auto;
      margin-top: 20px;
      width:632px;
      height:68px;
      background:#f8e5f6;
      border-radius:15px;

    }
    .below_itemImg{
      width:41px;
      height:41px;
      margin-left: 20px;
      margin-top: 15px;
   }

   .below_itemTitle_aposta{
    margin-left: 105px;
    margin-top: 24px;
   
    font-family:Arial;
    font-weight:700;
    color:#f43f5e;
    font-size:24px;
  }

  .below_itemTitle_cashback{
    margin-left: 110px;
  }


}


.below_itemDevPa{
    display: flex;
    flex-direction:column;
    align-items: center;
    width: 632px;
    height: 68px;
    line-height: 68px;
    margin:0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
    position: relative;
    &:nth-child(2n){
      background:var(--theme-main-bg-color);
      border-radius:15px;   
      width:632px;
      height:68px;
      line-height: 68px;
    }
   

    .itemIcon{
      position: absolute;
      width:28px;
      height:32px;
      margin-left:-550px;
      margin-top: 20px;
    }
    .itemIconLev{
      position: absolute;
      margin-left:-550px;
      margin-top: 2px;
     
      font-family:Arial;
      font-weight:700;
      font-style:italic;
      color:#f43f5e;
      font-size:20px;
    }

    .itemContent{
      position: absolute;
      margin-left:-200px;
   
      font-family:Arial;
      color:var(--app-title-color);
      font-size:24px;
      padding-bottom:39px;
      text-align:center;
      transform: translateY(4px);
    }

    .itemContent1{
      position: absolute;
      margin-left:250px;
    
      font-family:Arial;
      color:var(--app-title-color);
      font-size:24px;
      padding-bottom:39px;
      text-align:center;
      transform: translateY(4px);
    }
}


.bottomDiv{
  width:684px;
  margin:0 auto;
  margin-top: 30px;
  .bottomImg{
    width:685px;
    height:557px;
    margin-bottom: 20px;
  }
}

.progressbg{
    position: absolute;
    width:629px;
    height: 11px;
    margin-top: -70px;
    margin-left: 35.5px;

    background:#ffffff;
    border:1px solid;
    border-color:#707070;
    border-radius:27px;
}

.progressBar{
    position: absolute;
    margin-top: -70px;
    margin-left: 35.5px;

    // width:404px;
    height:11px;
    background:#f43f5e;
    border:1px solid;
    border-color:#707070;
    border-radius:27px;
}
.table-label {
  padding-top: 4px;
}
}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
