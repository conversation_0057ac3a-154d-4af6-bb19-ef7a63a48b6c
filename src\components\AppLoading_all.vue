<script lang="ts" setup name="AppActivePopup">

const appStore = useAppStore();
const { isShowLoading} = storeToRefs(appStore);

</script>

<template>


  <van-popup v-model:show="isShowLoading" teleport="body" :close-on-click-overlay="false" >
    <div class="content" v-if="isShowLoading">
      <!-- <van-loading vertical>
        <template #icon>
          <van-icon name="star-o" size="30" />
        </template>
      </van-loading> -->
      <!-- <van-loading color="#1989fa"  /> -->
      <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>



.content {
   width:150px;
   height:150px;
   display: flex;
   align-items: center;
   justify-content: center;
  // margin: 0 auto;
}
</style>
