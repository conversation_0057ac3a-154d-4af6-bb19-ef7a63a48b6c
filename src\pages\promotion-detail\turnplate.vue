<script setup lang='ts' name='turnplate'>
	const appStore = useAppStore()
	const { userInfo, turnTableInfo } = storeToRefs(appStore)
	const myLucky = ref()
	let count = turnTableInfo?.value?.unuse_chance || 0
	const blocks = ref([{ padding: '6vw', imgs: [{
		src: '/img/promotion/turnplate/bg_pedestals.png',
		rotate: false,
		top: '72vw',
		left: '0px',
		width: '80vw',
		// height: '90vw',
	},{
		src: '/img/promotion/turnplate/dark.png',
		rotate: false,
		top: '0vw',
		left: '0px',
		width: '90vw',
		height: '90vw',
	}]}])
	const prizes = ref([
		{ type: 1, fonts: [{ text: '?', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fff7f2',imgs: [{
			src: '/img/promotion/turnplate/redicon.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}]},
		{ type: 2, fonts: [{ text: '?', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fcf1d3',imgs: [{
			src: '/img/promotion/turnplate/redicon.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
		{ type: 3, fonts: [{ text: '1000', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fff7f2',imgs: [{
			src: '/img/promotion/turnplate/redicon.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
		{ type: 4, fonts: [{ text: '50', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fcf1d3',imgs: [{
			src: '/img/promotion/turnplate/redicon.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
		{ type: 5, fonts: [{ text: '1', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fff7f2',imgs: [{
			src: '/img/promotion/turnplate/redicon.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
		{ type: 6, fonts: [{ text: '?', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fcf1d3',imgs: [{
			src: '/img/promotion/turnplate/prize-gift.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
		{ type: 7, fonts: [{ text: 'SACAR', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fff7f2',imgs: [{
			src: '/img/promotion/turnplate/prize-gift.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
		{ type: 8, fonts: [{ text: '', top: '50%',fontColor:'#c48a6b',fontSize:'4vw' }], background: '#fcf1d3',imgs: [{
			src: '/img/promotion/turnplate/prize-face.png',
			top: '20%',
			left: '0px',
			width: '6vw',
		}] },
	])
	const buttons = ref([{
		radius: '35%',
		// background: '#8a9bf3',
		pointer: false,
		fonts: [{ text: count, top: '-8vw',fontColor:'#fff',fontSize: '6vw' },{ text: `vezes`, top: '0vw',fontColor:'#fff',fontSize: '6vw' }],
		imgs:[{
			src: '/img/promotion/turnplate/lottery-btn.png',
			top: '-15vw',
			width: '30vw',
			height: '30vw'
		}]
	}])

	const { run: runTurnTableClick, data: turnTableClickData } = useRequest(ApiTurnTableClick, {
		manual: true,
		onSuccess: (data) => {

		}
	})
	console.log(userInfo.value)
	const { run: runTurnTableApply } = useRequest(() => ApiTurnTableApply({
		uid: userInfo.value.uid,
		username: userInfo.value.username,
		amount: turnTableInfo.value.reward_amount,
	}), {
		manual: true,
		// onError: (data) => {
		// 	showToast('A retirada falhou')
		// },
		onSuccess: (data) => {
			console.log(data)
			showToast('sucesso')
		}
	})
	// 点击抽奖按钮会触发star回调
	const startCallback = async () => {
		if (count < 1) {
			showToast('Você não tem mais chances')
			return
		}
		count = count - 1
		buttons.value[0].fonts[0].text = count
		// 调用抽奖组件的play方法开始游戏
		myLucky.value.play()
		console.log(JSON.stringify(prizes.value), 'prizes..')
		await runTurnTableClick({ uid: userInfo.value.uid })
		// 模拟调用接口异步抽奖
		setTimeout(() => {
			// 假设后端返回的中奖索引是2
			console.log(turnTableClickData.value.type, 'turnTableClickData')
			// 匹配中奖索引
			const index = prizes.value.findIndex(item => item.type == turnTableClickData.value.type)
			// const index = 1
			// 调用stop停止旋转并传递中奖索引
			myLucky.value.stop(index)
		}, 3000)
	}
	// 抽奖结束会触发end回调
	const endCallback = (prize: any) => {
		showToast(turnTableClickData.value.remark)
		// if (turnTableClickData.value.type == 8) {
		// 	showToast(turnTableClickData.value.remark)
		// 	return
		// }
		// showToast(`${turnTableClickData.value.remark},金额R$${turnTableClickData.value.remark}`)
	}
	// 提现
	const sacar = () => {
		runTurnTableApply()
	}
</script>
<template>
  <AppHeader leftArrow placeholder title="turnplate" />
  <div class="turnplate">
		<img src="/img/promotion/turnplate/pointers.png" class="turnplate-pointer">
		<LuckyWheel
			class="luck-wheel"
			ref="myLucky"
			width="90vw"
			height="110vw"
			:prizes="prizes"
			:blocks="blocks"
			:buttons="buttons"
			@start="startCallback"
			@end="endCallback"
		/>
		<div class="sacar">
			<div class="sacar-amount">R${{ turnTableInfo?.reward_amount || 0 }}</div>
			<div class="sacar-btn" @click="sacar">SACAR</div>
		</div>
  </div>
</template>

<style lang='scss' scoped>
.turnplate{
	position: relative;
	padding-top: 60px;
	width: 100%;
	&-pointer{
		width: 80px;
		height: 120px;
		position: absolute;
		left: calc(50% - 40px);
		top: 20px;
		z-index: 1;
	}
	.luck-wheel{
		margin: 0 auto;
	}
	.sacar{
		&-amount{
			height: 100px;
			line-height: 100px;
			color: #fff;
			text-align: center;
			font-size: 50px;
		}
		&-btn{
			width: 251px;
			height: 75px;
			line-height: 75px;
			background: url('/img/promotion/turnplate/btn_get_rewarded.png') no-repeat;
			background-size: 100% 100%;
			margin: 0 auto;
			color: #fff;
			text-align: center;
		}
	}

}
</style>
