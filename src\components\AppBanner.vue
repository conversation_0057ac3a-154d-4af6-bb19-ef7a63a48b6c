<script setup lang="ts" name="app-banner">
const router = useRouter();
const appStore = useAppStore();
interface BannerObj {
  src: string;
}

interface Props {
  listData: BannerObj[];
}

withDefaults(defineProps<Props>(), {});

const { data: list } = useRequest(ApiGetMemerBanner);

const banners = computed(() => {
  if (list.value && list.value.length) {
    return list.value.map((i) => {
      let images = i.images.split("&");
      let h5str = images.filter((m: any) => m.indexOf("h5=") != -1)[0];
      i.redirect_url = decodeURIComponent(decodeURIComponent(i.redirect_url));
      if (h5str) {
        i.h5img = h5str.split("=")[1];
      }
      return i;
    });
  }
  return [];
});

function imgClick(type: number, url: string) {
  console.log("imgClick =", type, url);
  if (!type || !url) return;
  switch (type) {
    case 1:
      appStore.setShowRouterView(true);

      router.push(url);
      break;
    case 2:
      window.open(url, "_blank");
      break;
  }
}

function subUrl(oldUrl: string) {
  let strStar = oldUrl.indexOf("http");
  let strEnd = oldUrl.lastIndexOf("&");
  // console.log("subUrl =",oldUrl.substring(strStar,strEnd))
  return oldUrl.substring(strStar, strEnd);
}
</script>

<template>
  <div class="app-banner">
    <van-swipe :autoplay="3000" :duration="1000">
      <van-swipe-item v-for="(item, index) in banners" :key="index">
        <div class="link">
          <!-- <AppImage class="img" loading="lazy" :src="'https://www.cloudstoragehubs.com/resource' + item.h5img" /> -->
          <div
            class="img ad-img"
            :style="{ backgroundImage: `url(${item.images})` }"
            @click="() => imgClick(item.url_type, item.redirect_url)"
          ></div>
        </div>

        <!-- <RouterLink :to="item.redirect_url" class="link">
          <div class="img ad-img"
            :style="{ backgroundImage: `url(${item.images})` }" @click="() => imgClick(item.url_type, item.redirect_url)">
          </div>
        </RouterLink> -->
      </van-swipe-item>
      <template v-if="banners && banners.length > 1" #indicator="{ active }">
        <div class="custom-content">
          <span
            v-for="(item, i) in banners"
            :key="i + item.images"
            :class="{
              active: active === i,
            }"
            class="custom-indicator"
          >
          </span>
        </div>
      </template>
    </van-swipe>
  </div>
</template>

<style lang="scss" scoped>
.app-banner {
  width: 710px;
  height: 225px;
  margin: 0 20px;
  margin-top: 15px;
  overflow: hidden;
  // border: 1px solid #000;
  .van-swipe {
    width: 710px;
    height: 225px;
  }
  .custom-content {
    position: absolute;
    bottom: 35px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    z-index: 1000;

    .custom-indicator {
      width: 12px;
      height: 12px;

      background: rgba(255, 255, 255, 0.5);
      border: 1px solid;
      border-color: #aaaaaa;

      transition: all 0.3s;
      border-radius: 100px;
      margin: 0 4px;

      &.active {
        // width: 46px;
        // background: var(--app-red-color);
        background: #ffffff;
        border: 1px solid;
        border-color: #d6d6d6;
      }
    }
  }

  .link {
    display: block;
    // width: 100%;
    // height: 222px;

    .img {
      width: 710px;
      height: 210px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border-radius: 20px;
    }
  }
}
</style>
