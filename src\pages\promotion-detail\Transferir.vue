<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Dialog, showToast, Icon } from 'vant'
import { useRouter } from 'vue-router'
import { eventBus } from './eventBus'
const props = defineProps({
  countdownSeconds: {
    type: Number,
    default: 3600  // 默认10秒
  },
  maxAmount: {
    type: Number,
    default: 20
  }
})

const router = useRouter()
const appStore = useAppStore()
const visible = ref(false)
const amount = ref('')
let numAmount = 0
const password = ref('')
const loading = ref(false)
const countdown = ref('00:00:00')
const showPassword = ref(false)
const animatedBalance = ref('0.00')
const { custService } = storeToRefs(appStore)
let timer: ReturnType<typeof setInterval> | null = null
const maxLength = 6

// 添加输入框验证状态和交互标记
const showAmountError = ref(false)
const hasInteracted = ref(false)

const transferirSuccessCallback = () => {
  // 通过事件总线触发
  eventBus.emit('transferirSuccess')
}


// 添加密码框交互状态
const hasPasswordInteracted = ref(false)
const isCountdownRunning = ref(false)
// 倒计时功能
const startCountdown = () => {
  // 如果已经在运行，先清除之前的定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }

  let totalSeconds = props.countdownSeconds
  

  const updateCountdown = () => {
    // 首先检查是否应该继续倒计时
    if (!isCountdownRunning.value) {
      console.log("停止倒计时")
      clearInterval(timer!)
      timer = null
      return
    }
    // console.log('isCountdownRunning.value', isCountdownRunning.value)
    // console.log('totalSeconds', totalSeconds)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60
    
    countdown.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}s`
    
    if (totalSeconds > 0) {
      totalSeconds--
    } else {
      // 倒计时结束
      clearInterval(timer!)
      timer = null
      isCountdownRunning.value = false
    }
  }
  if (isCountdownRunning.value) {
      // 立即执行一次
      updateCountdown()
      
      // 设置定时器
      timer = setInterval(updateCountdown, 1000)
  }
}


// 在 onTimeFinish 方法中重置状态
const onTimeFinish = () => {
  isCountdownRunning.value = false
  countdown.value = '00:00:00'
  if (timer) {
    clearInterval(timer)
    timer = null 
  }
}

const formatBalance = (value: number) => {
  return value.toFixed(2).replace('.', ',')
}

const animateBalance = () => {
  const targetBalance = props.maxAmount
  //console.log(targetBalance)
  let currentBalance = 0
  const duration = 1000 // 动画持续1秒
  const steps = 20 // 动画步数
  const increment = targetBalance / steps
  const stepDuration = duration / steps


  const updateBalance = () => {
    currentBalance = Math.min(currentBalance + increment, targetBalance)
    if(currentBalance + increment >= targetBalance){
      animatedBalance.value = UsAmountFormat(targetBalance)+"";
      return
    }
    // 生成0-99之间的随机数，用于小数点后的数字
    console.log(targetBalance)
    const decimals = currentBalance === targetBalance ? '00' : Math.floor(Math.random() * 100).toString().padStart(2, '0')
    animatedBalance.value = Math.floor(currentBalance).toString() + ',' + decimals
    
    if (currentBalance < targetBalance) {
      setTimeout(updateBalance, stepDuration)
    }
  }

  updateBalance()
}

const show = () => {
  visible.value = true
  // 重置表单和状态
  amount.value = ''
  password.value = ''
  loading.value = false
  showAmountError.value = false
  hasInteracted.value = false
  hasPasswordInteracted.value = false  // 重置密码框交互状态
  console.log('3isCountdownRunning.value', isCountdownRunning.value)
  if (!isCountdownRunning.value) {
    isCountdownRunning.value = true
    startCountdown()
  }
  // 开始余额动画
  animateBalance()
}

const hide = () => {
  console.log("清楚定时器")
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  isCountdownRunning.value = false
  visible.value = false
  transferirSuccessCallback()
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

const gotoHistory = () => {
  hide()
  router.push('/record-list')
}

const validateForm = () => {
  // 标记输入框已交互
  hasInteracted.value = true
  hasPasswordInteracted.value = true

  // 验证金额
  if (!amount.value) {
    showAmountError.value = true
    showToast('Por favor, insira o valor')
    return false
  }
  // console.log(amount.value)
  const numAmounts = Number(amount.value.replace(',', '.'))
  // console.log(numAmounts)
  // console.log(props.maxAmount)

  let tamount = `${props.maxAmount.toFixed(2).replace('.', ',')}`
  let tnumAmount = parseFloat(tamount.replace(',', '.'))
  // console.log(tnumAmount)
  if (numAmounts <= 0 || numAmounts > tnumAmount) {
    showAmountError.value = true
    showToast(`O valor deve ser entre 0 e ${props.maxAmount.toFixed(2).replace('.', ',')}`)
    return false
  }

  // 验证密码
  if (!password.value || password.value.length < 6) {
    showToast('Por favor, insira sua senha')
    return false
  }

  // 验证通过，清除错误状态
  showAmountError.value = false
  return true
}

const onSubmit = async () => {
  if (!validateForm()) return
  
  try {
    loading.value = true
    // TODO: 调用转账API
    console.log("numAmount="+numAmount)
    runGetWithdraw()
  } catch (error) {
    showToast('Falha na transferência')
  } finally {
    loading.value = false
  }
}


const { run: runGetWithdraw } = useRequest(() => ApiGetWithdrawParam({amount:numAmount > props.maxAmount ? props.maxAmount : numAmount,payp:password.value}), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
      console.log(data)
      
      showToast('Transferência bem-sucedida')
      hide()
      appStore.runGetMemberInfo()
    }
})

const handlePasswordInput = (e: Event) => {
  hasPasswordInteracted.value = true  // 设置密码框已交互
  const input = e.target as HTMLInputElement
  const value = input.value.replace(/\D/g, '').slice(0, maxLength)
  password.value = value
}

const gotoSupport = () => {
  // 处理客服支持点击事件
  router.push('/serviceMessages');
  //gotoCustService(custService)
}

//跳转到浏览器界面
function gotoCustService(data?:any){
  if (!data) return
  if(data.open_method == "2"){ //1 内部打开 2 外部打开
    window.open(data.link, '_blank')
  } else {
    // srcUrl.value = data.link;
    // showSrcUrl.value = true;
  }
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 添加金额格式化函数
const formatAmount = (val: string) => {
  // 移除非数字和逗号
  const cleaned = val.replace(/[^\d,]/g, '')
  // 保���一个逗号
  const parts = cleaned.split(',')
  if (parts.length > 1) {
    // 保留整数部分和小数部分（最多2位）
    return `${parts[0]},${parts[1].slice(0, 2)}`
  }
  return cleaned
}

// 添加处理 Tudo 按钮点击的函数
const handleTudoClick = () => {
  amount.value = `${props.maxAmount.toFixed(2).replace('.', ',')}`
  numAmount = parseFloat(amount.value.replace(',', '.'))
  console.log(numAmount)
  showAmountError.value = false
}

// 修改输入框处理函数
const handleAmountInput = (event: Event) => {
  const input = event.target as HTMLInputElement
  const rawValue = input.value
  
  // 移除非数字和逗号的字符
  const cleanedValue = rawValue.replace(/[^\d,]/g, '')
  
  // 更新响应式变量
  amount.value = cleanedValue
  hasInteracted.value = true
  
  // 解析数值
  numAmount = parseFloat(cleanedValue.replace(',', '.'))
  console.log(numAmount)
  // 只在用户交互后且手动输入时检查是否显示警告
  if (event.isTrusted && hasInteracted.value) {
    showAmountError.value = !input.value || !/\d/.test(input.value)
  }
}

defineExpose({
  show,
  hide,
})
</script>

<template>
  <div class="transfer-container">
    
    <van-dialog
      v-model:show="visible"
      :show-confirm-button="false"
      class="transfer-dialog"
      close-on-click-overlay
      @close=""
      width="96%"
    >
    
      <div class="dialog-content">
        
        <div class="dialog-header">
          <div class="title">Transferir</div>
          <div class="balance">
            <span class="balance-label">Poupança atual</span>
            <span class="balance-amount">{{ animatedBalance }}</span>
          </div>
          <div class="withdraw-row">
            <div class="withdraw-label">Valor do saque</div>
            <div class="countdown">
              <div class="countdown-text">A contagem regressiva desta liquidação</div>
              <div class="countdown-time">{{ countdown }}</div>
            </div>
          </div>
          <div class="withdraw-input-row">
            <div class="input-wrapper">
              <span class="prefix">R$</span>
              <van-field
                v-model="amount"
                type="text"
                :placeholder="`Valor máximo de saque ${props.maxAmount.toFixed(2).replace('.', ',')}`"
                :readonly="loading"
                class="withdraw-input"
                @input="handleAmountInput"
              >
                <template #button>
                  <span class="max-btn" @click="handleTudoClick">Tudo</span>
                </template>
              </van-field>
            </div>
            <div class="amount-hint" v-if="hasInteracted && showAmountError">
              <img src="/icons/icon_tips.webp" alt="tips" width="14" height="14">
              <span>Valor do saque campo não pode estar vazio</span>
            </div>
            <div class="warning-text">
              <span>Você perderá os juros dessa rodada após sacar!</span>
            </div>
          </div>
        </div>
        
        <div class="dialog-body">
          <div class="form-item">
            <div class="label-row">
              <div class="label">Senha de Saque</div>
              <div class="password-toggle" @click="togglePasswordVisibility">
                <img :src="showPassword ? '/img/withdraw/eye-open.webp' : '/img/withdraw/eye-close.webp'" alt="toggle password">
              </div>
            </div>
            <div class="password-input-wrapper">
              <input
                type="tel"
                class="password-input"
                v-model="password"
                maxlength="6"
                @input="handlePasswordInput"
              >
              <div class="password-grid">
                <div class="grid-item" v-for="i in maxLength" :key="i">
                  <template v-if="password.length >= i">
                    <span v-if="showPassword" class="number">{{ password[i-1] }}</span>
                    <span v-else class="dot"></span>
                  </template>
                </div>
              </div>
            </div>
            <div class="password-hint" v-if="hasPasswordInteracted && (password.length === 0 || password.length < 6)">
              <img src="/icons/icon_tips.webp" alt="tips" width="14" height="14">
              <template v-if="password.length === 0">
                <span>Verificar Senha de Saque campo não pode estar vazio</span>
              </template>
              <template v-else-if="password.length < 6">
                <span>6 números puros</span>
              </template>
            </div>
            <div class="help-text">
              <span>Esqueceu a Senha?</span>
              <span class="support-link" @click="gotoSupport">Suporte ao cliente</span>
            </div>
          </div>
        </div>
        
        <div class="dialog-footer">
          <van-button 
            block 
            type="primary" 
            @click="onSubmit"
            :loading="loading"
            loading-text="Processando..."
            class="submit-button"
          >
            Confirmar saque
          </van-button>
        </div>
        
      </div>
      
      <div class="close-wrap" style="position: fixed">
        <div class="close-btn" @click="hide">
          <img 
            src="/icons/dialog-close.png" 
            alt="close" 
            style="width: 40px; height: 40px;"
          >
        </div>
      </div>
    </van-dialog>
    
  </div>
</template>

<style lang="scss" scoped>
.transfer-container {
  position: relative;
}

.van-dialog {
  position: relative;
  overflow: visible !important;
    // 增加内部内容的可滚动区域
    .van-dialog__content {
    max-height: 60vh;
    border-radius: 16px;
    overflow-y: auto;
  }
}

:deep(.van-popup), :deep(.van-dialog) {
  height: 100vh !important;  // 全屏高度
  width: 100vw !important;   // 全屏宽度
  max-height: none !important;  // 移除最大高度限制
  max-width: none !important;   // 移除最大宽度限制
  top: 0 !important;            // 顶部对齐
  left: 0 !important;           // 左侧对齐
  transform: none !important;   // 移除任何平移
  background: transparent;
  border-radius: 0;             // 移除圆角
  margin: 0;                    // 移除边距
  padding: 0;                   // 移除内边距
  overflow: auto;               // 允许滚动
}

.transfer-dialog {
  :deep(.van-dialog) {
    background: transparent;
    overflow: visible;
    min-width: 96% !important;
  }

  :deep(.van-dialog__content) {
    overflow: visible;
    width: 100% !important;
    background: transparent;
    border-radius: 16px;
  }
}

.dialog-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);  // 水平垂直居中
  width: 90%;
  max-width: 800px;
  background: var(--theme-main-bg-color);
  border-radius: 16px;
  overflow: auto;
}


.close-wrap {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, calc(50% + 340px));
  width: 44px;
  height: 44px;
  z-index: 999;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-btn {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
  }
}

.dialog-header {
  position: relative;
  padding: 28px 22px 12px;
  text-align: center;
  background: var(--theme-main-bg-color);
  
  .title {
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 22px;
    color: var(--theme-text-color-darken);
  }

  .balance {
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 22px;
    
    .balance-label {
      color: var(--theme-text-color-lighten);
      font-size: 22px;
    }
    
    .balance-amount {
      color: var(--theme-text-color-darken);
      font-weight: 500;
      font-size: 28px;
    }
  }

  .withdraw-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    margin-bottom: 12px;
    
    .withdraw-label {
      text-align: left;
      color: var(--theme-text-color-darken);
      font-size: 22px;
      line-height: 1.2;
    }

    .countdown {
      text-align: right;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      
      .countdown-text {
        color: var(--theme-text-color-lighten);
        font-size: 20px;
        line-height: 1.2;
      }
      
      .countdown-time {
        color: var(--theme-text-color-lighten);
        font-size: 20px;
        font-weight: 500;
      }
    }
  }
}

.dialog-body {
  padding: 0 22px;
  background: var(--theme-main-bg-color);
  
  .form-item {
    margin-bottom: 0;
    
    .label-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .label {
      margin-bottom: 0;
      font-size: 20px;
      color: var(--theme-text-color-lighten);
    }

    .password-toggle {
      width: 32px;
      height: 32px;
      cursor: pointer;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}

.dialog-footer {
  padding: 2px 22px 22px;
  background: var(--theme-main-bg-color);
  height: 100px;

  .submit-button {
    background: var(--theme-primary-color);
    border-color: var(--theme-primary-color);
    color: var(--theme-primary-font-color);
    height: 70px;
    font-size: 24px;
  }
}

.withdraw-input-row {
  margin-top: 0;
  padding: 0;
  margin-bottom: 8px;

  .input-wrapper {
    position: relative;
    width: 100%;
    height: 11vw;
    display: flex;
    align-items: center;
  }

  .prefix {
    position: absolute;
    left: 10px;
    height: 20px;
    line-height: 20px;
    z-index: 2;
    color: var(--theme-text-color-lighten);
    font-size: 20px !important;
  }

  :deep(.van-cell) {
    background-color: transparent !important;
    border: 1px solid var(--theme-color-line);
    border-radius: 8px;
    display: flex;
    align-items: center;
    &::after {
      display: none;
    }
    padding-right: 5px;  // 增加右边距
  }

  .withdraw-input {
    background-color: transparent !important;
    position: relative;  
    width: 100%;  // 确保宽度100%

    :deep(.van-field__control) {
      background-color: transparent !important;
      -webkit-background-color: transparent !important;
      -webkit-appearance: none;
      padding-left: 20px;
      text-align: left;
      color: var(--theme-text-color-darken) !important;
      font-size: 20px !important;
      height: 20px;
      line-height: 20px;
      padding-right: 100px;  // 增加右边距，给数字更多空间
      width: 100%;  // 确保宽度100%

      &::placeholder {
        font-size: 20px !important;
        width: 100%;
        white-space: nowrap;
        overflow: visible;
      }
    }

    :deep(.van-field__value) {
      background-color: transparent !important;
      -webkit-background-color: transparent !important;
      font-size: 20px !important;
      height: 20px;
      line-height: 20px;
      display: flex;
      align-items: center;
      width: 100%;  // 确保宽度100%
    }

    :deep(input) {
      background-color: transparent !important;
      -webkit-background-color: transparent !important;
      -webkit-appearance: none;
    }

    :deep(input:-webkit-autofill),
    :deep(input:-webkit-autofill:hover),
    :deep(input:-webkit-autofill:focus) {
      -webkit-text-fill-color: var(--theme-text-color-darken);
      -webkit-box-shadow: 0 0 0px 1000px transparent inset;
      transition: background-color 5000s ease-in-out 0s;
    }

    ::placeholder {
      font-size: 20px !important;
      width: 100%;
      white-space: nowrap;
      overflow: visible;
    }
  }

  .max-btn {
    color: var(--theme-primary-color);
    font-size: 20px !important;
    cursor: pointer;
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
    height: 20px;
    line-height: 20px;
    z-index: 3;
    padding: 0 10px;  // 给按钮添加内边距
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &:active {
      opacity: 0.8;
    }
  }

  .amount-hint {
    font-size: 18px;
    color: #FF6B6B;
    text-align: left;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      flex-shrink: 0;
      margin-top: -2px;
    }

    span {
      line-height: 1.2;
    }
  }

  .warning-text {
    color: var(--theme-text-color-darken);
    font-size: 18px;
    margin-top: 8px;
    text-align: left;
  }
}

.password-input-wrapper {
  position: relative;
  width: 100%;
  height: 90px;
  margin-bottom: 8px;
}

.password-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 1;
}

.password-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  background: transparent;
  border: 1px solid var(--theme-color-line);
  border-radius: 8px;
  overflow: hidden;

  .grid-item {
    flex: 1;
    height: 100%;
    border-right: 1px solid var(--theme-color-line);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;

    &:last-child {
      border-right: none;
    }

    .dot {
      width: 24px;
      height: 24px;
      background-color: var(--theme-text-color-darken);
      border-radius: 50%;
      color: var(--theme-text-color-darken);
    }

    .number {
      font-size: 32px;
      color: var(--theme-text-color-darken);
    }
  }
}

.password-hint {
  font-size: 18px;
  color: #FF6B6B;
  text-align: left;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  gap: 4px;

  img {
    flex-shrink: 0;
    margin-top: -2px;
  }

  span {
    line-height: 1.2;
  }
}

.help-text {
  margin-top: 12px;
  text-align: right;
  font-size: 18px;
  color: var(--theme-text-color-lighten);
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: flex-end;
  margin-bottom: 12px;

  .support-link {
    color: var(--theme-primary-color);
    cursor: pointer;
    
    &:active {
      opacity: 0.8;
    }
  }
}

.password-input {
  :deep(.van-field__control) {
    font-size: 20px !important;
    color: var(--theme-text-color-darken) !important;
  }

  :deep(.van-cell) {
    font-size: 20px !important;
  }

  ::placeholder {
    font-size: 20px !important;
  }
}
</style>