<script lang="ts" setup>
interface Props {
  modelValue?: string
  info?: string
  color?: string
  fontSize?: number
}

const isShowPwd = ref(false)
const isFocus = ref(false)

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  info: 'text',
  color: '#000000',
  fontSize:32
})

const emits = defineEmits(['update:modelValue'])

function onInput(key:string) {
  let newValue = (props.modelValue + key).slice(0, 6);
  emits('update:modelValue', newValue)
}
function onDelete() {
  let newValue = props.modelValue.slice(0, props.modelValue.length - 1);
  emits('update:modelValue', newValue)
}

// const { required, pattern } = useAttrs()

</script>

<template>
  <section ref="outerRef" class="app-input-outer">
    <div class="password-input-info" :style="{
      color:color,
      fontSize:$toPx(fontSize)
    }">
      <div class="title">
        <span>{{ info }}</span>
        <span class="title-img">
          <AppImage :class="!isShowPwd ? `eye-close` : `eye-open`"
          @click="isShowPwd = !isShowPwd"
          :src="!isShowPwd ? `/img/withdraw/eye-close.webp` : `/img/withdraw/eye-open.webp`">
          </AppImage>
        </span>
      </div>

      
    </div>
    <van-password-input class="van-password-input" :value="modelValue" :focused="isFocus"
      @focus="isFocus = true" :mask="!isShowPwd" />
    <van-number-keyboard :show="isFocus" @input="onInput" @delete="onDelete"
      @blur="isFocus = false" />
    <br>
  </section>
</template>

<style lang="scss">
[theme='blue']:root {
  // --van-padding-md:0px;
  
}
</style>

<style lang="scss" scoped>
.app-input-outer {
  display: block;
  width: 710px;
  font-family:Arial;
 
  height: 147px;
}
.password-input-info{
  
  width: 710px;
  height: 32px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  line-height: normal;
  font-size: 22px;
}

.title{
  width: 710px;
  height: 32px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: normal;
  font-size: 22px;
}
.title-img {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
}
.van-password-input{
  width:710px;
  --van-password-input-margin: 0;
  --van-password-input-background:transparent;
  --van-password-input-height: 100px;
  --van-password-input-radius:15px;
  --van-password-input-text-color:var(--theme-text-color);
  --van-password-input-dot-color:var(--theme-text-color);

  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  background: var(--theme-main-bg-color);
  border-radius: 15px;
  height: 100px;
 
  border:1px solid  var(--theme-color-line);
  :deep(li:not(:last-child)){
    border-right: 1px solid var(--theme-color-line);
  }
}

.eye-close{
  width: 36px;
  // height: 29px;
 
}

.eye-open{
  width: 36px;
  // height: 37px;
  
}

// .eye-open {
//   width: 38px;
//   margin: 0 27px 0 16px;
// }

</style>
<style>
:root{
  .van-password-input__security:after {
    border-radius: 15px;
    border-color: var(--theme-color-line);
    border-width: 1px;
    
  }

}
</style>
