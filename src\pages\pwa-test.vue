<template>
  <div class="pwa-test-page">
    <div class="container">
      <h1>PWA安装测试页面</h1>
      
      <!-- 设备和浏览器信息 -->
      <div class="info-section">
        <h2>设备信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <label>操作系统:</label>
            <span>{{ deviceInfo.os }}</span>
          </div>
          <div class="info-item">
            <label>浏览器:</label>
            <span>{{ deviceInfo.browser }}</span>
          </div>
          <div class="info-item">
            <label>是否支持PWA:</label>
            <span :class="deviceInfo.pwaSupported ? 'success' : 'error'">
              {{ deviceInfo.pwaSupported ? '✅ 支持' : '❌ 不支持' }}
            </span>
          </div>
          <div class="info-item">
            <label>是否已安装:</label>
            <span :class="deviceInfo.isInstalled ? 'success' : 'warning'">
              {{ deviceInfo.isInstalled ? '✅ 已安装' : '⚠️ 未安装' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 安装状态 -->
      <div class="status-section">
        <h2>安装状态</h2>
        <div class="status-card">
          <div class="status-icon">
            {{ getStatusIcon() }}
          </div>
          <div class="status-text">
            <h3>{{ getStatusTitle() }}</h3>
            <p>{{ getStatusDescription() }}</p>
          </div>
        </div>
      </div>

      <!-- 安装组件测试 -->
      <div class="components-section">
        <h2>安装组件测试</h2>
        
        <div class="component-test">
          <h3>通用PWA安装器</h3>
          <p>支持所有Android浏览器的智能安装组件</p>
          <UniversalPWAInstaller />
        </div>

        <div class="component-test">
          <h3>PWA下载按钮</h3>
          <p>跨平台的应用下载解决方案</p>
          <PWADownloadButton />
        </div>
      </div>

      <!-- 手动测试按钮 -->
      <div class="manual-test-section">
        <h2>手动测试</h2>
        <div class="test-buttons">
          <button @click="testInstallPrompt" class="test-btn">
            测试安装提示
          </button>
          <button @click="testServiceWorker" class="test-btn">
            测试Service Worker
          </button>
          <button @click="testManifest" class="test-btn">
            测试Manifest
          </button>
          <button @click="refreshInfo" class="test-btn">
            刷新信息
          </button>
        </div>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResults.length > 0" class="results-section">
        <h2>测试结果</h2>
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="result.type"
          >
            <div class="result-time">{{ result.time }}</div>
            <div class="result-message">{{ result.message }}</div>
          </div>
        </div>
      </div>

      <!-- 浏览器特定指导 -->
      <div class="guide-section">
        <h2>{{ deviceInfo.browser }} 安装指导</h2>
        <div class="guide-content">
          <div class="guide-steps">
            <div v-for="(step, index) in getBrowserGuide()" :key="index" class="guide-step">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-text">{{ step }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import UniversalPWAInstaller from '../components/UniversalPWAInstaller.vue'
import PWADownloadButton from '../components/PWADownloadButton.vue'
import { deviceDetection, pwaInstaller, serviceWorkerManager } from '../utils/pwa'

interface TestResult {
  time: string
  message: string
  type: 'success' | 'error' | 'info'
}

const deviceInfo = ref({
  os: '',
  browser: '',
  pwaSupported: false,
  isInstalled: false
})

const testResults = ref<TestResult[]>([])

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent
  
  // 检测操作系统
  let os = 'Unknown'
  if (deviceDetection.isAndroid()) {
    os = 'Android'
  } else if (deviceDetection.isIOS()) {
    os = 'iOS'
  } else if (/Windows/i.test(userAgent)) {
    os = 'Windows'
  } else if (/Mac/i.test(userAgent)) {
    os = 'macOS'
  } else if (/Linux/i.test(userAgent)) {
    os = 'Linux'
  }

  // 检测浏览器
  let browser = 'Unknown'
  if (deviceDetection.isChrome()) {
    browser = 'Chrome'
  } else if (deviceDetection.isSamsungInternet()) {
    browser = 'Samsung Internet'
  } else if (deviceDetection.isEdge()) {
    browser = 'Microsoft Edge'
  } else if (deviceDetection.isFirefox()) {
    browser = 'Firefox'
  } else if (deviceDetection.isOpera()) {
    browser = 'Opera'
  } else if (deviceDetection.isSafari()) {
    browser = 'Safari'
  } else if (/UCBrowser/i.test(userAgent)) {
    browser = 'UC Browser'
  } else if (/MiuiBrowser/i.test(userAgent) || /XiaoMi/i.test(userAgent) || /MI\s/i.test(userAgent)) {
    browser = 'MIUI浏览器'
    // 检测MIUI浏览器版本
    const versionMatch = userAgent.match(/MiuiBrowser\/(\d+\.\d+)/i)
    if (versionMatch) {
      browser += ` ${versionMatch[1]}`
    }
  } else if (/HuaweiBrowser/i.test(userAgent)) {
    browser = 'Huawei Browser'
  }

  deviceInfo.value = {
    os,
    browser,
    pwaSupported: pwaInstaller.isSupported(),
    isInstalled: pwaInstaller.isInstalled()
  }
}

// 获取状态图标
const getStatusIcon = () => {
  if (deviceInfo.value.isInstalled) return '✅'
  if (deviceInfo.value.pwaSupported) return '📱'
  return '❌'
}

// 获取状态标题
const getStatusTitle = () => {
  if (deviceInfo.value.isInstalled) return '应用已安装'
  if (deviceInfo.value.pwaSupported) return '可以安装'
  return '不支持安装'
}

// 获取状态描述
const getStatusDescription = () => {
  if (deviceInfo.value.isInstalled) {
    return '您已经安装了此应用，可以从桌面直接启动'
  }
  if (deviceInfo.value.pwaSupported) {
    return '您的浏览器支持PWA安装，可以将网站添加到桌面'
  }
  return '您的浏览器不支持PWA安装功能'
}

// 获取浏览器特定指导
const getBrowserGuide = () => {
  const browser = deviceInfo.value.browser.toLowerCase()
  
  const guides: Record<string, string[]> = {
    'chrome': [
      '点击浏览器右上角的菜单按钮（三个竖点）',
      '在菜单中找到"安装应用"选项',
      '点击安装，应用将添加到您的桌面'
    ],
    'samsung internet': [
      '点击浏览器底部的菜单按钮',
      '选择"添加页面到"选项',
      '选择"主屏幕"完成安装'
    ],
    'microsoft edge': [
      '点击浏览器右上角的菜单按钮',
      '在菜单中找到"应用"选项',
      '点击"将此站点安装为应用"'
    ],
    'firefox': [
      '点击浏览器菜单按钮',
      '查找"安装"或"添加到主屏幕"选项',
      '确认安装到桌面'
    ],
    'opera': [
      '点击Opera浏览器菜单',
      '找到"主屏幕"相关选项',
      '选择"添加到主屏幕"'
    ],
    'miui浏览器': [
      '点击浏览器底部的菜单按钮（三条横线）',
      '选择"工具箱"或"更多"选项',
      '找到"添加到桌面"或"创建快捷方式"',
      '确认添加到桌面'
    ]
  }
  
  return guides[browser] || [
    '在浏览器菜单中查找"安装应用"、"添加到主屏幕"等选项',
    '如果没有找到，可以尝试在地址栏或分享菜单中查找',
    '按照浏览器提示完成安装过程'
  ]
}

// 添加测试结果
const addTestResult = (message: string, type: TestResult['type'] = 'info') => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制结果数量
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 测试安装提示
const testInstallPrompt = () => {
  addTestResult('正在测试安装提示...', 'info')
  
  // 触发自定义事件来测试安装提示
  const event = new CustomEvent('test-install-prompt')
  window.dispatchEvent(event)
  
  addTestResult('安装提示测试完成', 'success')
}

// 测试Service Worker
const testServiceWorker = async () => {
  addTestResult('正在测试Service Worker...', 'info')
  
  try {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        addTestResult('Service Worker已注册并运行', 'success')
      } else {
        addTestResult('Service Worker未注册', 'error')
      }
    } else {
      addTestResult('浏览器不支持Service Worker', 'error')
    }
  } catch (error) {
    addTestResult(`Service Worker测试失败: ${error}`, 'error')
  }
}

// 测试Manifest
const testManifest = async () => {
  addTestResult('正在测试Web App Manifest...', 'info')
  
  try {
    const manifestLink = document.querySelector('link[rel="manifest"]') as HTMLLinkElement
    if (manifestLink) {
      const response = await fetch(manifestLink.href)
      const manifest = await response.json()
      addTestResult(`Manifest加载成功: ${manifest.name}`, 'success')
    } else {
      addTestResult('未找到Manifest链接', 'error')
    }
  } catch (error) {
    addTestResult(`Manifest测试失败: ${error}`, 'error')
  }
}

// 刷新信息
const refreshInfo = () => {
  getDeviceInfo()
  addTestResult('设备信息已刷新', 'success')
}

onMounted(() => {
  getDeviceInfo()
  addTestResult('PWA测试页面已加载', 'success')
})
</script>

<style scoped>
.pwa-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
}

h2 {
  color: #444;
  margin: 30px 0 15px 0;
  font-size: 20px;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.info-section {
  margin-bottom: 30px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007AFF;
}

.info-item label {
  font-weight: 600;
  color: #555;
}

.success {
  color: #28a745;
  font-weight: 600;
}

.error {
  color: #dc3545;
  font-weight: 600;
}

.warning {
  color: #ffc107;
  font-weight: 600;
}

.status-section {
  margin-bottom: 30px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.status-icon {
  font-size: 48px;
  margin-right: 20px;
}

.status-text h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.status-text p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.components-section {
  margin-bottom: 30px;
}

.component-test {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fafafa;
}

.component-test h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.component-test p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}

.manual-test-section {
  margin-bottom: 30px;
}

.test-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.test-btn {
  padding: 12px 20px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #0056CC;
}

.results-section {
  margin-bottom: 30px;
}

.results-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  background: #d4edda;
  border-left: 4px solid #28a745;
}

.result-item.error {
  background: #f8d7da;
  border-left: 4px solid #dc3545;
}

.result-item.info {
  background: #d1ecf1;
  border-left: 4px solid #17a2b8;
}

.result-time {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.result-message {
  flex: 1;
  margin-left: 12px;
  font-size: 14px;
}

.guide-section {
  margin-bottom: 30px;
}

.guide-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.guide-steps {
  space-y: 12px;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 16px;
  flex-shrink: 0;
}

.step-text {
  flex: 1;
  color: #333;
  line-height: 1.5;
  padding-top: 4px;
}

@media (max-width: 768px) {
  .container {
    padding: 20px;
    margin: 10px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .test-buttons {
    grid-template-columns: 1fr;
  }
  
  .status-card {
    flex-direction: column;
    text-align: center;
  }
  
  .status-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
