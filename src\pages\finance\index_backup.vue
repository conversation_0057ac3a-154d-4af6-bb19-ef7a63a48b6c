<script lang="ts" setup name="FinancePage">

const router = useRouter()

const appStore = useAppStore()
const { userInfo } = storeToRefs(appStore)
const balance = computed(() => toDecimal(userInfo.value.brl ?? 0))
const unlock_amount = computed(() => toDecimal(userInfo.value.unlock_amount ?? 0))
const deposit_amount = computed(() => toDecimal(userInfo.value.deposit_amount ?? 0))
const deposit_balance = computed(() => toDecimal(userInfo.value.deposit_balance ?? 0))
const agency_lock_amount = computed(() => toDecimal(userInfo.value.agency_lock_amount ?? 0))
const agency_balance = computed(() => toDecimal(userInfo.value.agency_balance ?? 0))

const tabs = [
  { label: 'Depósito', value: '1' },
  { label: 'Retirar', value: '2' },
]
const activeTab = ref(router.currentRoute.value.query.tab === 'withdraw' ? '2' : '1')

const showPopoverOne = ref(false)
const showPopoverTwo = ref(false)

watch(activeTab, (val) => {
  if (val === '2') {
    if (userInfo.value && userInfo.value.pay_password && +userInfo.value.pay_password !== 1) {
      payPwdTip.value = true
    }
  }
})

const payPwdTip = ref(false)

const goPayPwd = () => {
  router.push('/safe-center/bind-pay-pwd')
}

</script>

<template>
  <div class="finance-page">
    <AppHeader title="Conta" left-arrow />
    <section class="content">
      <div class="money-info">
        <div class="bar">
          <div class="tag">Total Da Conta</div>
          <ul class="">
            <li>
              <div><span>R$</span>{{ balance }}</div>
              <span class="desc">Fundos totais</span>
            </li>
            <li>
              <div><span>R$</span>{{ unlock_amount }}</div>
              <span class="desc">Retirável Total</span>
            </li>
          </ul>
        </div>
        <ul class="detail">
          <van-popover class="warning-popover" v-model:show="showPopoverOne" placement="top-start">
            <div class="small-white-txt">Uma conta composta por depósito inicial e depósito manual.</div>
            <template #reference>
              <li>
                <div><span>Conta da lotaria</span>
                  <AppImage src="/icons/btn_instruction.png" />
                </div>
                <div><span>Balanço: </span>{{ agency_balance }}
                </div>
                <!-- <div><span>Retirável: </span>{{ deposit_balance }}
                </div> -->
              </li>
            </template>
          </van-popover>
          <li class="li2">
            <div><span>Conta bloqueada na loteria</span></div>
            <div><span>Balanço: </span>{{ agency_lock_amount }}</div>
            <!-- <div><span>Retirável: </span>{{ agency_balance }}
            </div> -->
          </li>
          <!-- <van-popover class="warning-popover" v-model:show="showPopoverTwo" placement="top-end">
            <div class="small-white-txt">Uma conta composta por recompensas por convidar amigos e retorno de comissões com
              base no valor da transação dos usuários convidados.</div>
            <template #reference>
              <li>
                <div><span>Conta bloqueada na loteria</span>
                  <AppImage src="/icons/btn_instruction.png" />
                </div>
                <div><span>Balanço: </span>{{ agency_lock_amount }}</div>
              </li>
            </template>
          </van-popover> -->
        </ul>
      </div>

      <section class="fin">
        <div class="fin-tab">
          <AppTab :list-data="tabs" v-model="activeTab" />
        </div>

        <ul class="fin-inner">
          <li v-if="activeTab === '1'">
            <AppDeposit />
          </li>

          <li v-if="activeTab === '2'">
            <AppWithdraw />
          </li>
        </ul>
      </section>
    </section>

    <van-popup class="" v-model:show="payPwdTip" teleport="body" round>
      <div class="tip-content">
        <div class="text">
          A senha de pagamento ainda não foi definida, por favor, defina-a primeiro
        </div>
        <div class="btns">
          <div class="cancel" @click="payPwdTip = false">Cancelar</div>
          <AppButton @click="goPayPwd" width="280" height="90" blue :radius="45" white-text>
            Confirme
          </AppButton>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';
.tip-content {
  width: 634px;
  border-radius: 20px;
  background: #374a69;
  padding-top: 48px;

  .text {
    color: #FFF;
    text-align: center;
    font-size: 26px;
    font-weight: 400;
    line-height: 40px;
    padding: 0 66px;
  }

  .btns {
    padding-top: 48px;
    padding-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .cancel {
      width: 280px;
      height: 90px;
      line-height: 90px;
      color: #FFF;
      text-align: center;
      font-size: 28px;
      font-weight: 400;
      border-radius: 100px;
      background: #28374e;
      cursor: pointer;
    }
  }
}

.finance-page {
  background: #061531;
  min-height: 100vh;
  padding-top: 90px;
  padding-bottom: 132px;

  .money-info {
    width: 710px;
    // height: 581px;
    margin: 30px 20px 0;
    color: #fff;
    border-radius: 50px;
    background-color: #28374d;
    position: relative;
    padding-bottom: 20px;
    .bar {
      // height: 192px;
      // border-radius: 50px;
      // background: linear-gradient(360deg, #044B9A 0%, #1373EF 53.65%, #0ED1F4 100%), linear-gradient(144deg, #466AC5 0%, #46B5FB 100%);
      // box-shadow: 0px -16px 34px 0px #49B5FF inset;
      // position: relative;

      .tag {
        width: 710px;
        height: 82px;
        line-height: 82px;
        background-color: #324b6e;
        text-align: center;
	      font-size: 30px;
        color: #679fea;
        border-radius: 50px 50px 0px 0px;
      }

      ul {
        list-style: none;
        color: #FFF;
        font-size: 30px;
        // font-weight: 700;
        display: flex;
        text-align: center;
        align-items: center;

        li {
          width: 50%;
          // padding-top: 56px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          &:nth-child(1){
            width: 197px;
            height: 236px;
            @include webp('/icons/icon_full.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            position: absolute;
            top: 112px;
            left: 85px;
            .desc {
              width: 260px;
              color: #ff3485;
              font-size: 30px;
              // padding-top: 17px;
              font-weight: normal;
              position: absolute;
              // border: 1px solid #fff;
              top: 222px;
              left: -30px;
            }
          }
          &:nth-child(2){
            width: 197px;
            height: 236px;
            @include webp('/icons/icon_full_withdrawable.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            position: absolute;
            top: 112px;
            right: 89px;
            .desc {
              width: 260px;
              color: #a44cff;
              font-size: 30px;
              // padding-top: 17px;
              font-weight: normal;
              position: absolute;
              // border: 1px solid #fff;
              top: 222px;
              right: -40px;
            }
          }
          div{
            margin-top: -40px;
            font-size: 25px;
            font-weight: normal;
          }
        }
      }
    }

    .detail {
      color: #FFF;
      font-size: 28px;
      line-height: 40px;
      display: flex;
      margin-top: 310px;
      li {
        // width: 50%;
        padding-left: 60px;
        position: relative;
        >div:nth-child(2) {
          padding-top: 16px;
          padding-bottom: 16px;
        }
        img{
          width: 43px;
          height: 43px;
          margin-left: 10px;
          position: absolute;
        }
      }
      .van-popover__wrapper{
        li{
          >div:nth-child(2) {
            padding-left: 35px;
          }
        }
      }
      .li2{
        >div:nth-child(1) {
          padding-right: 20px;
        }
        >div:nth-child(2) {
          padding-left: 90px;
        }
      }
      .small-white-txt{
        background: #334b6f;
        color: #4e7aaf;
      }
    }
  }

  .fin {
    .fin-tab {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      height: 110px;
      background: linear-gradient(180deg, rgba(1, 26, 81, 0.00) 35.94%, #011A51 100%);
      box-shadow: 0px -1px 0px 0px rgba(255, 255, 255, 0.10) inset, 0px 4px 5px 0px rgba(0, 0, 0, 0.30);
    }

    ul.fin-inner {

      li.content-withdraw {
        padding: 32px 20px 88px;
      }
    }
  }
}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
