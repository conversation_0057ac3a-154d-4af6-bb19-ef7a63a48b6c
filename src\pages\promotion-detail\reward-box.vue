<script setup lang='ts' name='reward-box'>
const router = useRouter()
const appStore = useAppStore()
const { userInfo, isLogin, isApp, token } = storeToRefs(appStore)
const inviteNum = computed(() => userInfo.value.invite_num ?? 0)
const lastTreasure = computed(() => userInfo.value.last_treasure ?? 0)

const { data: rewardList } = useRequest(ApiGetRewardBoxConfig)
const curentBox = computed(() => {
  const boxObj = rewardList.value?.find((a) => a.invite_num > lastTreasure.value)

  if (boxObj) {
    const amount = inviteNum.value >= boxObj.invite_num ? boxObj.amount : 0
    return { invite_num: inviteNum.value, amount, apply_num: boxObj.invite_num }
  }
  else {
    return { invite_num: inviteNum.value, amount: 0, apply_num: 0 }
  }
})

const { data: record, run: runGetRecord } = useRequest(ApiGetRewardBoxRecord, { ready: isLogin, })

const applyInviteNum = ref(0)
const { run: runApply, loading } = useRequest(() => ApiApplyRewardBox(applyInviteNum.value), {
  manual: true,
  onSuccess() {
    showToast('Aplicação Bem Sucedida')
    appStore.runGetMemberInfo()
    runGetRecord()
  }
})

const handlApply = () => {
  if (!isLogin.value) return openLoginDialog()

  console.log("🔰🔰🔰 ~ curentBox:", curentBox.value)
  if (curentBox.value.amount === 0) {
    showToast('Atualmente não há baús de tesouro para reivindicar')
    return
  }
  applyInviteNum.value = curentBox.value.apply_num
  runApply()
}
</script>
<template>
  <div class="reward-box">
    <div class="banner" :style="{
      paddingTop: isApp ? 'var(--app-px-30)' : '',
      height: isApp ? 'var(--app-px-670)' : ''
    }">
      <AppHeader title="Jackpot de desempacotar" leftArrow :placeholder="true" />
      <div class="top-title">
        <div>Convide amigos,abra baús de tesouro</div>
        <div>e ganhe dinheiro de verdade!</div>
        <div>Convide amigos para atingir o número especificado para resgatar recompensas.</div>
      </div>
      <!-- <p class="text1">Convide amigos para atingir o número especificado para resgatar recompensas.</p> -->
      <div class="info">
        <div class="item">
          <label>Convidado: </label>
          <span>{{ curentBox.invite_num }}</span>
        </div>
        <div class="item">
          <label>Premiado: </label>
          <span>R${{ toDecimal(curentBox.amount, 0) }}</span>
        </div>
      </div>

      <div class="box-wrapper">
        <div class="box-item" v-for="item, i in rewardList" :key="i">
          <span class="money1">+R${{ toDecimal(item.amount, 0) }}</span>
          <div class="box">
            <AppImage src="/img/promotion/item_treasure_chest.png" alt="" />
            <span class="line" :class="{'line-active': curentBox.invite_num >= item.invite_num}"></span>
          </div>
          <div class="people">
            <AppImage class="icon" src="/img/promotion/icon_number_of_people.png" alt="" />
            <span>{{ item.invite_num }}</span>
          </div>
        </div>
      </div>
      <AppButton :loading="loading" @click="handlApply" width="348" height="80" blue center radius="15"
        white-text>Receber prêmio
      </AppButton>
    </div>

    <div class="content">
      <p> Vá para a página de convite, copie o link e envie para outras pessoas para convidar amigos para participar do
        evento e receber dinheiro real</p>
      <AppButton @click="router.push(`/promotion-detail/invite${isApp ? `?is-app=1&t=${token}` : ''}`)" width="274"
        height="63" radius="15" center blue white-text>
        <div style="display: flex;justify-content: center;align-items: center;">
          <span style="font-size: var(--app-px-28);">Ir para convidar</span>
          <AppImage src="/icons/i-arrowright-small.png"
            style="height: var(--app-px-21);width: auto;margin-left: var(--app-px-15);" alt="" />
        </div>
      </AppButton>
    </div>

    <div class="des">
      <label>Regras de atividade</label>
      <p> 1. Convide um número suficiente de usuários válidos (os clientes devem preencher o número do celular para se
        registrar e concluir a recarga) para receber recompensas</p>
      <p> 2. As recompensas chegarão diretamente ao saldo da sua conta e você poderá retirar mais lucros nas transações do
        jogo.</p>
      <p> 3. O usuário convidado deve ser real. Caso a plataforma detecte trapaça, os usuários convidados serão
        invalidados, e se a fraude for grave, a conta será suspensa.</p>
      <p> 4. As recompensas por convidar o número correspondente de pessoas são as seguintes:</p>
      <div class="table-wrapper">
        <table>
          <colgroup>
            <col style="width:var(--app-px-220);">
            <col style="width:var(--app-px-217);">
            <col style="width:var(--app-px-2221);">
          </colgroup>
          <thead>
            <tr>
              <th>Quantidade</th>
              <th>Prêmio</th>
              <th>Total geral</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item, i in rewardList" :key="i">
              <td>{{ item.invite_num }} pessoas</td>
              <td>R${{ toDecimal(item.amount, 0) }}</td>
              <td>R${{ toDecimal(item.total_amount, 0) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="record">
      <label>Registro de unboxing</label>
      <div v-if="record && record.length > 0" class="record-table-wrapper">
        <table>
          <thead>
            <tr>
              <th>Aberto</th>
              <th>Tempo</th>
              <th>Prêmio</th>
            </tr>
          </thead>
          <tbody>
            <tr class="data-item" v-for="r in record" :key="r.id">
              <td>{{ r.invite_num }}</td>
              <td>
                {{ timestamp2Date(r.created_at * 1000, 'YYYY-MM-DD') }}<br />
                {{ timestamp2Date(r.created_at * 1000, 'hh:mm:ss') }}
              </td>
              <td>{{ toDecimal(r.amount) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <AppEmpty v-else :padding-top="99">Nada aqui</AppEmpty>
    </div>
  </div>
</template>

<style lang='scss' scoped>
.reward-box {
  color: #fff;
  background-color: #12192b;
  padding-bottom: 150px;
}

.banner {
  width: 100%;
  height: 750px;
  background: #131a2c;
  color: #fff;
  font-size: 28px;
  margin-bottom: 23px;
  .top-title{
    width: 716px;
    height: 205px;
    background-image: url('/img/promotion/bg_unboxing_prize.png');
    background-size: 716px 205px;
    margin: 0 auto;
    margin-top: 20px;
    padding: 52px;
    div{
      &:nth-child(1){
        width: 650px;
        line-height: 40px;
        font-size: 36px;
        margin: 0 auto;
      }
      &:nth-child(2){
        width: 486px;
        line-height: 50px;
        font-size: 36px;
        margin: 0 auto;
      }
      &:nth-child(3){
        width: 545px;
        line-height: 18px;
        font-size: 18px;
        margin: 0 auto;
        margin-top: 20px;
        text-align: center;
      }
    }
  }
  .title {
    width: 100%;
    margin-bottom: 28.73px;
    margin-top: 20px;
  }

  .text1 {
    width: 692px;
    line-height: 33.89px;
    height: 80px;
    text-align: center;
    margin: 0 auto 8px;
  }

  .info {
    width: 100%;
    height: 82px;
    line-height: 82px;
    margin-top: 20px;
    font-size: 30px;
    background-color: #324b6e;
    display: flex;
    justify-content: center;

    .item {
      width: 320px;
      text-align: center;
      flex-shrink: 0;
      &:nth-of-type(1) {
        margin-right: 36px;
      }
    }
  }

  .box-wrapper {
    width: 100%;
    height: 184px;
    overflow-x: auto;
    padding: 10px 20px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 40px;
    background-color: #28374d;
    .box-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 99.2px;
      margin-right: 49.8px;

      .box {
        margin-bottom: 5px;
        position: relative;
        width: 99.2px;
        height: 78px;

        img {
          width: 95px;
          height: 72px;
          position: absolute;
          // margin-top: ;
          z-index: 1;
        }

        .line {
          display: block;
          height: 10px;
          width: 150px;
          background-color:#111a29;
          position: absolute;
          top: 50%;
          left: 20px;
        }
        .line-active{
          background-color:#fcff0f;
        }
      }

      .money {
        color: #0ED1F4;
        font-size: 28px;
      }
      .money1 {
        color: #679fea;
        font-size: 24px;
        margin-bottom: 10px;
      }
      .people {
        display: flex;
        justify-content: center;
        font-size: 26px;
        width: 99.2px;
        height: 31px;
        align-items: center;

        .icon {
          flex-shrink: 0;
          width: 34px;
          height: 28px;
          margin-right: 10px;
        }
      }

      &:last-of-type {
        margin-right: 0;

        .box {
          .line {
            display: none;
          }
        }
      }
    }
  }
}

.content {
  width: 711px;
  height: 259px;
  background: #28374d;
  margin: 0 auto;
  padding: 27px 20px;
  // margin-bottom: 16px;
  border-radius: 20px;
  box-sizing: border-box;

  p {
    width: 670px;
    height: 114px;
    font-size: 24px;
    line-height: 32.81px;
    margin: 0 auto 15px;
    color: #679fea;
  }
}

.des {
  margin: 0 auto;
  width: 710px;
  border-radius: 20px;
  // background: linear-gradient(180deg, rgba(4, 75, 154, 0.30) 0%, rgba(1, 26, 81, 0.30) 100%);
  color: #679fea;
  // padding: 27px 25px 26px 0;
  padding-top: 25px;
  box-sizing: border-box;
  font-size: 24px;

  label {
    padding: 0 20px;
    margin-bottom: 17px;
    display: block;
  }

  p {
    line-height: 30px;
    padding: 0 20px;
    // margin-bottom: 15px;
  }

  .table-wrapper {
    width: 710px;
    margin: 0 auto;
    margin-top: 50px;
    border-radius: 50px 50px 20px 20px; 
    background: #28374d;
    table {
      width: 710px;
      border-collapse: collapse;
      padding: 0;
      thead{
        th{
          background: #324b6e;
          color: #679fea;
          font-size: 24px;
          font-weight: normal;
          height: 80px;
          padding: 14px 0;
          // border-bottom: 1px solid #152237;
          border-right: 1px solid #152237;
          &:nth-child(1){
            border-radius: 50px 0px 0px 0px;
          }
          &:nth-child(3){
            border-radius: 0px 50px 0px 0px;
            border-right: none;
          }
        }
      }
      tbody {
        tr {
          color: #fefefe;
          font-size: 24px;
          font-weight: normal;
          height: 80px;
          text-align: center;
          border-bottom: 1px solid #152237;
          td{
            border-right: 1px solid #152237;
            &:nth-child(4){
              border-right: none;
            }
          }
        }
      }
    }
  }
}

.record {
  width: 100%;
  padding: 0 21px;
  margin-top: 30px;

  label {
    font-size: 30px;
    color: #679fea;
  }

  .record-table-wrapper {
    width: 710px;
    margin: 0 auto;
    margin-top: 50px;
    border-radius: 50px 50px 20px 20px; 
    background: #28374d;
    table {
      width: 710px;
      border-collapse: collapse;
      padding: 0;
      thead{
        th{
          background: #324b6e;
          color: #679fea;
          font-size: 24px;
          font-weight: normal;
          height: 80px;
          padding: 14px 0;
          // border-bottom: 1px solid #152237;
          border-right: 1px solid #152237;
          &:nth-child(1){
            border-radius: 50px 0px 0px 0px;
          }
          &:nth-child(3){
            border-radius: 0px 50px 0px 0px;
            border-right: none;
          }
        }
      }
      tbody {
        tr {
          color: #fefefe;
          font-size: 24px;
          font-weight: normal;
          height: 80px;
          text-align: center;
          border-bottom: 1px solid #152237;
          td{
            border-right: 1px solid #152237;
            &:nth-child(4){
              border-right: none;
            }
          }
        }
      }
    }
  }
}
</style>
