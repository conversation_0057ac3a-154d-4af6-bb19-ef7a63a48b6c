<script lang="ts" setup>
const route = useRoute()
const router = useRouter()

const records = ref([
  { icon: 'convidar', text: 'Convidar', path: '/promotion-detail/invite', iconWidth: 0 }, // icon 图标从 figma 导出 x3 倍图

])
const appStore = useAppStore()
const { userInfo, loadingBalance, loadingUserinfo } = storeToRefs(appStore)
appStore.setFooterDialogVisble(false)


const copyName = () => {
  copy(userInfo.value.uid || '')
  showToast('Copied!')
}

// onMounted(() => {
//   runGetProSignConf()
// })

// const { run: runGetProSignConf, data: signProData } = useRequest(ApiGetPromotionSignConfig, {
//   manual: true
// })

// {{ timestamp2Date(r.created_at * 1000, 'YYYY-MM-DD') }}<br />
// {{ timestamp2Date(r.created_at * 1000, 'hh:mm:ss') }}


const thisWeekStr = ref(getWeekDate());
const lastWeekStr = ref(getWeekDate(true));

const posts = ref([
  { id: 1, title: "0+BRL", cashback: "12%" },
  { id: 2, title: "5000+BRL", cashback: "14%" },
  { id: 3, title: "20000+BRL", cashback: "16%" },
  { id: 1, title: "80000+BRL", cashback: "18%" },

])

const progressData = ref(0)
const myprogress = computed(() => {
  return progressData.value + "vw"
})

const lostbackData = ref({
  minLostCashbackRatio: 0,
  maxLostCashbackRatio: 0
})

function clickLeft() {
  appStore.setFooterDialogVisble(true)
}

function clickBack(){
  // router.push('/advancement')
  router.go(-1)
  appStore.setFooterDialogVisble(true)
}

const dataList = ref<any>([])
const activeIndex = ref<any>([])  //ref(-1) //已经领取到第几位

const curHaveGetMoney = ref(-1)//当前可领取第几位
const sendGetMoneyIndex = ref(-1) //发送领取的位置
const depositAmount = ref(0)
const rewardAmount = ref(0);

const { run :runGetRechargeActivity} = useRequest(RechargeActivity, {
  onError: () => {
  },
  onSuccess: (data) => {
    if (data) {
      activeIndex.value=[]
      rewardAmount.value = 0;

      dataList.value=[...data.item]
      depositAmount.value = data.deposit_amount
      for(let i=0;i<data.item.length;i++){
        if(data.deposit_amount>=data.item[i].accu_deposit_amount){
          curHaveGetMoney.value = i
          if(data.state & (1 << i)){
            activeIndex.value.push(1) //已经领取
          }else{
            activeIndex.value.push(2) //未领取
            rewardAmount.value += data.item[i].bonus;
          }
        }else{
          activeIndex.value.push(0) //不可领取
        }
      }
      console.log(activeIndex, dataList, data)
    }
  }
})

const { run :runGetRechargeActivityMoney} = useRequest( ()=>getRechargeActivity({state:0}), {
  manual: true,
  onError: (data) => {
    console.log(data)
    showErrorTip(data)
  },
  onSuccess: (data) => {
    if (data) {
        //领取成功刷新界面
        runGetRechargeActivity()
        //刷新金币
        appStore.runGetUserBalance();
        //提示
        // showToast({type: "success",message: "Bônus +R$ "+data});
        // appStore.setIsShowMessage(true,"Bônus +R$ "+data)
        appStore.setIsShowBonusMessage(true,"Reivindicado com êxito.Continue a jogar para obter mais bónus~")
    }
  },

})

//按钮点击
const isRunAni = ref(false)
function gotoGetMoney() {
  if (isRunAni.value) return;
  if (rewardAmount.value > 0) {
    runGetRechargeActivityMoney();
  }

  isRunAni.value = true
    setTimeout(() => {
      isRunAni.value = false
    }, 800);
  console.log("gotoGetMoney click")
}

</script>

<template>
  <AppPageTitle left-arrow title="Bônus extra parа recarga diária acumulada" 
    @clickLeft="clickLeft" />
  <div class="weekly-loss-content">
    <div class="content">
      <div class="topDiv">
        <div class="reward">
          <div class="noData">
            <div v-if="rewardAmount > 0">
              <span>{{ "R$" + transf(rewardAmount) }}</span>
            </div>
            <div v-else>
              <span>Não existem prémios a receber neste momento</span>
            </div>
          </div>
        </div>
      </div>
      <div class="belowDiv"> <!--下面显示-->
        <!-- <label class="below_title">CASHBACK E REGRAS</label> -->
        <div class="below_itemTitle">
          <!-- <AppImage class="below_itemImg" src="/img/weekly/weekly_c_icon.webp" /> -->
          <label class="below_itemTitle_aposta">Depósito Total</label>
          <label class="below_itemTitle_aposta below_itemTitle_cashback">Recompensa</label>
        </div>

        <div v-for="(data, index) in dataList" :key="index" class="below_itemDevPa">
          <!-- <AppImage class="itemIcon" src="/img/weekly/weekly_top_le.webp" /> -->
          <!-- <label class="itemIconLev">{{ index + 1 }}</label> -->
          <label class="itemContent">{{ "≥" + data.accu_deposit_amount }}</label>
          <label class="itemContent copy">{{ transf(data.bonus) }}</label>
        </div>

      </div>
      <div class="rule">
        <AppRule title="Instruções Do Evento:" content="1.Durante o evento, a recarga acumulada de Diário receberá diferentes níveis de recompensas. Quanto mais recargas, maiores serão as recompensas. A recompensa mais alta é 5555；
2.Depósito sem limite. Recompensas previstas para serem atualizadas em 10 minutos. Por favor, aguarde a distribuição da recompensa;
3.As recompensas recebidas só podem ser resgatadas após No dia seguinte00:00:00. O resgate só pode ser feito manualmente no APP/iOS、APP/Android、PC/Windows.
4.As recompensas recebidas expirarão 1 dias após o término de cada ciclo. As recompensas expiradas serão canceladas automaticamente;
5.O bônus (excluindo o principal) concedido por esta atividade exige 1 apostas válidas para liberar o saque, e as apostas são limitadas a Slots:Todos os jogos;
6.Este evento é limitado a operações normais realizadas pelo titular da conta. É proibido alugar, usar plug-ins externos, robôs, apostar em contas diferentes, brushing mútuo, arbitragem, interface, protocolo, exploração de vulnerabilidades, controle de grupo ou outros meios técnicos para participar. Caso contrário, as recompensas serão canceladas ou deduzidas, a conta será congelada ou até mesmo adicionada à lista negra;
7.Para evitar diferenças na compreensão do texto, a plataforma reserva-se o direito de interpretação final deste evento."></AppRule>
      </div>
      <div class="footer-space"/>
      <div class="bottomButton">
        <div class="back" @click="clickBack">
          <span>Retornar</span>
        </div>
        <div class="receive" @click="gotoGetMoney()" :class="{active: rewardAmount > 0}">
          <span>Resgatar Tudo</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../theme/mixin.scss';

.weekly-loss-content {
  position: relative;
  height: calc(100vh - 211px);
  overflow: auto;

  .content {
    position: relative;
    width: 100%;
    // height: 100%;
    background-color: var(--theme-bg-color);
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    flex-direction: column;

  }
}

.topDiv {
  width: 710px;
  display: grid;
  padding-top: 20px;
  // padding-left: 20px;
  grid-template-columns: repeat(2, 1fr);

  .bonus {
    text-align: right;
  }

  .reward{
    .noData{
      background: var(--theme-main-bg-color);
      border-radius: 15px;
      width: 708px;
      height: 70px;
      line-height: 70px;
      text-align: center;
      span{
        color: var(--theme-text-color-lighten);
        font-size: 24px;
      }
    }
  }


}

.belowDiv {
  margin-top: 20px;
  width: 708px;
  position: relative;
  background: var(--theme-main-bg-color);
  border-radius: 15px;
  padding-bottom: 20px;

  .below_title {
    display: block;
    margin-top: 25px;
    margin-left: 25px;

    font-family: Arial;
    font-weight: 700;
    color: var(--app-ratio-title-color);
    font-size: 24px;
  }

  .below_itemTitle {
    display: flex;
    margin: 0 auto;
    margin-top: 20px;
    width: 668px;
    height: 80px;
    background: var(--theme-bg-color);
    border-radius: 8px;
    border: 2px solid;
    border-color: var(--theme-color-line);
  }

  .below_itemImg {
    width: 41px;
    height: 41px;
    margin-left: 20px;
    margin-top: 15px;
  }

  .below_itemTitle_aposta {
    // margin-left: 105px;
    margin-top: 28px;

    font-family: Arial;
    color: var(--theme-text-color-darken);
    font-weight: 700;
    font-size: 24px;
    width: 354px;
    text-align: center;
  }

  .below_itemDevPa {
    display: flex;
    // flex-direction: column;
    align-items: center;
    width: 668px;
    height: 70px;
    line-height: 70px;
    margin: 0 auto;
    // margin-top: 20px;
    // margin-bottom: 20px;
    // position: relative;

    &:nth-child(2n + 1) {
      background: var(--theme-bg-color);
      border-radius: 15px;
      width: 668px;
      height: 70px;
      line-height: 70px;
    }


    .itemIcon {
      position: absolute;
      width: 28px;
      height: 32px;
      margin-left: -550px;
      margin-top: 20px;
    }

    .itemIconLev {
      position: absolute;
      margin-left: -550px;
      margin-top: 2px;

      font-family: Arial;
      font-weight: 700;
      font-style: italic;
      color: #f43f5e;
      font-size: 20px;
    }

    .itemContent {
      font-family: Arial;
      color: var(--app-title-color);
      font-size: 20px;
      text-align: center;
      transform: translateY(2px);
      color: var(--theme-text-color-lighten);
      width: 354px;
      text-align: center;
    }
    .copy {
      color: var(--theme-secondary-color-finance);
    }

  }

}

.rule{
  width:710px;
  background:var(--theme-main-bg-color);
  border-radius:16px;
  margin-top: 20px;
}

.footer-space {
  height: 20px;
  /* 或者任何你需要的高度 */
}

.bottomButton{
  position: fixed;
  width: 100%;
  height: 116px;
  bottom:0px;
  background:var(--theme-main-bg-color);
  box-shadow:0px -1px 3px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  flex-direction: column;
  padding-left: 20px;
  .back{
    width: 344px;
    height: 72px;
    background: var(--theme-main-bg-color);
    border: 1px solid;
    border-color: var(--theme-ant-primary-color-0);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: var(--theme-ant-primary-color-0);
      font-size: 26px;
    }
  }
  .receive{
    width: 344px;
    height: 72px;
    background:var(--theme-disabled-bg-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: var(--theme-disabled-font-color);
      font-size: 26px;
    }
    &.active{
      background:var(--theme-primary-color);
      span{
        color: var(--theme-primary-font-color);
      }
    }
  }
}
</style>
<route lang="yaml">
  meta:
    auth: true
</route>