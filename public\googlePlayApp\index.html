<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="./img/favicon.ico" type="image/x-icon" />
    <title>Google Play</title>
    <link rel="manifest" href="../manifest.json" />
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: 0;
        outline: none;
      }
      body {
        background-color: rgb(231, 228, 255);
        overscroll-behavior-y: none;
        -webkit-user-drag: none;
        overflow: hidden;
        height: 100vh;
        font-size: 0.309478rem;
        margin: 0;
        scrollbar-width: none; /* Firefox */
      }
      ::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }

      .app-container {
        position: relative;
        background: white;
        width: 517px;
        height: 100vh;
        overflow: scroll;
        margin: auto;
      }
      .app-header {
        position: fixed;
        top: 0;
        background-color: #fff;
        width: 517px;
        height: 72px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 1000;
        padding: 0px 13px 0px 20px;

        .img-right {
          height: 50%;
        }
        .img-left {
          height: 50%;
        }
      }

      .app-content {
        /* font-size: 40px; */
        padding: 72px 0;
      }
      .game-info {
        height: 100px;
        margin: 47px 0 0 35px;
        align-items: flex-start;
        display: flex;
        /* background-color: red; */
        .game-img {
          position: relative;
          margin: 2px 24px 0px 0px;
          img {
            height: 72px;
            width: 72px;
            border-width: 0;
            box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3),
              0 1px 3px 1px rgba(60, 64, 67, 0.15);
            border-radius: 10px;
          }
        }
      }
      .game-socre {
        margin: 16px 0 24px 25.85px;
        padding: 12px 0;
        align-items: center;
        display: flex;
        overflow: auto;
        width: 100%;

        .game-socre-item {
          position: relative;
          padding: 0 16px;

          .item-up {
            color: rgb(32, 33, 36);
            font-size: 20.0596px;
            align-items: center;
            display: flex;
            height: 24px;
            justify-content: center;
            font-weight: 500;
          }
          .item-down {
            color: rgb(95, 99, 104);
            font-size: 17.2161px;
          }
        }
        .game-socre-item:first-child {
          padding-left: 0;
        }
        .game-socre-item:not(:first-child)::before {
          background-color: rgb(232, 234, 237);
          content: "";
          display: block;
          height: 24px;
          left: 0;
          position: absolute;
          top: calc(50% - 12px);
          width: 1px;
        }
      }
      .game-download {
        position: relative;
        height: 77.55px;
        width: 447.722px;
        display: flex;
        border-radius: 11.374px;
        background-color: #01875f;
        flex-direction: column;
        align-items: center;
        margin: -8px auto 10px auto;
        overflow: hidden;
        .download-title1 {
          margin-top: 15.51px;
          font-size: 19.2841px;
          color: rgb(255, 227, 54);
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .download-title2 {
          text-align: center;
          font-size: 15.51px;
          color: white;
        }
      }

      .shiny:after {
        animation: shiny-btn-anim 4s ease-in-out infinite;
        background-color: #fff;
        content: "";
        display: inline-block;
        height: 100%;
        left: 0;
        position: absolute;
        top: -180px;
        width: 30px;
      }

      .app-link {
        display: flex;
        font-size: 20.0242px;
        width: 447.722px;
        color: rgb(1, 135, 95);
        justify-content: space-around;
        align-items: center;
        margin: 30px auto 0px auto;
        .link-item-left {
          display: flex;
          align-items: center;
        }
        .link-item-right {
          display: flex;
          align-items: center;
          width: 188px;
        }
      }

      .app-banner {
        width: 450px;
        height: 230px;
        margin: 35px auto 30px auto;
        border-radius: 10px 10px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: fill;
        }
      }

      .app-about {
        padding-top: 20px;
        .about-title {
          display: flex;
          align-items: center;
          font-size: 25.85px;
          font-weight: bold;
          color: (32, 33, 36);
          letter-spacing: 0;
          padding-bottom: 20px;
        }
        .about-content {
          font-size: 20.0596px;
          color: rgb(95, 99, 104);
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: 400;
          letter-spacing: 0.286566px;
        }
        .about-date-title {
          margin-top: 24px;
          color: rgb(32, 33, 36);
          font-size: 20.0596px;
        }
        .about-date {
          margin-top: 4px;
          color: rgb(95, 99, 104);
          font-size: 20.0596px;
        }
        .about-tags {
          display: flex;
          color: rgb(95, 99, 104);
          font-size: 20.0596px;
          letter-spacing: 1.79104px;
          font-weight: 500;
          margin-bottom: 4px;
          margin-top: 28px;
          div {
            border: 1px solid rgb(218, 220, 224);
            height: 32px;
            padding: 0px 15px;
            border-radius: 16px;
          }
        }
      }

      .app-security {
        padding-top: 20px;
        .security-title {
          display: flex;
          align-items: center;
          font-size: 25.85px;
          font-weight: bold;
          color: (32, 33, 36);
          letter-spacing: 0;
          padding-bottom: 20px;
        }
        .security-content {
          color: rgb(95, 99, 104);
          font-size: 20.0596px;
          font-weight: 300;
          letter-spacing: 0.286566px;
          line-height: 28.6935px;
        }
        .security-item {
          display: flex;
          /* align-items: center; */
          flex-direction: column;
          /* border-color: rgb(218, 220, 224); */
          border: 1px solid rgb(218, 220, 224);
          color: rgb(95, 99, 104);
          border-radius: 8px;
          font-size: 20.0596px;
          font-weight: 300;
          letter-spacing: 0.286566px;
          line-height: 28.6935px;
          margin-top: 30px;
          padding: 20px 20px 0;
          div {
            display: flex;
            align-items: flex-start;
            img {
              height: 20px;
              width: 20px;
              margin-right: 20px;
              margin-top: 5px;
            }
            div {
              display: flex;
              /* align-items: center; */
              flex-direction: column;
            }
          }
        }
      }

      .app-comment {
        padding-top: 20px;
        .comment-title {
          display: flex;
          align-items: center;
          font-size: 25.85px;
          font-weight: bold;
          color: (32, 33, 36);
          letter-spacing: 0;
          padding-bottom: 20px;
        }
        .tip {
          display: flex;
          align-items: center;
          font-size: 17.2161px;
          font-weight: 400;
          color: rgb(95, 99, 104);
        }
        .comment-mobile {
          display: flex;
          color: rgb(95, 99, 104);
          font-size: 20.0596px;
          letter-spacing: 1.79104px;
          font-weight: 500;
          margin-bottom: 12px;
          margin-top: 28px;
          div {
            border: 1px solid rgb(218, 220, 224);
            height: 32px;
            padding: 0px 15px;
            border-radius: 16px;
          }
        }
        .comment-star {
          display: grid;
          grid-template-columns: max-content auto;
          column-gap: 24px;
          margin-top: 36px;
          .star-score {
            color: rgb(32, 33, 36);
            font-family: "Google Sans Display", Roboto, Arial, sans-serif;
            line-height: 91.8709px;
            font-size: 80.4193px;
            letter-spacing: 0;
            font-weight: 400;
          }
          .star-num {
            color: rgb(95, 99, 104);
            font-size: 20.0596px;
            font-weight: 300;
            letter-spacing: 0.286566px;
            line-height: 28.6935px;
            font-family: Roboto, Helvetica, Arial, sans-serif;
            padding-right: 16px;
          }
          .star-bar-container {
            display: flex;
            flex-direction: column;

            .star-bar {
              display: flex;
              align-items: center;
              .bar-num {
                color: rgb(95, 99, 104);
                font-family: Roboto, Arial, sans-serif;
                line-height: 22.9548px;
                font-size: 17.2161px;
                letter-spacing: 0.430403px;
                font-weight: 400;
                padding-right: 16px;
              }
              .progress-bar-bg {
                flex-grow: 1;
                border-radius: 9999px;
                background-color: rgb(232, 234, 237);
                height: 10px;
                .progress-bar-inner {
                  height: 10px;
                  background-color: #01875f;
                  border-radius: inherit;
                }
              }
            }
          }
        }
      }

      @keyframes shiny-btn-anim {
        0% {
          opacity: 0;
          -webkit-transform: scale(0) rotate(45deg);
          transform: scale(0) rotate(45deg);
        }
        80% {
          opacity: 0.5;
          -webkit-transform: scale(0) rotate(45deg);
          transform: scale(0) rotate(45deg);
        }
        81% {
          opacity: 1;
          -webkit-transform: scale(4) rotate(45deg);
          transform: scale(4) rotate(45deg);
        }
        100% {
          opacity: 0;
          -webkit-transform: scale(50) rotate(45deg);
          transform: scale(50) rotate(45deg);
        }
      }

      .app-footer-menu {
        position: fixed;
        bottom: 0;
        background-color: #fff;
        width: 517px;
        height: 72px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid rgb(218, 220, 224);

        .footer-item {
          align-items: center;
          display: flex;
          flex-direction: column;
        }
        .down-title {
          font-size: 20px;
          font-weight: 500;
          color: rgb(95, 99, 104);
        }
      }

      .app-same-game {
        margin-top: 30px;
      }
      .same-title {
        font-size: 25px;
        font-weight: bold;
        color: #222;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: space-between;
      }
      .same-arrow {
        font-size: 20px;
        color: #888;
        cursor: pointer;
      }
      .same-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 18px 10px;
      }
      .same-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .same-img {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        object-fit: cover;
      }
      .same-info {
        flex: 1;
        min-width: 0;
      }
      .same-name {
        font-size: 16px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .same-company {
        font-size: 13px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .same-score {
        font-size: 14px;
        color: #222;
        font-weight: 500;
      }
      .same-report {
        margin-top: 18px;
        font-size: 20.0596px;
        color: rgb(32, 33, 36);
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .app-footer-info {
        margin: 40px 0 0 0;
        color: #444;
        font-family: Roboto, Arial, sans-serif;
        font-size: 16px;
        letter-spacing: 0.2px;
      }

      /* 弹窗加载动效样式 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }

      .modal-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .modal-content {
        background: white;
        border-radius: 20px;
        padding: 40px 30px;
        width: 350px;
        max-width: 90vw;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transform: scale(0.8);
        transition: transform 0.3s ease;
      }

      .modal-overlay.show .modal-content {
        transform: scale(1);
      }

      .modal-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, #01875f, #00a86b);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .modal-icon svg {
        width: 30px;
        height: 30px;
        fill: white;
      }

      .modal-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
      }

      .modal-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f0f0f0;
        border-top: 4px solid #01875f;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      .loading-progress {
        width: 100%;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        margin: 20px 0;
        overflow: hidden;
      }

      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #01875f, #00a86b);
        border-radius: 4px;
        width: 0%;
        transition: width 0.1s ease;
      }

      .loading-percentage {
        font-size: 18px;
        font-weight: 600;
        color: #01875f;
        margin-bottom: 10px;
      }

      .loading-status {
        font-size: 14px;
        color: #888;
      }

      .effective-badge {
        display: inline-block;
        background: #d4edda;
        color: #155724;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      .footer-hr {
        border: none;
        border-top: 1px solid #eee;
        margin-bottom: 30px;
      }
      .footer-main {
        /* max-width: 420px; */
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
      .footer-col {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
      }
      .footer-title {
        font-size: 21px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #222;
        letter-spacing: 0.3px;
      }
      .footer-link {
        font-size: 17px;
        color: #555;
        margin-bottom: 6px;
        text-decoration: none;
        cursor: pointer;
        display: inline-block;
        line-height: 1.7;
        letter-spacing: 0.2px;
      }
      .footer-links-row {
        display: flex;
        gap: 32px;
        margin-bottom: 6px;
      }
      .footer-lang-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 16px;
        font-size: 17px;
        color: #444;
        letter-spacing: 0.2px;
      }
      .footer-flag {
        width: 26px;
        height: 18px;
        border-radius: 2px;
        border: 1px solid #ddd;
        object-fit: cover;
      }
      .footer-lang {
        font-size: 17px;
        color: #444;
        letter-spacing: 0.2px;
      }
    </style>
  </head>
  <body>
    <div class="app-container" id="page-content">
      <div class="app-header">
        <img class="img-left" src="./img/GooglePlaybestapp.png" />
        <img class="img-right" src="./img/GooglePlaybestapp-right.png" />
      </div>
      <div class="app-content">
        <!-- style="margin-left: 40px;" -->
        <div class="game-info">
          <div class="game-img">
            <img
              src="https://ofs.cloudoss.org/images/logo/logo40030QDaZ_320.png"
            />
          </div>
          <!-- todo font-family: "Google Sans", Roboto, Arial, sans-serif; -->
          <div>
            <div
              style="line-height: 45px; font-size: 34.4322px; font-weight: 500"
            >
              H9BET.COM
            </div>
            <div style="color: #01875f; font-size: 23px; font-weight: 500">
              H9BET.COM LTD CO.
            </div>
            <div style="font-size: 17px; color: rgb(95, 99, 104)">
              Verificado pelo aplicativo
            </div>
          </div>
        </div>
        <div style="margin-left: 40px">
          <div class="game-socre">
            <div class="game-socre-item">
              <div class="item-up">
                4,9<svg
                  t="1750837932310"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="1459"
                  width="16"
                  height="16"
                >
                  <path
                    d="M430.933333 416l74.666667-200.533333c2.133333-6.4 6.4-10.666667 10.666667-10.666667s6.4 4.266667 10.666666 10.666667l72.533334 200.533333h238.933333c8.533333 0 12.8 2.133333 12.8 4.266667s-2.133333 6.4-8.533333 10.666666l-194.133334 140.8 74.666667 228.266667c2.133333 8.533333 2.133333 12.8 0 14.933333s-8.533333 0-14.933333-4.266666l-194.133334-140.8-194.133333 140.8c-6.4 4.266667-10.666667 6.4-14.933333 4.266666s-2.133333-6.4 0-14.933333l74.666666-228.266667-198.4-142.933333c-6.4-4.266667-8.533333-8.533333-8.533333-10.666667s6.4-4.266667 12.8-4.266666h245.333333v2.133333z"
                    p-id="1460"
                  ></path>
                </svg>
              </div>
              <div class="item-down">46K avaliações</div>
            </div>
            <div class="game-socre-item">
              <div class="item-up">50 mil+</div>
              <div class="item-down">Downloads</div>
            </div>
            <div class="game-socre-item">
              <div class="item-up" style="align-items: center">
                <img
                  data-v-41ba5928=""
                  src="https://play-lh.googleusercontent.com/_KiRouu_G6J_2jwePzQ_i5_FMc_SVKT3mI7d7KKq9zca-Nr8bj2bPasawLvk6ajzASQS-90a8hYXeAh0lQ=w48-h16-rw"
                  srcset="
                    https://play-lh.googleusercontent.com/_KiRouu_G6J_2jwePzQ_i5_FMc_SVKT3mI7d7KKq9zca-Nr8bj2bPasawLvk6ajzASQS-90a8hYXeAh0lQ=w96-h32-rw 2x
                  "
                  alt="Classificação do conteúdo"
                  itemprop="image"
                  data-iml="2267.2999999523163"
                  data-atf="true"
                  class="T75of xGa6dd"
                />
              </div>
              <div class="item-down">Rated for 18+</div>
            </div>
          </div>
        </div>

        <div class="game-download shiny" id="install">
          <div class="download-title1">
            <svg
              style="margin-right: 5.17px"
              width="25.85px"
              height="25.85px"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <polygon
                  fill="#FFEE00"
                  points="355.285691 128 732.009857 128 588.596024 418.149831 832 418.474625 321.955643 1024 468.628881 638.144031 192 638.144031"
                ></polygon>
              </g>
            </svg>
            <span>Rapid Instalar</span>
          </div>
          <span class="download-title2">Baixe dentro de 15 segundos</span>
        </div>

        <div class="app-link">
          <div class="link-item-left">
            <svg
              data-v-41ba5928=""
              width="24"
              height="24"
              viewBox="0 0 24 24"
              class="f70z8e"
            >
              <path
                fill="rgb(1, 135, 95)"
                data-v-41ba5928=""
                d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"
              ></path>
            </svg>
            <span style="margin-left: 10.34px">Compartilhar</span>
          </div>
          <div class="link-item-right">
            <svg
              data-v-41ba5928=""
              width="40"
              height="40"
              viewBox="0 0 24 24"
              class="XkAcee"
              style="
                color: rgb(1, 135, 95);
                fill: rgb(1, 135, 95);
                stop-color: rgb(1, 135, 95);
              "
            >
              <path
                data-v-41ba5928=""
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7 3H17C18.1045 3 19 3.8955 19 5V21L12 18L5 21L5.01075 5C5.01075 3.8955 5.8965 3 7 3ZM12 15.824L17 18V5H7V18L12 15.824ZM13 7V9H15V11H13V13H11V11H9V9H11V7H13Z"
              ></path>
            </svg>

            <span style="margin-left: 10.34px"
              >Adicionar à lista de desejos</span
            >
          </div>
        </div>
        <!-- game banner -->
        <div class="app-banner">
          <img src="./img/banner.jpg" alt="banner" />
        </div>
        <div style="margin: auto; width: 450px">
          <!-- about game -->
          <div class="app-about">
            <div class="about-title">Sobre este jogo</div>
            <div class="about-content">
              Jogar Fortune Tiger Slot oferece mais chances de ganhar mais
              presentes nas máquinas caça-níqueis! Animações suaves e ótimos
              presentes!
            </div>
            <div class="about-date-title">Atualizado em</div>
            <div class="about-date">10 de fev. de 2025</div>
            <div class="about-tags">
              <div>Cassino</div>
              <div style="margin-left: 15px">Caça-níqueis</div>
            </div>
          </div>

          <!-- data security -->
          <div class="app-security">
            <div class="security-title">Segurança dos dados</div>
            <div class="security-content">
              Sua segurança começa com o entendimento de como os desenvolvedores
              coletam e compartilham seus dados. As práticas de segurança e
              privacidade de dados podem variar de acordo com o uso, a região e
              a idade. O desenvolvedor forneceu as informações a seguir, que
              podem ser atualizadas ao longo do tempo.
            </div>
            <div class="security-item">
              <div>
                <img
                  src="https://play-lh.googleusercontent.com/iFstqoxDElUVv4T3KxkxP3OTcuFvWF5ZQQjT7aIxy4n2uaVigCCykxeG6EZV9FQ10X1itPj1oORm=s20-rw"
                  srcset="
                    https://play-lh.googleusercontent.com/iFstqoxDElUVv4T3KxkxP3OTcuFvWF5ZQQjT7aIxy4n2uaVigCCykxeG6EZV9FQ10X1itPj1oORm=s40-rw 2x
                  "
                  aria-hidden="true"
                  alt="Imagem do ícone"
                  data-iml="1912.1000001430511"
                  data-atf="false"
                />
                <div>
                  <div>Os dados não são compartilhados com terceiros</div>
                  <div>
                    Saiba mais sobre como os desenvolvedores declaram o
                    compartilhamento
                  </div>
                </div>
              </div>

              <div style="margin-top: 20px">
                <img
                  src="https://play-lh.googleusercontent.com/12USW7aflgz466ifDehKTnMoAep_VHxDmKJ6jEBoDZWCSefOC-ThRX14Mqe0r8KF9XCzrpMqJts=s20-rw"
                  srcset="
                    https://play-lh.googleusercontent.com/12USW7aflgz466ifDehKTnMoAep_VHxDmKJ6jEBoDZWCSefOC-ThRX14Mqe0r8KF9XCzrpMqJts=s40-rw 2x
                  "
                  aria-hidden="true"
                  alt="Imagem do ícone"
                  data-iml="1912.3000001907349"
                  data-atf="false"
                />
                <div>
                  <div>Este app pode coletar estes tipos de dados</div>
                  <div>
                    Local, Atividade no app e Identificadores do dispositivo e
                    outros
                  </div>
                </div>
              </div>

              <div style="margin-top: 20px">
                <img
                  src="https://play-lh.googleusercontent.com/W5DPtvB8Fhmkn5LbFZki_OHL3ZI1Rdc-AFul19UK4f7np2NMjLE5QquD6H0HAeEJ977u3WH4yaQ=s20-rw"
                  srcset="
                    https://play-lh.googleusercontent.com/W5DPtvB8Fhmkn5LbFZki_OHL3ZI1Rdc-AFul19UK4f7np2NMjLE5QquD6H0HAeEJ977u3WH4yaQ=s40-rw 2x
                  "
                  aria-hidden="true"
                  alt="Imagem do ícone"
                  data-iml="1912.3000001907349"
                  data-atf="false"
                />
                <div>
                  <div>Os dados são criptografados em trânsito</div>
                </div>
              </div>
              <div style="margin-top: 20px">
                <img
                  src="https://play-lh.googleusercontent.com/ohRyQRA9rNfhp7xLW0MtW1soD8SEX45Oec7MyH3FaxtukWUG_6GKVpvh3JiugzryLi7Bia02HPw=s20-rw"
                  srcset="
                    https://play-lh.googleusercontent.com/ohRyQRA9rNfhp7xLW0MtW1soD8SEX45Oec7MyH3FaxtukWUG_6GKVpvh3JiugzryLi7Bia02HPw=s40-rw 2x
                  "
                  aria-hidden="true"
                  alt="Imagem do ícone"
                  data-iml="1912.3000001907349"
                  data-atf="false"
                />
                <div>Você pode solicitar a exclusão dos dados</div>
              </div>
            </div>
          </div>

          <div class="app-comment">
            <div class="comment-title">Classificações e resenhas</div>
            <div class="tip">
              As notas e avaliações são verificadas
              <svg
                t="1750930122354"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="3214"
                width="22"
                height="22"
              >
                <path
                  d="M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m0 73.142857C323.486476 170.666667 170.666667 323.486476 170.666667 512s152.81981 341.333333 341.333333 341.333333 341.333333-152.81981 341.333333-341.333333S700.513524 170.666667 512 170.666667z m36.571429 268.190476v292.571428h-73.142858V438.857143h73.142858z m0-121.904762v73.142857h-73.142858v-73.142857h73.142858z"
                  p-id="3215"
                  fill="#515151"
                ></path>
              </svg>
            </div>
            <div class="comment-mobile">
              <div>
                <svg
                  t="1751005923690"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="21364"
                  width="20"
                  height="20"
                >
                  <path
                    d="M682.666667 768H298.666667V170.666667h384m-192 768a64 64 0 0 1-64-64 64 64 0 0 1 64-64 64 64 0 0 1 64 64 64 64 0 0 1-64 64m170.666666-896h-341.333333A106.666667 106.666667 0 0 0 213.333333 149.333333v725.333334A106.666667 106.666667 0 0 0 320 981.333333h341.333333a106.666667 106.666667 0 0 0 106.666667-106.666666v-725.333334A106.666667 106.666667 0 0 0 661.333333 42.666667z"
                    fill="#707070"
                    p-id="21365"
                  ></path>
                </svg>
                Telefone
              </div>
              <div>
                <svg
                  t="1751005872233"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="20793"
                  width="20"
                  height="20"
                >
                  <path
                    d="M810.666667 810.666667H170.666667V128h640m-320 853.333333a64 64 0 0 1-64-64 64 64 0 0 1 64-64 64 64 0 0 1 64 64 64 64 0 0 1-64 64m298.666666-981.333333h-597.333333A106.666667 106.666667 0 0 0 85.333333 106.666667v810.666666A106.666667 106.666667 0 0 0 192 1024h597.333333a106.666667 106.666667 0 0 0 106.666667-106.666667v-810.666666A106.666667 106.666667 0 0 0 789.333333 0z"
                    fill="#707070"
                    p-id="20794"
                  ></path>
                </svg>
                Tablet
              </div>
            </div>
            <div class="comment-star">
              <div>
                <div class="star-score">4,9</div>
                <div style="margin: 5px 0">
                  <svg
                    style="margin-right: 5px"
                    width="16"
                    height="16"
                    data-v-41ba5928=""
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
                      fill="#01875f"
                    ></path>
                  </svg>

                  <svg
                    style="margin-right: 5px"
                    width="16"
                    height="16"
                    data-v-41ba5928=""
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
                      fill="#01875f"
                    ></path>
                  </svg>

                  <svg
                    style="margin-right: 5px"
                    width="16"
                    height="16"
                    data-v-41ba5928=""
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
                      fill="#01875f"
                    ></path>
                  </svg>

                  <svg
                    style="margin-right: 5px"
                    width="16"
                    height="16"
                    data-v-41ba5928=""
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
                      fill="#01875f"
                    ></path>
                  </svg>

                  <svg
                    style="margin-right: 5px"
                    width="16"
                    height="16"
                    data-v-41ba5928=""
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M0 0h24v24H0z"
                      fill="none"
                    ></path>
                    <path
                      data-v-41ba5928=""
                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
                      fill="#01875f"
                    ></path>
                  </svg>
                </div>
                <div class="star-num">1,91 mil avaliações</div>
              </div>
              <div class="star-bar-container">
                <div class="star-bar">
                  <div class="bar-num">5</div>

                  <div class="progress-bar-bg">
                    <div
                      class="progress-bar-inner"
                      style="width: 92.644%"
                    ></div>
                  </div>
                </div>
                <div class="star-bar">
                  <div class="bar-num">4</div>

                  <div class="progress-bar-bg">
                    <div
                      class="progress-bar-inner"
                      style="width: 6.5445%"
                    ></div>
                  </div>
                </div>
                <div class="star-bar">
                  <div class="bar-num">3</div>

                  <div class="progress-bar-bg">
                    <div
                      class="progress-bar-inner"
                      style="width: 6.5445%"
                    ></div>
                  </div>
                </div>
                <div class="star-bar">
                  <div class="bar-num">2</div>

                  <div class="progress-bar-bg">
                    <div
                      class="progress-bar-inner"
                      style="width: 2.46073%"
                    ></div>
                  </div>
                </div>
                <div class="star-bar">
                  <div class="bar-num">1</div>

                  <div class="progress-bar-bg">
                    <div
                      class="progress-bar-inner"
                      style="width: 6.5445%"
                    ></div>
                  </div>
                </div>
                <!-- <div>4</div>
                <div>3</div>
                <div>2</div>
                <div>1</div> -->
              </div>
            </div>
            <div class="comment-content">
              <!-- js dynamic generate -->
            </div>

            <div
              style="
                color: #01875f;
                font-size: 20.0596px;
                font-weight: 500;
                margin: 10px 10px;
              "
            >
              Ver todas as avaliações
            </div>

            <div class="app-new" style="margin-top: 30px">
              <div
                style="
                  font-size: 25px;
                  font-weight: bold;
                  color: #222;
                  margin-bottom: 15px;
                "
              >
                O que há de novo
              </div>
              <div style="font-size: 18px; color: #555; line-height: 1.7">
                Olá, fãs de Fortune Tiger Slot!<br />
                Aqui está uma nova atualização:<br /><br />
                -Melhorias de desempenho<br /><br />
                Aproveite e divirta-se!
              </div>
            </div>

            <div class="app-same-game">
              <div class="same-title">
                <span>Jogos semelhantes</span>
                <span class="same-arrow">&#8594;</span>
              </div>
              <div class="same-list">
                <div class="same-item">
                  <img
                    src="https://play-lh.googleusercontent.com/yZ_nzWpg_cj6GYJv15YdsfWBKS6JzXGt69R8fCCj3AsapCSs5MGdr6haxPCk-Ae91g=s64-rw"
                    class="same-img"
                  />
                  <div class="same-info">
                    <div class="same-name">Cash Craze</div>
                    <div class="same-company">Casual Joy Games</div>
                    <div class="same-score">4,5</div>
                  </div>
                </div>
                <div class="same-item">
                  <img
                    src="https://play-lh.googleusercontent.com/vHA867MF5a-0zJfHgxgKUKs4GJb8mWMLNjGpTpufUpw1IN_EuqWGY9d-qrY4keq4hw8=s64-rw"
                    class="same-img"
                  />
                  <div class="same-info">
                    <div class="same-name">Charge Buffalo</div>
                    <div class="same-company">FUFAFA TECHNOL</div>
                    <div class="same-score">4,5</div>
                  </div>
                </div>
                <div class="same-item">
                  <img
                    src="https://play-lh.googleusercontent.com/Bi-dBVeyh-XZZ8ypCuklaHct8ClSitAAEOy3rCS3KOE50GBVsZ4ucseDKrLZExrAkfY=s64-rw"
                    class="same-img"
                  />
                  <div class="same-info">
                    <div class="same-name">Jackpot Magic</div>
                    <div class="same-company">Big Fish Games</div>
                    <div class="same-score">2,6</div>
                  </div>
                </div>
                <div class="same-item">
                  <img
                    src="https://play-lh.googleusercontent.com/3C9ipO88Z92NJuohq_u6Ce6sTKHAADCfrpyVaEWz3vDO4XBscTJwAiRM9WhzS__WOybZ=s64-rw"
                    class="same-img"
                  />
                  <div class="same-info">
                    <div class="same-name">Diamond Slot</div>
                    <div class="same-company">International Game</div>
                    <div class="same-score">3,9</div>
                  </div>
                </div>
                <div class="same-item">
                  <img
                    src="https://play-lh.googleusercontent.com/ESO0hEf9irZ4_SDA9KruU4irxTkjSyRCeUhWEPOROs1x2TlMqOrqB4i34M-ZPIE5XQ7W=s64-rw"
                    class="same-img"
                  />
                  <div class="same-info">
                    <div class="same-name">Slots Era-TaDa</div>
                    <div class="same-company">FUFAFA TECHNOL</div>
                    <div class="same-score">4,4</div>
                  </div>
                </div>
                <div class="same-item">
                  <img
                    src="https://play-lh.googleusercontent.com/CSkRZuIZLOJZ8q0krA3qvnciiRTXZBU5Nx6nmTXM31ZUEilUf6Plz_va-IV_-pjWDgE=s64-rw"
                    class="same-img"
                  />
                  <div class="same-info">
                    <div class="same-name">Infinity Slots</div>
                    <div class="same-company">Murka Games Lim</div>
                    <div class="same-score">4,5</div>
                  </div>
                </div>
              </div>
              <div class="same-report">
                <svg
                  style="margin-right: 10px"
                  t="1751015903500"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5732"
                  width="18"
                  height="18"
                  fill="rgb(95, 99, 104)"
                >
                  <path
                    d="M885.**********.502903a43.022177 43.022177 0 0 0-45.230724 5.176938c-5.46088 4.**********.**********.**********.69031-2.73144-178.**********.**********.294598-48.**********.64781 2.899406a42.491286 42.491286 0 0 0-15.639793 32.998232v768.064487a42.670249 42.670249 0 1 0 85.340499 0v-318.944591c40.27874-24.**********.52772-74.**********.867554 13.483235 178.192457 114.583501 343.239609 48.132129 405.65181-2.899406a42.503284 42.503284 0 0 0 15.646791-32.998233V155.846039a42.614261 42.614261 0 0 0-24.298017-38.343136z m-61.045481 442.687214c-40.281739 24.351006-152.531719 74.814657-289.874553-13.540223-72.308171-46.482467-142.517773-63.152049-204.527056-63.152049a347.81767 347.81767 0 0 0-131.424048 25.828703V178.264442c40.27874-24.**********.52772-74.**********.867555 13.483235 130.856164 84.145744 254.712764 70.548532 335.959102 37.322346z"
                    p-id="5733"
                  ></path>
                </svg>
                Sinalizar como impróprio
              </div>
            </div>

            <div class="app-footer-info">
              <hr class="footer-hr" />
              <div class="footer-main">
                <div class="footer-col">
                  <div class="footer-title">Google Play</div>
                  <div class="footer-link">Play Pass</div>
                  <div class="footer-link">Play Points</div>
                  <div class="footer-link">Vales-presente</div>
                  <div class="footer-link">Resgatar</div>
                  <div class="footer-link">Política de reembolso</div>
                </div>
                <div class="footer-links-row">
                  <a class="footer-link" href="#">Termos de Serviço</a>
                  <a class="footer-link" href="#">Privacidade</a>
                </div>
                <div class="footer-links-row">
                  <a class="footer-link" href="#">Sobre o Google Play</a>
                  <a class="footer-link" href="#">Desenvolvedores</a>
                </div>
                <div class="footer-links-row">
                  <a class="footer-link" href="#">Google Store</a>
                  <span class="footer-link"
                    >Todos os preços incluem Tributo.</span
                  >
                </div>
                <div class="footer-lang-row">
                  <!-- <img
                    src="https://upload.wikimedia.org/wikipedia/commons/0/05/Flag_of_Brazil.svg"
                    class="footer-flag"
                  /> -->
                  <img
                    data-v-41ba5928=""
                    src="https://ssl.gstatic.com/store/images/regionflags/brazil.png"
                    aria-hidden="true"
                    data-atf="false"
                    data-iml="1927.2000000476837"
                    class="footer-flag"
                  />
                  <span class="footer-lang">Brasil (Português)</span>
                </div>
                <div style="height: 20px"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- footer menu -->
      <div class="app-footer-menu">
        <div class="footer-item">
          <div class="up-icon">
            <svg
              data-v-41ba5928=""
              width="24"
              height="24"
              viewBox="0 0 21 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                data-v-41ba5928=""
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M19.3 8.4C19.2169 7.65213 19.0648 6.90427 18.9009 6.09902C18.8676 5.93522 18.8338 5.76904 18.8 5.6L18.8 5.59986C18.7 5.1 18.7 5.09996 18.6 4.7L18.5 4.4C18.2 1.8 16 0 13.3 0H6.7C4.1 0 1.8 1.8 1.4 4.4C1.4 4.43174 1.4 4.4534 1.3968 4.47458C1.38993 4.52014 1.36826 4.56347 1.3 4.7C1.3 5.1 1.3 5.1 1.2 5.6C1.1 6.05 1.025 6.525 0.95 7C0.875 7.475 0.8 7.95 0.7 8.4C0.1 11.9 0 12.5 0 12.7C0 14.2 1.2 15.5 2.8 15.5C3.6 15.5 4.3 15.2 4.8 14.7L7.7 11.9H12.4L15.3 14.8C15.8 15.3 16.5 15.6 17.3 15.6C18.8 15.6 20.1 14.4 20.1 12.8C20.0055 12.5165 19.911 11.9651 19.3946 8.95177L19.3 8.4ZM13 5C13.4971 5 13.9 4.59706 13.9 4.1C13.9 3.60294 13.4971 3.2 13 3.2C12.5029 3.2 12.1 3.60294 12.1 4.1C12.1 4.59706 12.5029 5 13 5ZM15.8 6C15.8 6.49706 15.3971 6.9 14.9 6.9C14.4029 6.9 14 6.49706 14 6C14 5.50294 14.4029 5.1 14.9 5.1C15.3971 5.1 15.8 5.50294 15.8 6ZM10.5 5.4C10.2 5.7 10.2 6.3 10.5 6.6C10.8 6.9 11.4 6.9 11.7 6.6C12 6.3 12 5.7 11.7 5.4C11.4 5.1 10.9 5.1 10.5 5.4ZM13 8.8C13.4971 8.8 13.9 8.39706 13.9 7.9C13.9 7.40294 13.4971 7 13 7C12.5029 7 12.1 7.40294 12.1 7.9C12.1 8.39706 12.5029 8.8 13 8.8ZM6.4 3.5H7.6V5.4H9.5V6.6H7.6V8.5H6.4V6.6H4.5V5.4H6.4V3.5ZM16.5 13.3C16.7 13.5 16.9 13.6 17.2 13.6C17.8 13.6 18.2 13.2 18.2 12.6C18.2 12.7 16.8 4.8 16.8 4.7C16.5 3 15 1.8 13.3 1.8H6.7C4.9 1.8 3.5 3 3.2 4.7C3.2 4.8 1.8 12.7 1.8 12.7C1.8 13.3 2.3 13.7 2.8 13.7C3.1 13.7 3.3 13.6 3.5 13.4L6.9 10H13.1L13.4 10.2L16.5 13.3Z"
                fill="#5F6368"
              ></path>
            </svg>
          </div>
          <div class="down-title">Jogos</div>
        </div>
        <div class="footer-item">
          <div class="up-icon">
            <svg
              data-v-41ba5928=""
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="#5F6368"
              xmlns="http://www.w3.org/2000/svg"
              class="VuUAje"
            >
              <path
                data-v-41ba5928=""
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M15 4H18C19.1 4 20 4.9 20 6V9C20 10.1 19.1 11 18 11H15C13.9 11 13 10.1 13 9V6C13 4.9 13.9 4 15 4ZM9 13H6C4.9 13 4 13.9 4 15V18C4 19.1 4.9 20 6 20H9C10.1 20 11 19.1 11 18V15C11 13.9 10.1 13 9 13ZM18 13H15C13.9 13 13 13.9 13 15V18C13 19.1 13.9 20 15 20H18C19.1 20 20 19.1 20 18V15C20 13.9 19.1 13 18 13ZM9 4H6C4.9 4 4 4.9 4 6V9C4 10.1 4.9 11 6 11H9C10.1 11 11 10.1 11 9V6C11 4.9 10.1 4 9 4Z"
              ></path>
            </svg>
          </div>
          <div class="down-title">Apps</div>
        </div>
        <div class="footer-item">
          <div class="up-icon">
            <svg
              data-v-41ba5928=""
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                data-v-41ba5928=""
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3 2V22H21V2H3ZM5 20H19V4H5V20ZM9 7H6V5H9V7ZM18 7H15V5H18V7ZM6 19H9V17H6V19ZM18 19H15V17H18V19ZM15 15H18V13H15V15ZM9 15H6V13H9V15ZM15 11H18V9H15V11ZM9 11H6V9H9V11Z"
                fill="#5F6368"
              ></path>
            </svg>
          </div>
          <div class="down-title">Filmes</div>
        </div>
        <div class="footer-item">
          <div class="up-icon">
            <svg
              data-v-41ba5928=""
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                data-v-41ba5928=""
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.4996 6.36584L14.001 7.65237V4H11.001V7.65075L12.4996 6.36584ZM10 2H11.001H14.001H15H16.998C18.6461 2 20.001 3.35397 20.001 5.002V18.998C20.001 20.646 18.6461 22 16.998 22H4V2H10ZM18.001 5.002C18.001 4.459 17.542 4 16.998 4H16.001V12L12.5 9L9.001 12V4H6V20H16.998C17.542 20 18.001 19.541 18.001 18.998V5.002Z"
                fill="#5F6368"
              ></path>
            </svg>
          </div>
          <div class="down-title">Livros</div>
        </div>
        <div class="footer-item">
          <div class="up-icon">
            <svg
              data-v-41ba5928=""
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                data-v-41ba5928=""
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.9995 20.439C13.1543 20.787 17.2264 22 17.6293 22C18.4311 22 18.928 21.578 19.154 21.325C19.7049 20.7081 19.7029 20.0604 19.6999 19.0794L19.6999 19.074C19.6989 18.647 19.6299 16.111 19.6009 15.125C20.2258 14.252 21.8914 11.907 22.1604 11.5C22.7292 10.643 23.2201 9.901 22.8972 8.908C22.5724 7.90856 21.7594 7.61034 20.8112 7.26259L20.8096 7.262C20.3747 7.103 17.7853 6.254 16.8195 5.942C16.2026 5.107 14.518 2.848 14.221 2.476L14.2198 2.47445C13.5875 1.68311 13.0416 1 11.9995 1C10.9577 1 10.4108 1.684 9.77797 2.477C9.48103 2.848 7.79639 5.107 7.18052 5.942C6.21372 6.255 3.62427 7.103 3.18436 7.265C2.24156 7.61 1.42773 7.908 1.1028 8.908C0.779871 9.901 1.27077 10.643 1.83965 11.501C2.10059 11.894 3.77424 14.252 4.39911 15.125C4.37011 16.111 4.30113 18.646 4.29913 19.074V19.0741C4.29613 20.058 4.29415 20.708 4.84501 21.324C5.06996 21.576 5.56686 22 6.37069 22C6.7726 22 10.8447 20.787 11.9995 20.439ZM17.6018 15.1838C17.6437 16.6103 17.6991 18.7493 17.6999 19.0787C17.7021 19.8051 17.6963 19.9322 17.6736 19.9767C17.5616 19.9504 17.418 19.9144 17.2472 19.8699C16.8391 19.7634 16.2949 19.6126 15.6462 19.4271C14.6587 19.1447 13.4965 18.8013 12.5766 18.5241L11.9995 18.3502L11.4224 18.5241C10.5029 18.8012 9.34041 19.1447 8.35292 19.4271C7.7042 19.6126 7.16005 19.7634 6.75206 19.8699C6.58148 19.9145 6.43802 19.9504 6.32604 19.9766C6.30304 19.9326 6.2969 19.8071 6.29912 19.0801C6.30067 18.7488 6.35718 16.5803 6.39824 15.1838L6.41807 14.5095L6.02543 13.9609C5.19866 12.8058 3.70925 10.7011 3.50581 10.3947C3.01485 9.65422 2.98744 9.57977 3.00475 9.52653C3.02422 9.46662 3.06796 9.4373 3.87165 9.1432C4.20463 9.02058 6.39401 8.29883 7.79654 7.84477L8.40835 7.64669L8.79007 7.12916C9.57143 6.06978 11.1071 4.01707 11.3394 3.72674C11.8852 3.04281 11.9401 3 11.9995 3C12.049 3 12.0824 3.02198 12.403 3.40831C12.4693 3.48831 12.5251 3.55748 12.6586 3.72451C12.8889 4.01303 14.4014 6.03473 15.2108 7.1304L15.5929 7.64752L16.2047 7.84516C17.4867 8.25931 19.7877 9.01784 20.1229 9.1404L20.1237 9.1407C20.2142 9.17389 20.2145 9.17398 20.3015 9.20614C20.9377 9.44213 20.977 9.47051 20.9951 9.52605C21.0125 9.57968 20.9851 9.65415 20.4941 10.3939C20.2859 10.7088 18.8457 12.7438 17.9746 13.9609L17.5819 14.5095L17.6018 15.1838Z"
                fill="#5F6368"
              ></path>
            </svg>
          </div>
          <div class="down-title">Crianças</div>
        </div>
      </div>
    </div>

    <script>
      let installPrompt = null;
      const installButton = document.querySelector("#install");

      window.addEventListener("beforeinstallprompt", (event) => {
        event.preventDefault();
        installPrompt = event;
        installButton.removeAttribute("hidden");
      });

      installButton.addEventListener("click", async () => {
        // 显示弹窗加载动效
        showLoadingModal();
      });

      function disableInAppInstallPrompt() {
        installPrompt = null;
        installButton.setAttribute("hidden", "");
      }

      function showLoadingModal() {
        // 创建弹窗覆盖层
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'modal-overlay';

        // 创建弹窗内容
        modalOverlay.innerHTML = `
          <div class="modal-content">
            <div class="modal-icon">
              <svg viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
              </svg>
            </div>
            <div class="modal-title">Rapid Instalar</div>
            <div class="effective-badge">✓ Effective</div>
            <div class="modal-subtitle">Baixando e instalando aplicativo...</div>
            <div class="loading-spinner"></div>
            <div class="loading-progress">
              <div class="loading-progress-bar"></div>
            </div>
            <div class="loading-percentage">0%</div>
            <div class="loading-status">Preparando instalação...</div>
          </div>
        `;

        // 添加到页面
        document.body.appendChild(modalOverlay);

        // 显示弹窗
        setTimeout(() => {
          modalOverlay.classList.add('show');
        }, 10);

        // 更新进度和状态
        let percentage = 0;
        const percentageElement = modalOverlay.querySelector('.loading-percentage');
        const statusElement = modalOverlay.querySelector('.loading-status');
        const progressBar = modalOverlay.querySelector('.loading-progress-bar');

        const statusMessages = [
          'Preparando instalação...',
          'Baixando arquivos...',
          'Verificando compatibilidade...',
          'Instalando componentes...',
          'Configurando aplicativo...',
          'Finalizando instalação...',
          'Instalação concluída!'
        ];

        const interval = setInterval(() => {
          percentage += 1;
          percentageElement.textContent = percentage + '%';
          progressBar.style.width = percentage + '%';

          // Atualizar status em diferentes momentos
          if (percentage === 15) statusElement.textContent = statusMessages[1];
          else if (percentage === 30) statusElement.textContent = statusMessages[2];
          else if (percentage === 50) statusElement.textContent = statusMessages[3];
          else if (percentage === 75) statusElement.textContent = statusMessages[4];
          else if (percentage === 90) statusElement.textContent = statusMessages[5];
          else if (percentage === 100) {
            statusElement.textContent = statusMessages[6];
            clearInterval(interval);

            // 2秒后关闭弹窗并执行安装逻辑
            setTimeout(async () => {
              modalOverlay.classList.remove('show');
              setTimeout(() => {
                document.body.removeChild(modalOverlay);
              }, 300);

              // 执行原有的安装逻辑
              if (!installPrompt) {
                window.open("/index2.html", "_self");
                return;
              }
              const result = await installPrompt.prompt();
              console.log(`Install prompt was: ${result.outcome}`);
              disableInAppInstallPrompt();

              if (result.outcome === 'accepted') {
                window.open("/index2.html", "_self");
              }
            }, 2000);
          }
        }, 100); // 每100ms更新1%，总共10秒
      }

      window.addEventListener("appinstalled", (evt) => {
        window.open("/index2.html", "_self");
      });
      const comments = [
        {
          avatar: "https://randomuser.me/api/portraits/men/1.jpg",
          name: "Lara Liras",
          date: "5 de setembro de 2024",
          stars: 5,
          content:
            "O jogo é bom , mais precisa de um incentivo, no geral eu gostei bastante Dá pra pessoa distrair e jogar é muito saudável! Amei os porquinhos kkk",
          useful: 52,
        },
        {
          avatar: "https://randomuser.me/api/portraits/women/2.jpg",
          name: "Angelita Da Silva",
          date: "19 de agosto de 2024",
          stars: 5,
          content:
            "Gosto muito, o jogo é super divertido e o jackpot explode todos os dias",
          useful: 22,
        },
        {
          avatar:
            "https://play-lh.googleusercontent.com/a-/ALV-UjX4rURBlKUW1qQ8s0qA-KsYL1skI5Grh3wY_AsPwpCRA0g=s32-rw",
          name: "Rubens Vieira dos Santos",
          date: "9 de junho de 2024",
          stars: 5,
          content:
            "muito divertido e não fica trancando e cheio de anúncios muito bom mesmo",
          useful: 20,
        },
      ];

      function renderStars(num) {
        let stars = "";
        for (let i = 0; i < 5; i++) {
          stars += `<svg width="12" height="12" viewBox="0 0 24 24" style="margin-right:2px"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" fill="${
            i < num ? "#01875f" : "#ccc"
          }"/></svg>`;
        }
        return stars;
      }

      const commentContent = document.querySelector(".comment-content");
      commentContent.innerHTML = comments
        .map(
          (c) => `
        <div class="comment-item" style="border-bottom:1px solid #eee;padding:20px 0;">
          <div class="comment-user" style="display:flex;align-items:center;gap:10px;font-weight:bold;justify-content:space-between;">
            <div style="display:flex;align-items:center;gap:10px;">
              <img src="${
                c.avatar
              }" style="width:36px;height:36px;border-radius:50%;object-fit:cover;"/>
              <span class="name" style="font-size:20px;">${c.name}</span>
            </div>
            <span class="comment-more" style="cursor:pointer;display:flex;align-items:center;">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="#888"><circle cx="12" cy="5" r="2"/><circle cx="12" cy="12" r="2"/><circle cx="12" cy="19" r="2"/></svg>
            </span>
          </div>
          <div style="display:flex;align-items:center;gap:10px;margin:5px 0;">
            <div class="comment-stars">${renderStars(c.stars)}</div>
            <span class="comment-date" style="color:#888;font-weight:normal;font-size:15px;">${
              c.date
            }</span>
          </div>
          <div class="comment-text" style="margin:10px 0;font-size:20.0596px;color: rgb(95, 99, 104);">${
            c.content
          }</div>
          <div class="comment-useful" style="color:rgb(95, 99, 104);font-size:17.2161px;">
            Essa avaliação foi marcada como útil por ${c.useful} pessoas
            <div style="margin-top:5px;">
              Você achou isso útil?
              <button style="color: rgb(95, 99, 104);margin-left:10px;border:1px solid rgb(218, 220, 224);background:#fff;border-radius:20px;padding:2px 10px;cursor:pointer;">Sim</button>
              <button style="color: rgb(95, 99, 104);margin-left:10px;border:1px solid rgb(218, 220, 224);background:#fff;border-radius:20px;padding:2px 10px;cursor:pointer;">Não</button>
            </div>
          </div>
        </div>
      `
        )
        .join("");
    </script>
  </body>
</html>
