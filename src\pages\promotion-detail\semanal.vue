<script setup lang='ts' name='semanal'>
const { userInfo } = storeToRefs(useAppStore())
const url = computed(() => location.origin + '/register' + `?id=${userInfo.value.semanal_code}`)
const isLeft = ref(true)
const btnType = ref('HOJE')
const showDatePicker = ref(false)
const currentDate = ref({
  start: dayjs().subtract(7, 'day').format('YYYY/MM/DD'),
  end: dayjs().format('YYYY/MM/DD')
})
const dateStr = computed(() => dayjs(currentDate.value.start).format('YYYY-MM-DD') + ' - ' + dayjs(currentDate.value.end).format('YYYY-MM-DD'))
const dataList = ref([
  {left: '5.000- 10.000',right: 'R$50 + N1 X 0.2%'},
  {left: '5.000- 10.000',right: 'R$50 + N1 X 0.2%'},
  {left: '5.000- 10.000',right: 'R$50 + N1 X 0.2%'},
  {left: '5.000- 10.000',right: 'R$50 + N1 X 0.2%'},
  {left: '5.000- 10.000',right: 'R$50 + N1 X 0.2%'},
])
const goToPage = (index: any) => {
  if (index === 0) {
    isLeft.value = true
  } else if (index === 1) {
    isLeft.value = false
  }
}
const toCopy = (str: string) => {
  showToast('Copied!')
  copy(str)
}
const queryBtn = (type: string) => {
  btnType.value = type
}
const onConfirm = (data: any) => {
  currentDate.value = data
}
</script>
<template>
  <div class="semanal">
    <AppIndexHeader />
    <!-- 顶部按钮 -->
    <div class="semanal-top-btn">
      <AppButton fontSize="32" radius="10" whiteText :red1="isLeft" :red2="!isLeft" width="350" height="80" @click="goToPage(0)">SEMANA</AppButton>
      <AppButton fontSize="32" radius="10" whiteText :red1="!isLeft" :red2="isLeft" width="350" height="80" @click="goToPage(1)">Semana anterior </AppButton>
    </div>
    <!-- 查询条件 -->
    <div class="semanal-query">
      <div class="semanal-query-input">
        <div class="date" @click="showDatePicker = true">{{ dateStr }}</div>
        <AppImage src="/icons/invite_search.webp" />
      </div>
      <div class="semanal-query-label">
        <div class="left">Apostas totais</div>
        <div class="right">0.00</div>
      </div>
    </div>
    <AppDatePicker v-model="showDatePicker" :startDate="currentDate.start" group @confirm="onConfirm" />
    <div class="semanal-text">
      <div class="semanal-text-title">BONUS DE GRADIENTE DE CONVITE</div>
      <div class="semanal-text-body">
        1.A medida que o numero de membros que completamseu pr
        imeiro deposito aumenta e atinge o gradientenumérico corre
        spondente,voce pode receber bonusadicionais
      </div>
      <div class="semanal-text-body">
        1.A medida que o numero de membros que completamseu pr
        imeiro deposito aumenta e atinge o gradientenumérico corre
        spondente,voce pode receber bonusadicionais
      </div>
      <div class="semanal-text-body">
        1.A medida que o numero de membros que completamseu pr
        imeiro deposito aumenta e atinge o gradientenumérico corre
        spondente,voce pode receber bonusadicionais
      </div>
    </div>
    <!-- 列表 -->
    <div class="semanal-list">
      <div>
        <div class="semanal-list-item">
          <div class="semanal-list-item-top">
            <div class="semanal-list-item-top-left">Apostas validas</div>
            <div class="semanal-list-item-top-right">salario semanal</div>
          </div>
          <div class="semanal-list-item-body" v-for="item in dataList" :key="item.id">
            <div class="semanal-list-item-body-item">
              <div class="semanal-list-item-body-item-title">{{ item.left }}</div>
            </div>
            <div class="semanal-list-item-body-item">
              <div class="semanal-list-item-body-item-title">{{ item.right }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang='scss' scoped>
@import '../../theme/mixin.scss';
.semanal {
  padding-bottom: 120px;
  &-top-btn {
    margin-top: 130px;
    width: 100%;
    height: 80px;
    button{
      display: inline-block;
      margin-left: 17px;
    }
  }
  &-query{
    width: 723px;
    margin: 0 auto;
    margin-top: 22px;
    &-input{
      width: 717px;
      height: 64px;
      line-height: 64px;
      background:#e2e8f0;
      border-radius:10px;
      margin-top: 20px;
      position: relative;
      .date{
        margin-left: 22px;
        font-family:Arial;
        color:#474b51;
        font-size:24px;
      }
      img{
        position: absolute;
        width: 52px;
        right: 13px;
        top: 6px;
      }
    }
    &-label{
      width: 717px;
      height: 89px;
      line-height: 89px;
      background:#e2e8f0;
      border-radius:10px;
      margin-top: 15px;
      position: relative;
      .left{
        position: absolute;
        left: 30px;
        font-family:Arial;
        color:#474b51;
        font-size:24px;
      }
      .right{
        position: absolute;
        right: 35px;
        font-family:Arial;
        color:#474b51;
        font-size:24px;
      }
    }
  }
  &-text{
    width: 655px;
    margin: 0 auto;
    margin-top: 40px;
    &-title{
      font-family:Arial;
      font-weight:700;
      color:#474b51;
      font-size:30px;
    }
    &-body{
      font-family:Arial;
      color:#474b51;
      font-size:24px;
      margin-top: 17px;
      margin-top: 17px;
    }
  }
  &-list{
    width: 705px;
    margin: 0 auto;
    margin-top: 30px;
    &-item{
      width: 100%;
      height: 143px;
      margin-bottom: 20px;
      &-top{
        width: 100%;
        height: 55px;
        background-color: #df2646;
        color: #fff;
        font-family:Arial;
        font-size:24px;
        position: relative;
        border-radius:10px 10px 0px 0px ;
        &-left{
          position: absolute;
          top: 12px;
          left: 108px;
        }
        &-right{
          position: absolute;
          top: 12px;
          right: 110px;
        }
      }
      &-body{
        width: 100%;
        height: 64px;
        line-height: 64px;
        background-color: #fff;
        display: flex;
        border: 1px solid #e2e8f0;;
        &-item{
          width: 50%;
          text-align: center;
          &-title{
            font-family:Arial;
            color:#474b51;
            font-size:24px;
          }
        }
        &:last-child{
          border-radius:0px 0px 10px 10px;
          border: none;
        }
      }
    }
  }
}
</style>

<route lang="yaml">
  meta:
    auth: true
</route>
