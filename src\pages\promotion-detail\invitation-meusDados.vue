<script setup lang='ts' name='promotion'>
const router = useRouter()
const timeSelect = ref(0)
const data=[    {text:"Ontem",type:1},{text:"Hoje",type:2},{text:"Está Semana",type:3},
                {text:"Última Semana",type:4},{text:"Este Mês",type:5},{text:"Mês Passado",type:6}           
              ]

timeSelect.value = data[0].type

//获取数据
function getData(type:any){
  timeSelect.value =type
}

function getMeuTime(){
  console.log("meu time")
}

function getDesem(){
  console.log("desem")
}

function getCom(){
  console.log("com")
}


</script>
<template>
  <div class="meusDados">
    <!--时间 -->
    <div class="timeSelect">
        <div v-for="item in data" class="timeSelectItem" @click="getData(item.type)" :class="{active : item.type ==timeSelect}" >
          <label class="timeSelect_text">{{ item.text }} </label>
        </div>
    </div>
    <!--个人代理信息  -->
    <div class="info">
      <div class="info_item">
        <label class="item_text">Adicionar membros diretos</label>
        <label class="item_num">0</label>
      </div>

      <div class="info_item">
        <label class="item_text">Primeiros Depósitos</label>
        <label class="item_num">0</label>
      </div>
      
      <div class="info_item">
        <label class="item_text">Depósitos</label>
        <label class="item_num">0</label>
      </div>
      <!--下一行-->
      <div class="info_item">
        <label class="item_text">Depósito</label>
        <label class="item_num">0,00</label>
      </div>

      <div class="info_item">
        <label class="item_text">Desempenho</label>
        <label class="item_num">0,00</label>
      </div>

      <div class="info_item">
        <label class="item_text">Comissão</label>
        <label class="item_num item_num2">0,00</label>
      </div>
    </div>

    <!--Visão Geral dos Dados  -->
    <div class="visao">
      <label class="visaoTitle">Visão Geral dos Dados</label>
      <div class="line"></div>
      <!-- meu time -->
       <div class="meuTime"> 
          <label class="meuTime_text" @click="getMeuTime">Meu Time</label>
          <AppImage src="/icons/agent_arrRight" class="arrRight" @click="getMeuTime"/>
          <div class="meuTimeInfo">
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Membros</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">0</label>
              </div>
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Membros Diretos</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">0</label>
              </div>
              <div class="meuTimeInfo_item">
                <label class="meuTimeInfo_item_text">Outros Membros</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">0</label>
              </div>
          </div>
       </div>

       <div class="meuTime"> 
          <label class="meuTime_text" @click="getDesem">Desempenho</label>
          <AppImage src="/icons/agent_arrRight" class="arrRight" @click="getDesem" />
          <div class="meuTimeInfo">
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Rendimento Total</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">0,00</label>
              </div>
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">D. Direto</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">0,00</label>
              </div>
              <div class="meuTimeInfo_item">
                <label class="meuTimeInfo_item_text">D. dos Outros</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num">0,00</label>
              </div>
          </div>
       </div>

       <div class="meuTime"> 
          <label class="meuTime_text" @click="getCom">Comissão</label>
          <AppImage src="/icons/agent_arrRight" class="arrRight" @click="getCom" />
          <div class="meuTimeInfo">
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Comissões Totais</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num2">0,00</label>
              </div>
              <div class="meuTimeInfo_item meuTimeInfo_item_right">
                <label class="meuTimeInfo_item_text">Comissão Direta</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num2">0,00</label>
              </div>
              <div class="meuTimeInfo_item">
                <label class="meuTimeInfo_item_text">Outra Comissão</label>
                <label class="meuTimeInfo_item_text meuTimeInfo_item_num2">0,00</label>
              </div>
          </div>
       </div>


    </div>


  </div>
</template>

<style lang='scss' scoped>
.meusDados{
   width: 100%;
   font-size:23px;

  
}

.timeSelect{
  margin-top: 5px;
  width:100%;
  height:82px;
  background:#164633;

  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;

  .timeSelectItem{
    background:#164633;
    border:1px solid;
    border-color:var(--app-page-bg-color3);
    border-radius:103px;
    padding: 8px 18px;
    margin: 10px;
    color:#C5E2D2;
    &.active{
      color: #164633;
      background:#FFF0BB;
    }
  }

  .timeSelect_text{
    white-space: nowrap;
  }
}


.info{
  width: 100%;
  height: 360px;

  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;
  flex-wrap:wrap;

  .info_item{
    width:224px;
    height:148px;
    background:var(--theme-text-color-lighten);
    border-radius:10px;
    margin-left: 20px;
  }

  .item_text{
    display: flex;
    color:var(--theme-text-color-lighten);
    align-items: center;
    justify-content: center;
    margin-left: 10%;
    margin-top: 15px;
    width: 80%;
    text-align: center;
    // background-color: #C5E2D2;
    height: 60px;
  }

  .item_num{
    display: inline-block;
    font-size: 32px;
    color: #FFFFFF;
    width: 100%;
    text-align: center;
    margin-top: 15px;
  }

  .item_num2{
    color: #f7b10b;
  }

}

.visao{
  margin: 0 auto;
  width:710px;
  height:654px;
  background:var(--theme-main-bg-color);
  border-radius:10px;

  .visaoTitle{
    display: block;
    color:#ffffff;
    font-size:24px;
    padding-top: 25px;
    padding-left: 20px;
    margin-bottom: 25px;
  }

  .line{
    // position: absolute;
    // top: 180px;
    width:100%;
    height:1px;
    background:var(--app-page-bg-color3);
    // margin-top: 25px;

  }

  .meuTime{
    // background:var(--app-page-bg-color3);
   
    width:710px;
   
    .meuTime_text{
      // display: flex;
      // align-items: center;
      // justify-content: center;

      display: inline-block;
      margin-left:7.5%;
      width: 85%;
      text-align: center;
      color:var(--theme-text-color);
      // background-color: #C5E2D2;
      // height: 100%;
      line-height: 100%;
      padding-top: 17.5px;
      height:65px;
    }
    .arrRight{
      position: absolute;
      float:right;
      width: 14px;
      margin-top: 17.5px;
    }

    .meuTimeInfo{
      width:93%;
      height:125px;
      margin: 0 auto;
      border-top: 1px solid;
      border-bottom: 1px solid;
      border-color:var(--app-page-bg-color3);
      display: flex;
      align-items: center;
      justify-content: center;
      .meuTimeInfo_item{
        width: 33.3%;
        height: 60%;
        // background-color: #C5E2D2;
      }
      .meuTimeInfo_item_right{
        border-right: 1px solid;
        border-color:var(--app-page-bg-color3);
      }

      .meuTimeInfo_item_text{
        display: flex;
        color:var(--theme-text-color-lighten);
        align-items: center;
        justify-content: center;
        margin-left: 10%;
        width: 80%;
       
      }

      .meuTimeInfo_item_num{
        margin-top: 15px;
        color: #ffffff;
        
      }

      .meuTimeInfo_item_num2{
        margin-top: 15px;
        color: #f7b10b;
        
      }

      
    }
  }

  .color{
    color: #f7b10b;
  }

  .desemenho{
    margin-top: 120px;
  }

}

</style>
